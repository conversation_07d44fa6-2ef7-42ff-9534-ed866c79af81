// Frontend Currency Service
class CurrencyService {
  private exchangeRates: { [key: string]: number } = {
    USD: 1.0,
    SAR: 3.75,
    EUR: 0.85,
    GBP: 0.73,
    AED: 3.67,
    KWD: 0.30,
    QAR: 3.64,
    BHD: 0.38,
    OMR: 0.38
  };

  private lastUpdated: number | null = null;
  private readonly CACHE_DURATION = 60 * 60 * 1000; // 1 hour

  // Get current exchange rates
  async getExchangeRates(): Promise<{ [key: string]: number }> {
    try {
      // Check if cache is still valid
      if (this.lastUpdated && (Date.now() - this.lastUpdated) < this.CACHE_DURATION) {
        return this.exchangeRates;
      }

      // Try to fetch fresh rates from backend
      try {
        const response = await fetch('http://localhost:5000/api/currency/rates');
        if (response.ok) {
          const data = await response.json();
          if (data.success && data.data && data.data.rates) {
            this.exchangeRates = data.data.rates;
            this.lastUpdated = Date.now();
            console.log('✅ Currency rates updated from backend');
          }
        }
      } catch (apiError) {
        console.warn('⚠️ Failed to fetch fresh currency rates, using cached rates:', apiError);
      }

      return this.exchangeRates;
    } catch (error) {
      console.error('❌ Currency service error:', error);
      return this.exchangeRates; // Return cached rates as fallback
    }
  }

  // Convert amount from one currency to another
  async convertCurrency(amount: number, fromCurrency: string, toCurrency: string): Promise<number> {
    try {
      if (fromCurrency === toCurrency) {
        return amount;
      }

      const rates = await this.getExchangeRates();
      
      // Convert to USD first, then to target currency
      const usdAmount = amount / rates[fromCurrency];
      const convertedAmount = usdAmount * rates[toCurrency];
      
      return Math.round(convertedAmount * 100) / 100; // Round to 2 decimal places
    } catch (error) {
      console.error('❌ Currency conversion error:', error);
      throw new Error('Currency conversion failed');
    }
  }

  // Convert amount to USD (base currency for comparisons)
  async convertToUSD(amount: number, fromCurrency: string): Promise<number> {
    return this.convertCurrency(amount, fromCurrency, 'USD');
  }

  // Convert amount from USD to target currency
  async convertFromUSD(amount: number, toCurrency: string): Promise<number> {
    return this.convertCurrency(amount, 'USD', toCurrency);
  }

  // Get currency symbol
  getCurrencySymbol(currency: string): string {
    const symbols: { [key: string]: string } = {
      USD: '$',
      SAR: 'ر.س',
      EUR: '€',
      GBP: '£',
      AED: 'د.إ',
      KWD: 'د.ك',
      QAR: 'ر.ق',
      BHD: 'د.ب',
      OMR: 'ر.ع'
    };
    return symbols[currency] || currency;
  }

  // Format amount with currency
  formatAmount(amount: number, currency: string): string {
    // Handle null/undefined amounts
    if (amount === null || amount === undefined || isNaN(amount)) {
      amount = 0;
    }

    const symbol = this.getCurrencySymbol(currency);
    const formattedAmount = amount.toLocaleString();
    
    // For Arabic currencies, put symbol after number
    if (['SAR', 'AED', 'KWD', 'QAR', 'BHD', 'OMR'].includes(currency)) {
      return `${formattedAmount} ${symbol}`;
    }
    
    // For Western currencies, put symbol before number
    return `${symbol}${formattedAmount}`;
  }

  // Get supported currencies
  getSupportedCurrencies() {
    return [
      { code: 'USD', name: 'US Dollar', symbol: '$' },
      { code: 'SAR', name: 'Saudi Riyal', symbol: 'ر.س' },
      { code: 'EUR', name: 'Euro', symbol: '€' },
      { code: 'GBP', name: 'British Pound', symbol: '£' },
      { code: 'AED', name: 'UAE Dirham', symbol: 'د.إ' },
      { code: 'KWD', name: 'Kuwaiti Dinar', symbol: 'د.ك' },
      { code: 'QAR', name: 'Qatari Riyal', symbol: 'ر.ق' },
      { code: 'BHD', name: 'Bahraini Dinar', symbol: 'د.ب' },
      { code: 'OMR', name: 'Omani Rial', symbol: 'ر.ع' }
    ];
  }

  // Validate currency code
  isValidCurrency(currency: string): boolean {
    const supportedCurrencies = ['USD', 'SAR', 'EUR', 'GBP', 'AED', 'KWD', 'QAR', 'BHD', 'OMR'];
    return supportedCurrencies.includes(currency);
  }

  // Get user's preferred currency from localStorage or default
  getUserCurrency(): string {
    try {
      return localStorage.getItem('preferredCurrency') || 'SAR';
    } catch {
      return 'SAR';
    }
  }

  // Set user's preferred currency
  setUserCurrency(currency: string): void {
    try {
      if (this.isValidCurrency(currency)) {
        localStorage.setItem('preferredCurrency', currency);
      }
    } catch (error) {
      console.error('Failed to save currency preference:', error);
    }
  }

  // Convert auction data to user's preferred currency
  async convertAuctionData(auction: any, userCurrency?: string): Promise<any> {
    const targetCurrency = userCurrency || this.getUserCurrency();
    
    if (!auction.currency || auction.currency === targetCurrency) {
      return auction;
    }

    try {
      const convertedAuction = { ...auction };
      
      // Convert prices
      if (auction.currentBid) {
        convertedAuction.currentBid = await this.convertCurrency(
          auction.currentBid, 
          auction.currency, 
          targetCurrency
        );
      }
      
      if (auction.startingPrice) {
        convertedAuction.startingPrice = await this.convertCurrency(
          auction.startingPrice, 
          auction.currency, 
          targetCurrency
        );
      }
      
      if (auction.reservePrice) {
        convertedAuction.reservePrice = await this.convertCurrency(
          auction.reservePrice, 
          auction.currency, 
          targetCurrency
        );
      }
      
      // Update currency
      convertedAuction.currency = targetCurrency;
      convertedAuction.originalCurrency = auction.currency;
      
      return convertedAuction;
    } catch (error) {
      console.error('Failed to convert auction data:', error);
      return auction; // Return original data if conversion fails
    }
  }
}

export const currencyService = new CurrencyService();
export default currencyService;
