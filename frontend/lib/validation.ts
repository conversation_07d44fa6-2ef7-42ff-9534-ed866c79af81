// Comprehensive validation utilities to prevent user bypass attempts

export class ValidationError extends Error {
  constructor(message: string, public field?: string) {
    super(message)
    this.name = 'ValidationError'
  }
}

// Sanitize input to prevent XSS and injection attacks
export const sanitizeInput = (input: string): string => {
  if (typeof input !== 'string') return ''
  
  return input
    .trim()
    .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
    .replace(/javascript:/gi, '')
    .replace(/on\w+\s*=/gi, '')
    .replace(/[<>]/g, '')
}

// Validate email format
export const validateEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email.trim().toLowerCase())
}

// Validate phone number (Saudi format)
export const validatePhone = (phone: string): boolean => {
  const phoneRegex = /^(\+966|966|0)?[5][0-9]{8}$/
  return phoneRegex.test(phone.replace(/\s/g, ''))
}

// Validate national ID (Saudi format)
export const validateNationalId = (id: string): boolean => {
  const idRegex = /^[12][0-9]{9}$/
  return idRegex.test(id)
}

// Validate commercial register
export const validateCommercialRegister = (cr: string): boolean => {
  const crRegex = /^[0-9]{10}$/
  return crRegex.test(cr)
}

// Validate monetary amounts
export const validateAmount = (amount: string | number, min: number = 0, max: number = Infinity): boolean => {
  const numAmount = typeof amount === 'string' ? parseFloat(amount) : amount
  
  if (isNaN(numAmount)) return false
  if (numAmount < min) return false
  if (numAmount > max) return false
  if (numAmount !== Math.floor(numAmount * 100) / 100) return false // Max 2 decimal places
  
  return true
}

// Validate bid amount against current price
export const validateBidAmount = (bidAmount: string | number, currentPrice: number, minIncrement: number = 1): { isValid: boolean; error?: string } => {
  const numBid = typeof bidAmount === 'string' ? parseFloat(bidAmount) : bidAmount
  
  if (isNaN(numBid)) {
    return { isValid: false, error: 'مبلغ المزايدة غير صحيح' }
  }
  
  if (numBid <= currentPrice) {
    return { isValid: false, error: 'يجب أن تكون المزايدة أعلى من السعر الحالي' }
  }
  
  if (numBid < currentPrice + minIncrement) {
    return { isValid: false, error: `الحد الأدنى للزيادة هو ${minIncrement}` }
  }
  
  if (numBid > currentPrice * 10) {
    return { isValid: false, error: 'مبلغ المزايدة مرتفع جداً' }
  }
  
  return { isValid: true }
}

// Validate date ranges
export const validateDateRange = (startDate: Date | string, endDate: Date | string): { isValid: boolean; error?: string } => {
  const start = new Date(startDate)
  const end = new Date(endDate)
  const now = new Date()
  
  if (isNaN(start.getTime()) || isNaN(end.getTime())) {
    return { isValid: false, error: 'تاريخ غير صحيح' }
  }
  
  if (start >= end) {
    return { isValid: false, error: 'تاريخ البداية يجب أن يكون قبل تاريخ النهاية' }
  }
  
  if (end <= now) {
    return { isValid: false, error: 'تاريخ النهاية يجب أن يكون في المستقبل' }
  }
  
  return { isValid: true }
}

// Validate file uploads
export const validateFile = (file: File, allowedTypes: string[], maxSize: number): { isValid: boolean; error?: string } => {
  if (!file) {
    return { isValid: false, error: 'لم يتم اختيار ملف' }
  }
  
  if (!allowedTypes.includes(file.type)) {
    return { isValid: false, error: 'نوع الملف غير مدعوم' }
  }
  
  if (file.size > maxSize) {
    return { isValid: false, error: `حجم الملف يجب أن يكون أقل من ${maxSize / 1024 / 1024}MB` }
  }
  
  return { isValid: true }
}

// Validate user permissions
export const validateUserPermissions = (userRole: string, requiredRoles: string[]): boolean => {
  if (!userRole || !requiredRoles.length) return false
  return requiredRoles.includes(userRole)
}

// Validate form data comprehensively
export const validateFormData = (data: Record<string, any>, rules: Record<string, any>): { isValid: boolean; errors: Record<string, string> } => {
  const errors: Record<string, string> = {}
  
  for (const [field, rule] of Object.entries(rules)) {
    const value = data[field]
    
    // Required field check
    if (rule.required && (!value || (typeof value === 'string' && !value.trim()))) {
      errors[field] = rule.requiredMessage || `${field} مطلوب`
      continue
    }
    
    // Skip validation if field is empty and not required
    if (!value && !rule.required) continue
    
    // Type validation
    if (rule.type === 'email' && !validateEmail(value)) {
      errors[field] = 'البريد الإلكتروني غير صحيح'
    }
    
    if (rule.type === 'phone' && !validatePhone(value)) {
      errors[field] = 'رقم الهاتف غير صحيح'
    }
    
    if (rule.type === 'number' && !validateAmount(value, rule.min, rule.max)) {
      errors[field] = 'الرقم غير صحيح'
    }
    
    // Length validation
    if (rule.minLength && value.length < rule.minLength) {
      errors[field] = `يجب أن يكون ${rule.minLength} أحرف على الأقل`
    }
    
    if (rule.maxLength && value.length > rule.maxLength) {
      errors[field] = `يجب أن يكون ${rule.maxLength} أحرف كحد أقصى`
    }
    
    // Custom validation
    if (rule.custom && !rule.custom(value)) {
      errors[field] = rule.customMessage || 'قيمة غير صحيحة'
    }
  }
  
  return {
    isValid: Object.keys(errors).length === 0,
    errors
  }
}

// Rate limiting helper
export const checkRateLimit = (key: string, maxRequests: number = 10, windowMs: number = 60000): boolean => {
  const now = Date.now()
  const requests = JSON.parse(localStorage.getItem(`rate_limit_${key}`) || '[]')
  
  // Remove old requests outside the window
  const validRequests = requests.filter((timestamp: number) => now - timestamp < windowMs)
  
  if (validRequests.length >= maxRequests) {
    return false
  }
  
  // Add current request
  validRequests.push(now)
  localStorage.setItem(`rate_limit_${key}`, JSON.stringify(validRequests))
  
  return true
}

// Prevent rapid form submissions
export const preventRapidSubmission = (formId: string, cooldownMs: number = 2000): boolean => {
  const lastSubmission = localStorage.getItem(`last_submit_${formId}`)
  const now = Date.now()
  
  if (lastSubmission && now - parseInt(lastSubmission) < cooldownMs) {
    return false
  }
  
  localStorage.setItem(`last_submit_${formId}`, now.toString())
  return true
}
