'use client'

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { useToast } from '@/components/ui/use-toast'
import DashboardLayout from '@/components/DashboardLayout'
import { useRouter } from 'next/navigation'
import api from '@/lib/api'
import { useCurrency } from '@/contexts/CurrencyContext'
import { CurrencyDisplay } from '@/components/CurrencyDisplay'
import { 
  Search, 
  Filter, 
  SlidersHorizontal,
  Gavel, 
  FileText,
  Clock, 
  DollarSign, 
  Users, 
  Eye, 
  Calendar,
  MapPin,
  Tag,
  TrendingUp,
  Grid,
  List,
  RefreshCw
} from 'lucide-react'

interface SearchResult {
  _id: string
  title: string
  description: string
  currentBid?: number
  budget?: number
  currency?: string
  endTime?: string
  deadline?: string
  status: string
  category: string
  location?: string
  organizer: {
    _id: string
    profile: {
      companyName?: string
      fullName?: string
      governmentEntity?: string
    }
  }
  bidsCount?: number
  applicationsCount?: number
  viewsCount: number
  type: 'auction' | 'tender'
  createdAt: string
}

export default function SearchPage() {
  const [searchTerm, setSearchTerm] = useState('')
  const [results, setResults] = useState<SearchResult[]>([])
  const [loading, setLoading] = useState(false)
  const [activeTab, setActiveTab] = useState('all')
  const [categoryFilter, setCategoryFilter] = useState('all')
  const { formatAmount, formatAmountWithConversion } = useCurrency()
  const [statusFilter, setStatusFilter] = useState('all')
  const [locationFilter, setLocationFilter] = useState('all')
  const [sortBy, setSortBy] = useState('newest')
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')
  const [showFilters, setShowFilters] = useState(false)
  const { toast } = useToast()
  const router = useRouter()

  useEffect(() => {
    performSearch()
  }, [activeTab, categoryFilter, statusFilter, locationFilter, sortBy])

  const performSearch = async () => {
    try {
      setLoading(true)
      
      const params = new URLSearchParams({
        q: searchTerm,
        type: activeTab,
        category: categoryFilter,
        status: statusFilter,
        location: locationFilter,
        sort: sortBy,
        limit: '20'
      })

      const response = await api.get(`/search?${params.toString()}`)
      
      if (response.data.success) {
        setResults(response.data.data.results)
      }
    } catch (error) {
      console.error('Search error:', error)
      toast({
        title: 'خطأ في البحث',
        description: 'حدث خطأ أثناء البحث',
        variant: 'destructive'
      })
    } finally {
      setLoading(false)
    }
  }

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    performSearch()
  }

  const formatPrice = (price: number) => {
    return formatAmount(price)
  }

  const formatTimeRemaining = (endTime: string) => {
    const now = new Date()
    const end = new Date(endTime)
    const diff = end.getTime() - now.getTime()
    
    if (diff <= 0) return 'انتهى'
    
    const days = Math.floor(diff / (1000 * 60 * 60 * 24))
    const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
    
    if (days > 0) return `${days} يوم`
    if (hours > 0) return `${hours} ساعة`
    return 'أقل من ساعة'
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge className="bg-green-100 text-green-800">نشط</Badge>
      case 'completed':
        return <Badge className="bg-blue-100 text-blue-800">مكتمل</Badge>
      case 'ended':
        return <Badge variant="secondary">منتهي</Badge>
      case 'cancelled':
        return <Badge variant="destructive">ملغي</Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  const ResultCard = ({ item }: { item: SearchResult }) => (
    <Card 
      className="hover:shadow-lg transition-shadow cursor-pointer"
      onClick={() => router.push(`/${item.type === 'auction' ? 'auctions' : 'tenders'}/${item._id}`)}
    >
      <CardHeader>
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <CardTitle className="text-lg line-clamp-2">{item.title}</CardTitle>
            <div className="flex items-center gap-2 mt-2">
              {item.type === 'auction' ? (
                <Gavel className="h-4 w-4 text-blue-600" />
              ) : (
                <FileText className="h-4 w-4 text-green-600" />
              )}
              <span className="text-sm text-gray-600">
                {item.type === 'auction' ? 'مزاد' : 'مناقصة'}
              </span>
              {getStatusBadge(item.status)}
            </div>
          </div>
          <div className="text-right">
            <div className="text-lg font-bold text-blue-600">
              <CurrencyDisplay
                amount={item.type === 'auction' ? (item.currentBid || 0) : (item.budget || 0)}
                fromCurrency={item.currency || 'SAR'}
                className="text-lg font-bold text-blue-600"
              />
            </div>
            <div className="text-sm text-gray-500">
              {item.type === 'auction' ? 'المزايدة الحالية' : 'الميزانية'}
            </div>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <p className="text-gray-600 line-clamp-2 mb-4">{item.description}</p>
        
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div className="flex items-center gap-2">
            <Calendar className="h-4 w-4 text-gray-400" />
            <span>
              {item.type === 'auction' 
                ? formatTimeRemaining(item.endTime || '')
                : formatTimeRemaining(item.deadline || '')
              }
            </span>
          </div>
          <div className="flex items-center gap-2">
            <Users className="h-4 w-4 text-gray-400" />
            <span>
              {item.type === 'auction' 
                ? `${item.bidsCount || 0} مزايدة`
                : `${item.applicationsCount || 0} طلب`
              }
            </span>
          </div>
          <div className="flex items-center gap-2">
            <Eye className="h-4 w-4 text-gray-400" />
            <span>{item.viewsCount} مشاهدة</span>
          </div>
          <div className="flex items-center gap-2">
            <Tag className="h-4 w-4 text-gray-400" />
            <span>{item.category}</span>
          </div>
        </div>

        <div className="mt-4 pt-4 border-t">
          <div className="flex items-center justify-between">
            <div className="text-sm text-gray-600">
              بواسطة: {item.organizer.profile.companyName || 
                      item.organizer.profile.governmentEntity || 
                      item.organizer.profile.fullName}
            </div>
            <div className="text-xs text-gray-500">
              {new Date(item.createdAt).toLocaleDateString('ar-SA')}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )

  return (
    <DashboardLayout allowedRoles={['individual', 'company', 'government', 'admin', 'super_admin']}>
      <div className="space-y-6">
        {/* Header */}
        <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg p-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold mb-2">البحث والاستكشاف</h1>
              <p className="text-blue-100">ابحث في المزادات والمناقصات المتاحة</p>
            </div>
          </div>
        </div>

        {/* Search Form */}
        <Card>
          <CardContent className="p-6">
            <form onSubmit={handleSearch} className="space-y-4">
              <div className="flex gap-4">
                <div className="flex-1 relative">
                  <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
                  <Input
                    placeholder="ابحث في المزادات والمناقصات..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pr-12"
                  />
                </div>
                <Button type="submit" disabled={loading}>
                  {loading ? (
                    <RefreshCw className="h-4 w-4 animate-spin ml-2" />
                  ) : (
                    <Search className="h-4 w-4 ml-2" />
                  )}
                  بحث
                </Button>
                <Button 
                  type="button" 
                  variant="outline"
                  onClick={() => setShowFilters(!showFilters)}
                >
                  <SlidersHorizontal className="h-4 w-4 ml-2" />
                  فلاتر
                </Button>
              </div>

              {/* Filters */}
              {showFilters && (
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4 pt-4 border-t">
                  <Select value={categoryFilter} onValueChange={setCategoryFilter}>
                    <SelectTrigger>
                      <SelectValue placeholder="الفئة" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">جميع الفئات</SelectItem>
                      <SelectItem value="electronics">إلكترونيات</SelectItem>
                      <SelectItem value="vehicles">سيارات</SelectItem>
                      <SelectItem value="real_estate">عقارات</SelectItem>
                      <SelectItem value="construction">إنشاءات</SelectItem>
                      <SelectItem value="it_technology">تقنية المعلومات</SelectItem>
                    </SelectContent>
                  </Select>

                  <Select value={statusFilter} onValueChange={setStatusFilter}>
                    <SelectTrigger>
                      <SelectValue placeholder="الحالة" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">جميع الحالات</SelectItem>
                      <SelectItem value="active">نشط</SelectItem>
                      <SelectItem value="completed">مكتمل</SelectItem>
                      <SelectItem value="ended">منتهي</SelectItem>
                    </SelectContent>
                  </Select>

                  <Select value={locationFilter} onValueChange={setLocationFilter}>
                    <SelectTrigger>
                      <SelectValue placeholder="الموقع" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">جميع المواقع</SelectItem>
                      <SelectItem value="riyadh">الرياض</SelectItem>
                      <SelectItem value="jeddah">جدة</SelectItem>
                      <SelectItem value="dammam">الدمام</SelectItem>
                      <SelectItem value="mecca">مكة</SelectItem>
                    </SelectContent>
                  </Select>

                  <Select value={sortBy} onValueChange={setSortBy}>
                    <SelectTrigger>
                      <SelectValue placeholder="ترتيب حسب" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="newest">الأحدث</SelectItem>
                      <SelectItem value="oldest">الأقدم</SelectItem>
                      <SelectItem value="price_high">السعر (الأعلى)</SelectItem>
                      <SelectItem value="price_low">السعر (الأقل)</SelectItem>
                      <SelectItem value="ending_soon">ينتهي قريباً</SelectItem>
                      <SelectItem value="most_viewed">الأكثر مشاهدة</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              )}
            </form>
          </CardContent>
        </Card>

        {/* Tabs and Results */}
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <div className="flex items-center justify-between">
            <TabsList>
              <TabsTrigger value="all">الكل</TabsTrigger>
              <TabsTrigger value="auction">المزادات</TabsTrigger>
              <TabsTrigger value="tender">المناقصات</TabsTrigger>
            </TabsList>
            
            <div className="flex items-center gap-2">
              <span className="text-sm text-gray-600">
                {results.length} نتيجة
              </span>
              <div className="flex border rounded-md">
                <Button
                  variant={viewMode === 'grid' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setViewMode('grid')}
                >
                  <Grid className="h-4 w-4" />
                </Button>
                <Button
                  variant={viewMode === 'list' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setViewMode('list')}
                >
                  <List className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>

          <TabsContent value={activeTab} className="mt-6">
            {loading ? (
              <div className="flex items-center justify-center py-12">
                <div className="text-center">
                  <RefreshCw className="h-8 w-8 animate-spin text-blue-600 mx-auto mb-4" />
                  <p className="text-gray-600">جاري البحث...</p>
                </div>
              </div>
            ) : results.length > 0 ? (
              <div className={`grid gap-6 ${
                viewMode === 'grid' 
                  ? 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3' 
                  : 'grid-cols-1'
              }`}>
                {results.map((item) => (
                  <ResultCard key={item._id} item={item} />
                ))}
              </div>
            ) : (
              <div className="text-center py-12">
                <Search className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">لا توجد نتائج</h3>
                <p className="text-gray-600">جرب تغيير كلمات البحث أو الفلاتر</p>
              </div>
            )}
          </TabsContent>
        </Tabs>
      </div>
    </DashboardLayout>
  )
}
