'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Wallet, Plus, Minus, ArrowUpRight, ArrowDownLeft, CreditCard, DollarSign, TrendingUp, History } from 'lucide-react'
import api from '@/lib/api'
import { useToast } from '@/hooks/use-toast'
import { useCurrency } from '@/contexts/CurrencyContext'
import ProtectedRoute from '@/components/ProtectedRoute'

interface WalletBalance {
  available: number
  pending: number
  total: number
  currency: string
}

interface Transaction {
  id: string
  type: 'deposit' | 'withdrawal' | 'payment' | 'refund' | 'fee'
  amount: number
  description: string
  status: 'completed' | 'pending' | 'failed'
  date: string
  reference?: string
  auctionId?: string
  tenderId?: string
}

export default function WalletPage() {
  const [balance, setBalance] = useState<WalletBalance | null>(null)
  const [transactions, setTransactions] = useState<Transaction[]>([])
  const [loading, setLoading] = useState(true)
  const [depositAmount, setDepositAmount] = useState('')
  const [withdrawAmount, setWithdrawAmount] = useState('')
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState('')
  const [isDepositing, setIsDepositing] = useState(false)
  const [isWithdrawing, setIsWithdrawing] = useState(false)
  const { toast } = useToast()
  const { userCurrency } = useCurrency()

  useEffect(() => {
    loadWalletData()
  }, [])

  const loadWalletData = async () => {
    try {
      const [balanceResponse, transactionsResponse] = await Promise.all([
        api.get('/wallet/balance'),
        api.get('/wallet/transactions')
      ])
      
      if (balanceResponse.data.success) {
        setBalance(balanceResponse.data.data.balance)
      }
      
      if (transactionsResponse.data.success) {
        setTransactions(transactionsResponse.data.data.transactions || [])
      }
    } catch (error) {
      console.error('Error loading wallet data:', error)
      // Demo data
      setBalance({
        available: 25000,
        pending: 5000,
        total: 30000,
        currency: 'SAR'
      })
      
      setTransactions([
        {
          id: '1',
          type: 'deposit',
          amount: 10000,
          description: 'إيداع عبر بطاقة مدى',
          status: 'completed',
          date: '2025-01-10T10:30:00Z',
          reference: 'DEP-001'
        },
        {
          id: '2',
          type: 'payment',
          amount: -15000,
          description: 'دفع مزاد لابتوب عالي الأداء',
          status: 'completed',
          date: '2025-01-09T14:20:00Z',
          auctionId: 'auction-123'
        },
        {
          id: '3',
          type: 'refund',
          amount: 8000,
          description: 'استرداد من مزاد ملغي',
          status: 'completed',
          date: '2025-01-08T09:15:00Z',
          auctionId: 'auction-456'
        },
        {
          id: '4',
          type: 'withdrawal',
          amount: -5000,
          description: 'سحب إلى الحساب البنكي',
          status: 'pending',
          date: '2025-01-07T16:45:00Z',
          reference: 'WTH-001'
        }
      ])
    } finally {
      setLoading(false)
    }
  }

  const handleDeposit = async () => {
    // Import validation utilities
    const { validateAmount, preventRapidSubmission, checkRateLimit, sanitizeInput } = await import('@/lib/validation')

    // Prevent rapid submissions
    if (!preventRapidSubmission('deposit_form', 5000)) {
      toast({
        title: 'انتظر قليلاً',
        description: 'يرجى الانتظار قبل تقديم طلب إيداع آخر',
        variant: 'destructive'
      })
      return
    }

    // Check rate limiting
    if (!checkRateLimit('deposit', 3, 300000)) { // 3 deposits per 5 minutes
      toast({
        title: 'تم تجاوز الحد المسموح',
        description: 'لقد تجاوزت الحد المسموح من عمليات الإيداع. يرجى الانتظار 5 دقائق',
        variant: 'destructive'
      })
      return
    }

    if (!depositAmount || !selectedPaymentMethod) {
      toast({
        title: 'بيانات ناقصة',
        description: 'يرجى إدخال المبلغ واختيار طريقة الدفع',
        variant: 'destructive'
      })
      return
    }

    // Sanitize and validate amount
    const sanitizedAmount = sanitizeInput(depositAmount.toString())
    if (!validateAmount(sanitizedAmount, 1, 1000000)) {
      toast({
        title: 'مبلغ غير صحيح',
        description: 'يجب أن يكون المبلغ بين 1 و 1,000,000',
        variant: 'destructive'
      })
      return
    }

    const amount = parseFloat(sanitizedAmount)

    // Additional security checks
    if (amount > 100000) {
      toast({
        title: 'مبلغ مرتفع',
        description: 'للمبالغ الكبيرة يرجى التواصل مع خدمة العملاء',
        variant: 'destructive'
      })
      return
    }

    try {
      setIsDepositing(true)
      const response = await api.post('/wallet/deposit', {
        amount: amount,
        paymentMethod: selectedPaymentMethod
      })
      
      if (response.data.success) {
        toast({
          title: 'تم الإيداع بنجاح',
          description: `تم إيداع ${depositAmount} ريال في محفظتك`
        })
        setDepositAmount('')
        setSelectedPaymentMethod('')
        loadWalletData()
      }
    } catch (error) {
      toast({
        title: 'خطأ في الإيداع',
        description: 'حدث خطأ في عملية الإيداع',
        variant: 'destructive'
      })
    } finally {
      setIsDepositing(false)
    }
  }

  const handleWithdraw = async () => {
    if (!withdrawAmount) {
      toast({
        title: 'بيانات ناقصة',
        description: 'يرجى إدخال مبلغ السحب',
        variant: 'destructive'
      })
      return
    }

    const amount = parseFloat(withdrawAmount)
    if (amount > (balance?.available || 0)) {
      toast({
        title: 'رصيد غير كافي',
        description: 'المبلغ المطلوب أكبر من الرصيد المتاح',
        variant: 'destructive'
      })
      return
    }

    try {
      setIsWithdrawing(true)
      const response = await api.post('/wallet/withdraw', {
        amount: amount
      })
      
      if (response.data.success) {
        toast({
          title: 'تم طلب السحب',
          description: `تم إرسال طلب سحب ${withdrawAmount} ريال وسيتم معالجته خلال 24 ساعة`
        })
        setWithdrawAmount('')
        loadWalletData()
      }
    } catch (error) {
      toast({
        title: 'خطأ في السحب',
        description: 'حدث خطأ في عملية السحب',
        variant: 'destructive'
      })
    } finally {
      setIsWithdrawing(false)
    }
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('ar-SA', {
      style: 'currency',
      currency: 'SAR'
    }).format(Math.abs(amount))
  }

  const getTransactionIcon = (type: string) => {
    switch (type) {
      case 'deposit':
        return <ArrowDownLeft className="h-4 w-4 text-green-600" />
      case 'withdrawal':
        return <ArrowUpRight className="h-4 w-4 text-red-600" />
      case 'payment':
        return <Minus className="h-4 w-4 text-red-600" />
      case 'refund':
        return <Plus className="h-4 w-4 text-green-600" />
      case 'fee':
        return <Minus className="h-4 w-4 text-orange-600" />
      default:
        return <DollarSign className="h-4 w-4 text-gray-600" />
    }
  }

  const getTransactionColor = (type: string, amount: number) => {
    if (amount > 0) return 'text-green-600'
    return 'text-red-600'
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800'
      case 'pending':
        return 'bg-yellow-100 text-yellow-800'
      case 'failed':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'completed':
        return 'مكتملة'
      case 'pending':
        return 'في الانتظار'
      case 'failed':
        return 'فشلت'
      default:
        return status
    }
  }

  if (loading) {
    return (
      <ProtectedRoute allowedRoles={['individual', 'company', 'government']} requireApproved={true}>
        <div className="container mx-auto p-6">
          <div className="animate-pulse space-y-4">
          <div className="h-8 bg-gray-200 rounded w-1/4"></div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {[1, 2, 3].map(i => (
              <div key={i} className="h-32 bg-gray-200 rounded"></div>
            ))}
          </div>
          </div>
        </div>
      </ProtectedRoute>
    )
  }

  return (
    <ProtectedRoute allowedRoles={['individual', 'company', 'government']} requireApproved={true}>
      <div className="container mx-auto p-6 max-w-6xl">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-3xl font-bold">المحفظة</h1>
          <p className="text-gray-600 mt-2">إدارة رصيدك ومعاملاتك المالية</p>
        </div>
      </div>

      {/* Balance Cards */}
      {balance && (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">الرصيد المتاح</p>
                  <p className="text-2xl font-bold text-green-600">
                    {formatCurrency(balance.available)}
                  </p>
                </div>
                <Wallet className="h-8 w-8 text-green-600" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">الرصيد المعلق</p>
                  <p className="text-2xl font-bold text-yellow-600">
                    {formatCurrency(balance.pending)}
                  </p>
                </div>
                <History className="h-8 w-8 text-yellow-600" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">إجمالي الرصيد</p>
                  <p className="text-2xl font-bold text-blue-600">
                    {formatCurrency(balance.total)}
                  </p>
                </div>
                <TrendingUp className="h-8 w-8 text-blue-600" />
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Actions */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
        {/* Deposit */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Plus className="h-5 w-5 text-green-600" />
              إيداع أموال
            </CardTitle>
            <CardDescription>أضف أموال إلى محفظتك</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="depositAmount">المبلغ ({userCurrency})</Label>
              <Input
                id="depositAmount"
                type="number"
                placeholder="0.00"
                value={depositAmount}
                onChange={(e) => setDepositAmount(e.target.value)}
              />
            </div>
            
            <div>
              <Label htmlFor="paymentMethod">طريقة الدفع</Label>
              <Select value={selectedPaymentMethod} onValueChange={setSelectedPaymentMethod}>
                <SelectTrigger>
                  <SelectValue placeholder="اختر طريقة الدفع" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="mada">مدى</SelectItem>
                  <SelectItem value="visa">فيزا</SelectItem>
                  <SelectItem value="mastercard">ماستركارد</SelectItem>
                  <SelectItem value="bank_transfer">تحويل بنكي</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <Button 
              onClick={handleDeposit} 
              disabled={isDepositing || !depositAmount || !selectedPaymentMethod}
              className="w-full"
            >
              {isDepositing ? 'جاري الإيداع...' : 'إيداع'}
            </Button>
          </CardContent>
        </Card>

        {/* Withdraw */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Minus className="h-5 w-5 text-red-600" />
              سحب أموال
            </CardTitle>
            <CardDescription>اسحب أموال من محفظتك</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="withdrawAmount">المبلغ ({userCurrency})</Label>
              <Input
                id="withdrawAmount"
                type="number"
                placeholder="0.00"
                value={withdrawAmount}
                onChange={(e) => setWithdrawAmount(e.target.value)}
                max={balance?.available || 0}
              />
              <p className="text-xs text-gray-500 mt-1">
                الحد الأقصى: {formatCurrency(balance?.available || 0)}
              </p>
            </div>
            
            <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
              <p className="text-sm text-yellow-800">
                ⚠️ عمليات السحب تستغرق 1-3 أيام عمل للمعالجة
              </p>
            </div>
            
            <Button 
              onClick={handleWithdraw} 
              disabled={isWithdrawing || !withdrawAmount}
              variant="outline"
              className="w-full"
            >
              {isWithdrawing ? 'جاري السحب...' : 'سحب'}
            </Button>
          </CardContent>
        </Card>
      </div>

      {/* Transaction History */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <History className="h-5 w-5" />
            سجل المعاملات
          </CardTitle>
          <CardDescription>آخر المعاملات المالية</CardDescription>
        </CardHeader>
        <CardContent>
          {transactions.length === 0 ? (
            <div className="text-center py-8">
              <Wallet className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">لا توجد معاملات</h3>
              <p className="text-gray-600">لم تقم بأي معاملات مالية بعد</p>
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>النوع</TableHead>
                  <TableHead>الوصف</TableHead>
                  <TableHead>المبلغ</TableHead>
                  <TableHead>الحالة</TableHead>
                  <TableHead>التاريخ</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {transactions.map((transaction) => (
                  <TableRow key={transaction.id}>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        {getTransactionIcon(transaction.type)}
                        <span className="capitalize">{transaction.type}</span>
                      </div>
                    </TableCell>
                    <TableCell>{transaction.description}</TableCell>
                    <TableCell className={`font-semibold ${getTransactionColor(transaction.type, transaction.amount)}`}>
                      {transaction.amount > 0 ? '+' : ''}{formatCurrency(transaction.amount)}
                    </TableCell>
                    <TableCell>
                      <Badge className={getStatusColor(transaction.status)}>
                        {getStatusText(transaction.status)}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      {new Date(transaction.date).toLocaleDateString('ar-SA')}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>
    </div>
    </ProtectedRoute>
  )
}
