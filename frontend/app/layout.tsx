import type { Metadata } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import { Toaster } from "@/components/ui/toaster";
import { AuthProvider } from "../contexts/AuthContext";
import { CurrencyProvider } from "../contexts/CurrencyContext";

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: 'منصة المزادات والمناقصات | Auction & Tender Platform',
  description: 'منصة شاملة للمزادات والمناقصات للشركات والأفراد والجهات الحكومية',
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en">
      <body className={inter.className}>
        <AuthProvider>
          <CurrencyProvider>
            {children}
            <Toaster />
          </CurrencyProvider>
        </AuthProvider>
      </body>
    </html>
  );
}
