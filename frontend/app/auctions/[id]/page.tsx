'use client'

import React, { useState, useEffect } from 'react'
import { useP<PERSON><PERSON>, useRouter } from 'next/navigation'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { 
  Clock, 
  Eye, 
  Users, 
  MapPin, 
  Calendar, 
  DollarSign, 
  ArrowLeft,
  Heart,
  Share2,
  Gavel,
  AlertCircle,
  CheckCircle,
  Timer
} from 'lucide-react'
import api from '@/lib/api'
import { useToast } from '@/components/ui/use-toast'
import { useCurrency } from '@/contexts/CurrencyContext'
import { CurrencyDisplay } from '@/components/CurrencyDisplay'

interface Auction {
  _id: string
  title: string
  description: string
  category: string
  seller: {
    _id: string
    profile: {
      fullName?: string
      companyName?: string
    }
  }
  status: string
  startingPrice: number
  currentPrice: number
  reservePrice?: number
  buyNowPrice?: number
  endDate: string
  startDate: string
  bids: any[]
  views: number
  createdAt: string
  condition: string
  location?: {
    city?: string
    region?: string
  }
  images?: Array<{
    url: string
    caption?: string
    isMain?: boolean
  }>
  auctionType: string
  watchers?: string[]
}

export default function AuctionDetailPage() {
  const params = useParams()
  const router = useRouter()
  const { toast } = useToast()
  const { formatAmount } = useCurrency()
  const [auction, setAuction] = useState<Auction | null>(null)
  const [loading, setLoading] = useState(true)
  const [bidAmount, setBidAmount] = useState('')
  const [timeRemaining, setTimeRemaining] = useState('')
  const [isWatching, setIsWatching] = useState(false)
  const [user, setUser] = useState<any>(null)

  useEffect(() => {
    // Get user data from localStorage
    const userData = localStorage.getItem('user')
    if (userData) {
      setUser(JSON.parse(userData))
    }
  }, [])

  useEffect(() => {
    if (params.id) {
      fetchAuction(params.id as string)
    }
  }, [params.id])

  useEffect(() => {
    if (auction) {
      const timer = setInterval(() => {
        updateTimeRemaining()
      }, 1000)
      return () => clearInterval(timer)
    }
  }, [auction])

  const fetchAuction = async (auctionId: string) => {
    try {
      setLoading(true)
      const response = await api.get(`/auctions/${auctionId}`)
      
      if (response.data.success) {
        setAuction(response.data.data.auction)
        setBidAmount((response.data.data.auction.currentPrice + 10).toString())
      }
    } catch (error) {
      console.error('Error fetching auction:', error)
      toast({
        title: 'خطأ في التحميل',
        description: 'حدث خطأ في تحميل بيانات المزاد',
        variant: 'destructive'
      })
    } finally {
      setLoading(false)
    }
  }

  const updateTimeRemaining = () => {
    if (!auction) return
    
    const now = new Date().getTime()
    const endTime = new Date(auction.endDate).getTime()
    const difference = endTime - now

    if (difference > 0) {
      const days = Math.floor(difference / (1000 * 60 * 60 * 24))
      const hours = Math.floor((difference % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
      const minutes = Math.floor((difference % (1000 * 60 * 60)) / (1000 * 60))
      const seconds = Math.floor((difference % (1000 * 60)) / 1000)

      if (days > 0) {
        setTimeRemaining(`${days} يوم ${hours} ساعة`)
      } else if (hours > 0) {
        setTimeRemaining(`${hours} ساعة ${minutes} دقيقة`)
      } else if (minutes > 0) {
        setTimeRemaining(`${minutes} دقيقة ${seconds} ثانية`)
      } else {
        setTimeRemaining(`${seconds} ثانية`)
      }
    } else {
      setTimeRemaining('انتهى المزاد')
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge className="bg-green-100 text-green-800 border-green-200">نشط</Badge>
      case 'ended':
        return <Badge className="bg-red-100 text-red-800 border-red-200">منتهي</Badge>
      case 'sold':
        return <Badge className="bg-blue-100 text-blue-800 border-blue-200">مباع</Badge>
      case 'pending_approval':
        return <Badge className="bg-yellow-100 text-yellow-800 border-yellow-200">قيد المراجعة</Badge>
      case 'cancelled':
        return <Badge className="bg-gray-100 text-gray-800 border-gray-200">ملغي</Badge>
      default:
        return <Badge variant="secondary">{status}</Badge>
    }
  }

  const getCategoryName = (category: string) => {
    const categories: { [key: string]: string } = {
      electronics: 'إلكترونيات',
      vehicles: 'مركبات',
      real_estate: 'عقارات',
      art_collectibles: 'فنون ومقتنيات',
      machinery: 'معدات وآلات',
      furniture: 'أثاث',
      jewelry: 'مجوهرات',
      books_media: 'كتب ووسائط',
      clothing: 'ملابس',
      sports: 'رياضة',
      other: 'أخرى'
    }
    return categories[category] || category
  }

  const getConditionName = (condition: string) => {
    const conditions: { [key: string]: string } = {
      new: 'جديد',
      like_new: 'كالجديد',
      excellent: 'ممتاز',
      good: 'جيد',
      fair: 'مقبول',
      poor: 'ضعيف'
    }
    return conditions[condition] || condition
  }

  const formatPrice = (price: number) => {
    return formatAmount(price)
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('ar-SA', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  // Check if user can bid based on their role
  const canUserBid = () => {
    if (!user) return false

    // Only individuals and companies can bid
    if (user.role !== 'individual' && user.role !== 'company') {
      return false
    }

    // Companies cannot bid on their own auctions
    if (user.role === 'company' && auction?.seller._id === user._id) {
      return false
    }

    return true
  }

  const handlePlaceBid = async () => {
    if (!auction || !bidAmount) return

    // Check if user is allowed to bid
    if (!canUserBid()) {
      if (user?.role === 'admin' || user?.role === 'super_admin') {
        toast({
          title: 'وصول غير مسموح',
          description: 'المديرون لا يمكنهم المزايدة على المزادات',
          variant: 'destructive'
        })
      } else if (user?.role === 'government') {
        toast({
          title: 'وصول غير مسموح',
          description: 'الجهات الحكومية لا يمكنها المزايدة على المزادات',
          variant: 'destructive'
        })
      } else if (user?.role === 'company' && auction?.seller._id === user._id) {
        toast({
          title: 'وصول غير مسموح',
          description: 'لا يمكنك المزايدة على مزادك الخاص',
          variant: 'destructive'
        })
      } else {
        toast({
          title: 'وصول غير مسموح',
          description: 'غير مسموح لك بالمزايدة',
          variant: 'destructive'
        })
      }
      return
    }

    const amount = parseFloat(bidAmount)
    if (amount <= auction.currentPrice) {
      toast({
        title: 'مزايدة غير صحيحة',
        description: 'يجب أن تكون المزايدة أعلى من السعر الحالي',
        variant: 'destructive'
      })
      return
    }

    try {
      const response = await api.post(`/auctions/${auction._id}/bid`, {
        bidAmount: amount
      })

      if (response.data.success) {
        toast({
          title: 'تم تقديم المزايدة بنجاح',
          description: `مبلغ المزايدة: ${formatAmount(amount)}`
        })
        fetchAuction(auction._id) // Refresh auction data
      }
    } catch (error: any) {
      console.error('Error placing bid:', error)
      const errorMessage = error.response?.data?.message || 'حدث خطأ في تقديم المزايدة'
      toast({
        title: 'خطأ في المزايدة',
        description: errorMessage,
        variant: 'destructive'
      })
    }
  }

  const handleWatchAuction = async () => {
    if (!auction) return

    try {
      if (isWatching) {
        await api.delete(`/auctions/${auction._id}/watch`)
        toast({
          title: 'تم إزالة المزاد',
          description: 'تم إزالة المزاد من قائمة المتابعة'
        })
      } else {
        await api.post(`/auctions/${auction._id}/watch`)
        toast({
          title: 'تم إضافة المزاد',
          description: 'تم إضافة المزاد إلى قائمة المتابعة'
        })
      }
      setIsWatching(!isWatching)
    } catch (error) {
      console.error('Error watching auction:', error)
      toast({
        title: 'خطأ في المتابعة',
        description: 'حدث خطأ في متابعة المزاد',
        variant: 'destructive'
      })
    }
  }

  // Calculate derived values
  const isAuctionActive = auction?.status === 'active' && auction && new Date(auction.endDate) > new Date()
  const sellerName = auction?.seller?.profile?.fullName || auction?.seller?.profile?.companyName || 'غير محدد'

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-muted-foreground">جاري تحميل بيانات المزاد...</p>
        </div>
      </div>
    )
  }

  if (!auction) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <AlertCircle className="h-16 w-16 text-red-500 mx-auto mb-4" />
          <h1 className="text-2xl font-bold text-gray-900 mb-2">المزاد غير موجود</h1>
          <p className="text-gray-600 mb-4">لم يتم العثور على المزاد المطلوب</p>
          <Button onClick={() => router.back()}>
            <ArrowLeft className="h-4 w-4 ml-2" />
            العودة
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <Button variant="outline" onClick={() => router.back()}>
            <ArrowLeft className="h-4 w-4 ml-2" />
            العودة
          </Button>
          
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm" onClick={handleWatchAuction}>
              <Heart className={`h-4 w-4 ml-2 ${isWatching ? 'fill-red-500 text-red-500' : ''}`} />
              {isWatching ? 'إزالة من المتابعة' : 'متابعة'}
            </Button>
            <Button variant="outline" size="sm">
              <Share2 className="h-4 w-4 ml-2" />
              مشاركة
            </Button>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-6">
            {/* Auction Header */}
            <Card>
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div>
                    <CardTitle className="text-2xl font-bold mb-2">{auction.title}</CardTitle>
                    <div className="flex items-center gap-4 text-sm text-gray-600">
                      <div className="flex items-center gap-1">
                        <Eye className="h-4 w-4" />
                        <span>{auction.views} مشاهدة</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <Users className="h-4 w-4" />
                        <span>{auction.bids?.length || 0} مزايدة</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <MapPin className="h-4 w-4" />
                        <span>{auction.location?.city}, {auction.location?.region}</span>
                      </div>
                    </div>
                  </div>
                  {getStatusBadge(auction.status)}
                </div>
              </CardHeader>
              <CardContent>
                <p className="text-gray-700 leading-relaxed">{auction.description}</p>
              </CardContent>
            </Card>

            {/* Auction Details */}
            <Card>
              <CardHeader>
                <CardTitle>تفاصيل المزاد</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div>
                    <p className="text-sm text-gray-600">الفئة</p>
                    <p className="font-semibold">{getCategoryName(auction.category)}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">الحالة</p>
                    <p className="font-semibold">{getConditionName(auction.condition)}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">نوع المزاد</p>
                    <p className="font-semibold">{auction.auctionType === 'standard' ? 'عادي' : auction.auctionType}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">البائع</p>
                    <p className="font-semibold">{sellerName}</p>
                  </div>
                </div>

                <div className="mt-6 grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="bg-blue-50 p-4 rounded-lg">
                    <p className="text-sm text-blue-600">السعر الابتدائي</p>
                    <div className="text-xl font-bold text-blue-800">
                      <CurrencyDisplay
                        amount={auction.startingPrice}
                        fromCurrency="SAR"
                      />
                    </div>
                  </div>
                  {auction.reservePrice && auction.reservePrice > 0 && (
                    <div className="bg-yellow-50 p-4 rounded-lg">
                      <p className="text-sm text-yellow-600">السعر الاحتياطي</p>
                      <div className="text-xl font-bold text-yellow-800">
                        <CurrencyDisplay
                          amount={auction.reservePrice}
                          fromCurrency="SAR"
                        />
                      </div>
                    </div>
                  )}
                  {auction.buyNowPrice && (
                    <div className="bg-green-50 p-4 rounded-lg">
                      <p className="text-sm text-green-600">اشتري الآن</p>
                      <div className="text-xl font-bold text-green-800">
                        <CurrencyDisplay
                          amount={auction.buyNowPrice}
                          fromCurrency="SAR"
                        />
                      </div>
                    </div>
                  )}
                </div>

                <div className="mt-6 grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <p className="text-sm text-gray-600 flex items-center gap-1">
                      <Calendar className="h-4 w-4" />
                      تاريخ البداية
                    </p>
                    <p className="font-semibold">{formatDate(auction.startDate)}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600 flex items-center gap-1">
                      <Calendar className="h-4 w-4" />
                      تاريخ النهاية
                    </p>
                    <p className="font-semibold">{formatDate(auction.endDate)}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Bidding History */}
            <Card>
              <CardHeader>
                <CardTitle>تاريخ المزايدات</CardTitle>
              </CardHeader>
              <CardContent>
                {auction.bids && auction.bids.length > 0 ? (
                  <div className="space-y-3">
                    {auction.bids.slice(0, 10).map((bid, index) => (
                      <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div>
                          <div className="font-semibold">
                            <CurrencyDisplay
                              amount={bid.amount}
                              fromCurrency="SAR"
                            />
                          </div>
                          <p className="text-sm text-gray-600">
                            {new Date(bid.timestamp).toLocaleDateString('ar-SA', {
                              month: 'short',
                              day: 'numeric',
                              hour: '2-digit',
                              minute: '2-digit'
                            })}
                          </p>
                        </div>
                        <div className="text-right">
                          <p className="text-sm text-gray-600">مزايد</p>
                          {bid.isWinning && (
                            <Badge className="bg-green-100 text-green-800 text-xs">الأعلى</Badge>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <Gavel className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <p className="text-gray-600">لا توجد مزايدات بعد</p>
                    <p className="text-sm text-gray-500">كن أول من يزايد على هذا المزاد</p>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Current Price & Bidding */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <DollarSign className="h-5 w-5" />
                  السعر الحالي
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-center mb-6">
                  <div className="text-3xl font-bold text-green-600 mb-2">
                    <CurrencyDisplay
                      amount={auction.currentPrice}
                      fromCurrency="SAR"
                    />
                  </div>
                  <p className="text-sm text-gray-600">
                    {auction.bids?.length || 0} مزايدة
                  </p>
                </div>

                {/* Time Remaining */}
                <div className="bg-red-50 p-4 rounded-lg mb-6">
                  <div className="flex items-center gap-2 mb-2">
                    <Timer className="h-4 w-4 text-red-600" />
                    <p className="text-sm font-semibold text-red-600">الوقت المتبقي</p>
                  </div>
                  <p className="text-xl font-bold text-red-800">{timeRemaining}</p>
                </div>

                {/* Bidding Form */}
                {isAuctionActive ? (
                  canUserBid() ? (
                    <div className="space-y-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          مبلغ المزايدة
                        </label>
                        <Input
                          type="number"
                          value={bidAmount}
                          onChange={(e) => setBidAmount(e.target.value)}
                          placeholder="أدخل مبلغ المزايدة"
                          min={auction.currentPrice + 1}
                        />
                        <p className="text-xs text-gray-500 mt-1">
                          الحد الأدنى: <CurrencyDisplay
                            amount={auction.currentPrice + 10}
                            fromCurrency="SAR"
                          />
                        </p>
                      </div>

                      <Button
                        onClick={handlePlaceBid}
                        className="w-full bg-blue-600 hover:bg-blue-700"
                        disabled={!bidAmount || parseFloat(bidAmount) <= auction.currentPrice}
                      >
                        <Gavel className="h-4 w-4 ml-2" />
                        تقديم مزايدة
                      </Button>
                    </div>
                  ) : (
                    <div className="text-center py-4 bg-gray-50 rounded-lg">
                      <AlertCircle className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                      <p className="text-gray-600 font-medium">
                        {user?.role === 'admin' || user?.role === 'super_admin'
                          ? 'المديرون لا يمكنهم المزايدة'
                          : user?.role === 'government'
                          ? 'الجهات الحكومية لا تزايد على المزادات'
                          : user?.role === 'company' && auction?.seller._id === user._id
                          ? 'لا يمكنك المزايدة على مزادك الخاص'
                          : 'غير مسموح لك بالمزايدة'
                        }
                      </p>
                      <p className="text-sm text-gray-500 mt-1">
                        {user?.role === 'admin' || user?.role === 'super_admin'
                          ? 'يمكنك إدارة المزادات من لوحة التحكم'
                          : user?.role === 'government'
                          ? 'يمكنك إنشاء مناقصات من لوحة التحكم'
                          : 'تواصل مع الدعم للمساعدة'
                        }
                      </p>
                    </div>
                  )
                ) : (
                  <div className="text-center py-4 bg-gray-50 rounded-lg">
                    <AlertCircle className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                    <p className="text-gray-600">المزاد غير متاح للمزايدة</p>
                  </div>
                )}

                {auction.buyNowPrice && isAuctionActive && canUserBid() && (
                  <Button
                    variant="outline"
                    className="w-full border-green-600 text-green-600 hover:bg-green-50"
                  >
                    <CheckCircle className="h-4 w-4 ml-2" />
                    اشتري الآن - <CurrencyDisplay
                      amount={auction.buyNowPrice}
                      fromCurrency="SAR"
                    />
                  </Button>
                )}
              </CardContent>
            </Card>

            {/* Seller Information */}
            <Card>
              <CardHeader>
                <CardTitle>معلومات البائع</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div>
                    <p className="text-sm text-gray-600">اسم البائع</p>
                    <p className="font-semibold">{sellerName}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">الموقع</p>
                    <p className="font-semibold">
                      {auction.location?.city}, {auction.location?.region}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">تاريخ الانضمام</p>
                    <p className="font-semibold">
                      {new Date(auction.createdAt).toLocaleDateString('ar-SA', {
                        year: 'numeric',
                        month: 'long'
                      })}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}
