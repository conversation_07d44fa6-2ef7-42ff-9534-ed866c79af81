'use client'

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { <PERSON>alog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog'
import { Label } from '@/components/ui/label'

import { Search, Eye, Heart, Clock, TrendingUp, Gavel, DollarSign, Filter, SortAsc, RefreshCw, Pause, Play } from 'lucide-react'
import { auctionAPI, favoritesAPI } from '@/lib/api'
import { useRouter } from 'next/navigation'
import { useToast } from '@/components/ui/use-toast'
import currencyService from '@/lib/currencyService'


export default function UserAuctionsPage() {
  const [auctions, setAuctions] = useState<any[]>([])
  const [searchTerm, setSearchTerm] = useState('')
  const [filteredAuctions, setFilteredAuctions] = useState<any[]>([])
  const [loading, setLoading] = useState(false)
  const [savedAuctions, setSavedAuctions] = useState<Set<string>>(new Set())
  const [user, setUser] = useState<any>(null)

  // Bidding modal state
  const [showBidModal, setShowBidModal] = useState(false)
  const [selectedAuction, setSelectedAuction] = useState<any>(null)
  const [bidAmount, setBidAmount] = useState('')
  const [bidLoading, setBidLoading] = useState(false)

  // Filter and sort state
  const [sortBy, setSortBy] = useState('endDate')
  const [filterCategory, setFilterCategory] = useState('')

  // Auto-refresh state
  const [autoRefresh, setAutoRefresh] = useState(true)
  const [lastRefresh, setLastRefresh] = useState<Date>(new Date())

  // Currency state
  const [userCurrency, setUserCurrency] = useState('SAR')
  const [exchangeRates, setExchangeRates] = useState<any>({})

  // Optimistic locking
  const [auctionVersions, setAuctionVersions] = useState<{[key: string]: number}>({})

  const router = useRouter()
  const { toast } = useToast()

  useEffect(() => {
    // Get user data and check permissions
    const userData = localStorage.getItem('user')
    if (userData) {
      const parsedUser = JSON.parse(userData)
      setUser(parsedUser)

      // Redirect admins to their proper dashboard
      if (parsedUser.role === 'admin' || parsedUser.role === 'super_admin') {
        toast({
          title: 'وصول غير مسموح',
          description: 'المديرون يجب أن يستخدموا لوحة إدارة المزادات',
          variant: 'destructive'
        })
        router.push('/admin/auctions')
        return
      }

      // Redirect government users to their dashboard
      if (parsedUser.role === 'government') {
        toast({
          title: 'وصول غير مسموح',
          description: 'الجهات الحكومية تستخدم نظام المناقصات',
          variant: 'destructive'
        })
        router.push('/government/dashboard')
        return
      }
    }
  }, [])

  useEffect(() => {
    // Initialize currency
    const savedCurrency = currencyService.getUserCurrency()
    setUserCurrency(savedCurrency)

    loadAuctions()
    loadSavedAuctions()
    loadExchangeRates()
  }, [])

  const loadExchangeRates = async () => {
    try {
      const rates = await currencyService.getExchangeRates()
      setExchangeRates(rates)
    } catch (error) {
      console.error('Failed to load exchange rates:', error)
    }
  }

  // Auto-refresh auctions every 30 seconds
  useEffect(() => {
    if (!autoRefresh) return

    const interval = setInterval(async () => {
      try {
        await loadAuctions()
        setLastRefresh(new Date())
        console.log('🔄 Auto-refreshed auction data')
      } catch (error) {
        console.error('Auto-refresh failed:', error)
      }
    }, 30000) // 30 seconds

    return () => clearInterval(interval)
  }, [autoRefresh])

  const loadAuctions = async () => {
    try {
      setLoading(true)
      const response = await auctionAPI.getAll()

      console.log('Auctions API response:', response.data)

      // Handle the API response structure properly
      let auctionData = []
      if (response.data && response.data.success) {
        // If the response has a success field, extract the auctions from data.data.auctions
        auctionData = response.data.data?.auctions || []
      } else if (response.data && Array.isArray(response.data)) {
        // If response.data is directly an array
        auctionData = response.data
      } else if (response.data && response.data.data && Array.isArray(response.data.data)) {
        // If the auctions are in response.data.data
        auctionData = response.data.data
      }

      // Transform and convert currency for each auction
      const transformedData = await Promise.all(auctionData.map(async (auction: any) => {
        const baseAuction = {
          id: auction._id || auction.id,
          title: auction.title,
          description: auction.description,
          currentBid: auction.currentPrice || auction.currentBid || auction.startingPrice || 0,
          endDate: auction.endDate,
          bidsCount: auction.bids?.length || auction.bidsCount || 0,
          seller: auction.seller?.profile?.fullName || auction.seller?.profile?.companyName || auction.seller?.name || auction.seller?._id || 'غير محدد',
          category: auction.category,
          status: auction.status,
          views: auction.views || 0,
          location: auction.location,
          currency: auction.currency || 'SAR',
          version: auction.version || 0
        }

        // Store version for optimistic locking
        setAuctionVersions(prev => ({
          ...prev,
          [baseAuction.id]: baseAuction.version
        }))

        // Convert currency if needed
        try {
          if (baseAuction.currency !== userCurrency && userCurrency !== 'SAR') {
            const convertedBid = await currencyService.convertCurrency(
              baseAuction.currentBid,
              baseAuction.currency,
              userCurrency
            )
            return {
              ...baseAuction,
              currentBid: Math.round(convertedBid),
              originalCurrency: baseAuction.currency,
              currency: userCurrency
            }
          }
        } catch (conversionError) {
          console.warn('Currency conversion failed for auction:', baseAuction.id, conversionError)
        }

        return baseAuction
      }))

      setAuctions(transformedData)
      setFilteredAuctions(transformedData)
    } catch (error) {
      console.error('Error loading auctions:', error)
      setAuctions([])
      setFilteredAuctions([])

      toast({
        title: 'فشل في تحميل المزادات',
        description: 'حدث خطأ أثناء تحميل المزادات. يرجى المحاولة مرة أخرى.',
        variant: 'destructive'
      })
    } finally {
      setLoading(false)
    }
  }

  const loadSavedAuctions = async () => {
    try {
      const response = await favoritesAPI.getFavorites({ type: 'auction' })

      // Handle different response structures
      let favoritesData = []
      if (response.data && response.data.success) {
        favoritesData = response.data.data || []
      } else if (response.data && Array.isArray(response.data)) {
        favoritesData = response.data
      } else if (response.data && response.data.favorites && Array.isArray(response.data.favorites)) {
        favoritesData = response.data.favorites
      }

      const savedIds = new Set<string>(favoritesData.map((fav: any) => fav.itemId || fav._id || fav.id))
      setSavedAuctions(savedIds)
    } catch (error) {
      console.error('Error loading saved auctions:', error)
      // Don't show error toast for favorites as it's not critical
      setSavedAuctions(new Set())
    }
  }

  useEffect(() => {
    // Ensure auctions is an array before filtering
    if (Array.isArray(auctions)) {
      let filtered = auctions.filter(auction => {
        const matchesSearch = auction.title?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                             auction.description?.toLowerCase().includes(searchTerm.toLowerCase())
        const matchesCategory = !filterCategory || auction.category === filterCategory
        return matchesSearch && matchesCategory
      })

      // Sort the filtered results
      filtered = filtered.sort((a, b) => {
        switch (sortBy) {
          case 'endDate':
            return new Date(a.endDate).getTime() - new Date(b.endDate).getTime()
          case 'currentBid':
            return (b.currentBid || 0) - (a.currentBid || 0)
          case 'bidsCount':
            return (b.bidsCount || 0) - (a.bidsCount || 0)
          case 'title':
            return a.title.localeCompare(b.title)
          default:
            return 0
        }
      })

      setFilteredAuctions(filtered)
    } else {
      setFilteredAuctions([])
    }
  }, [searchTerm, auctions, filterCategory, sortBy])

  const getTimeRemaining = (endDate: string) => {
    const now = new Date()
    const end = new Date(endDate)
    const diff = end.getTime() - now.getTime()

    if (diff <= 0) return 'انتهى'

    const hours = Math.floor(diff / (1000 * 60 * 60))
    const days = Math.floor(hours / 24)

    if (days > 0) return `${days} يوم`
    return `${hours} ساعة`
  }

  // Check if user can bid
  const canUserBid = (auction: any) => {
    if (!user) return false

    // Only individuals and companies can bid
    if (user.role !== 'individual' && user.role !== 'company') {
      return false
    }

    // Companies cannot bid on their own auctions
    if (user.role === 'company' && auction.seller === user.profile?.companyName) {
      return false
    }

    return true
  }

  const handleBid = async (auctionId: string) => {
    console.log('🎯 Bid button clicked for auction:', auctionId)

    try {
      // Fetch fresh auction data before opening modal
      setLoading(true)
      const response = await auctionAPI.getById(auctionId)

      let freshAuction = null
      if (response.data && response.data.success) {
        freshAuction = response.data.data.auction
      } else if (response.data && response.data.auction) {
        freshAuction = response.data.auction
      }

      if (!freshAuction) {
        toast({
          title: 'خطأ',
          description: 'لم يتم العثور على المزاد',
          variant: 'destructive'
        })
        return
      }

      // Transform fresh data to match our format
      const transformedAuction = {
        id: freshAuction._id || freshAuction.id,
        title: freshAuction.title,
        description: freshAuction.description,
        currentBid: freshAuction.currentPrice || freshAuction.currentBid || freshAuction.startingPrice || 0,
        endDate: freshAuction.endDate,
        bidsCount: freshAuction.bids?.length || freshAuction.bidsCount || 0,
        seller: freshAuction.seller?.profile?.fullName || freshAuction.seller?.profile?.companyName || freshAuction.seller?.name || 'غير محدد',
        category: freshAuction.category,
        status: freshAuction.status
      }

      // Check if user can bid
      if (!canUserBid(transformedAuction)) {
        if (user?.role === 'admin' || user?.role === 'super_admin') {
          toast({
            title: 'وصول غير مسموح',
            description: 'المديرون لا يمكنهم المزايدة على المزادات',
            variant: 'destructive'
          })
        } else if (user?.role === 'government') {
          toast({
            title: 'وصول غير مسموح',
            description: 'الجهات الحكومية لا تزايد على المزادات',
            variant: 'destructive'
          })
        } else if (user?.role === 'company') {
          toast({
            title: 'وصول غير مسموح',
            description: 'لا يمكنك المزايدة على مزادك الخاص',
            variant: 'destructive'
          })
        } else {
          toast({
            title: 'وصول غير مسموح',
            description: 'غير مسموح لك بالمزايدة',
            variant: 'destructive'
          })
        }
        return
      }

      // Open bidding modal with fresh data
      setSelectedAuction(transformedAuction)
      setBidAmount(String((transformedAuction.currentBid || 0) + 1000))
      setShowBidModal(true)

      toast({
        title: '📊 تم تحديث بيانات المزاد',
        description: `المزايدة الحالية: ${(transformedAuction.currentBid || 0).toLocaleString()} ر.س`
      })

    } catch (error: any) {
      console.error('Error fetching fresh auction data:', error)
      toast({
        title: 'خطأ في تحديث البيانات',
        description: 'سيتم استخدام البيانات المحفوظة محلياً',
        variant: 'destructive'
      })

      // Fallback to cached data
      const currentAuction = auctions.find(a => a.id === auctionId)
      if (currentAuction && canUserBid(currentAuction)) {
        setSelectedAuction(currentAuction)
        setBidAmount(String((currentAuction.currentBid || 0) + 1000))
        setShowBidModal(true)
      }
    } finally {
      setLoading(false)
    }
  }

  const submitBid = async () => {
    if (!selectedAuction) return

    const bidAmountNum = parseFloat(bidAmount)
    if (isNaN(bidAmountNum) || bidAmountNum <= (selectedAuction.currentBid || 0)) {
      toast({
        title: 'مزايدة غير صالحة',
        description: 'يجب أن تكون المزايدة أكبر من المزايدة الحالية',
        variant: 'destructive'
      })
      return
    }

    try {
      setBidLoading(true)

      // Get auction version for optimistic locking
      const auctionVersion = auctionVersions[selectedAuction.id]

      // Prepare bid data with currency and version
      const bidData = {
        bidAmount: bidAmountNum,
        currency: userCurrency,
        version: auctionVersion
      }

      // Submit bid and wait for server response
      const response = await fetch(`/api/auctions/${selectedAuction.id}/bid`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify(bidData)
      })

      const result = await response.json()

      if (!response.ok) {
        // Handle specific error cases
        if (response.status === 409) {
          // Optimistic locking conflict
          toast({
            title: 'تم تحديث المزاد ⚠️',
            description: 'قام شخص آخر بتحديث المزاد. سيتم تحديث البيانات.',
            variant: 'destructive'
          })
          await loadAuctions()
          setShowBidModal(false)
          setBidAmount('')
          setSelectedAuction(null)
          return
        }
        throw new Error(result.message || 'Bid failed')
      }

      // Close modal first
      setShowBidModal(false)
      setBidAmount('')
      setSelectedAuction(null)

      // Reload fresh data from server
      await loadAuctions()

      // Show success message with currency formatting
      const formattedAmount = currencyService.formatAmount(bidAmountNum, userCurrency)
      toast({
        title: 'تم تقديم المزايدة بنجاح! 🎉',
        description: `مقدار المزايدة: ${formattedAmount}`
      })

    } catch (error: any) {
      console.error('Error placing bid:', error)

      // Close modal on error too
      setShowBidModal(false)
      setBidAmount('')
      setSelectedAuction(null)

      // Show specific error message
      const errorMessage = error.response?.data?.message || 'يرجى المحاولة مرة أخرى'

      if (errorMessage.includes('higher than current price') || errorMessage.includes('أكبر من السعر الحالي')) {
        toast({
          title: 'مزايدة منخفضة ⚠️',
          description: 'قام شخص آخر بمزايدة أعلى. يرجى المحاولة بمبلغ أكبر.',
          variant: 'destructive'
        })
      } else if (errorMessage.includes('ended') || errorMessage.includes('انتهى')) {
        toast({
          title: 'انتهى المزاد ⏰',
          description: 'لقد انتهى هذا المزاد ولا يمكن قبول مزايدات جديدة.',
          variant: 'destructive'
        })
      } else {
        toast({
          title: 'فشل في تقديم المزايدة',
          description: errorMessage,
          variant: 'destructive'
        })
      }

      // Always reload data on error to sync with server
      await loadAuctions()
    } finally {
      setBidLoading(false)
    }
  }

  const handleSaveAuction = async (auctionId: string) => {
    console.log('❤️ Save button clicked for auction:', auctionId)
    
    // Show immediate feedback that button was clicked
    toast({
      title: '💆 تم الضغط على زر الحفظ',
      description: 'جاري معالجة الطلب...'
    })
    
    try {
      const auction = auctions.find(a => a.id === auctionId)
      if (!auction) {
        toast({
          title: 'خطأ',
          description: 'لم يتم العثور على المزاد',
          variant: 'destructive'
        })
        return
      }
      
      const isCurrentlySaved = savedAuctions.has(auctionId)
      
      // Optimistic UI update
      setSavedAuctions(prev => {
        const newSet = new Set(prev)
        if (isCurrentlySaved) {
          newSet.delete(auctionId)
        } else {
          newSet.add(auctionId)
        }
        return newSet
      })
      
      if (isCurrentlySaved) {
        // Remove from favorites
        await favoritesAPI.removeFavorite('auction', auctionId)
        toast({
          title: '❤️ تم إزالة من المفضلة',
          description: `تم إزالة "${auction.title}" من المفضلة`
        })
      } else {
        // Add to favorites
        await favoritesAPI.addFavorite({
          itemType: 'auction',
          itemId: auctionId,
          notifications: {
            bidUpdates: true,
            statusChanges: true,
            endingSoon: true
          }
        })
        toast({
          title: '💖 تم إضافة إلى المفضلة',
          description: `تم إضافة "${auction.title}" إلى المفضلة`
        })
      }
    } catch (error: any) {
      console.error('Error saving auction:', error)
      // Revert optimistic update on error
      setSavedAuctions(prev => {
        const newSet = new Set(prev)
        if (savedAuctions.has(auctionId)) {
          newSet.add(auctionId)
        } else {
          newSet.delete(auctionId)
        }
        return newSet
      })
      toast({
        title: 'فشل في حفظ المزاد',
        description: error.response?.data?.message || 'يرجى المحاولة مرة أخرى',
        variant: 'destructive'
      })
    }
  }

  const handleViewAuction = async (auctionId: string) => {
    console.log('👁️ View button clicked for auction:', auctionId)

    try {
      const auction = auctions.find(a => a.id === auctionId)
      if (!auction) {
        toast({
          title: 'خطأ',
          description: 'لم يتم العثور على المزاد',
          variant: 'destructive'
        })
        return
      }

      // Navigate to auction detail page
      router.push(`/auctions/${auctionId}`)

      toast({
        title: '👁️ جاري تحميل التفاصيل',
        description: 'سيتم عرض تفاصيل المزاد'
      })
    } catch (error: any) {
      console.error('Error viewing auction:', error)
      toast({
        title: 'فشل في عرض تفاصيل المزاد',
        description: error.response?.data?.message || 'يرجى المحاولة مرة أخرى',
        variant: 'destructive'
      })
    }
  }

  return (
    <div className="space-y-6">
        <header>
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold">المزادات المتاحة</h1>
              <p className="text-muted-foreground">
                استكشف المزادات الحالية وشارك في المزايدة
                {autoRefresh && (
                  <span className="text-xs text-green-600 ml-2">
                    • تحديث تلقائي كل 30 ثانية
                  </span>
                )}
              </p>
            </div>
            <div className="flex items-center gap-2">
              <div className="flex items-center gap-2">
                <Label htmlFor="currency" className="text-sm font-medium">العملة:</Label>
                <select
                  id="currency"
                  value={userCurrency}
                  onChange={async (e) => {
                    const newCurrency = e.target.value
                    setUserCurrency(newCurrency)
                    currencyService.setUserCurrency(newCurrency)
                    setLoading(true)
                    await loadAuctions()
                    setLoading(false)
                    toast({
                      title: '💱 تم تغيير العملة',
                      description: `تم التحويل إلى ${currencyService.getCurrencySymbol(newCurrency)}`
                    })
                  }}
                  className="px-2 py-1 border border-gray-300 rounded text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  {currencyService.getSupportedCurrencies().map(currency => (
                    <option key={currency.code} value={currency.code}>
                      {currency.symbol} {currency.code}
                    </option>
                  ))}
                </select>
              </div>
              <Button
                onClick={async () => {
                  setLoading(true)
                  await loadAuctions()
                  setLastRefresh(new Date())
                  setLoading(false)
                  toast({
                    title: '🔄 تم تحديث البيانات',
                    description: `تم العثور على ${filteredAuctions.length} مزاد متاح`
                  })
                }}
                variant="outline"
                disabled={loading}
              >
                <RefreshCw className={`h-4 w-4 ml-2 ${loading ? 'animate-spin' : ''}`} />
                تحديث
              </Button>
              <Button
                onClick={() => {
                  setAutoRefresh(!autoRefresh)
                  toast({
                    title: autoRefresh ? '⏸️ تم إيقاف التحديث التلقائي' : '▶️ تم تشغيل التحديث التلقائي',
                    description: autoRefresh ? 'يمكنك التحديث يدوياً' : 'سيتم التحديث كل 30 ثانية'
                  })
                }}
                variant={autoRefresh ? "default" : "outline"}
                size="icon"
              >
                {autoRefresh ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
              </Button>
            </div>
          </div>
        </header>

        {/* Search and Filters */}
        <Card>
          <CardContent className="pt-6">
            <div className="space-y-4">
              <div className="relative">
                <Search className="absolute right-3 top-3 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="ابحث في المزادات..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pr-10"
                />
              </div>

              <div className="flex flex-col sm:flex-row gap-4">
                <div className="flex-1">
                  <Label htmlFor="category" className="text-sm font-medium">الفئة</Label>
                  <select
                    id="category"
                    value={filterCategory}
                    onChange={(e) => setFilterCategory(e.target.value)}
                    className="w-full mt-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="">جميع الفئات</option>
                    <option value="electronics">إلكترونيات</option>
                    <option value="vehicles">سيارات</option>
                    <option value="real_estate">عقارات</option>
                    <option value="art_collectibles">فنون ومقتنيات</option>
                    <option value="machinery">معدات</option>
                    <option value="furniture">أثاث</option>
                    <option value="jewelry">مجوهرات</option>
                    <option value="books_media">كتب ووسائط</option>
                    <option value="clothing">ملابس</option>
                    <option value="sports">رياضة</option>
                    <option value="other">أخرى</option>
                  </select>
                </div>

                <div className="flex-1">
                  <Label htmlFor="sort" className="text-sm font-medium">ترتيب حسب</Label>
                  <select
                    id="sort"
                    value={sortBy}
                    onChange={(e) => setSortBy(e.target.value)}
                    className="w-full mt-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="endDate">تاريخ الانتهاء</option>
                    <option value="currentBid">أعلى مزايدة</option>
                    <option value="bidsCount">عدد المزايدات</option>
                    <option value="title">الاسم</option>
                  </select>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Stats */}
        {Array.isArray(filteredAuctions) && filteredAuctions.length > 0 && (
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">إجمالي المزادات</CardTitle>
                <CardDescription className="text-2xl font-bold text-blue-600">
                  {filteredAuctions.length}
                </CardDescription>
              </CardHeader>
            </Card>
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">المزايدات النشطة</CardTitle>
                <CardDescription className="text-2xl font-bold text-green-600">
                  {filteredAuctions.reduce((sum, a) => sum + (a.bidsCount || 0), 0)}
                </CardDescription>
              </CardHeader>
            </Card>
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">أعلى مزايدة</CardTitle>
                <CardDescription className="text-2xl font-bold text-purple-600">
                  {filteredAuctions.length > 0
                    ? currencyService.formatAmount(
                        Math.max(...filteredAuctions.map(a => a.currentBid || 0)),
                        userCurrency
                      )
                    : currencyService.formatAmount(0, userCurrency)
                  }
                </CardDescription>
              </CardHeader>
            </Card>
          </div>
        )}

        {/* Auctions Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {Array.isArray(filteredAuctions) && filteredAuctions.map((auction) => (
            <Card key={auction.id} className="hover:shadow-lg transition-shadow cursor-pointer" onClick={() => handleViewAuction(auction.id)}>
              <CardHeader>
                <div className="aspect-video bg-gradient-to-br from-gray-100 to-gray-200 rounded-lg mb-4 flex items-center justify-center relative overflow-hidden">
                  {auction.images && auction.images.length > 0 ? (
                    <img
                      src={auction.images[0].url}
                      alt={auction.title}
                      className="w-full h-full object-cover"
                      onError={(e) => {
                        e.currentTarget.style.display = 'none'
                        e.currentTarget.nextElementSibling.style.display = 'flex'
                      }}
                    />
                  ) : null}
                  <div className="flex items-center justify-center w-full h-full text-gray-500" style={{display: auction.images && auction.images.length > 0 ? 'none' : 'flex'}}>
                    <div className="text-center">
                      <Gavel className="h-8 w-8 mx-auto mb-2 text-gray-400" />
                      <span className="text-sm">صورة المزاد</span>
                    </div>
                  </div>
                  {auction.status === 'active' && (
                    <Badge className="absolute top-2 right-2 bg-green-500">
                      نشط
                    </Badge>
                  )}
                </div>
                <CardTitle className="text-lg line-clamp-2">{auction.title}</CardTitle>
                <CardDescription className="line-clamp-2">
                  {auction.description}
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-muted-foreground">أعلى مزايدة</p>
                    <p className="text-xl font-bold text-green-600">
                      {currencyService.formatAmount(auction.currentBid || 0, auction.currency || userCurrency)}
                    </p>
                  </div>
                  <div className="text-center">
                    <p className="text-sm text-muted-foreground">المزايدات</p>
                    <p className="text-lg font-semibold">{auction.bidsCount || 0}</p>
                  </div>
                </div>

                <div className="flex items-center gap-2">
                  <Clock className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm text-muted-foreground">
                    ينتهي خلال: {getTimeRemaining(auction.endDate)}
                  </span>
                </div>

                <div className="text-xs text-muted-foreground">
                  البائع: {auction.seller}
                </div>

                <div className="flex gap-2">
                  <Button
                    onClick={(e) => {
                      e.preventDefault()
                      e.stopPropagation()
                      handleBid(auction.id)
                    }}
                    className="flex-1"
                    disabled={loading || bidLoading}
                  >
                    <TrendingUp className="h-4 w-4 ml-2" />
                    {bidLoading ? 'جاري المزايدة...' : 'مزايدة'}
                  </Button>
                  <Button
                    variant={savedAuctions.has(auction.id) ? "default" : "outline"}
                    size="icon"
                    onClick={(e) => {
                      e.preventDefault()
                      e.stopPropagation()
                      handleSaveAuction(auction.id)
                    }}
                    disabled={loading}
                  >
                    <Heart className={`h-4 w-4 ${savedAuctions.has(auction.id) ? 'fill-current' : ''}`} />
                  </Button>
                  <Button
                    variant="outline"
                    size="icon"
                    onClick={(e) => {
                      e.preventDefault()
                      e.stopPropagation()
                      handleViewAuction(auction.id)
                    }}
                    disabled={loading}
                  >
                    <Eye className="h-4 w-4" />
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* No data state */}
        {!loading && (!Array.isArray(filteredAuctions) || filteredAuctions.length === 0) && (
          <Card>
            <CardContent className="py-16 text-center">
              <div className="flex flex-col items-center gap-4">
                <div className="w-16 h-16 rounded-full bg-gray-100 flex items-center justify-center">
                  <TrendingUp className="h-8 w-8 text-gray-400" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold mb-2">
                    {searchTerm ? 'لا توجد مزادات تطابق البحث' : 'لا توجد مزادات متاحة حالياً'}
                  </h3>
                  <p className="text-muted-foreground">
                    {searchTerm 
                      ? 'جرب البحث بكلمات مختلفة أو امسح مربع البحث لعرض جميع المزادات'
                      : 'سيتم عرض المزادات المتاحة هنا عند إضافتها من قبل الشركات'
                    }
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Loading state */}
        {loading && (
          <Card>
            <CardContent className="py-16 text-center">
              <div className="flex flex-col items-center gap-4">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                <p className="text-muted-foreground">جاري تحميل المزادات...</p>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Bidding Modal */}
        <Dialog open={showBidModal} onOpenChange={setShowBidModal}>
          <DialogContent className="sm:max-w-md">
            <DialogHeader>
              <DialogTitle className="flex items-center gap-2">
                <Gavel className="h-5 w-5" />
                تقديم مزايدة
              </DialogTitle>
            </DialogHeader>

            {selectedAuction && (
              <div className="space-y-4">
                <div className="bg-gray-50 p-4 rounded-lg">
                  <h4 className="font-semibold mb-2">{selectedAuction.title}</h4>
                  <div className="flex items-center gap-4 text-sm text-gray-600">
                    <div className="flex items-center gap-1">
                      <DollarSign className="h-4 w-4" />
                      <span>المزايدة الحالية: {(selectedAuction.currentBid || 0).toLocaleString()} ر.س</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <TrendingUp className="h-4 w-4" />
                      <span>{selectedAuction.bidsCount || 0} مزايدة</span>
                    </div>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="bidAmount">مقدار المزايدة (ر.س)</Label>
                  <Input
                    id="bidAmount"
                    type="number"
                    value={bidAmount}
                    onChange={(e) => setBidAmount(e.target.value)}
                    placeholder="أدخل مقدار المزايدة"
                    min={(selectedAuction.currentBid || 0) + 1}
                  />
                  <p className="text-xs text-gray-500">
                    الحد الأدنى: {((selectedAuction.currentBid || 0) + 1).toLocaleString()} ر.س
                  </p>
                </div>
              </div>
            )}

            <DialogFooter>
              <Button
                variant="outline"
                onClick={() => {
                  setShowBidModal(false)
                  setBidAmount('')
                  setSelectedAuction(null)
                }}
                disabled={bidLoading}
              >
                إلغاء
              </Button>
              <Button
                onClick={submitBid}
                disabled={bidLoading || !bidAmount}
                className="bg-green-600 hover:bg-green-700"
              >
                {bidLoading ? 'جاري التقديم...' : 'تقديم المزايدة'}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
  )
}
