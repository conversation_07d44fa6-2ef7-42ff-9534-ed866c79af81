'use client'

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { useToast } from '@/components/ui/use-toast'

import { useRouter } from 'next/navigation'
import api from '@/lib/api'
import { 
  FileText, 
  Clock, 
  DollarSign, 
  Users, 
  Eye, 
  Calendar,
  MapPin,
  Tag,
  Search,
  Filter,
  RefreshCw,
  Building,
  TrendingUp,
  AlertCircle,
  CheckCircle,
  Send,
  Heart,
  Star
} from 'lucide-react'

interface Tender {
  _id: string
  title: string
  description: string
  budget: number
  deadline: string
  status: string
  category: string
  location: string
  organizer: {
    _id: string
    profile: {
      governmentEntity?: string
      fullName?: string
    }
  }
  applicationsCount: number
  viewsCount: number
  createdAt: string
  requirements: string[]
  hasApplied?: boolean
  isFavorite?: boolean
}

export default function UserTendersPage() {
  const [tenders, setTenders] = useState<Tender[]>([])
  const [filteredTenders, setFilteredTenders] = useState<Tender[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [categoryFilter, setCategoryFilter] = useState('all')
  const [statusFilter, setStatusFilter] = useState('active')
  const [locationFilter, setLocationFilter] = useState('all')
  const [sortBy, setSortBy] = useState('newest')
  const { toast } = useToast()
  const router = useRouter()

  useEffect(() => {
    loadTenders()
  }, [])

  useEffect(() => {
    filterTenders()
  }, [tenders, searchTerm, categoryFilter, statusFilter, locationFilter, sortBy])

  const loadTenders = async () => {
    try {
      setLoading(true)
      const response = await api.get('/tenders?status=active&limit=50')
      
      if (response.data.success) {
        setTenders(response.data.data.tenders)
      } else {
        setTenders([])
      }
    } catch (error) {
      console.error('Error loading tenders:', error)
      toast({
        title: 'خطأ في التحميل',
        description: 'حدث خطأ في تحميل المناقصات',
        variant: 'destructive'
      })
    } finally {
      setLoading(false)
    }
  }

  const filterTenders = () => {
    let filtered = tenders

    if (searchTerm) {
      filtered = filtered.filter(tender =>
        tender.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        tender.description.toLowerCase().includes(searchTerm.toLowerCase())
      )
    }

    if (categoryFilter !== 'all') {
      filtered = filtered.filter(tender => tender.category === categoryFilter)
    }

    if (statusFilter !== 'all') {
      filtered = filtered.filter(tender => tender.status === statusFilter)
    }

    if (locationFilter !== 'all') {
      filtered = filtered.filter(tender => tender.location.includes(locationFilter))
    }

    // Sort
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'newest':
          return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
        case 'oldest':
          return new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
        case 'budget_high':
          return b.budget - a.budget
        case 'budget_low':
          return a.budget - b.budget
        case 'deadline_soon':
          return new Date(a.deadline).getTime() - new Date(b.deadline).getTime()
        case 'most_viewed':
          return b.viewsCount - a.viewsCount
        case 'most_applications':
          return b.applicationsCount - a.applicationsCount
        default:
          return 0
      }
    })

    setFilteredTenders(filtered)
  }

  const toggleFavorite = async (tenderId: string) => {
    try {
      const tender = tenders.find(t => t._id === tenderId)
      if (!tender) return

      const response = await api.post(`/favorites/toggle`, {
        itemId: tenderId,
        type: 'tender'
      })
      
      if (response.data.success) {
        setTenders(tenders.map(t => 
          t._id === tenderId ? { ...t, isFavorite: !t.isFavorite } : t
        ))
        toast({
          title: tender.isFavorite ? 'تم الحذف من المفضلة' : 'تم الإضافة للمفضلة',
          description: tender.isFavorite ? 'تم حذف المناقصة من المفضلة' : 'تم إضافة المناقصة للمفضلة'
        })
      }
    } catch (error) {
      toast({
        title: 'خطأ',
        description: 'حدث خطأ في تحديث المفضلة',
        variant: 'destructive'
      })
    }
  }

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('ar-SA', {
      style: 'currency',
      currency: 'SAR',
      minimumFractionDigits: 0
    }).format(price)
  }

  const formatTimeRemaining = (deadline: string) => {
    const now = new Date()
    const end = new Date(deadline)
    const diff = end.getTime() - now.getTime()
    
    if (diff <= 0) return 'انتهى'
    
    const days = Math.floor(diff / (1000 * 60 * 60 * 24))
    const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
    
    if (days > 0) return `${days} يوم`
    if (hours > 0) return `${hours} ساعة`
    return 'أقل من ساعة'
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge className="bg-green-100 text-green-800">نشط</Badge>
      case 'completed':
        return <Badge className="bg-blue-100 text-blue-800">مكتمل</Badge>
      case 'review':
        return <Badge className="bg-yellow-100 text-yellow-800">قيد المراجعة</Badge>
      case 'cancelled':
        return <Badge variant="destructive">ملغي</Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  const getCategoryName = (category: string) => {
    const categories = {
      'construction': 'إنشاءات',
      'it_technology': 'تقنية المعلومات',
      'consulting': 'استشارات',
      'healthcare': 'رعاية صحية',
      'education': 'تعليم',
      'transportation': 'نقل ومواصلات',
      'other': 'أخرى'
    }
    return categories[category as keyof typeof categories] || category
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">جاري تحميل المناقصات...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
        {/* Header */}
        <div className="bg-gradient-to-r from-green-600 to-emerald-600 text-white rounded-lg p-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold">المناقصات المتاحة</h1>
              <p className="text-green-100 mt-1">تصفح وتقدم للمناقصات الحكومية المناسبة لك</p>
            </div>
            <div className="flex items-center gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold">{filteredTenders.length}</div>
                <div className="text-sm text-green-100">مناقصة متاحة</div>
              </div>
              <Button 
                onClick={() => router.push('/user/applications')}
                className="bg-white text-green-600 hover:bg-gray-100"
              >
                <FileText className="h-4 w-4 ml-2" />
                طلباتي
              </Button>
            </div>
          </div>
        </div>

        {/* Filters */}
        <Card>
          <CardContent className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-6 gap-4">
              <div className="md:col-span-2">
                <div className="relative">
                  <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder="البحث في المناقصات..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pr-10"
                  />
                </div>
              </div>
              
              <Select value={categoryFilter} onValueChange={setCategoryFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="الفئة" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">جميع الفئات</SelectItem>
                  <SelectItem value="construction">إنشاءات</SelectItem>
                  <SelectItem value="it_technology">تقنية المعلومات</SelectItem>
                  <SelectItem value="consulting">استشارات</SelectItem>
                  <SelectItem value="healthcare">رعاية صحية</SelectItem>
                  <SelectItem value="education">تعليم</SelectItem>
                  <SelectItem value="transportation">نقل ومواصلات</SelectItem>
                </SelectContent>
              </Select>

              <Select value={locationFilter} onValueChange={setLocationFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="الموقع" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">جميع المواقع</SelectItem>
                  <SelectItem value="الرياض">الرياض</SelectItem>
                  <SelectItem value="جدة">جدة</SelectItem>
                  <SelectItem value="الدمام">الدمام</SelectItem>
                  <SelectItem value="مكة">مكة</SelectItem>
                </SelectContent>
              </Select>

              <Select value={sortBy} onValueChange={setSortBy}>
                <SelectTrigger>
                  <SelectValue placeholder="ترتيب" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="newest">الأحدث</SelectItem>
                  <SelectItem value="oldest">الأقدم</SelectItem>
                  <SelectItem value="budget_high">الميزانية (الأعلى)</SelectItem>
                  <SelectItem value="budget_low">الميزانية (الأقل)</SelectItem>
                  <SelectItem value="deadline_soon">ينتهي قريباً</SelectItem>
                  <SelectItem value="most_viewed">الأكثر مشاهدة</SelectItem>
                  <SelectItem value="most_applications">الأكثر طلبات</SelectItem>
                </SelectContent>
              </Select>

              <Button 
                onClick={loadTenders}
                variant="outline"
                className="w-full"
              >
                <RefreshCw className="h-4 w-4 ml-2" />
                تحديث
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Tenders Grid */}
        {filteredTenders.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredTenders.map((tender) => (
              <Card 
                key={tender._id} 
                className="hover:shadow-lg transition-shadow cursor-pointer relative"
                onClick={() => router.push(`/tenders/${tender._id}`)}
              >
                <CardHeader>
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <CardTitle className="text-lg line-clamp-2">{tender.title}</CardTitle>
                      <div className="flex items-center gap-2 mt-2">
                        <FileText className="h-4 w-4 text-green-600" />
                        <span className="text-sm text-gray-600">مناقصة</span>
                        {getStatusBadge(tender.status)}
                        {tender.hasApplied && (
                          <Badge className="bg-blue-100 text-blue-800">تقدمت</Badge>
                        )}
                      </div>
                    </div>
                    <div className="flex flex-col items-end gap-2">
                      <div className="text-right">
                        <div className="text-lg font-bold text-green-600">
                          {formatPrice(tender.budget)}
                        </div>
                        <div className="text-sm text-gray-500">الميزانية</div>
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation()
                          toggleFavorite(tender._id)
                        }}
                        className={`p-1 ${tender.isFavorite ? 'text-red-600' : 'text-gray-400'}`}
                      >
                        <Heart className={`h-4 w-4 ${tender.isFavorite ? 'fill-current' : ''}`} />
                      </Button>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-600 line-clamp-3 mb-4">{tender.description}</p>
                  
                  <div className="grid grid-cols-2 gap-4 text-sm mb-4">
                    <div className="flex items-center gap-2">
                      <Clock className="h-4 w-4 text-gray-400" />
                      <span>{formatTimeRemaining(tender.deadline)}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Users className="h-4 w-4 text-gray-400" />
                      <span>{tender.applicationsCount} طلب</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Eye className="h-4 w-4 text-gray-400" />
                      <span>{tender.viewsCount} مشاهدة</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Tag className="h-4 w-4 text-gray-400" />
                      <span>{getCategoryName(tender.category)}</span>
                    </div>
                  </div>

                  <div className="flex items-center gap-2 text-sm text-gray-500 mb-4">
                    <MapPin className="h-4 w-4" />
                    <span>{tender.location}</span>
                  </div>

                  {/* Requirements Preview */}
                  {tender.requirements && tender.requirements.length > 0 && (
                    <div className="mb-4">
                      <div className="text-sm font-medium text-gray-700 mb-2">المتطلبات:</div>
                      <div className="space-y-1">
                        {tender.requirements.slice(0, 2).map((req, index) => (
                          <div key={index} className="flex items-center gap-2 text-xs text-gray-600">
                            <CheckCircle className="h-3 w-3 text-green-600" />
                            <span>{req}</span>
                          </div>
                        ))}
                        {tender.requirements.length > 2 && (
                          <div className="text-xs text-gray-500">
                            +{tender.requirements.length - 2} متطلبات أخرى
                          </div>
                        )}
                      </div>
                    </div>
                  )}

                  <div className="pt-4 border-t">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <Building className="h-4 w-4 text-gray-400" />
                        <span className="text-sm text-gray-600">
                          {tender.organizer.profile.governmentEntity}
                        </span>
                      </div>
                      <div className="text-xs text-gray-500">
                        {new Date(tender.createdAt).toLocaleDateString('ar-SA')}
                      </div>
                    </div>
                  </div>

                  {/* Action Buttons */}
                  <div className="flex gap-2 mt-4">
                    <Button 
                      className="flex-1"
                      onClick={(e) => {
                        e.stopPropagation()
                        router.push(`/tenders/${tender._id}`)
                      }}
                    >
                      <Eye className="h-4 w-4 ml-2" />
                      عرض التفاصيل
                    </Button>
                    {!tender.hasApplied && tender.status === 'active' && (
                      <Button 
                        variant="outline"
                        onClick={(e) => {
                          e.stopPropagation()
                          router.push(`/tenders/${tender._id}`)
                        }}
                      >
                        <Send className="h-4 w-4 ml-2" />
                        تقدم
                      </Button>
                    )}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <FileText className="h-16 w-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">لا توجد مناقصات</h3>
            <p className="text-gray-600">
              {searchTerm || categoryFilter !== 'all' || locationFilter !== 'all'
                ? 'جرب تغيير معايير البحث أو الفلترة'
                : 'لا توجد مناقصات متاحة حالياً'
              }
            </p>
          </div>
        )}
      </div>
  )
}
