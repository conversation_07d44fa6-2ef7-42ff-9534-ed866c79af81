'use client'

import React, { useEffect, useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'

import { User, Mail, Phone, MapPin, Calendar, Edit, Save, X } from 'lucide-react'

export default function UserProfile() {
  const [user, setUser] = useState<any>(null)
  const [isEditing, setIsEditing] = useState(false)
  const [editedProfile, setEditedProfile] = useState<any>({})

  useEffect(() => {
    const userData = localStorage.getItem('user')
    if (userData) {
      const parsedUser = JSON.parse(userData)
      setUser(parsedUser)
      setEditedProfile(parsedUser.profile || {})
    }
  }, [])

  const handleSave = () => {
    // Here you would typically make an API call to save the changes
    console.log('Saving profile changes:', editedProfile)
    
    // Update local user data
    const updatedUser = { ...user, profile: editedProfile }
    setUser(updatedUser)
    localStorage.setItem('user', JSON.stringify(updatedUser))
    
    setIsEditing(false)
  }

  const handleCancel = () => {
    setEditedProfile(user?.profile || {})
    setIsEditing(false)
  }

  if (!user) {
    return (
      <div className="text-center">
        <p>جاري التحميل...</p>
      </div>
    )
  }

  return (
    <div className="space-y-6">
        <header className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold">الملف الشخصي</h1>
            <p className="text-muted-foreground">إدارة معلوماتك الشخصية</p>
          </div>
          {!isEditing ? (
            <Button onClick={() => setIsEditing(true)}>
              <Edit className="h-4 w-4 ml-2" />
              تعديل
            </Button>
          ) : (
            <div className="flex gap-2">
              <Button onClick={handleSave}>
                <Save className="h-4 w-4 ml-2" />
                حفظ
              </Button>
              <Button variant="outline" onClick={handleCancel}>
                <X className="h-4 w-4 ml-2" />
                إلغاء
              </Button>
            </div>
          )}
        </header>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <User className="h-5 w-5" />
              المعلومات الشخصية
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="fullName">الاسم الكامل</Label>
                {isEditing ? (
                  <Input
                    id="fullName"
                    value={editedProfile.fullName || ''}
                    onChange={(e) => setEditedProfile({...editedProfile, fullName: e.target.value})}
                    placeholder="أدخل الاسم الكامل"
                  />
                ) : (
                  <p className="p-2 bg-gray-50 rounded border">
                    {user.profile?.fullName || 'غير محدد'}
                  </p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="nationalId">رقم الهوية الوطنية</Label>
                {isEditing ? (
                  <Input
                    id="nationalId"
                    value={editedProfile.nationalId || ''}
                    onChange={(e) => setEditedProfile({...editedProfile, nationalId: e.target.value})}
                    placeholder="أدخل رقم الهوية"
                  />
                ) : (
                  <p className="p-2 bg-gray-50 rounded border">
                    {user.profile?.nationalId || 'غير محدد'}
                  </p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="phone">رقم الهاتف</Label>
                {isEditing ? (
                  <Input
                    id="phone"
                    value={editedProfile.phone || ''}
                    onChange={(e) => setEditedProfile({...editedProfile, phone: e.target.value})}
                    placeholder="أدخل رقم الهاتف"
                  />
                ) : (
                  <p className="p-2 bg-gray-50 rounded border">
                    {user.profile?.phone || 'غير محدد'}
                  </p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="address">العنوان</Label>
                {isEditing ? (
                  <Input
                    id="address"
                    value={editedProfile.address || ''}
                    onChange={(e) => setEditedProfile({...editedProfile, address: e.target.value})}
                    placeholder="أدخل العنوان"
                  />
                ) : (
                  <p className="p-2 bg-gray-50 rounded border">
                    {user.profile?.address || 'غير محدد'}
                  </p>
                )}
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Mail className="h-5 w-5" />
              معلومات الحساب
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label>البريد الإلكتروني</Label>
                <p className="p-2 bg-gray-50 rounded border">{user.email}</p>
              </div>

              <div className="space-y-2">
                <Label>حالة الحساب</Label>
                <p className="p-2 bg-gray-50 rounded border">
                  {user.status === 'approved' ? 'مفعل' : 
                   user.status === 'pending' ? 'قيد المراجعة' : 
                   user.status === 'rejected' ? 'مرفوض' : 
                   user.status}
                </p>
              </div>

              <div className="space-y-2">
                <Label>تاريخ التسجيل</Label>
                <p className="p-2 bg-gray-50 rounded border">
                  {user.createdAt ? new Date(user.createdAt).toLocaleDateString('ar') : 'غير محدد'}
                </p>
              </div>

              <div className="space-y-2">
                <Label>آخر تحديث</Label>
                <p className="p-2 bg-gray-50 rounded border">
                  {user.updatedAt ? new Date(user.updatedAt).toLocaleDateString('ar') : 'غير محدد'}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Activity Summary */}
        <Card>
          <CardHeader>
            <CardTitle>ملخص النشاط</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="text-center p-4 bg-blue-50 rounded-lg">
                <p className="text-2xl font-bold text-blue-600">5</p>
                <p className="text-sm text-blue-600">مزايدات نشطة</p>
              </div>
              <div className="text-center p-4 bg-green-50 rounded-lg">
                <p className="text-2xl font-bold text-green-600">3</p>
                <p className="text-sm text-green-600">مزادات مكسوبة</p>
              </div>
              <div className="text-center p-4 bg-purple-50 rounded-lg">
                <p className="text-2xl font-bold text-purple-600">12</p>
                <p className="text-sm text-purple-600">مزادات محفوظة</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
  )
}
