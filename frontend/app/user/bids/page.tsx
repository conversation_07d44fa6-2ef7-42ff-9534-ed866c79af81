'use client'

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { useToast } from '@/components/ui/use-toast'

import { useRouter } from 'next/navigation'
import api from '@/lib/api'
import { 
  Gavel, 
  TrendingUp,
  Clock, 
  DollarSign, 
  Eye, 
  Edit,
  Trash2,
  Calendar,
  Search,
  Filter,
  RefreshCw,
  Plus,
  AlertCircle,
  CheckCircle,
  XCircle,
  BarChart3,
  Target,
  Trophy,
  Activity
} from 'lucide-react'

interface Bid {
  _id: string
  auction: {
    _id: string
    title: string
    status: string
    endTime: string
    currentBid: number
  }
  amount: number
  timestamp: string
  status: 'active' | 'outbid' | 'winning' | 'won' | 'lost'
  isHighest: boolean
}

export default function UserBidsPage() {
  const [bids, setBids] = useState<Bid[]>([])
  const [filteredBids, setFilteredBids] = useState<Bid[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState('all')
  const [stats, setStats] = useState({
    total: 0,
    active: 0,
    won: 0,
    totalAmount: 0
  })
  const { toast } = useToast()
  const router = useRouter()

  useEffect(() => {
    loadBids()
  }, [])

  useEffect(() => {
    filterBids()
  }, [bids, searchTerm, statusFilter])

  const loadBids = async () => {
    try {
      setLoading(true)
      const response = await api.get('/users/bids')
      
      if (response.data.success) {
        setBids(response.data.data.bids)
        calculateStats(response.data.data.bids)
      } else {
        setBids([])
        calculateStats([])
      }
    } catch (error) {
      console.error('Error loading bids:', error)
      toast({
        title: 'خطأ في التحميل',
        description: 'حدث خطأ في تحميل بيانات المزايدات',
        variant: 'destructive'
      })
    } finally {
      setLoading(false)
    }
  }

  const calculateStats = (bidsData: Bid[]) => {
    const total = bidsData.length
    const active = bidsData.filter(bid => bid.auction.status === 'active').length
    const won = bidsData.filter(bid => bid.status === 'won').length
    const totalAmount = bidsData.reduce((sum, bid) => sum + bid.amount, 0)

    setStats({ total, active, won, totalAmount })
  }

  const filterBids = () => {
    let filtered = bids

    if (searchTerm) {
      filtered = filtered.filter(bid =>
        bid.auction.title.toLowerCase().includes(searchTerm.toLowerCase())
      )
    }

    if (statusFilter !== 'all') {
      filtered = filtered.filter(bid => bid.status === statusFilter)
    }

    setFilteredBids(filtered)
  }

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('ar-SA', {
      style: 'currency',
      currency: 'SAR',
      minimumFractionDigits: 0
    }).format(price)
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('ar-SA')
  }

  const formatTimeRemaining = (endTime: string) => {
    const now = new Date()
    const end = new Date(endTime)
    const diff = end.getTime() - now.getTime()
    
    if (diff <= 0) return 'انتهى'
    
    const days = Math.floor(diff / (1000 * 60 * 60 * 24))
    const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
    
    if (days > 0) return `${days} يوم`
    if (hours > 0) return `${hours} ساعة`
    return 'أقل من ساعة'
  }

  const getStatusBadge = (status: string, isHighest: boolean) => {
    switch (status) {
      case 'winning':
        return <Badge className="bg-green-100 text-green-800">رابح حالياً</Badge>
      case 'outbid':
        return <Badge className="bg-red-100 text-red-800">تم تجاوزه</Badge>
      case 'won':
        return <Badge className="bg-blue-100 text-blue-800">فائز</Badge>
      case 'lost':
        return <Badge variant="secondary">خاسر</Badge>
      case 'active':
        return isHighest 
          ? <Badge className="bg-green-100 text-green-800">الأعلى</Badge>
          : <Badge className="bg-yellow-100 text-yellow-800">نشط</Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  const updateBid = async (bidId: string, newAmount: number) => {
    try {
      const response = await api.patch(`/bids/${bidId}`, { amount: newAmount })
      
      if (response.data.success) {
        toast({
          title: 'تم التحديث',
          description: 'تم تحديث المزايدة بنجاح'
        })
        loadBids()
      }
    } catch (error: any) {
      toast({
        title: 'خطأ في التحديث',
        description: error.response?.data?.message || 'حدث خطأ في تحديث المزايدة',
        variant: 'destructive'
      })
    }
  }

  const deleteBid = async (bidId: string) => {
    if (!confirm('هل أنت متأكد من حذف هذه المزايدة؟')) return

    try {
      const response = await api.delete(`/bids/${bidId}`)
      
      if (response.data.success) {
        setBids(bids.filter(bid => bid._id !== bidId))
        toast({
          title: 'تم الحذف',
          description: 'تم حذف المزايدة بنجاح'
        })
      }
    } catch (error: any) {
      toast({
        title: 'خطأ في الحذف',
        description: error.response?.data?.message || 'حدث خطأ في حذف المزايدة',
        variant: 'destructive'
      })
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">جاري تحميل مزايداتك...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
        {/* Header */}
        <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg p-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold">مزايداتي</h1>
              <p className="text-blue-100 mt-1">إدارة ومتابعة جميع مزايداتك</p>
            </div>
            <div className="flex items-center gap-4">
              <Button 
                onClick={loadBids}
                variant="outline"
                className="bg-white/10 border-white/20 text-white hover:bg-white/20"
              >
                <RefreshCw className="h-4 w-4 ml-2" />
                تحديث
              </Button>
              <Button 
                onClick={() => router.push('/search')}
                className="bg-white text-blue-600 hover:bg-gray-100"
              >
                <Plus className="h-4 w-4 ml-2" />
                تصفح المزادات
              </Button>
            </div>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card className="bg-gradient-to-br from-blue-50 to-blue-100 border-blue-200">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-blue-600">إجمالي المزايدات</p>
                  <p className="text-3xl font-bold text-blue-900">{stats.total}</p>
                </div>
                <Gavel className="h-8 w-8 text-blue-600" />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-br from-green-50 to-green-100 border-green-200">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-green-600">المزايدات النشطة</p>
                  <p className="text-3xl font-bold text-green-900">{stats.active}</p>
                </div>
                <Activity className="h-8 w-8 text-green-600" />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-br from-purple-50 to-purple-100 border-purple-200">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-purple-600">المزادات المكسوبة</p>
                  <p className="text-3xl font-bold text-purple-900">{stats.won}</p>
                </div>
                <Trophy className="h-8 w-8 text-purple-600" />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-br from-orange-50 to-orange-100 border-orange-200">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-orange-600">إجمالي المبلغ</p>
                  <p className="text-3xl font-bold text-orange-900">{formatPrice(stats.totalAmount)}</p>
                </div>
                <DollarSign className="h-8 w-8 text-orange-600" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Filters */}
        <Card>
          <CardContent className="p-6">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder="البحث في المزايدات..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pr-10"
                  />
                </div>
              </div>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-full md:w-48">
                  <SelectValue placeholder="فلترة حسب الحالة" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">جميع الحالات</SelectItem>
                  <SelectItem value="winning">رابح حالياً</SelectItem>
                  <SelectItem value="outbid">تم تجاوزه</SelectItem>
                  <SelectItem value="won">فائز</SelectItem>
                  <SelectItem value="lost">خاسر</SelectItem>
                  <SelectItem value="active">نشط</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>

        {/* Bids Table */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="h-5 w-5 text-blue-600" />
              قائمة المزايدات
            </CardTitle>
            <CardDescription>
              جميع مزايداتك مع إمكانية التعديل والحذف ({filteredBids.length} من {bids.length})
            </CardDescription>
          </CardHeader>
          <CardContent>
            {filteredBids.length > 0 ? (
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="w-[300px]">تفاصيل المزاد</TableHead>
                      <TableHead>مزايدتي</TableHead>
                      <TableHead>المزايدة الحالية</TableHead>
                      <TableHead>الحالة</TableHead>
                      <TableHead>الوقت المتبقي</TableHead>
                      <TableHead className="text-center">الإجراءات</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredBids.map((bid) => (
                      <TableRow key={bid._id} className="hover:bg-gray-50">
                        <TableCell>
                          <div className="space-y-1">
                            <h4 className="font-medium text-gray-900">{bid.auction.title}</h4>
                            <div className="flex items-center gap-2 text-xs text-gray-500">
                              <Calendar className="h-3 w-3" />
                              مزايدة في: {formatDate(bid.timestamp)}
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="font-medium text-blue-600">
                            {formatPrice(bid.amount)}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="font-medium">
                            {formatPrice(bid.auction.currentBid)}
                          </div>
                        </TableCell>
                        <TableCell>
                          {getStatusBadge(bid.status, bid.isHighest)}
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-1">
                            <Clock className="h-4 w-4 text-gray-400" />
                            <span className="text-sm">
                              {formatTimeRemaining(bid.auction.endTime)}
                            </span>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center justify-center gap-2">
                            <Button 
                              variant="outline" 
                              size="sm"
                              onClick={() => router.push(`/auctions/${bid.auction._id}`)}
                            >
                              <Eye className="h-4 w-4 ml-1" />
                              عرض
                            </Button>
                            {bid.auction.status === 'active' && bid.status !== 'won' && (
                              <Button 
                                variant="outline" 
                                size="sm"
                                onClick={() => {
                                  const newAmount = prompt('أدخل المبلغ الجديد:', bid.amount.toString())
                                  if (newAmount && parseFloat(newAmount) > bid.auction.currentBid) {
                                    updateBid(bid._id, parseFloat(newAmount))
                                  }
                                }}
                              >
                                <Edit className="h-4 w-4 ml-1" />
                                تعديل
                              </Button>
                            )}
                            {bid.auction.status === 'active' && (
                              <Button 
                                variant="destructive" 
                                size="sm"
                                onClick={() => deleteBid(bid._id)}
                              >
                                <Trash2 className="h-4 w-4 ml-1" />
                                حذف
                              </Button>
                            )}
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            ) : (
              <div className="text-center py-12">
                <Gavel className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  {searchTerm || statusFilter !== 'all' 
                    ? 'لا توجد مزايدات تطابق البحث' 
                    : 'لا توجد مزايدات'
                  }
                </h3>
                <p className="text-gray-600 mb-6">
                  {searchTerm || statusFilter !== 'all'
                    ? 'جرب تغيير معايير البحث أو الفلترة'
                    : 'ابدأ بتصفح المزادات وتقديم مزايداتك'
                  }
                </p>
                {(!searchTerm && statusFilter === 'all') && (
                  <Button onClick={() => router.push('/search')}>
                    <Plus className="h-4 w-4 ml-2" />
                    تصفح المزادات
                  </Button>
                )}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
  )
}
