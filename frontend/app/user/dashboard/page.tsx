'use client'

import { useEffect, useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { useToast } from '@/components/ui/use-toast'

import { useRouter } from 'next/navigation'
import api from '@/lib/api'
import { useCurrency } from '@/contexts/CurrencyContext'
import { CurrencySelector } from '@/components/CurrencySelector'
import {
  Gavel,
  Activity,
  Bell,
  Trophy,
  Calendar,
  TrendingUp,
  Clock,
  Search,
  Eye,
  Heart,
  DollarSign,
  Users,
  Star,
  ArrowRight,
  FileText,
  Target,
  Zap,
  BarChart3,
  PieC<PERSON>,
  Line<PERSON>hart
} from 'lucide-react'
import {
  LineChart as RechartsLineChart,
  Line,
  AreaChart,
  Area,
  BarChart,
  Bar,
  PieChart as RechartsPieChart,
  Pie,
  Cell,
  XAxis,
  <PERSON>A<PERSON>s,
  <PERSON><PERSON>ianGrid,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  ResponsiveContainer
} from 'recharts'

interface DashboardStats {
  activeBids: number
  wonAuctions: number
  totalSpent: number
  savedAuctions: number
  totalBids: number
  successRate: number
}

interface RecentBid {
  _id: string
  auction: {
    _id: string
    title: string
    currentBid: number
    endTime: string
    status: string
  }
  amount: number
  placedAt: string
  isWinning: boolean
}

interface RecentAuction {
  _id: string
  title: string
  description: string
  startingBid: number
  currentBid: number
  endTime: string
  status: string
  category: string
  bidsCount: number
}

interface Notification {
  _id: string
  type: string
  title: string
  message: string
  createdAt: string
  read: boolean
}

export default function UserDashboard() {
  const [stats, setStats] = useState<DashboardStats | null>(null)
  const [recentBids, setRecentBids] = useState<RecentBid[]>([])
  const [recentAuctions, setRecentAuctions] = useState<RecentAuction[]>([])
  const [notifications, setNotifications] = useState<Notification[]>([])
  const [loading, setLoading] = useState(true)
  const [user, setUser] = useState<any>(null)
  const [chartData, setChartData] = useState<any>({
    biddingActivity: [],
    categorySpending: [],
    monthlyPerformance: [],
    winLossRatio: []
  })
  const { toast } = useToast()
  const router = useRouter()

  // Chart colors
  const COLORS = {
    primary: '#3B82F6',
    secondary: '#8B5CF6',
    success: '#10B981',
    warning: '#F59E0B',
    danger: '#EF4444',
    info: '#06B6D4'
  }

  const PIE_COLORS = ['#3B82F6', '#8B5CF6', '#10B981', '#F59E0B', '#EF4444', '#06B6D4']

  useEffect(() => {
    const userData = localStorage.getItem('user')
    if (userData) {
      setUser(JSON.parse(userData))
    }

    loadDashboardData()
  }, [])

  const loadDashboardData = async () => {
    try {
      setLoading(true)

      // Load user stats
      const statsResponse = await api.get('/user/dashboard/stats')
      if (statsResponse.data.success) {
        setStats(statsResponse.data.data)
      }

      // Load recent bids
      const bidsResponse = await api.get('/user/bids?limit=5')
      if (bidsResponse.data.success) {
        setRecentBids(bidsResponse.data.data.bids)
      }

      // Load recent auctions (recommended)
      const auctionsResponse = await api.get('/auctions?limit=6&status=active')
      if (auctionsResponse.data.success) {
        setRecentAuctions(auctionsResponse.data.data.auctions)
      }

      // Load notifications
      const notificationsResponse = await api.get('/user/notifications?limit=5')
      if (notificationsResponse.data.success) {
        setNotifications(notificationsResponse.data.data.notifications)
      }

      // Load chart data
      const chartResponse = await api.get('/user/analytics/charts')
      if (chartResponse.data.success) {
        setChartData(chartResponse.data.data)
      }

    } catch (error) {
      console.error('Error loading dashboard data:', error)
      // Fallback to sample data
      setStats({
        activeBids: 5,
        wonAuctions: 3,
        totalSpent: 45000,
        savedAuctions: 12,
        totalBids: 28,
        successRate: 75
      })
      setRecentBids([])
      setRecentAuctions([])
      setNotifications([])

      // Sample chart data
      setChartData({
        biddingActivity: [
          { month: 'يناير', bids: 12, wins: 3 },
          { month: 'فبراير', bids: 19, wins: 5 },
          { month: 'مارس', bids: 15, wins: 2 },
          { month: 'أبريل', bids: 22, wins: 7 },
          { month: 'مايو', bids: 18, wins: 4 },
          { month: 'يونيو', bids: 25, wins: 8 }
        ],
        categorySpending: [
          { name: 'إلكترونيات', value: 15000, color: PIE_COLORS[0] },
          { name: 'سيارات', value: 25000, color: PIE_COLORS[1] },
          { name: 'أثاث', value: 8000, color: PIE_COLORS[2] },
          { name: 'مجوهرات', value: 12000, color: PIE_COLORS[3] },
          { name: 'أخرى', value: 5000, color: PIE_COLORS[4] }
        ],
        monthlyPerformance: [
          { month: 'يناير', spent: 8000, saved: 2000 },
          { month: 'فبراير', spent: 12000, saved: 3000 },
          { month: 'مارس', spent: 6000, saved: 1500 },
          { month: 'أبريل', spent: 15000, saved: 4000 },
          { month: 'مايو', spent: 9000, saved: 2500 },
          { month: 'يونيو', spent: 18000, saved: 5000 }
        ],
        winLossRatio: [
          { name: 'فوز', value: 65, color: COLORS.success },
          { name: 'خسارة', value: 35, color: COLORS.danger }
        ]
      })
    } finally {
      setLoading(false)
    }
  }

  const { formatAmount } = useCurrency()

  const formatPrice = (price: number) => {
    return formatAmount(price)
  }

  const formatTimeRemaining = (endTime: string) => {
    const now = new Date()
    const end = new Date(endTime)
    const diff = end.getTime() - now.getTime()

    if (diff <= 0) return 'انتهى'

    const days = Math.floor(diff / (1000 * 60 * 60 * 24))
    const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60))

    if (days > 0) return `${days} يوم`
    if (hours > 0) return `${hours} ساعة`
    return `${minutes} دقيقة`
  }

  const formatRelativeTime = (dateString: string) => {
    const now = new Date()
    const date = new Date(dateString)
    const diff = now.getTime() - date.getTime()

    const minutes = Math.floor(diff / (1000 * 60))
    const hours = Math.floor(diff / (1000 * 60 * 60))
    const days = Math.floor(diff / (1000 * 60 * 60 * 24))

    if (days > 0) return `منذ ${days} يوم`
    if (hours > 0) return `منذ ${hours} ساعة`
    if (minutes > 0) return `منذ ${minutes} دقيقة`
    return 'الآن'
  }

  const getBidStatus = (bid: RecentBid) => {
    if (bid.isWinning) return { label: 'أعلى مزايدة', variant: 'default' as const, color: 'text-green-600' }
    if (bid.auction.status === 'active') return { label: 'متنافس', variant: 'secondary' as const, color: 'text-blue-600' }
    return { label: 'تم تجاوزها', variant: 'outline' as const, color: 'text-gray-600' }
  }

  if (loading) {
    return (
      <div className="text-center">
        <p>جاري تحميل الإحصائيات...</p>
      </div>
    )
  }

  return (
    <div className="space-y-6">
        {/* Welcome Header */}
        <header className="bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg p-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold">
                مرحباً، {user?.profile?.fullName || 'المستخدم'}
              </h1>
              <p className="text-blue-100 mt-2">
                استكشف المزادات المتاحة وتابع مزايداتك
              </p>
            </div>
            <div className="hidden md:flex items-center gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold">{stats?.successRate || 0}%</div>
                <div className="text-sm text-blue-100">معدل النجاح</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold">{stats?.totalBids || 0}</div>
                <div className="text-sm text-blue-100">إجمالي المزايدات</div>
              </div>
              <div className="bg-white/10 rounded-lg p-2">
                <CurrencySelector showLabel={false} className="text-white" />
              </div>
            </div>
          </div>
        </header>

        {/* Quick Actions */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Button
            className="h-16 text-lg bg-blue-600 hover:bg-blue-700"
            size="lg"
            onClick={() => router.push('/user/auctions')}
          >
            <Search className="h-6 w-6 ml-2" />
            استكشاف المزادات
          </Button>
          <Button
            variant="outline"
            className="h-16 text-lg border-green-200 text-green-700 hover:bg-green-50"
            size="lg"
            onClick={() => router.push('/user/my-bids')}
          >
            <Activity className="h-6 w-6 ml-2" />
            مزايداتي
          </Button>
          <Button
            variant="outline"
            className="h-16 text-lg border-purple-200 text-purple-700 hover:bg-purple-50"
            size="lg"
            onClick={() => router.push('/user/notifications')}
          >
            <Bell className="h-6 w-6 ml-2" />
            التنبيهات
          </Button>
          <Button
            variant="outline"
            className="h-16 text-lg border-orange-200 text-orange-700 hover:bg-orange-50"
            size="lg"
            onClick={() => router.push('/user/profile')}
          >
            <Users className="h-6 w-6 ml-2" />
            الملف الشخصي
          </Button>
        </div>

        {/* Enhanced Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card className="bg-gradient-to-br from-blue-50 to-blue-100 border-blue-200">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-blue-600">مزايداتي النشطة</p>
                  <p className="text-3xl font-bold text-blue-900">{stats?.activeBids || 0}</p>
                  <p className="text-xs text-blue-600 mt-1">
                    <Zap className="h-3 w-3 inline ml-1" />
                    نشط الآن
                  </p>
                </div>
                <div className="h-12 w-12 bg-blue-200 rounded-full flex items-center justify-center">
                  <Activity className="h-6 w-6 text-blue-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-br from-green-50 to-green-100 border-green-200">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-green-600">المزادات المكسوبة</p>
                  <p className="text-3xl font-bold text-green-900">{stats?.wonAuctions || 0}</p>
                  <p className="text-xs text-green-600 mt-1">
                    <Trophy className="h-3 w-3 inline ml-1" />
                    إنجاز رائع
                  </p>
                </div>
                <div className="h-12 w-12 bg-green-200 rounded-full flex items-center justify-center">
                  <Trophy className="h-6 w-6 text-green-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-br from-purple-50 to-purple-100 border-purple-200">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-purple-600">إجمالي المبلغ المنفق</p>
                  <p className="text-3xl font-bold text-purple-900">{formatPrice(stats?.totalSpent || 0)}</p>
                  <p className="text-xs text-purple-600 mt-1">
                    <TrendingUp className="h-3 w-3 inline ml-1" />
                    استثمار ذكي
                  </p>
                </div>
                <div className="h-12 w-12 bg-purple-200 rounded-full flex items-center justify-center">
                  <DollarSign className="h-6 w-6 text-purple-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-br from-orange-50 to-orange-100 border-orange-200">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-orange-600">المزادات المحفوظة</p>
                  <p className="text-3xl font-bold text-orange-900">{stats?.savedAuctions || 0}</p>
                  <p className="text-xs text-orange-600 mt-1">
                    <Heart className="h-3 w-3 inline ml-1" />
                    مفضلة
                  </p>
                </div>
                <div className="h-12 w-12 bg-orange-200 rounded-full flex items-center justify-center">
                  <Heart className="h-6 w-6 text-orange-600" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Analytics Charts */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Bidding Activity Chart */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <LineChart className="h-5 w-5 text-blue-600" />
                نشاط المزايدات الشهري
              </CardTitle>
              <CardDescription>
                عدد المزايدات والانتصارات خلال الأشهر الماضية
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <AreaChart data={chartData.biddingActivity}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="month" />
                  <YAxis />
                  <Tooltip />
                  <Legend />
                  <Area
                    type="monotone"
                    dataKey="bids"
                    stackId="1"
                    stroke={COLORS.primary}
                    fill={COLORS.primary}
                    fillOpacity={0.6}
                    name="المزايدات"
                  />
                  <Area
                    type="monotone"
                    dataKey="wins"
                    stackId="2"
                    stroke={COLORS.success}
                    fill={COLORS.success}
                    fillOpacity={0.8}
                    name="الانتصارات"
                  />
                </AreaChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>

          {/* Category Spending Chart */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <PieChart className="h-5 w-5 text-purple-600" />
                الإنفاق حسب الفئة
              </CardTitle>
              <CardDescription>
                توزيع إنفاقك على الفئات المختلفة
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <RechartsPieChart>
                  <Pie
                    data={chartData.categorySpending}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="value"
                  >
                    {chartData.categorySpending.map((entry: any, index: number) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip formatter={(value: any) => formatPrice(value)} />
                </RechartsPieChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </div>

        {/* Performance Charts */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Monthly Performance */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="h-5 w-5 text-green-600" />
                الأداء الشهري
              </CardTitle>
              <CardDescription>
                المبلغ المنفق والمدخر شهرياً
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <BarChart data={chartData.monthlyPerformance}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="month" />
                  <YAxis />
                  <Tooltip formatter={(value: any) => formatPrice(value)} />
                  <Legend />
                  <Bar dataKey="spent" fill={COLORS.danger} name="المنفق" />
                  <Bar dataKey="saved" fill={COLORS.success} name="المدخر" />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>

          {/* Win/Loss Ratio */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Trophy className="h-5 w-5 text-yellow-600" />
                نسبة النجاح
              </CardTitle>
              <CardDescription>
                نسبة الفوز إلى الخسارة في المزايدات
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <RechartsPieChart>
                  <Pie
                    data={chartData.winLossRatio}
                    cx="50%"
                    cy="50%"
                    innerRadius={60}
                    outerRadius={100}
                    paddingAngle={5}
                    dataKey="value"
                  >
                    {chartData.winLossRatio.map((entry: any, index: number) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip formatter={(value: any) => `${value}%`} />
                  <Legend />
                </RechartsPieChart>
              </ResponsiveContainer>
              <div className="mt-4 flex justify-center gap-6">
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">{chartData.winLossRatio[0]?.value || 0}%</div>
                  <div className="text-sm text-gray-600">معدل النجاح</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-red-600">{chartData.winLossRatio[1]?.value || 0}%</div>
                  <div className="text-sm text-gray-600">معدل الخسارة</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Activity Overview */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* My Recent Bids */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Activity className="h-5 w-5 text-blue-600" />
                  مزايداتي الحديثة
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => router.push('/user/my-bids')}
                >
                  عرض الكل
                  <ArrowRight className="h-4 w-4 mr-1" />
                </Button>
              </CardTitle>
              <CardDescription>
                آخر المزايدات التي شاركت فيها
              </CardDescription>
            </CardHeader>
            <CardContent>
              {recentBids.length > 0 ? (
                <div className="space-y-3">
                  {recentBids.slice(0, 3).map((bid) => {
                    const status = getBidStatus(bid)
                    return (
                      <div key={bid._id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                        <div className="flex-1">
                          <h4 className="font-medium text-gray-900">{bid.auction.title}</h4>
                          <div className="flex items-center gap-4 mt-1">
                            <p className="text-sm text-gray-600">
                              <Clock className="h-4 w-4 inline ml-1" />
                              {formatTimeRemaining(bid.auction.endTime)}
                            </p>
                            <p className="text-sm text-gray-600">
                              مزايدتي: <span className="font-medium">{formatPrice(bid.amount)}</span>
                            </p>
                          </div>
                          <p className="text-xs text-gray-500 mt-1">
                            {formatRelativeTime(bid.placedAt)}
                          </p>
                        </div>
                        <div className="flex flex-col items-end gap-2">
                          <Badge variant={status.variant} className={status.color}>
                            {status.label}
                          </Badge>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => router.push(`/auctions/${bid.auction._id}`)}
                          >
                            <Eye className="h-4 w-4 ml-1" />
                            عرض
                          </Button>
                        </div>
                      </div>
                    )
                  })}
                </div>
              ) : (
                <div className="text-center py-8">
                  <Activity className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">لا توجد مزايدات حديثة</h3>
                  <p className="text-gray-600 mb-4">ابدأ بالمشاركة في المزادات المتاحة</p>
                  <Button onClick={() => router.push('/user/auctions')}>
                    استكشاف المزادات
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Notifications */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Bell className="h-5 w-5 text-orange-600" />
                  التنبيهات
                  {notifications.filter(n => !n.read).length > 0 && (
                    <Badge variant="destructive" className="text-xs">
                      {notifications.filter(n => !n.read).length}
                    </Badge>
                  )}
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => router.push('/user/notifications')}
                >
                  عرض الكل
                  <ArrowRight className="h-4 w-4 mr-1" />
                </Button>
              </CardTitle>
              <CardDescription>
                آخر التحديثات والإشعارات
              </CardDescription>
            </CardHeader>
            <CardContent>
              {notifications.length > 0 ? (
                <div className="space-y-3">
                  {notifications.slice(0, 3).map((notification) => {
                    const getNotificationStyle = (type: string) => {
                      switch (type) {
                        case 'auction_won':
                          return 'bg-green-50 border-green-200 text-green-800'
                        case 'auction_ending':
                          return 'bg-yellow-50 border-yellow-200 text-yellow-800'
                        case 'new_auction':
                          return 'bg-blue-50 border-blue-200 text-blue-800'
                        case 'bid_outbid':
                          return 'bg-red-50 border-red-200 text-red-800'
                        default:
                          return 'bg-gray-50 border-gray-200 text-gray-800'
                      }
                    }

                    return (
                      <div
                        key={notification._id}
                        className={`p-3 border rounded-lg ${getNotificationStyle(notification.type)} ${!notification.read ? 'ring-2 ring-blue-200' : ''}`}
                      >
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <p className="text-sm font-medium">{notification.title}</p>
                            <p className="text-xs mt-1">{notification.message}</p>
                            <p className="text-xs opacity-75 mt-2">
                              {formatRelativeTime(notification.createdAt)}
                            </p>
                          </div>
                          {!notification.read && (
                            <div className="w-2 h-2 bg-blue-600 rounded-full mt-1"></div>
                          )}
                        </div>
                      </div>
                    )
                  })}
                </div>
              ) : (
                <div className="text-center py-8">
                  <Bell className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">لا توجد تنبيهات</h3>
                  <p className="text-gray-600">ستظهر هنا التحديثات المهمة</p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Recommended Auctions */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Target className="h-5 w-5 text-purple-600" />
                مزادات مقترحة لك
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => router.push('/user/auctions')}
              >
                عرض المزيد
                <ArrowRight className="h-4 w-4 mr-1" />
              </Button>
            </CardTitle>
            <CardDescription>
              مزادات قد تهمك بناءً على اهتماماتك السابقة
            </CardDescription>
          </CardHeader>
          <CardContent>
            {recentAuctions.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {recentAuctions.slice(0, 6).map((auction) => (
                  <article
                    key={auction._id}
                    className="border rounded-lg p-4 hover:shadow-lg transition-all duration-200 hover:border-blue-300 cursor-pointer"
                    onClick={() => router.push(`/auctions/${auction._id}`)}
                  >
                    <div className="aspect-video bg-gradient-to-br from-gray-100 to-gray-200 rounded mb-3 flex items-center justify-center">
                      <Gavel className="h-8 w-8 text-gray-400" />
                    </div>

                    <div className="space-y-2">
                      <h4 className="font-medium text-gray-900 line-clamp-2">{auction.title}</h4>

                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm text-gray-600">
                            السعر الحالي: <span className="font-medium text-green-600">{formatPrice(auction.currentBid)}</span>
                          </p>
                          <p className="text-xs text-gray-500">
                            يبدأ من: {formatPrice(auction.startingBid)}
                          </p>
                        </div>
                        <Badge variant="outline" className="text-xs">
                          {auction.category}
                        </Badge>
                      </div>

                      <div className="flex items-center justify-between pt-2">
                        <div className="flex items-center gap-1 text-xs text-gray-500">
                          <Clock className="h-3 w-3" />
                          {formatTimeRemaining(auction.endTime)}
                        </div>
                        <div className="flex items-center gap-1 text-xs text-gray-500">
                          <Users className="h-3 w-3" />
                          {auction.bidsCount} مزايدة
                        </div>
                      </div>

                      <Button
                        size="sm"
                        className="w-full mt-3"
                        onClick={(e) => {
                          e.stopPropagation()
                          router.push(`/auctions/${auction._id}`)
                        }}
                      >
                        <Eye className="h-4 w-4 ml-1" />
                        شاهد التفاصيل
                      </Button>
                    </div>
                  </article>
                ))}
              </div>
            ) : (
              <div className="text-center py-12">
                <Target className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-xl font-medium text-gray-900 mb-2">لا توجد مزادات متاحة حالياً</h3>
                <p className="text-gray-600 mb-6">تحقق مرة أخرى لاحقاً أو استكشف الفئات المختلفة</p>
                <Button onClick={() => router.push('/user/auctions')}>
                  استكشاف المزادات
                </Button>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
  )
}
