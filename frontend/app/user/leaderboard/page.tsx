'use client'

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import {
  Trophy,
  Medal,
  Award,
  Target,
  TrendingUp,
  Activity,
  Crown,
  Star,
  RefreshCw,
  Calendar,
  User,
  Gavel
} from 'lucide-react'
import { useToast } from '@/components/ui/use-toast'

import api from '@/lib/api'

type TimePeriod = 'daily' | 'weekly' | 'monthly' | 'all-time'

interface LeaderboardEntry {
  _id: string
  user: {
    profile: {
      fullName: string
      avatarUrl: string
    }
    email: string
  }
  totalBids: number
  totalAmount: number
  wonAuctions: number
  points: number
  badge: string
  rank: number
}

const getBadgeIcon = (badge: string) => {
  switch (badge) {
    case 'Champion':
      return <Crown className="h-4 w-4" />
    case 'Master':
      return <Trophy className="h-4 w-4" />
    case 'Expert':
      return <Medal className="h-4 w-4" />
    case 'Pro':
      return <Award className="h-4 w-4" />
    case 'Active':
      return <Star className="h-4 w-4" />
    default:
      return <Target className="h-4 w-4" />
  }
}

const getBadgeColor = (badge: string) => {
  switch (badge) {
    case 'Champion':
      return 'bg-yellow-500 text-white'
    case 'Master':
      return 'bg-purple-500 text-white'
    case 'Expert':
      return 'bg-blue-500 text-white'
    case 'Pro':
      return 'bg-green-500 text-white'
    case 'Active':
      return 'bg-orange-500 text-white'
    default:
      return 'bg-gray-500 text-white'
  }
}

const getRankIcon = (rank: number) => {
  if (rank === 1) return <Trophy className="h-6 w-6 text-yellow-500" />
  if (rank === 2) return <Medal className="h-6 w-6 text-gray-400" />
  if (rank === 3) return <Award className="h-6 w-6 text-amber-600" />
  return <span className="text-lg font-bold text-gray-600">#{rank}</span>
}

export default function LeaderboardPage() {
  const [leaderboard, setLeaderboard] = useState<LeaderboardEntry[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [refreshing, setRefreshing] = useState(false)
  const [selectedPeriod, setSelectedPeriod] = useState<TimePeriod>('all-time')
  const [user, setUser] = useState<any>(null)
  const { toast } = useToast()



  const fetchLeaderboard = async (period: TimePeriod = selectedPeriod) => {
    try {
      const response = await api.get(`/users/leaderboard?period=${period}&limit=50`)

      if (response.data.success) {
        setLeaderboard(response.data.data.leaderboard)
        setError(null)
      } else {
        throw new Error('فشل في تحميل لوحة الصدارة')
      }
    } catch (err) {
      console.error('Leaderboard error:', err)
      setError(err instanceof Error ? err.message : 'حدث خطأ غير متوقع')
      toast({
        title: 'خطأ في التحميل',
        description: 'حدث خطأ في تحميل بيانات المتصدرين',
        variant: 'destructive'
      })
      setLeaderboard([])
    } finally {
      setLoading(false)
      setRefreshing(false)
    }
  }

  const handleRefresh = () => {
    setRefreshing(true)
    fetchLeaderboard()
  }

  const handlePeriodChange = (period: TimePeriod) => {
    setSelectedPeriod(period)
    setLoading(true)
    fetchLeaderboard(period)
  }

  const getPeriodLabel = (period: TimePeriod) => {
    switch (period) {
      case 'daily': return 'اليوم'
      case 'weekly': return 'الأسبوع'
      case 'monthly': return 'الشهر'
      case 'all-time': return 'جميع الأوقات'
    }
  }

  useEffect(() => {
    fetchLeaderboard()
  }, [])

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="p-4 bg-primary/10 rounded-full w-20 h-20 mx-auto mb-6 flex items-center justify-center">
            <RefreshCw className="h-8 w-8 animate-spin text-primary" />
          </div>
          <h3 className="text-lg font-semibold mb-2">جاري تحميل لوحة الصدارة</h3>
          <p className="text-gray-600">يرجى الانتظار بينما نحضر أحدث البيانات...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <Card className="max-w-md mx-auto bg-gradient-to-br from-red-50 to-orange-50 border-red-200">
          <CardContent className="pt-6">
            <div className="text-center">
              <div className="p-4 bg-red-100 rounded-full w-20 h-20 mx-auto mb-6 flex items-center justify-center">
                <Trophy className="h-10 w-10 text-red-500" />
              </div>
              <h3 className="text-xl font-semibold mb-3 text-red-700">خطأ في تحميل البيانات</h3>
              <p className="text-gray-600 mb-6">{error}</p>
              <Button
                onClick={handleRefresh}
                className="w-full bg-gradient-to-r from-red-500 to-orange-500 hover:from-red-600 hover:to-orange-600"
                disabled={refreshing}
              >
                <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
                إعادة المحاولة
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="space-y-6">
        {/* Header */}
        <div className="bg-gradient-to-r from-yellow-500 to-orange-500 text-white rounded-lg p-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold flex items-center">
                <Trophy className="h-8 w-8 mr-3" />
                لوحة الصدارة
              </h1>
              <p className="mt-2 opacity-90">
                تصنيف أفضل المزايدين في المنصة - {getPeriodLabel(selectedPeriod)}
              </p>
            </div>
            <Button
              onClick={handleRefresh}
              disabled={refreshing}
              variant="secondary"
              className="bg-white/20 hover:bg-white/30 text-white border-white/30"
            >
              <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
              تحديث
            </Button>
          </div>

          {/* Time Period Filter */}
          <div className="mt-6">
            <div className="flex items-center space-x-2 space-x-reverse">
              <Calendar className="h-5 w-5 text-white/80" />
              <span className="text-sm font-medium text-white/90 ml-3">الفترة الزمنية:</span>
              <div className="flex space-x-2 space-x-reverse">
                {(['daily', 'weekly', 'monthly', 'all-time'] as TimePeriod[]).map((period) => (
                  <Button
                    key={period}
                    variant={selectedPeriod === period ? "secondary" : "outline"}
                    size="sm"
                    onClick={() => handlePeriodChange(period)}
                    disabled={loading}
                    className={selectedPeriod === period
                      ? "bg-white text-orange-600 hover:bg-white/90"
                      : "bg-white/20 text-white border-white/30 hover:bg-white/30"
                    }
                  >
                    {getPeriodLabel(period)}
                  </Button>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Stats Overview */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card className="bg-gradient-to-br from-blue-50 to-blue-100 border-blue-200">
            <CardContent className="pt-6">
              <div className="flex items-center">
                <div className="p-3 bg-blue-500 rounded-full">
                  <Activity className="h-6 w-6 text-white" />
                </div>
                <div className="mr-4">
                  <p className="text-2xl font-bold text-blue-700">{leaderboard.length}</p>
                  <p className="text-blue-600 text-sm">مزايد نشط</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-br from-green-50 to-green-100 border-green-200">
            <CardContent className="pt-6">
              <div className="flex items-center">
                <div className="p-3 bg-green-500 rounded-full">
                  <TrendingUp className="h-6 w-6 text-white" />
                </div>
                <div className="mr-4">
                  <p className="text-2xl font-bold text-green-700">
                    {Array.isArray(leaderboard) ? leaderboard.reduce((sum, entry) => sum + entry.totalBids, 0) : 0}
                  </p>
                  <p className="text-green-600 text-sm">إجمالي المزايدات</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-br from-purple-50 to-purple-100 border-purple-200">
            <CardContent className="pt-6">
              <div className="flex items-center">
                <div className="p-3 bg-purple-500 rounded-full">
                  <Target className="h-6 w-6 text-white" />
                </div>
                <div className="mr-4">
                  <p className="text-2xl font-bold text-purple-700">
                    {Array.isArray(leaderboard) ? leaderboard.reduce((sum, entry) => sum + entry.points, 0) : 0}
                  </p>
                  <p className="text-purple-600 text-sm">إجمالي النقاط</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-br from-orange-50 to-orange-100 border-orange-200">
            <CardContent className="pt-6">
              <div className="flex items-center">
                <div className="p-3 bg-orange-500 rounded-full">
                  <Crown className="h-6 w-6 text-white" />
                </div>
                <div className="mr-4">
                  <p className="text-2xl font-bold text-orange-700">
                    {Array.isArray(leaderboard) ? leaderboard.reduce((sum, entry) => sum + entry.totalAmount, 0).toLocaleString('ar-SA') : 0}
                  </p>
                  <p className="text-orange-600 text-sm">إجمالي المبالغ (ر.س)</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Leaderboard */}
        {leaderboard.length === 0 ? (
          <Card className="bg-gradient-to-br from-gray-50 to-gray-100">
            <CardContent className="pt-6">
              <div className="text-center py-12">
                <div className="p-4 bg-gray-200 rounded-full w-20 h-20 mx-auto mb-6 flex items-center justify-center">
                  <Trophy className="h-10 w-10 text-gray-400" />
                </div>
                <h3 className="text-xl font-semibold mb-3 text-gray-700">لا توجد بيانات حالياً</h3>
                <p className="text-gray-600 mb-6 max-w-md mx-auto">
                  لم يتم العثور على أي مزايدات بعد. ابدأ بالمزايدة لتظهر في لوحة الصدارة!
                </p>
                <Button className="bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700">
                  <Gavel className="h-4 w-4 mr-2" />
                  ابدأ المزايدة الآن
                </Button>
              </div>
            </CardContent>
          </Card>
        ) : (
          <div className="space-y-4">
            {/* Top 3 Special Cards */}
            {leaderboard.slice(0, 3).map((entry, index) => (
              <Card
                key={entry._id}
                className={`relative overflow-hidden ${
                  index === 0 ? 'bg-gradient-to-r from-yellow-50 to-orange-50 border-2 border-yellow-300 shadow-lg' :
                  index === 1 ? 'bg-gradient-to-r from-gray-50 to-slate-50 border-2 border-gray-300 shadow-md' :
                  'bg-gradient-to-r from-amber-50 to-orange-50 border-2 border-amber-300 shadow-md'
                }`}
              >
                {/* Rank Badge */}
                <div className={`absolute top-0 right-0 w-16 h-16 ${
                  index === 0 ? 'bg-gradient-to-br from-yellow-400 to-yellow-600' :
                  index === 1 ? 'bg-gradient-to-br from-gray-400 to-gray-600' :
                  'bg-gradient-to-br from-amber-400 to-amber-600'
                } transform rotate-45 translate-x-6 -translate-y-6`}>
                  <div className="absolute bottom-2 left-2 text-white font-bold text-sm transform -rotate-45">
                    #{entry.rank}
                  </div>
                </div>

                <CardContent className="pt-6">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4 space-x-reverse">
                      {/* Rank Icon */}
                      <div className="flex-shrink-0 w-16 h-16 flex items-center justify-center">
                        {getRankIcon(entry.rank)}
                      </div>

                      {/* User Info */}
                      <div className="flex-1 min-w-0 flex items-center">
                        <Avatar className="mr-4 w-12 h-12 border-2 border-white shadow-md">
                          <AvatarImage src={entry.user.profile.avatarUrl} alt="Avatar" />
                          <AvatarFallback className="bg-gradient-to-br from-blue-400 to-purple-500 text-white">
                            <User className="h-6 w-6" />
                          </AvatarFallback>
                        </Avatar>
                        <div>
                          <h3 className="text-xl font-bold text-gray-900 truncate">
                            {entry.user.profile?.fullName || entry.user.email}
                          </h3>
                          <div className="flex items-center space-x-3 space-x-reverse mt-2">
                            <Badge className={`${getBadgeColor(entry.badge)} flex items-center px-3 py-1`}>
                              {getBadgeIcon(entry.badge)}
                              <span className="mr-1 font-medium">{entry.badge}</span>
                            </Badge>
                            <span className="text-sm font-medium text-gray-600 bg-white px-2 py-1 rounded-full">
                              {entry.points} نقطة
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Stats */}
                    <div className="flex space-x-8 space-x-reverse text-center">
                      <div className="bg-white/70 rounded-lg p-3 min-w-[80px]">
                        <p className="text-2xl font-bold text-blue-600">{entry.totalBids}</p>
                        <p className="text-xs text-gray-600 font-medium">مزايدة</p>
                      </div>
                      <div className="bg-white/70 rounded-lg p-3 min-w-[100px]">
                        <p className="text-2xl font-bold text-green-600">
                          {entry.totalAmount.toLocaleString('ar-SA')}
                        </p>
                        <p className="text-xs text-gray-600 font-medium">ر.س</p>
                      </div>
                      <div className="bg-white/70 rounded-lg p-3 min-w-[80px]">
                        <p className="text-2xl font-bold text-purple-600">{entry.wonAuctions}</p>
                        <p className="text-xs text-gray-600 font-medium">فوز</p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}

            {/* Remaining entries */}
            {leaderboard.slice(3).map((entry, index) => (
              <Card key={entry._id} className="hover:shadow-md transition-shadow duration-200">
                <CardContent className="pt-6">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4 space-x-reverse">
                      {/* Rank */}
                      <div className="flex-shrink-0 w-12 h-12 flex items-center justify-center bg-gray-100 rounded-full">
                        <span className="text-lg font-bold text-gray-600">#{entry.rank}</span>
                      </div>

                      {/* User Info */}
                      <div className="flex-1 min-w-0 flex items-center">
                        <Avatar className="mr-4">
                          <AvatarImage src={entry.user.profile.avatarUrl} alt="Avatar" />
                          <AvatarFallback className="bg-gradient-to-br from-gray-400 to-gray-500 text-white">
                            <User className="h-4 w-4" />
                          </AvatarFallback>
                        </Avatar>
                        <div>
                          <h3 className="text-lg font-semibold text-gray-900 truncate">
                            {entry.user.profile?.fullName || entry.user.email}
                          </h3>
                          <div className="flex items-center space-x-2 space-x-reverse mt-1">
                            <Badge className={`${getBadgeColor(entry.badge)} flex items-center`}>
                              {getBadgeIcon(entry.badge)}
                              <span className="mr-1">{entry.badge}</span>
                            </Badge>
                            <span className="text-sm text-gray-500">
                              {entry.points} نقطة
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Stats */}
                    <div className="flex space-x-6 space-x-reverse text-center">
                      <div>
                        <p className="text-xl font-bold text-blue-600">{entry.totalBids}</p>
                        <p className="text-xs text-gray-600">مزايدة</p>
                      </div>
                      <div>
                        <p className="text-xl font-bold text-green-600">
                          {entry.totalAmount.toLocaleString('ar-SA')}
                        </p>
                        <p className="text-xs text-gray-600">ر.س</p>
                      </div>
                      <div>
                        <p className="text-xl font-bold text-purple-600">{entry.wonAuctions}</p>
                        <p className="text-xs text-gray-600">فوز</p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}

        {/* Footer Note */}
        <Card className="bg-gradient-to-r from-blue-50 to-purple-50 border-blue-200">
          <CardContent className="pt-6">
            <div className="text-center">
              <div className="flex items-center justify-center mb-3">
                <Star className="h-5 w-5 text-yellow-500 mr-2" />
                <h4 className="font-semibold text-gray-700">كيف يتم حساب النقاط؟</h4>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-600">
                <div className="flex items-center justify-center">
                  <Target className="h-4 w-4 text-blue-500 mr-2" />
                  <span>+10 نقاط لكل مزايدة</span>
                </div>
                <div className="flex items-center justify-center">
                  <Trophy className="h-4 w-4 text-green-500 mr-2" />
                  <span>+50 نقطة لكل فوز</span>
                </div>
                <div className="flex items-center justify-center">
                  <Activity className="h-4 w-4 text-purple-500 mr-2" />
                  <span>نقاط إضافية حسب القيمة</span>
                </div>
              </div>
              <p className="text-xs text-gray-500 mt-4">
                يتم تحديث لوحة الصدارة كل ساعة. النقاط تُحسب بناءً على النشاط والأداء في المزايدات.
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
  )
}
