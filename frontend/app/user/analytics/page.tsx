'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { TrendingUp, TrendingDown, Award, Target, Calendar, DollarSign, Activity, Users } from 'lucide-react'
import api from '@/lib/api'
import { useCurrency } from '@/contexts/CurrencyContext'
import { CurrencySelector } from '@/components/CurrencySelector'

interface UserStats {
  totalBids: number
  wonAuctions: number
  totalSpent: number
  averageBidAmount: number
  successRate: number
  activeApplications: number
  rank: number
  totalUsers: number
}

interface ActivityData {
  date: string
  bids: number
  applications: number
  spent: number
}

interface Achievement {
  id: string
  title: string
  description: string
  icon: string
  earned: boolean
  earnedDate?: string
  progress?: number
  target?: number
}

export default function UserAnalyticsPage() {
  const [stats, setStats] = useState<UserStats | null>(null)
  const [activities, setActivities] = useState<ActivityData[]>([])
  const [achievements, setAchievements] = useState<Achievement[]>([])
  const [timeRange, setTimeRange] = useState('30d')
  const [loading, setLoading] = useState(true)
  const { formatAmount } = useCurrency()

  useEffect(() => {
    loadAnalytics()
  }, [timeRange])

  const loadAnalytics = async () => {
    try {
      setLoading(true)
      const response = await api.get(`/user/analytics?range=${timeRange}`)
      if (response.data.success) {
        setStats(response.data.data.stats)
        setActivities(response.data.data.activities || [])
        setAchievements(response.data.data.achievements || [])
      }
    } catch (error) {
      console.error('Error loading analytics:', error)
      // Demo data
      setStats({
        totalBids: 45,
        wonAuctions: 8,
        totalSpent: 125000,
        averageBidAmount: 2780,
        successRate: 17.8,
        activeApplications: 3,
        rank: 156,
        totalUsers: 1250
      })
      
      setActivities([
        { date: '2025-01-01', bids: 3, applications: 1, spent: 15000 },
        { date: '2025-01-02', bids: 2, applications: 0, spent: 8000 },
        { date: '2025-01-03', bids: 5, applications: 2, spent: 22000 },
        { date: '2025-01-04', bids: 1, applications: 1, spent: 5000 },
        { date: '2025-01-05', bids: 4, applications: 0, spent: 18000 }
      ])
      
      setAchievements([
        {
          id: '1',
          title: 'أول مزايدة',
          description: 'قم بأول مزايدة لك',
          icon: '🎯',
          earned: true,
          earnedDate: '2024-12-15'
        },
        {
          id: '2',
          title: 'الفائز',
          description: 'اربح أول مزاد لك',
          icon: '🏆',
          earned: true,
          earnedDate: '2024-12-20'
        },
        {
          id: '3',
          title: 'المزايد النشط',
          description: 'شارك في 50 مزاد',
          icon: '⚡',
          earned: false,
          progress: 45,
          target: 50
        },
        {
          id: '4',
          title: 'الخبير',
          description: 'اربح 10 مزادات',
          icon: '🎖️',
          earned: false,
          progress: 8,
          target: 10
        }
      ])
    } finally {
      setLoading(false)
    }
  }

  const formatCurrency = (amount: number) => {
    return formatAmount(amount)
  }

  const getTimeRangeText = (range: string) => {
    switch (range) {
      case '7d': return 'آخر 7 أيام'
      case '30d': return 'آخر 30 يوم'
      case '90d': return 'آخر 3 أشهر'
      case '1y': return 'آخر سنة'
      default: return 'آخر 30 يوم'
    }
  }

  if (loading) {
    return (
      <div className="container mx-auto p-6">
        <div className="animate-pulse space-y-4">
          <div className="h-8 bg-gray-200 rounded w-1/4"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {[1, 2, 3, 4].map(i => (
              <div key={i} className="h-32 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto p-6 max-w-6xl">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-3xl font-bold">إحصائياتي</h1>
          <p className="text-gray-600 mt-2">تتبع أداءك ونشاطك على المنصة</p>
        </div>
        
        <div className="flex items-center gap-4">
          <CurrencySelector />
          <Select value={timeRange} onValueChange={setTimeRange}>
            <SelectTrigger className="w-40">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7d">آخر 7 أيام</SelectItem>
              <SelectItem value="30d">آخر 30 يوم</SelectItem>
              <SelectItem value="90d">آخر 3 أشهر</SelectItem>
              <SelectItem value="1y">آخر سنة</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Key Metrics */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">إجمالي المزايدات</p>
                  <p className="text-2xl font-bold">{stats.totalBids}</p>
                </div>
                <Target className="h-8 w-8 text-blue-600" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">المزادات المربوحة</p>
                  <p className="text-2xl font-bold">{stats.wonAuctions}</p>
                </div>
                <Award className="h-8 w-8 text-green-600" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">إجمالي الإنفاق</p>
                  <p className="text-2xl font-bold">{formatCurrency(stats.totalSpent)}</p>
                </div>
                <DollarSign className="h-8 w-8 text-purple-600" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">معدل النجاح</p>
                  <p className="text-2xl font-bold">{stats.successRate}%</p>
                </div>
                <TrendingUp className="h-8 w-8 text-orange-600" />
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Performance Overview */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
          <Card>
            <CardHeader>
              <CardTitle>نظرة عامة على الأداء</CardTitle>
              <CardDescription>{getTimeRangeText(timeRange)}</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <div className="flex justify-between mb-2">
                  <span className="text-sm font-medium">معدل النجاح</span>
                  <span className="text-sm text-gray-600">{stats.successRate}%</span>
                </div>
                <Progress value={stats.successRate} className="h-2" />
              </div>
              
              <div>
                <div className="flex justify-between mb-2">
                  <span className="text-sm font-medium">متوسط المزايدة</span>
                  <span className="text-sm text-gray-600">{formatCurrency(stats.averageBidAmount)}</span>
                </div>
              </div>
              
              <div>
                <div className="flex justify-between mb-2">
                  <span className="text-sm font-medium">التطبيقات النشطة</span>
                  <span className="text-sm text-gray-600">{stats.activeApplications}</span>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader>
              <CardTitle>ترتيبك</CardTitle>
              <CardDescription>موقعك بين جميع المستخدمين</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center">
                <div className="text-4xl font-bold text-blue-600 mb-2">#{stats.rank}</div>
                <p className="text-gray-600">من أصل {stats.totalUsers.toLocaleString()} مستخدم</p>
                <div className="mt-4">
                  <Badge variant="secondary" className="text-sm">
                    <Users className="h-3 w-3 mr-1" />
                    أفضل {Math.round((1 - stats.rank / stats.totalUsers) * 100)}%
                  </Badge>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Activity Chart */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle>النشاط اليومي</CardTitle>
          <CardDescription>تتبع نشاطك اليومي على المنصة</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {activities.map((activity, index) => (
              <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                <div className="flex items-center gap-3">
                  <Calendar className="h-4 w-4 text-gray-500" />
                  <span className="font-medium">
                    {new Date(activity.date).toLocaleDateString('ar-SA')}
                  </span>
                </div>
                <div className="flex items-center gap-6 text-sm">
                  <div className="flex items-center gap-1">
                    <Target className="h-4 w-4 text-blue-600" />
                    <span>{activity.bids} مزايدة</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <Activity className="h-4 w-4 text-green-600" />
                    <span>{activity.applications} تطبيق</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <DollarSign className="h-4 w-4 text-purple-600" />
                    <span>{formatCurrency(activity.spent)}</span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Achievements */}
      <Card>
        <CardHeader>
          <CardTitle>الإنجازات</CardTitle>
          <CardDescription>تتبع إنجازاتك وأهدافك</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {achievements.map((achievement) => (
              <div 
                key={achievement.id} 
                className={`p-4 border rounded-lg ${
                  achievement.earned ? 'bg-green-50 border-green-200' : 'bg-gray-50'
                }`}
              >
                <div className="flex items-start gap-3">
                  <div className="text-2xl">{achievement.icon}</div>
                  <div className="flex-1">
                    <h3 className="font-semibold mb-1">{achievement.title}</h3>
                    <p className="text-sm text-gray-600 mb-2">{achievement.description}</p>
                    
                    {achievement.earned ? (
                      <Badge variant="default" className="text-xs">
                        تم الإنجاز في {new Date(achievement.earnedDate!).toLocaleDateString('ar-SA')}
                      </Badge>
                    ) : achievement.progress !== undefined && achievement.target !== undefined ? (
                      <div className="space-y-1">
                        <div className="flex justify-between text-xs">
                          <span>التقدم</span>
                          <span>{achievement.progress}/{achievement.target}</span>
                        </div>
                        <Progress 
                          value={(achievement.progress / achievement.target) * 100} 
                          className="h-1"
                        />
                      </div>
                    ) : (
                      <Badge variant="secondary" className="text-xs">
                        لم يتم الإنجاز بعد
                      </Badge>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
