'use client'

import React, { useEffect, useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useToast } from '@/components/ui/use-toast';

import { useRouter } from 'next/navigation';
import api from '@/lib/api';
import { useCurrency } from '@/contexts/CurrencyContext';
import {
  Eye,
  Search,
  Filter,
  RefreshCw,
  TrendingUp,
  TrendingDown,
  Activity,
  Trophy,
  Clock,
  DollarSign,
  BarChart3,
  PieC<PERSON>
} from 'lucide-react';
import {
  <PERSON><PERSON><PERSON>,
  Line,
  AreaChart,
  Area,
  BarC<PERSON>,
  Bar,
  <PERSON><PERSON><PERSON> as Recharts<PERSON>ie<PERSON>hart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer
} from 'recharts';

interface Bid {
  _id: string;
  amount: number;
  placedAt: string;
  isWinning: boolean;
  auction: {
    _id: string;
    title: string;
    currentBid: number;
    endTime: string;
    status: string;
  };
}

export default function UserBidsPage() {
  const [bids, setBids] = useState<Bid[]>([]);
  const [filteredBids, setFilteredBids] = useState<Bid[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [chartData, setChartData] = useState<any>({
    bidTrends: [],
    statusDistribution: [],
    monthlyActivity: []
  });
  const [stats, setStats] = useState({
    totalBids: 0,
    activeBids: 0,
    wonBids: 0,
    totalSpent: 0,
    averageBid: 0,
    successRate: 0
  });

  const { toast } = useToast();
  const router = useRouter();

  // Chart colors
  const COLORS = ['#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6'];

  useEffect(() => {
    loadBidsData();
  }, []);

  useEffect(() => {
    filterBids();
  }, [bids, searchTerm, statusFilter]);

  const loadBidsData = async () => {
    try {
      setLoading(true);

      // Load user bids
      const bidsResponse = await api.get('/users/bids?limit=100');
      if (bidsResponse.data.success) {
        setBids(bidsResponse.data.data.bids);
        calculateStats(bidsResponse.data.data.bids);
        generateChartData(bidsResponse.data.data.bids);
      }
    } catch (error) {
      console.error('Error loading bids:', error);
      toast({
        title: 'خطأ في التحميل',
        description: 'حدث خطأ في تحميل بيانات المزايدات',
        variant: 'destructive'
      });
    } finally {
      setLoading(false);
    }
  };

  const calculateStats = (bidsData: Bid[]) => {
    const totalBids = bidsData.length;
    const activeBids = bidsData.filter(bid => bid.auction.status === 'active').length;
    const wonBids = bidsData.filter(bid =>
      bid.auction.status === 'completed' && bid.isWinning
    ).length;
    const totalSpent = bidsData
      .filter(bid => bid.auction.status === 'completed' && bid.isWinning)
      .reduce((sum, bid) => sum + bid.amount, 0);
    const averageBid = totalBids > 0 ? bidsData.reduce((sum, bid) => sum + bid.amount, 0) / totalBids : 0;
    const successRate = totalBids > 0 ? (wonBids / totalBids) * 100 : 0;

    setStats({
      totalBids,
      activeBids,
      wonBids,
      totalSpent,
      averageBid,
      successRate
    });
  };

  const generateChartData = (bidsData: Bid[]) => {
    // Generate bid trends over time
    const bidTrends = [];
    const monthNames = ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو'];

    for (let i = 5; i >= 0; i--) {
      const monthDate = new Date();
      monthDate.setMonth(monthDate.getMonth() - i);
      const monthStart = new Date(monthDate.getFullYear(), monthDate.getMonth(), 1);
      const monthEnd = new Date(monthDate.getFullYear(), monthDate.getMonth() + 1, 0);

      const monthBids = bidsData.filter(bid => {
        const bidDate = new Date(bid.placedAt);
        return bidDate >= monthStart && bidDate <= monthEnd;
      });

      const wins = monthBids.filter(bid =>
        bid.auction.status === 'completed' && bid.isWinning
      ).length;

      bidTrends.push({
        month: monthNames[monthDate.getMonth()],
        bids: monthBids.length,
        wins,
        amount: monthBids.reduce((sum, bid) => sum + bid.amount, 0)
      });
    }

    // Generate status distribution
    const statusCounts = {
      active: bidsData.filter(bid => bid.auction.status === 'active' && bid.isWinning).length,
      outbid: bidsData.filter(bid => bid.auction.status === 'active' && !bid.isWinning).length,
      won: bidsData.filter(bid => bid.auction.status === 'completed' && bid.isWinning).length,
      lost: bidsData.filter(bid => bid.auction.status === 'completed' && !bid.isWinning).length
    };

    const statusDistribution = [
      { name: 'متقدم', value: statusCounts.active, color: COLORS[0] },
      { name: 'متجاوز', value: statusCounts.outbid, color: COLORS[2] },
      { name: 'فائز', value: statusCounts.won, color: COLORS[1] },
      { name: 'خاسر', value: statusCounts.lost, color: COLORS[3] }
    ].filter(item => item.value > 0);

    setChartData({
      bidTrends,
      statusDistribution,
      monthlyActivity: bidTrends
    });
  };

  const filterBids = () => {
    let filtered = bids;

    if (searchTerm) {
      filtered = filtered.filter(bid =>
        bid.auction.title.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    if (statusFilter !== 'all') {
      filtered = filtered.filter(bid => {
        switch (statusFilter) {
          case 'active':
            return bid.auction.status === 'active' && bid.isWinning;
          case 'outbid':
            return bid.auction.status === 'active' && !bid.isWinning;
          case 'won':
            return bid.auction.status === 'completed' && bid.isWinning;
          case 'lost':
            return bid.auction.status === 'completed' && !bid.isWinning;
          default:
            return true;
        }
      });
    }

    setFilteredBids(filtered);
  };

  const getStatusBadge = (bid: Bid) => {
    if (bid.auction.status === 'active') {
      return bid.isWinning ?
        <Badge className="bg-blue-100 text-blue-800">متقدم</Badge> :
        <Badge variant="secondary">متجاوز</Badge>;
    } else if (bid.auction.status === 'completed') {
      return bid.isWinning ?
        <Badge className="bg-green-100 text-green-800">فائز</Badge> :
        <Badge variant="destructive">خاسر</Badge>;
    }
    return <Badge variant="outline">منتهي</Badge>;
  };

  const { formatAmount } = useCurrency();

  const formatPrice = (price: number) => {
    return formatAmount(price);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('ar-SA');
  };

  const formatTimeRemaining = (endTime: string) => {
    const now = new Date();
    const end = new Date(endTime);
    const diff = end.getTime() - now.getTime();

    if (diff <= 0) return 'انتهى';

    const days = Math.floor(diff / (1000 * 60 * 60 * 24));
    const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));

    if (days > 0) return `${days} يوم`;
    if (hours > 0) return `${hours} ساعة`;
    return 'أقل من ساعة';
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">جاري تحميل المزايدات...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
        {/* Header */}
        <header className="bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg p-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold">مزايداتي</h1>
              <p className="text-blue-100 mt-1">تتبع جميع مزايداتك وأدائك في المزادات</p>
            </div>
            <div className="flex items-center gap-4">
              {/* Currency selector moved to profile page */}
              <Button
                onClick={loadBidsData}
                variant="outline"
                className="bg-white/10 border-white/20 text-white hover:bg-white/20"
              >
                <RefreshCw className="h-4 w-4 ml-2" />
                تحديث
              </Button>
            </div>
          </div>
        </header>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card className="bg-gradient-to-br from-blue-50 to-blue-100 border-blue-200">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-blue-600">إجمالي المزايدات</p>
                  <p className="text-3xl font-bold text-blue-900">{stats.totalBids}</p>
                </div>
                <Activity className="h-8 w-8 text-blue-600" />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-br from-green-50 to-green-100 border-green-200">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-green-600">المزايدات الفائزة</p>
                  <p className="text-3xl font-bold text-green-900">{stats.wonBids}</p>
                </div>
                <Trophy className="h-8 w-8 text-green-600" />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-br from-purple-50 to-purple-100 border-purple-200">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-purple-600">إجمالي الإنفاق</p>
                  <p className="text-3xl font-bold text-purple-900">{formatPrice(stats.totalSpent)}</p>
                </div>
                <DollarSign className="h-8 w-8 text-purple-600" />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-br from-orange-50 to-orange-100 border-orange-200">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-orange-600">معدل النجاح</p>
                  <p className="text-3xl font-bold text-orange-900">{stats.successRate.toFixed(1)}%</p>
                </div>
                <TrendingUp className="h-8 w-8 text-orange-600" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Charts */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Bid Trends Chart */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="h-5 w-5 text-blue-600" />
                اتجاه المزايدات الشهري
              </CardTitle>
              <CardDescription>
                عدد المزايدات والانتصارات خلال الأشهر الماضية
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <AreaChart data={chartData.bidTrends}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="month" />
                  <YAxis />
                  <Tooltip />
                  <Legend />
                  <Area
                    type="monotone"
                    dataKey="bids"
                    stackId="1"
                    stroke="#3B82F6"
                    fill="#3B82F6"
                    fillOpacity={0.6}
                    name="المزايدات"
                  />
                  <Area
                    type="monotone"
                    dataKey="wins"
                    stackId="2"
                    stroke="#10B981"
                    fill="#10B981"
                    fillOpacity={0.8}
                    name="الانتصارات"
                  />
                </AreaChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>

          {/* Status Distribution Chart */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <PieChart className="h-5 w-5 text-purple-600" />
                توزيع حالة المزايدات
              </CardTitle>
              <CardDescription>
                نسبة المزايدات حسب الحالة
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <RechartsPieChart>
                  <Pie
                    data={chartData.statusDistribution}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="value"
                  >
                    {chartData.statusDistribution.map((entry: any, index: number) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip />
                </RechartsPieChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </div>

        {/* Filters */}
        <Card>
          <CardHeader>
            <CardTitle>البحث والتصفية</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder="البحث في المزايدات..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pr-10"
                  />
                </div>
              </div>
              <div className="w-full md:w-48">
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger>
                    <SelectValue placeholder="تصفية حسب الحالة" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">جميع الحالات</SelectItem>
                    <SelectItem value="active">متقدم</SelectItem>
                    <SelectItem value="outbid">متجاوز</SelectItem>
                    <SelectItem value="won">فائز</SelectItem>
                    <SelectItem value="lost">خاسر</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Bids Table */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span>قائمة المزايدات ({filteredBids.length})</span>
              <Badge variant="outline">{filteredBids.length} من {bids.length}</Badge>
            </CardTitle>
            <CardDescription>
              جميع مزايداتك مع تفاصيل الحالة والمبالغ
            </CardDescription>
          </CardHeader>
          <CardContent>
            {filteredBids.length > 0 ? (
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>عنوان المزاد</TableHead>
                      <TableHead>الحالة</TableHead>
                      <TableHead>مزايدتي</TableHead>
                      <TableHead>المزايدة الحالية</TableHead>
                      <TableHead>الوقت المتبقي</TableHead>
                      <TableHead>تاريخ المزايدة</TableHead>
                      <TableHead>الإجراءات</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredBids.map((bid) => (
                      <TableRow key={bid._id} className="hover:bg-gray-50">
                        <TableCell className="font-medium">
                          <div className="max-w-xs truncate">
                            {bid.auction.title}
                          </div>
                        </TableCell>
                        <TableCell>{getStatusBadge(bid)}</TableCell>
                        <TableCell className="font-medium">
                          {formatPrice(bid.amount)}
                        </TableCell>
                        <TableCell>
                          {formatPrice(bid.auction.currentBid)}
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-1">
                            <Clock className="h-4 w-4 text-gray-400" />
                            {formatTimeRemaining(bid.auction.endTime)}
                          </div>
                        </TableCell>
                        <TableCell className="text-gray-600">
                          {formatDate(bid.placedAt)}
                        </TableCell>
                        <TableCell>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => router.push(`/auctions/${bid.auction._id}`)}
                          >
                            <Eye className="h-4 w-4 ml-1" />
                            عرض
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            ) : (
              <div className="text-center py-12">
                <Activity className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-xl font-medium text-gray-900 mb-2">
                  {searchTerm || statusFilter !== 'all' ? 'لا توجد نتائج' : 'لا توجد مزايدات'}
                </h3>
                <p className="text-gray-600 mb-6">
                  {searchTerm || statusFilter !== 'all'
                    ? 'جرب تغيير معايير البحث أو التصفية'
                    : 'ابدأ بالمشاركة في المزادات المتاحة'
                  }
                </p>
                {!searchTerm && statusFilter === 'all' && (
                  <Button onClick={() => router.push('/user/auctions')}>
                    استكشاف المزادات
                  </Button>
                )}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
  );
}
