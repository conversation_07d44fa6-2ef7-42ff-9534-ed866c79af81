'use client'

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { useToast } from '@/components/ui/use-toast'

import { useRouter } from 'next/navigation'
import api from '@/lib/api'
import { 
  FileText, 
  Clock, 
  DollarSign, 
  Eye, 
  Edit,
  Trash2,
  Calendar,
  Search,
  Filter,
  RefreshCw,
  Plus,
  AlertCircle,
  CheckCircle,
  XCircle,
  BarChart3,
  Send,
  Building,
  Star
} from 'lucide-react'

interface Application {
  _id: string
  tender: {
    _id: string
    title: string
    budget: number
    deadline: string
    organizer: {
      profile: {
        governmentEntity?: string
      }
    }
  }
  submittedAt: string
  status: 'pending' | 'approved' | 'rejected' | 'review'
  proposedBudget?: number
  timeline?: string
  score?: number
  reviewNotes?: string
}

export default function UserApplicationsPage() {
  const [applications, setApplications] = useState<Application[]>([])
  const [filteredApplications, setFilteredApplications] = useState<Application[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState('all')
  const [stats, setStats] = useState({
    total: 0,
    pending: 0,
    approved: 0,
    rejected: 0
  })
  const { toast } = useToast()
  const router = useRouter()

  useEffect(() => {
    loadApplications()
  }, [])

  useEffect(() => {
    filterApplications()
  }, [applications, searchTerm, statusFilter])

  const loadApplications = async () => {
    try {
      setLoading(true)
      const response = await api.get('/users/applications')
      
      if (response.data.success) {
        setApplications(response.data.data.applications)
        calculateStats(response.data.data.applications)
      } else {
        setApplications([])
        calculateStats([])
      }
    } catch (error) {
      console.error('Error loading applications:', error)
      toast({
        title: 'خطأ في التحميل',
        description: 'حدث خطأ في تحميل بيانات الطلبات',
        variant: 'destructive'
      })
    } finally {
      setLoading(false)
    }
  }

  const calculateStats = (applicationsData: Application[]) => {
    const total = applicationsData.length
    const pending = applicationsData.filter(app => app.status === 'pending').length
    const approved = applicationsData.filter(app => app.status === 'approved').length
    const rejected = applicationsData.filter(app => app.status === 'rejected').length

    setStats({ total, pending, approved, rejected })
  }

  const filterApplications = () => {
    let filtered = applications

    if (searchTerm) {
      filtered = filtered.filter(app =>
        app.tender.title.toLowerCase().includes(searchTerm.toLowerCase())
      )
    }

    if (statusFilter !== 'all') {
      filtered = filtered.filter(app => app.status === statusFilter)
    }

    setFilteredApplications(filtered)
  }

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('ar-SA', {
      style: 'currency',
      currency: 'SAR',
      minimumFractionDigits: 0
    }).format(price)
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('ar-SA')
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'approved':
        return <Badge className="bg-green-100 text-green-800">موافق عليه</Badge>
      case 'rejected':
        return <Badge variant="destructive">مرفوض</Badge>
      case 'pending':
        return <Badge className="bg-yellow-100 text-yellow-800">في الانتظار</Badge>
      case 'review':
        return <Badge className="bg-blue-100 text-blue-800">قيد المراجعة</Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  const withdrawApplication = async (applicationId: string) => {
    if (!confirm('هل أنت متأكد من سحب هذا الطلب؟')) return

    try {
      const response = await api.delete(`/applications/${applicationId}`)
      
      if (response.data.success) {
        setApplications(applications.filter(app => app._id !== applicationId))
        toast({
          title: 'تم السحب',
          description: 'تم سحب الطلب بنجاح'
        })
      }
    } catch (error: any) {
      toast({
        title: 'خطأ في السحب',
        description: error.response?.data?.message || 'حدث خطأ في سحب الطلب',
        variant: 'destructive'
      })
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">جاري تحميل طلباتك...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
        {/* Header */}
        <div className="bg-gradient-to-r from-green-600 to-emerald-600 text-white rounded-lg p-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold">طلبات المناقصات</h1>
              <p className="text-green-100 mt-1">إدارة ومتابعة جميع طلباتك في المناقصات الحكومية</p>
            </div>
            <div className="flex items-center gap-4">
              <Button 
                onClick={loadApplications}
                variant="outline"
                className="bg-white/10 border-white/20 text-white hover:bg-white/20"
              >
                <RefreshCw className="h-4 w-4 ml-2" />
                تحديث
              </Button>
              <Button 
                onClick={() => router.push('/tenders')}
                className="bg-white text-green-600 hover:bg-gray-100"
              >
                <Plus className="h-4 w-4 ml-2" />
                تصفح المناقصات
              </Button>
            </div>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card className="bg-gradient-to-br from-blue-50 to-blue-100 border-blue-200">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-blue-600">إجمالي الطلبات</p>
                  <p className="text-3xl font-bold text-blue-900">{stats.total}</p>
                </div>
                <FileText className="h-8 w-8 text-blue-600" />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-br from-yellow-50 to-yellow-100 border-yellow-200">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-yellow-600">في الانتظار</p>
                  <p className="text-3xl font-bold text-yellow-900">{stats.pending}</p>
                </div>
                <Clock className="h-8 w-8 text-yellow-600" />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-br from-green-50 to-green-100 border-green-200">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-green-600">موافق عليها</p>
                  <p className="text-3xl font-bold text-green-900">{stats.approved}</p>
                </div>
                <CheckCircle className="h-8 w-8 text-green-600" />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-br from-red-50 to-red-100 border-red-200">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-red-600">مرفوضة</p>
                  <p className="text-3xl font-bold text-red-900">{stats.rejected}</p>
                </div>
                <XCircle className="h-8 w-8 text-red-600" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Filters */}
        <Card>
          <CardContent className="p-6">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder="البحث في الطلبات..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pr-10"
                  />
                </div>
              </div>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-full md:w-48">
                  <SelectValue placeholder="فلترة حسب الحالة" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">جميع الحالات</SelectItem>
                  <SelectItem value="pending">في الانتظار</SelectItem>
                  <SelectItem value="approved">موافق عليها</SelectItem>
                  <SelectItem value="rejected">مرفوضة</SelectItem>
                  <SelectItem value="review">قيد المراجعة</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>

        {/* Applications Table */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="h-5 w-5 text-green-600" />
              قائمة الطلبات
            </CardTitle>
            <CardDescription>
              جميع طلباتك في المناقصات الحكومية ({filteredApplications.length} من {applications.length})
            </CardDescription>
          </CardHeader>
          <CardContent>
            {filteredApplications.length > 0 ? (
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="w-[300px]">تفاصيل المناقصة</TableHead>
                      <TableHead>الميزانية المقترحة</TableHead>
                      <TableHead>الجدول الزمني</TableHead>
                      <TableHead>الحالة</TableHead>
                      <TableHead>التقييم</TableHead>
                      <TableHead className="text-center">الإجراءات</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredApplications.map((application) => (
                      <TableRow key={application._id} className="hover:bg-gray-50">
                        <TableCell>
                          <div className="space-y-1">
                            <h4 className="font-medium text-gray-900">{application.tender.title}</h4>
                            <div className="flex items-center gap-2 text-xs text-gray-500">
                              <Building className="h-3 w-3" />
                              {application.tender.organizer.profile.governmentEntity}
                            </div>
                            <div className="flex items-center gap-2 text-xs text-gray-500">
                              <Calendar className="h-3 w-3" />
                              تقدمت في: {formatDate(application.submittedAt)}
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="space-y-1">
                            <div className="font-medium text-green-600">
                              {application.proposedBudget ? formatPrice(application.proposedBudget) : '-'}
                            </div>
                            <div className="text-xs text-gray-500">
                              من أصل {formatPrice(application.tender.budget)}
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <span className="text-sm">{application.timeline || '-'}</span>
                        </TableCell>
                        <TableCell>
                          {getStatusBadge(application.status)}
                        </TableCell>
                        <TableCell>
                          {application.score ? (
                            <div className="flex items-center gap-1">
                              <Star className="h-4 w-4 text-yellow-500" />
                              <span className="font-medium">{application.score}/100</span>
                            </div>
                          ) : (
                            <span className="text-gray-400">-</span>
                          )}
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center justify-center gap-2">
                            <Button 
                              variant="outline" 
                              size="sm"
                              onClick={() => router.push(`/tenders/${application.tender._id}`)}
                            >
                              <Eye className="h-4 w-4 ml-1" />
                              عرض
                            </Button>
                            {application.status === 'pending' && (
                              <Button 
                                variant="destructive" 
                                size="sm"
                                onClick={() => withdrawApplication(application._id)}
                              >
                                <Trash2 className="h-4 w-4 ml-1" />
                                سحب
                              </Button>
                            )}
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            ) : (
              <div className="text-center py-12">
                <FileText className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  {searchTerm || statusFilter !== 'all' 
                    ? 'لا توجد طلبات تطابق البحث' 
                    : 'لا توجد طلبات'
                  }
                </h3>
                <p className="text-gray-600 mb-6">
                  {searchTerm || statusFilter !== 'all'
                    ? 'جرب تغيير معايير البحث أو الفلترة'
                    : 'ابدأ بتصفح المناقصات وتقديم طلباتك'
                  }
                </p>
                {(!searchTerm && statusFilter === 'all') && (
                  <Button onClick={() => router.push('/tenders')}>
                    <Plus className="h-4 w-4 ml-2" />
                    تصفح المناقصات
                  </Button>
                )}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Review Notes for Reviewed Applications */}
        {filteredApplications.some(app => app.reviewNotes) && (
          <Card>
            <CardHeader>
              <CardTitle>ملاحظات المراجعة</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {filteredApplications
                  .filter(app => app.reviewNotes)
                  .map((application) => (
                    <div key={application._id} className="p-4 bg-gray-50 rounded-lg">
                      <div className="flex items-center justify-between mb-2">
                        <h4 className="font-medium">{application.tender.title}</h4>
                        {getStatusBadge(application.status)}
                      </div>
                      <p className="text-gray-700 text-sm">{application.reviewNotes}</p>
                      {application.score && (
                        <div className="flex items-center gap-1 mt-2">
                          <Star className="h-4 w-4 text-yellow-500" />
                          <span className="text-sm font-medium">التقييم: {application.score}/100</span>
                        </div>
                      )}
                    </div>
                  ))}
              </div>
            </CardContent>
          </Card>
        )}
      </div>
  )
}
