'use client'

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react'
import { currencyService } from '@/lib/currencyService'

interface CurrencyContextType {
  userCurrency: string
  setUserCurrency: (currency: string) => void
  formatAmount: (amount: number, currency?: string) => string
  convertAmount: (amount: number, fromCurrency: string, toCurrency?: string) => Promise<number>
  exchangeRates: { [key: string]: number }
  isLoading: boolean
}

const CurrencyContext = createContext<CurrencyContextType | undefined>(undefined)

export function CurrencyProvider({ children }: { children: ReactNode }) {
  const [userCurrency, setUserCurrencyState] = useState<string>('SAR')
  const [exchangeRates, setExchangeRates] = useState<{ [key: string]: number }>({})
  const [isLoading, setIsLoading] = useState(true)

  // Initialize currency and exchange rates
  useEffect(() => {
    const initializeCurrency = async () => {
      try {
        // Load saved currency
        const savedCurrency = currencyService.getUserCurrency()
        setUserCurrencyState(savedCurrency)

        // Load exchange rates
        await currencyService.updateExchangeRates()
        setExchangeRates(currencyService.getExchangeRates())
      } catch (error) {
        console.error('Failed to initialize currency:', error)
      } finally {
        setIsLoading(false)
      }
    }

    initializeCurrency()
  }, [])

  // Update currency and save to localStorage
  const setUserCurrency = async (currency: string) => {
    try {
      setUserCurrencyState(currency)
      currencyService.setUserCurrency(currency)
      
      // Update exchange rates if needed
      await currencyService.updateExchangeRates()
      setExchangeRates(currencyService.getExchangeRates())
    } catch (error) {
      console.error('Failed to update currency:', error)
    }
  }

  // Format amount with currency
  const formatAmount = (amount: number, currency?: string) => {
    return currencyService.formatAmount(amount, currency || userCurrency)
  }

  // Convert amount between currencies
  const convertAmount = async (amount: number, fromCurrency: string, toCurrency?: string) => {
    return currencyService.convertCurrency(amount, fromCurrency, toCurrency || userCurrency)
  }

  const value: CurrencyContextType = {
    userCurrency,
    setUserCurrency,
    formatAmount,
    convertAmount,
    exchangeRates,
    isLoading
  }

  return (
    <CurrencyContext.Provider value={value}>
      {children}
    </CurrencyContext.Provider>
  )
}

export function useCurrency() {
  const context = useContext(CurrencyContext)
  if (context === undefined) {
    throw new Error('useCurrency must be used within a CurrencyProvider')
  }
  return context
}
