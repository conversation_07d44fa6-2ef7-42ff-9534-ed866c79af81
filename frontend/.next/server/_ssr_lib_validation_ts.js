"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_ssr_lib_validation_ts";
exports.ids = ["_ssr_lib_validation_ts"];
exports.modules = {

/***/ "(ssr)/./lib/validation.ts":
/*!***************************!*\
  !*** ./lib/validation.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ValidationError: () => (/* binding */ ValidationError),\n/* harmony export */   checkRateLimit: () => (/* binding */ checkRateLimit),\n/* harmony export */   preventRapidSubmission: () => (/* binding */ preventRapidSubmission),\n/* harmony export */   sanitizeInput: () => (/* binding */ sanitizeInput),\n/* harmony export */   validateAmount: () => (/* binding */ validateAmount),\n/* harmony export */   validateBidAmount: () => (/* binding */ validateBidAmount),\n/* harmony export */   validateCommercialRegister: () => (/* binding */ validateCommercialRegister),\n/* harmony export */   validateDateRange: () => (/* binding */ validateDateRange),\n/* harmony export */   validateEmail: () => (/* binding */ validateEmail),\n/* harmony export */   validateFile: () => (/* binding */ validateFile),\n/* harmony export */   validateFormData: () => (/* binding */ validateFormData),\n/* harmony export */   validateNationalId: () => (/* binding */ validateNationalId),\n/* harmony export */   validatePhone: () => (/* binding */ validatePhone),\n/* harmony export */   validateUserPermissions: () => (/* binding */ validateUserPermissions)\n/* harmony export */ });\n// Comprehensive validation utilities to prevent user bypass attempts\nclass ValidationError extends Error {\n    constructor(message, field){\n        super(message);\n        this.field = field;\n        this.name = \"ValidationError\";\n    }\n}\n// Sanitize input to prevent XSS and injection attacks\nconst sanitizeInput = (input)=>{\n    if (typeof input !== \"string\") return \"\";\n    return input.trim().replace(/<script\\b[^<]*(?:(?!<\\/script>)<[^<]*)*<\\/script>/gi, \"\").replace(/javascript:/gi, \"\").replace(/on\\w+\\s*=/gi, \"\").replace(/[<>]/g, \"\");\n};\n// Validate email format\nconst validateEmail = (email)=>{\n    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n    return emailRegex.test(email.trim().toLowerCase());\n};\n// Validate phone number (Saudi format)\nconst validatePhone = (phone)=>{\n    const phoneRegex = /^(\\+966|966|0)?[5][0-9]{8}$/;\n    return phoneRegex.test(phone.replace(/\\s/g, \"\"));\n};\n// Validate national ID (Saudi format)\nconst validateNationalId = (id)=>{\n    const idRegex = /^[12][0-9]{9}$/;\n    return idRegex.test(id);\n};\n// Validate commercial register\nconst validateCommercialRegister = (cr)=>{\n    const crRegex = /^[0-9]{10}$/;\n    return crRegex.test(cr);\n};\n// Validate monetary amounts\nconst validateAmount = (amount, min = 0, max = Infinity)=>{\n    const numAmount = typeof amount === \"string\" ? parseFloat(amount) : amount;\n    if (isNaN(numAmount)) return false;\n    if (numAmount < min) return false;\n    if (numAmount > max) return false;\n    if (numAmount !== Math.floor(numAmount * 100) / 100) return false // Max 2 decimal places\n    ;\n    return true;\n};\n// Validate bid amount against current price\nconst validateBidAmount = (bidAmount, currentPrice, minIncrement = 1)=>{\n    const numBid = typeof bidAmount === \"string\" ? parseFloat(bidAmount) : bidAmount;\n    if (isNaN(numBid)) {\n        return {\n            isValid: false,\n            error: \"مبلغ المزايدة غير صحيح\"\n        };\n    }\n    if (numBid <= currentPrice) {\n        return {\n            isValid: false,\n            error: \"يجب أن تكون المزايدة أعلى من السعر الحالي\"\n        };\n    }\n    if (numBid < currentPrice + minIncrement) {\n        return {\n            isValid: false,\n            error: `الحد الأدنى للزيادة هو ${minIncrement}`\n        };\n    }\n    if (numBid > currentPrice * 10) {\n        return {\n            isValid: false,\n            error: \"مبلغ المزايدة مرتفع جداً\"\n        };\n    }\n    return {\n        isValid: true\n    };\n};\n// Validate date ranges\nconst validateDateRange = (startDate, endDate)=>{\n    const start = new Date(startDate);\n    const end = new Date(endDate);\n    const now = new Date();\n    if (isNaN(start.getTime()) || isNaN(end.getTime())) {\n        return {\n            isValid: false,\n            error: \"تاريخ غير صحيح\"\n        };\n    }\n    if (start >= end) {\n        return {\n            isValid: false,\n            error: \"تاريخ البداية يجب أن يكون قبل تاريخ النهاية\"\n        };\n    }\n    if (end <= now) {\n        return {\n            isValid: false,\n            error: \"تاريخ النهاية يجب أن يكون في المستقبل\"\n        };\n    }\n    return {\n        isValid: true\n    };\n};\n// Validate file uploads\nconst validateFile = (file, allowedTypes, maxSize)=>{\n    if (!file) {\n        return {\n            isValid: false,\n            error: \"لم يتم اختيار ملف\"\n        };\n    }\n    if (!allowedTypes.includes(file.type)) {\n        return {\n            isValid: false,\n            error: \"نوع الملف غير مدعوم\"\n        };\n    }\n    if (file.size > maxSize) {\n        return {\n            isValid: false,\n            error: `حجم الملف يجب أن يكون أقل من ${maxSize / 1024 / 1024}MB`\n        };\n    }\n    return {\n        isValid: true\n    };\n};\n// Validate user permissions\nconst validateUserPermissions = (userRole, requiredRoles)=>{\n    if (!userRole || !requiredRoles.length) return false;\n    return requiredRoles.includes(userRole);\n};\n// Validate form data comprehensively\nconst validateFormData = (data, rules)=>{\n    const errors = {};\n    for (const [field, rule] of Object.entries(rules)){\n        const value = data[field];\n        // Required field check\n        if (rule.required && (!value || typeof value === \"string\" && !value.trim())) {\n            errors[field] = rule.requiredMessage || `${field} مطلوب`;\n            continue;\n        }\n        // Skip validation if field is empty and not required\n        if (!value && !rule.required) continue;\n        // Type validation\n        if (rule.type === \"email\" && !validateEmail(value)) {\n            errors[field] = \"البريد الإلكتروني غير صحيح\";\n        }\n        if (rule.type === \"phone\" && !validatePhone(value)) {\n            errors[field] = \"رقم الهاتف غير صحيح\";\n        }\n        if (rule.type === \"number\" && !validateAmount(value, rule.min, rule.max)) {\n            errors[field] = \"الرقم غير صحيح\";\n        }\n        // Length validation\n        if (rule.minLength && value.length < rule.minLength) {\n            errors[field] = `يجب أن يكون ${rule.minLength} أحرف على الأقل`;\n        }\n        if (rule.maxLength && value.length > rule.maxLength) {\n            errors[field] = `يجب أن يكون ${rule.maxLength} أحرف كحد أقصى`;\n        }\n        // Custom validation\n        if (rule.custom && !rule.custom(value)) {\n            errors[field] = rule.customMessage || \"قيمة غير صحيحة\";\n        }\n    }\n    return {\n        isValid: Object.keys(errors).length === 0,\n        errors\n    };\n};\n// Rate limiting helper\nconst checkRateLimit = (key, maxRequests = 10, windowMs = 60000)=>{\n    const now = Date.now();\n    const requests = JSON.parse(localStorage.getItem(`rate_limit_${key}`) || \"[]\");\n    // Remove old requests outside the window\n    const validRequests = requests.filter((timestamp)=>now - timestamp < windowMs);\n    if (validRequests.length >= maxRequests) {\n        return false;\n    }\n    // Add current request\n    validRequests.push(now);\n    localStorage.setItem(`rate_limit_${key}`, JSON.stringify(validRequests));\n    return true;\n};\n// Prevent rapid form submissions\nconst preventRapidSubmission = (formId, cooldownMs = 2000)=>{\n    const lastSubmission = localStorage.getItem(`last_submit_${formId}`);\n    const now = Date.now();\n    if (lastSubmission && now - parseInt(lastSubmission) < cooldownMs) {\n        return false;\n    }\n    localStorage.setItem(`last_submit_${formId}`, now.toString());\n    return true;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/validation.ts\n");

/***/ })

};
;