globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/user/leaderboard/page"]={"moduleLoading":{"prefix":"/_next/","crossOrigin":null},"ssrModuleMapping":{"(app-pages-browser)/./app/auctions/[id]/page.tsx":{"*":{"id":"(ssr)/./app/auctions/[id]/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/ui/toaster.tsx":{"*":{"id":"(ssr)/./components/ui/toaster.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./contexts/AuthContext.tsx":{"*":{"id":"(ssr)/./contexts/AuthContext.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./contexts/CurrencyContext.tsx":{"*":{"id":"(ssr)/./contexts/CurrencyContext.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/error.tsx":{"*":{"id":"(ssr)/./app/error.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/not-found.tsx":{"*":{"id":"(ssr)/./app/not-found.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/user/auctions/page.tsx":{"*":{"id":"(ssr)/./app/user/auctions/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/user/layout.tsx":{"*":{"id":"(ssr)/./app/user/layout.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/user/dashboard/page.tsx":{"*":{"id":"(ssr)/./app/user/dashboard/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/user/tenders/page.tsx":{"*":{"id":"(ssr)/./app/user/tenders/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/user/my-bids/page.tsx":{"*":{"id":"(ssr)/./app/user/my-bids/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/user/bids/page.tsx":{"*":{"id":"(ssr)/./app/user/bids/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/user/profile/page.tsx":{"*":{"id":"(ssr)/./app/user/profile/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/user/analytics/page.tsx":{"*":{"id":"(ssr)/./app/user/analytics/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/user/leaderboard/page.tsx":{"*":{"id":"(ssr)/./app/user/leaderboard/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/search/page.tsx":{"*":{"id":"(ssr)/./app/search/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/user/applications/page.tsx":{"*":{"id":"(ssr)/./app/user/applications/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/user/notifications/page.tsx":{"*":{"id":"(ssr)/./app/user/notifications/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/favorites/page.tsx":{"*":{"id":"(ssr)/./app/favorites/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/payment/methods/page.tsx":{"*":{"id":"(ssr)/./app/payment/methods/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/billing/invoices/page.tsx":{"*":{"id":"(ssr)/./app/billing/invoices/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/wallet/page.tsx":{"*":{"id":"(ssr)/./app/wallet/page.tsx","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"/Users/<USER>/Desktop/brid1/frontend/app/auctions/[id]/page.tsx":{"id":"(app-pages-browser)/./app/auctions/[id]/page.tsx","name":"*","chunks":[],"async":false},"/Users/<USER>/Desktop/brid1/frontend/components/ui/toaster.tsx":{"id":"(app-pages-browser)/./components/ui/toaster.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/Users/<USER>/Desktop/brid1/frontend/contexts/AuthContext.tsx":{"id":"(app-pages-browser)/./contexts/AuthContext.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/Users/<USER>/Desktop/brid1/frontend/contexts/CurrencyContext.tsx":{"id":"(app-pages-browser)/./contexts/CurrencyContext.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/Users/<USER>/Desktop/brid1/frontend/node_modules/next/font/google/target.css?{\"path\":\"app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/Users/<USER>/Desktop/brid1/frontend/app/globals.css":{"id":"(app-pages-browser)/./app/globals.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/Users/<USER>/Desktop/brid1/frontend/app/error.tsx":{"id":"(app-pages-browser)/./app/error.tsx","name":"*","chunks":["app/error","static/chunks/app/error.js"],"async":false},"/Users/<USER>/Desktop/brid1/frontend/app/not-found.tsx":{"id":"(app-pages-browser)/./app/not-found.tsx","name":"*","chunks":["app/not-found","static/chunks/app/not-found.js"],"async":false},"/Users/<USER>/Desktop/brid1/frontend/node_modules/next/dist/client/components/app-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Desktop/brid1/frontend/node_modules/next/dist/esm/client/components/app-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Desktop/brid1/frontend/node_modules/next/dist/client/components/client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Desktop/brid1/frontend/node_modules/next/dist/esm/client/components/client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Desktop/brid1/frontend/node_modules/next/dist/client/components/error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Desktop/brid1/frontend/node_modules/next/dist/esm/client/components/error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Desktop/brid1/frontend/node_modules/next/dist/client/components/layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Desktop/brid1/frontend/node_modules/next/dist/esm/client/components/layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Desktop/brid1/frontend/node_modules/next/dist/client/components/not-found-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Desktop/brid1/frontend/node_modules/next/dist/esm/client/components/not-found-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Desktop/brid1/frontend/node_modules/next/dist/client/components/render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Desktop/brid1/frontend/node_modules/next/dist/esm/client/components/render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx":{"id":"(app-pages-browser)/./app/user/auctions/page.tsx","name":"*","chunks":[],"async":false},"/Users/<USER>/Desktop/brid1/frontend/app/user/layout.tsx":{"id":"(app-pages-browser)/./app/user/layout.tsx","name":"*","chunks":["app/user/layout","static/chunks/app/user/layout.js"],"async":false},"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx":{"id":"(app-pages-browser)/./app/user/dashboard/page.tsx","name":"*","chunks":[],"async":false},"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx":{"id":"(app-pages-browser)/./app/user/tenders/page.tsx","name":"*","chunks":[],"async":false},"/Users/<USER>/Desktop/brid1/frontend/app/user/my-bids/page.tsx":{"id":"(app-pages-browser)/./app/user/my-bids/page.tsx","name":"*","chunks":[],"async":false},"/Users/<USER>/Desktop/brid1/frontend/app/user/bids/page.tsx":{"id":"(app-pages-browser)/./app/user/bids/page.tsx","name":"*","chunks":[],"async":false},"/Users/<USER>/Desktop/brid1/frontend/app/user/profile/page.tsx":{"id":"(app-pages-browser)/./app/user/profile/page.tsx","name":"*","chunks":[],"async":false},"/Users/<USER>/Desktop/brid1/frontend/app/user/analytics/page.tsx":{"id":"(app-pages-browser)/./app/user/analytics/page.tsx","name":"*","chunks":[],"async":false},"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx":{"id":"(app-pages-browser)/./app/user/leaderboard/page.tsx","name":"*","chunks":["app/user/leaderboard/page","static/chunks/app/user/leaderboard/page.js"],"async":false},"/Users/<USER>/Desktop/brid1/frontend/app/search/page.tsx":{"id":"(app-pages-browser)/./app/search/page.tsx","name":"*","chunks":[],"async":false},"/Users/<USER>/Desktop/brid1/frontend/app/user/applications/page.tsx":{"id":"(app-pages-browser)/./app/user/applications/page.tsx","name":"*","chunks":[],"async":false},"/Users/<USER>/Desktop/brid1/frontend/app/user/notifications/page.tsx":{"id":"(app-pages-browser)/./app/user/notifications/page.tsx","name":"*","chunks":[],"async":false},"/Users/<USER>/Desktop/brid1/frontend/app/favorites/page.tsx":{"id":"(app-pages-browser)/./app/favorites/page.tsx","name":"*","chunks":[],"async":false},"/Users/<USER>/Desktop/brid1/frontend/app/payment/methods/page.tsx":{"id":"(app-pages-browser)/./app/payment/methods/page.tsx","name":"*","chunks":[],"async":false},"/Users/<USER>/Desktop/brid1/frontend/app/billing/invoices/page.tsx":{"id":"(app-pages-browser)/./app/billing/invoices/page.tsx","name":"*","chunks":[],"async":false},"/Users/<USER>/Desktop/brid1/frontend/app/wallet/page.tsx":{"id":"(app-pages-browser)/./app/wallet/page.tsx","name":"*","chunks":[],"async":false}},"entryCSSFiles":{"/Users/<USER>/Desktop/brid1/frontend/":[],"/Users/<USER>/Desktop/brid1/frontend/app/layout":["static/css/app/layout.css"],"/Users/<USER>/Desktop/brid1/frontend/app/error":[],"/Users/<USER>/Desktop/brid1/frontend/app/loading":[],"/Users/<USER>/Desktop/brid1/frontend/app/not-found":[],"/Users/<USER>/Desktop/brid1/frontend/app/user/layout":[],"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page":[]}}