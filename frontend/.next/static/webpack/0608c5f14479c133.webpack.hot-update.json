{"c": ["app/layout", "app/user/dashboard/page", "webpack"], "r": [], "m": ["(app-pages-browser)/./app/user/dashboard/page.tsx", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/line-chart.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js", "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Ffahad%2FDesktop%2Fbrid1%2Ffrontend%2Fapp%2Fuser%2Fdashboard%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!", "(app-pages-browser)/./node_modules/recharts/es6/cartesian/Bar.js", "(app-pages-browser)/./node_modules/recharts/es6/cartesian/ErrorBar.js", "(app-pages-browser)/./node_modules/recharts/es6/chart/BarChart.js", "(app-pages-browser)/./node_modules/recharts/es6/state/ReportBar.js", "(app-pages-browser)/./node_modules/recharts/es6/state/selectors/barSelectors.js", "(app-pages-browser)/./node_modules/recharts/es6/util/BarUtils.js", "(app-pages-browser)/./node_modules/tiny-invariant/dist/esm/tiny-invariant.js"]}