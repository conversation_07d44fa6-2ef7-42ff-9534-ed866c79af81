{"c": ["app/layout", "app/user/leaderboard/page", "webpack"], "r": [], "m": ["(app-pages-browser)/./app/user/leaderboard/page.tsx", "(app-pages-browser)/./components/ui/avatar.tsx", "(app-pages-browser)/./node_modules/@radix-ui/react-avatar/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-use-is-hydrated/dist/index.mjs", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/award.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/medal.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js", "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Ffahad%2FDesktop%2Fbrid1%2Ffrontend%2Fapp%2Fuser%2Fleaderboard%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!"]}