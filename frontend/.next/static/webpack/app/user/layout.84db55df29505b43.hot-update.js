"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/user/layout",{

/***/ "(app-pages-browser)/./components/Sidebar.tsx":
/*!********************************!*\
  !*** ./components/Sidebar.tsx ***!
  \********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Sidebar; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_Heart_LayoutDashboard_LogOut_PlusCircle_Search_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Bell,Building,ChevronLeft,ChevronRight,Crown,FileText,Gavel,Heart,LayoutDashboard,LogOut,PlusCircle,Search,Settings,Shield,TrendingUp,Trophy,User,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/layout-dashboard.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_Heart_LayoutDashboard_LogOut_PlusCircle_Search_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Bell,Building,ChevronLeft,ChevronRight,Crown,FileText,Gavel,Heart,LayoutDashboard,LogOut,PlusCircle,Search,Settings,Shield,TrendingUp,Trophy,User,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_Heart_LayoutDashboard_LogOut_PlusCircle_Search_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Bell,Building,ChevronLeft,ChevronRight,Crown,FileText,Gavel,Heart,LayoutDashboard,LogOut,PlusCircle,Search,Settings,Shield,TrendingUp,Trophy,User,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_Heart_LayoutDashboard_LogOut_PlusCircle_Search_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Bell,Building,ChevronLeft,ChevronRight,Crown,FileText,Gavel,Heart,LayoutDashboard,LogOut,PlusCircle,Search,Settings,Shield,TrendingUp,Trophy,User,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_Heart_LayoutDashboard_LogOut_PlusCircle_Search_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Bell,Building,ChevronLeft,ChevronRight,Crown,FileText,Gavel,Heart,LayoutDashboard,LogOut,PlusCircle,Search,Settings,Shield,TrendingUp,Trophy,User,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/gavel.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_Heart_LayoutDashboard_LogOut_PlusCircle_Search_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Bell,Building,ChevronLeft,ChevronRight,Crown,FileText,Gavel,Heart,LayoutDashboard,LogOut,PlusCircle,Search,Settings,Shield,TrendingUp,Trophy,User,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_Heart_LayoutDashboard_LogOut_PlusCircle_Search_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Bell,Building,ChevronLeft,ChevronRight,Crown,FileText,Gavel,Heart,LayoutDashboard,LogOut,PlusCircle,Search,Settings,Shield,TrendingUp,Trophy,User,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_Heart_LayoutDashboard_LogOut_PlusCircle_Search_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Bell,Building,ChevronLeft,ChevronRight,Crown,FileText,Gavel,Heart,LayoutDashboard,LogOut,PlusCircle,Search,Settings,Shield,TrendingUp,Trophy,User,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_Heart_LayoutDashboard_LogOut_PlusCircle_Search_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Bell,Building,ChevronLeft,ChevronRight,Crown,FileText,Gavel,Heart,LayoutDashboard,LogOut,PlusCircle,Search,Settings,Shield,TrendingUp,Trophy,User,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_Heart_LayoutDashboard_LogOut_PlusCircle_Search_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Bell,Building,ChevronLeft,ChevronRight,Crown,FileText,Gavel,Heart,LayoutDashboard,LogOut,PlusCircle,Search,Settings,Shield,TrendingUp,Trophy,User,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_Heart_LayoutDashboard_LogOut_PlusCircle_Search_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Bell,Building,ChevronLeft,ChevronRight,Crown,FileText,Gavel,Heart,LayoutDashboard,LogOut,PlusCircle,Search,Settings,Shield,TrendingUp,Trophy,User,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_Heart_LayoutDashboard_LogOut_PlusCircle_Search_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Bell,Building,ChevronLeft,ChevronRight,Crown,FileText,Gavel,Heart,LayoutDashboard,LogOut,PlusCircle,Search,Settings,Shield,TrendingUp,Trophy,User,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_Heart_LayoutDashboard_LogOut_PlusCircle_Search_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Bell,Building,ChevronLeft,ChevronRight,Crown,FileText,Gavel,Heart,LayoutDashboard,LogOut,PlusCircle,Search,Settings,Shield,TrendingUp,Trophy,User,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trophy.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_Heart_LayoutDashboard_LogOut_PlusCircle_Search_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Bell,Building,ChevronLeft,ChevronRight,Crown,FileText,Gavel,Heart,LayoutDashboard,LogOut,PlusCircle,Search,Settings,Shield,TrendingUp,Trophy,User,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_Heart_LayoutDashboard_LogOut_PlusCircle_Search_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Bell,Building,ChevronLeft,ChevronRight,Crown,FileText,Gavel,Heart,LayoutDashboard,LogOut,PlusCircle,Search,Settings,Shield,TrendingUp,Trophy,User,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_Heart_LayoutDashboard_LogOut_PlusCircle_Search_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Bell,Building,ChevronLeft,ChevronRight,Crown,FileText,Gavel,Heart,LayoutDashboard,LogOut,PlusCircle,Search,Settings,Shield,TrendingUp,Trophy,User,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/crown.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_Heart_LayoutDashboard_LogOut_PlusCircle_Search_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Bell,Building,ChevronLeft,ChevronRight,Crown,FileText,Gavel,Heart,LayoutDashboard,LogOut,PlusCircle,Search,Settings,Shield,TrendingUp,Trophy,User,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_Heart_LayoutDashboard_LogOut_PlusCircle_Search_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Bell,Building,ChevronLeft,ChevronRight,Crown,FileText,Gavel,Heart,LayoutDashboard,LogOut,PlusCircle,Search,Settings,Shield,TrendingUp,Trophy,User,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_Heart_LayoutDashboard_LogOut_PlusCircle_Search_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Bell,Building,ChevronLeft,ChevronRight,Crown,FileText,Gavel,Heart,LayoutDashboard,LogOut,PlusCircle,Search,Settings,Shield,TrendingUp,Trophy,User,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nconst navItems = [\n    // Admin and Super Admin navigation\n    {\n        href: \"/admin/dashboard\",\n        label: \"لوحة التحكم\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_Heart_LayoutDashboard_LogOut_PlusCircle_Search_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n            className: \"h-5 w-5\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n            lineNumber: 46,\n            columnNumber: 11\n        }, undefined),\n        roles: [\n            \"admin\",\n            \"super_admin\"\n        ]\n    },\n    {\n        href: \"/search\",\n        label: \"البحث والاستكشاف\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_Heart_LayoutDashboard_LogOut_PlusCircle_Search_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n            className: \"h-5 w-5\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n            lineNumber: 52,\n            columnNumber: 11\n        }, undefined),\n        roles: [\n            \"admin\",\n            \"super_admin\"\n        ]\n    },\n    {\n        href: \"/admin/pending-accounts\",\n        label: \"الحسابات المعلقة\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_Heart_LayoutDashboard_LogOut_PlusCircle_Search_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n            className: \"h-5 w-5\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n            lineNumber: 58,\n            columnNumber: 11\n        }, undefined),\n        roles: [\n            \"admin\",\n            \"super_admin\"\n        ]\n    },\n    {\n        href: \"/admin/users\",\n        label: \"إدارة المستخدمين\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_Heart_LayoutDashboard_LogOut_PlusCircle_Search_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n            className: \"h-5 w-5\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n            lineNumber: 64,\n            columnNumber: 11\n        }, undefined),\n        roles: [\n            \"admin\",\n            \"super_admin\"\n        ]\n    },\n    {\n        href: \"/admin/auctions\",\n        label: \"إدارة المزادات\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_Heart_LayoutDashboard_LogOut_PlusCircle_Search_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n            className: \"h-5 w-5\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n            lineNumber: 70,\n            columnNumber: 11\n        }, undefined),\n        roles: [\n            \"admin\",\n            \"super_admin\"\n        ]\n    },\n    {\n        href: \"/admin/tenders\",\n        label: \"إدارة المناقصات\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_Heart_LayoutDashboard_LogOut_PlusCircle_Search_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n            className: \"h-5 w-5\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n            lineNumber: 76,\n            columnNumber: 11\n        }, undefined),\n        roles: [\n            \"admin\",\n            \"super_admin\"\n        ]\n    },\n    {\n        href: \"/admin/create-tender\",\n        label: \"إنشاء مناقصة\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_Heart_LayoutDashboard_LogOut_PlusCircle_Search_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n            className: \"h-5 w-5\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n            lineNumber: 82,\n            columnNumber: 11\n        }, undefined),\n        roles: [\n            \"admin\",\n            \"super_admin\"\n        ]\n    },\n    {\n        href: \"/favorites\",\n        label: \"المفضلة\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_Heart_LayoutDashboard_LogOut_PlusCircle_Search_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n            className: \"h-5 w-5\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n            lineNumber: 88,\n            columnNumber: 11\n        }, undefined),\n        roles: [\n            \"admin\",\n            \"super_admin\"\n        ]\n    },\n    {\n        href: \"/admin/settings\",\n        label: \"الإعدادات\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_Heart_LayoutDashboard_LogOut_PlusCircle_Search_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n            className: \"h-5 w-5\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n            lineNumber: 94,\n            columnNumber: 11\n        }, undefined),\n        roles: [\n            \"super_admin\"\n        ]\n    },\n    // Company navigation\n    {\n        href: \"/company/dashboard\",\n        label: \"لوحة التحكم\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_Heart_LayoutDashboard_LogOut_PlusCircle_Search_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n            className: \"h-5 w-5\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n            lineNumber: 102,\n            columnNumber: 11\n        }, undefined),\n        roles: [\n            \"company\"\n        ]\n    },\n    {\n        href: \"/search\",\n        label: \"البحث والاستكشاف\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_Heart_LayoutDashboard_LogOut_PlusCircle_Search_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n            className: \"h-5 w-5\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n            lineNumber: 108,\n            columnNumber: 11\n        }, undefined),\n        roles: [\n            \"company\"\n        ]\n    },\n    {\n        href: \"/company/auctions\",\n        label: \"مزاداتي\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_Heart_LayoutDashboard_LogOut_PlusCircle_Search_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n            className: \"h-5 w-5\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n            lineNumber: 114,\n            columnNumber: 11\n        }, undefined),\n        roles: [\n            \"company\"\n        ]\n    },\n    {\n        href: \"/company/tenders\",\n        label: \"مناقصاتي\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_Heart_LayoutDashboard_LogOut_PlusCircle_Search_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n            className: \"h-5 w-5\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n            lineNumber: 120,\n            columnNumber: 11\n        }, undefined),\n        roles: [\n            \"company\"\n        ]\n    },\n    {\n        href: \"/company/create-auction\",\n        label: \"إنشاء مزاد\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_Heart_LayoutDashboard_LogOut_PlusCircle_Search_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n            className: \"h-5 w-5\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n            lineNumber: 126,\n            columnNumber: 11\n        }, undefined),\n        roles: [\n            \"company\"\n        ]\n    },\n    {\n        href: \"/company/bids\",\n        label: \"عطاءاتي\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_Heart_LayoutDashboard_LogOut_PlusCircle_Search_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n            className: \"h-5 w-5\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n            lineNumber: 132,\n            columnNumber: 11\n        }, undefined),\n        roles: [\n            \"company\"\n        ]\n    },\n    {\n        href: \"/company/notifications\",\n        label: \"الإشعارات\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_Heart_LayoutDashboard_LogOut_PlusCircle_Search_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n            className: \"h-5 w-5\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n            lineNumber: 138,\n            columnNumber: 11\n        }, undefined),\n        roles: [\n            \"company\"\n        ]\n    },\n    {\n        href: \"/favorites\",\n        label: \"المفضلة\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_Heart_LayoutDashboard_LogOut_PlusCircle_Search_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n            className: \"h-5 w-5\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n            lineNumber: 144,\n            columnNumber: 11\n        }, undefined),\n        roles: [\n            \"company\"\n        ]\n    },\n    {\n        href: \"/company/profile\",\n        label: \"الملف الشخصي\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_Heart_LayoutDashboard_LogOut_PlusCircle_Search_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n            className: \"h-5 w-5\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n            lineNumber: 150,\n            columnNumber: 11\n        }, undefined),\n        roles: [\n            \"company\"\n        ]\n    },\n    // Individual user navigation\n    {\n        href: \"/user/dashboard\",\n        label: \"لوحة التحكم\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_Heart_LayoutDashboard_LogOut_PlusCircle_Search_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n            className: \"h-5 w-5\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n            lineNumber: 158,\n            columnNumber: 11\n        }, undefined),\n        roles: [\n            \"individual\"\n        ]\n    },\n    {\n        href: \"/search\",\n        label: \"البحث والاستكشاف\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_Heart_LayoutDashboard_LogOut_PlusCircle_Search_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n            className: \"h-5 w-5\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n            lineNumber: 164,\n            columnNumber: 11\n        }, undefined),\n        roles: [\n            \"individual\"\n        ]\n    },\n    {\n        href: \"/user/leaderboard\",\n        label: \"لوحة الصدارة\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_Heart_LayoutDashboard_LogOut_PlusCircle_Search_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n            className: \"h-5 w-5\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n            lineNumber: 170,\n            columnNumber: 11\n        }, undefined),\n        roles: [\n            \"individual\"\n        ]\n    },\n    {\n        href: \"/user/auctions\",\n        label: \"المزادات المتاحة\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_Heart_LayoutDashboard_LogOut_PlusCircle_Search_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n            className: \"h-5 w-5\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n            lineNumber: 176,\n            columnNumber: 11\n        }, undefined),\n        roles: [\n            \"individual\"\n        ]\n    },\n    {\n        href: \"/user/tenders\",\n        label: \"المناقصات المتاحة\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_Heart_LayoutDashboard_LogOut_PlusCircle_Search_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n            className: \"h-5 w-5\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n            lineNumber: 182,\n            columnNumber: 11\n        }, undefined),\n        roles: [\n            \"individual\"\n        ]\n    },\n    {\n        href: \"/user/my-bids\",\n        label: \"مزايداتي\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_Heart_LayoutDashboard_LogOut_PlusCircle_Search_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n            className: \"h-5 w-5\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n            lineNumber: 188,\n            columnNumber: 11\n        }, undefined),\n        roles: [\n            \"individual\"\n        ]\n    },\n    {\n        href: \"/user/bids\",\n        label: \"إدارة المزايدات\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_Heart_LayoutDashboard_LogOut_PlusCircle_Search_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n            className: \"h-5 w-5\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n            lineNumber: 194,\n            columnNumber: 11\n        }, undefined),\n        roles: [\n            \"individual\"\n        ]\n    },\n    {\n        href: \"/user/applications\",\n        label: \"طلبات المناقصات\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_Heart_LayoutDashboard_LogOut_PlusCircle_Search_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n            className: \"h-5 w-5\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n            lineNumber: 200,\n            columnNumber: 11\n        }, undefined),\n        roles: [\n            \"individual\"\n        ]\n    },\n    {\n        href: \"/user/analytics\",\n        label: \"التحليلات\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_Heart_LayoutDashboard_LogOut_PlusCircle_Search_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n            className: \"h-5 w-5\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n            lineNumber: 206,\n            columnNumber: 11\n        }, undefined),\n        roles: [\n            \"individual\"\n        ]\n    },\n    {\n        href: \"/user/notifications\",\n        label: \"الإشعارات\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_Heart_LayoutDashboard_LogOut_PlusCircle_Search_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n            className: \"h-5 w-5\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n            lineNumber: 212,\n            columnNumber: 11\n        }, undefined),\n        roles: [\n            \"individual\"\n        ]\n    },\n    {\n        href: \"/favorites\",\n        label: \"المفضلة\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_Heart_LayoutDashboard_LogOut_PlusCircle_Search_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n            className: \"h-5 w-5\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n            lineNumber: 218,\n            columnNumber: 11\n        }, undefined),\n        roles: [\n            \"individual\"\n        ]\n    },\n    {\n        href: \"/user/profile\",\n        label: \"الملف الشخصي\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_Heart_LayoutDashboard_LogOut_PlusCircle_Search_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n            className: \"h-5 w-5\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n            lineNumber: 224,\n            columnNumber: 11\n        }, undefined),\n        roles: [\n            \"individual\"\n        ]\n    },\n    // Government navigation\n    {\n        href: \"/government/dashboard\",\n        label: \"لوحة التحكم\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_Heart_LayoutDashboard_LogOut_PlusCircle_Search_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n            className: \"h-5 w-5\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n            lineNumber: 232,\n            columnNumber: 11\n        }, undefined),\n        roles: [\n            \"government\"\n        ]\n    },\n    {\n        href: \"/search\",\n        label: \"البحث والاستكشاف\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_Heart_LayoutDashboard_LogOut_PlusCircle_Search_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n            className: \"h-5 w-5\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n            lineNumber: 238,\n            columnNumber: 11\n        }, undefined),\n        roles: [\n            \"government\"\n        ]\n    },\n    {\n        href: \"/government/tenders\",\n        label: \"مناقصاتي\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_Heart_LayoutDashboard_LogOut_PlusCircle_Search_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n            className: \"h-5 w-5\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n            lineNumber: 244,\n            columnNumber: 11\n        }, undefined),\n        roles: [\n            \"government\"\n        ]\n    },\n    {\n        href: \"/government/applications\",\n        label: \"طلبات المشاركة\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_Heart_LayoutDashboard_LogOut_PlusCircle_Search_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n            className: \"h-5 w-5\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n            lineNumber: 250,\n            columnNumber: 11\n        }, undefined),\n        roles: [\n            \"government\"\n        ]\n    },\n    {\n        href: \"/government/notifications\",\n        label: \"الإشعارات\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_Heart_LayoutDashboard_LogOut_PlusCircle_Search_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n            className: \"h-5 w-5\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n            lineNumber: 256,\n            columnNumber: 11\n        }, undefined),\n        roles: [\n            \"government\"\n        ]\n    },\n    {\n        href: \"/favorites\",\n        label: \"المفضلة\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_Heart_LayoutDashboard_LogOut_PlusCircle_Search_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n            className: \"h-5 w-5\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n            lineNumber: 262,\n            columnNumber: 11\n        }, undefined),\n        roles: [\n            \"government\"\n        ]\n    },\n    {\n        href: \"/government/profile\",\n        label: \"الملف الشخصي\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_Heart_LayoutDashboard_LogOut_PlusCircle_Search_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n            className: \"h-5 w-5\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n            lineNumber: 268,\n            columnNumber: 11\n        }, undefined),\n        roles: [\n            \"government\"\n        ]\n    }\n];\nfunction Sidebar(param) {\n    let { userRole } = param;\n    var _user_profile, _user_profile1, _user_profile2;\n    _s();\n    const [isCollapsed, setIsCollapsed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const userData = localStorage.getItem(\"user\");\n        if (userData) {\n            setUser(JSON.parse(userData));\n        }\n    }, []);\n    const handleLogout = ()=>{\n        localStorage.removeItem(\"token\");\n        localStorage.removeItem(\"user\");\n        router.push(\"/auth/login\");\n    };\n    const roleIcon = ()=>{\n        switch(user === null || user === void 0 ? void 0 : user.role){\n            case \"super_admin\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_Heart_LayoutDashboard_LogOut_PlusCircle_Search_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                    className: \"h-6 w-6 text-yellow-500\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                    lineNumber: 295,\n                    columnNumber: 16\n                }, this);\n            case \"admin\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_Heart_LayoutDashboard_LogOut_PlusCircle_Search_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    className: \"h-6 w-6 text-blue-500\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                    lineNumber: 297,\n                    columnNumber: 16\n                }, this);\n            case \"company\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_Heart_LayoutDashboard_LogOut_PlusCircle_Search_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                    className: \"h-6 w-6 text-green-500\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                    lineNumber: 299,\n                    columnNumber: 16\n                }, this);\n            case \"government\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_Heart_LayoutDashboard_LogOut_PlusCircle_Search_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    className: \"h-6 w-6 text-red-500\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                    lineNumber: 301,\n                    columnNumber: 16\n                }, this);\n            case \"individual\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_Heart_LayoutDashboard_LogOut_PlusCircle_Search_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                    className: \"h-6 w-6 text-purple-500\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                    lineNumber: 303,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_Heart_LayoutDashboard_LogOut_PlusCircle_Search_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                    className: \"h-6 w-6\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                    lineNumber: 305,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    const getRoleLabel = (role)=>{\n        switch(role){\n            case \"super_admin\":\n                return \"مدير عام\";\n            case \"admin\":\n                return \"مدير\";\n            case \"company\":\n                return \"شركة\";\n            case \"government\":\n                return \"جهة حكومية\";\n            case \"individual\":\n                return \"فرد\";\n            default:\n                return role;\n        }\n    };\n    const filteredNavItems = navItems.filter((item)=>item.roles.includes((user === null || user === void 0 ? void 0 : user.role) || \"\"));\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"\".concat(isCollapsed ? \"w-16\" : \"w-64\", \" transition-all duration-300 backdrop-blur-xl bg-gradient-to-b from-white/95 via-white/90 to-white/85 border-r border-white/30 shadow-2xl flex flex-col h-screen relative z-10 overflow-hidden\"),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 bg-gradient-to-br from-blue-50/30 via-purple-50/20 to-indigo-50/30 opacity-60\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                lineNumber: 333,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-blue-400/10 to-purple-400/10 rounded-full blur-3xl animate-pulse\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                lineNumber: 334,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute bottom-0 left-0 w-24 h-24 bg-gradient-to-tr from-indigo-400/10 to-pink-400/10 rounded-full blur-2xl animate-pulse animation-delay-2000\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                lineNumber: 335,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10 p-4 border-b border-white/30 backdrop-blur-sm\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            href: \"/\",\n                            className: \"flex items-center gap-3 group\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-10 h-10 bg-gradient-to-br from-blue-600 via-purple-600 to-indigo-700 rounded-2xl flex items-center justify-center group-hover:scale-110 group-hover:rotate-6 transition-all duration-500 shadow-xl group-hover:shadow-2xl\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_Heart_LayoutDashboard_LogOut_PlusCircle_Search_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                        className: \"w-5 h-5 text-white drop-shadow-sm\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                                        lineNumber: 343,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                                    lineNumber: 342,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-right\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-lg font-bold bg-gradient-to-r from-gray-900 via-blue-800 to-purple-800 bg-clip-text text-transparent\",\n                                            children: \"المنصة\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                                            lineNumber: 346,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-gray-600 font-medium\",\n                                            children: \"المزادات والمناقصات\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                                            lineNumber: 347,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                                    lineNumber: 345,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                            lineNumber: 341,\n                            columnNumber: 13\n                        }, this),\n                        isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-10 h-10 bg-gradient-to-br from-blue-600 via-purple-600 to-indigo-700 rounded-2xl flex items-center justify-center shadow-xl mx-auto\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_Heart_LayoutDashboard_LogOut_PlusCircle_Search_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                className: \"w-5 h-5 text-white drop-shadow-sm\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                                lineNumber: 353,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                            lineNumber: 352,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                            variant: \"ghost\",\n                            size: \"sm\",\n                            onClick: ()=>setIsCollapsed(!isCollapsed),\n                            className: \"p-2 rounded-xl bg-white/60 hover:bg-white/80 transition-all duration-300 shadow-md hover:shadow-lg backdrop-blur-sm border border-white/40\",\n                            children: isCollapsed ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_Heart_LayoutDashboard_LogOut_PlusCircle_Search_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                className: \"h-4 w-4 text-gray-700\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                                lineNumber: 362,\n                                columnNumber: 28\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_Heart_LayoutDashboard_LogOut_PlusCircle_Search_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                className: \"h-4 w-4 text-gray-700\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                                lineNumber: 362,\n                                columnNumber: 80\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                            lineNumber: 356,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                    lineNumber: 339,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                lineNumber: 338,\n                columnNumber: 7\n            }, this),\n            user && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10 px-4 py-4 border-b border-white/30 backdrop-blur-sm\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center \".concat(isCollapsed ? \"justify-center\" : \"gap-4\", \" p-3 rounded-2xl bg-white/40 backdrop-blur-md border border-white/50 shadow-lg hover:shadow-xl transition-all duration-300\"),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-10 h-10 bg-gradient-to-br from-blue-600 via-purple-600 to-indigo-700 rounded-2xl flex items-center justify-center shadow-xl\",\n                                    children: roleIcon()\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                                    lineNumber: 372,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute -bottom-1 -right-1 w-4 h-4 bg-green-500 rounded-full border-2 border-white shadow-md\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full h-full bg-green-400 rounded-full animate-ping\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                                        lineNumber: 376,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                                    lineNumber: 375,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                            lineNumber: 371,\n                            columnNumber: 13\n                        }, this),\n                        !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 min-w-0\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm font-bold text-gray-900 truncate\",\n                                    children: ((_user_profile = user.profile) === null || _user_profile === void 0 ? void 0 : _user_profile.fullName) || ((_user_profile1 = user.profile) === null || _user_profile1 === void 0 ? void 0 : _user_profile1.companyName) || ((_user_profile2 = user.profile) === null || _user_profile2 === void 0 ? void 0 : _user_profile2.governmentEntity) || \"المدير\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                                    lineNumber: 381,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-600 font-medium truncate\",\n                                    children: getRoleLabel(user.role)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                                    lineNumber: 384,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2 mt-1\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-2 h-2 bg-green-500 rounded-full animate-pulse\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                                                lineNumber: 389,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs text-green-600 font-medium\",\n                                                children: \"متصل\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                                                lineNumber: 390,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                                        lineNumber: 388,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                                    lineNumber: 387,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                            lineNumber: 380,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                    lineNumber: 370,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                lineNumber: 369,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: \"flex-1 relative z-10 overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-0 left-0 right-0 h-4 bg-gradient-to-b from-white/80 to-transparent z-20 pointer-events-none\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                        lineNumber: 402,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-full overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300/50 scrollbar-track-transparent hover:scrollbar-thumb-gray-400/70 px-4 py-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-1.5\",\n                            children: filteredNavItems.map((item, index)=>{\n                                const isActive = pathname === item.href;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    href: item.href,\n                                    className: \"group flex items-center \".concat(isCollapsed ? \"justify-center px-2 py-3\" : \"gap-4 px-4 py-3.5\", \" rounded-2xl transition-all duration-300 relative overflow-hidden \").concat(isActive ? \"bg-gradient-to-r from-blue-500/15 via-purple-500/10 to-indigo-500/15 border border-blue-300/40 shadow-lg backdrop-blur-md scale-[1.02]\" : \"hover:bg-white/70 hover:shadow-lg hover:scale-[1.02] border border-transparent hover:border-white/50 backdrop-blur-sm\"),\n                                    style: {\n                                        animationDelay: \"\".concat(index * 50, \"ms\")\n                                    },\n                                    children: [\n                                        isActive && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-0 bg-gradient-to-r from-blue-500/8 via-purple-500/5 to-indigo-500/8\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                                                    lineNumber: 425,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute right-0 top-1/2 -translate-y-1/2 w-1 h-8 bg-gradient-to-b from-blue-500 to-purple-600 rounded-l-full\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                                                    lineNumber: 426,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"\".concat(isCollapsed ? \"w-7 h-7\" : \"w-10 h-10\", \" rounded-xl flex items-center justify-center transition-all duration-300 relative z-10 \").concat(isActive ? \"bg-gradient-to-br from-blue-500 via-purple-600 to-indigo-600 shadow-xl scale-110 rotate-3\" : \"bg-white/60 group-hover:bg-gradient-to-br group-hover:from-blue-100 group-hover:to-purple-100 group-hover:scale-105 shadow-md\"),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"transition-all duration-300 \".concat(isActive ? \"text-white scale-110 drop-shadow-sm\" : \"text-gray-600 group-hover:text-blue-600\"),\n                                                children: item.icon\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                                                lineNumber: 435,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                                            lineNumber: 430,\n                                            columnNumber: 19\n                                        }, this),\n                                        !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative z-10 flex-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-semibold transition-all duration-300 \".concat(isActive ? \"text-gray-900\" : \"text-gray-700 group-hover:text-gray-900\"),\n                                                    children: item.label\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                                                    lineNumber: 444,\n                                                    columnNumber: 23\n                                                }, this),\n                                                isActive && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-1 mt-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full animate-pulse\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                                                            lineNumber: 453,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-1 h-1 bg-gradient-to-r from-blue-400 to-purple-500 rounded-full animate-pulse animation-delay-200\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                                                            lineNumber: 454,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                                                    lineNumber: 452,\n                                                    columnNumber: 25\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                                            lineNumber: 443,\n                                            columnNumber: 21\n                                        }, this),\n                                        isActive && !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_Heart_LayoutDashboard_LogOut_PlusCircle_Search_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                            className: \"w-5 h-5 text-blue-600 relative z-10 animate-pulse\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                                            lineNumber: 461,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, item.href, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                                    lineNumber: 410,\n                                    columnNumber: 17\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                            lineNumber: 406,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                        lineNumber: 405,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute bottom-0 left-0 right-0 h-4 bg-gradient-to-t from-white/80 to-transparent z-20 pointer-events-none\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                        lineNumber: 470,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                lineNumber: 400,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10 px-4 pb-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"border-t border-white/30 pt-4 backdrop-blur-sm\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                        onClick: handleLogout,\n                        variant: \"ghost\",\n                        className: \"w-full group flex items-center \".concat(isCollapsed ? \"justify-center px-3 py-3\" : \"gap-4 px-4 py-3.5\", \" rounded-2xl hover:bg-red-50/80 hover:shadow-lg transition-all duration-300 border border-transparent hover:border-red-200/60 backdrop-blur-md hover:scale-[1.02]\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"\".concat(isCollapsed ? \"w-7 h-7\" : \"w-10 h-10\", \" rounded-xl bg-red-100/80 group-hover:bg-gradient-to-br group-hover:from-red-500 group-hover:to-pink-500 flex items-center justify-center transition-all duration-300 shadow-md group-hover:shadow-xl group-hover:scale-105\"),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Bell_Building_ChevronLeft_ChevronRight_Crown_FileText_Gavel_Heart_LayoutDashboard_LogOut_PlusCircle_Search_Settings_Shield_TrendingUp_Trophy_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                    className: \"\".concat(isCollapsed ? \"w-4 h-4\" : \"w-5 h-5\", \" text-red-600 group-hover:text-white transition-all duration-300 drop-shadow-sm\")\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                                    lineNumber: 482,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                                lineNumber: 481,\n                                columnNumber: 13\n                            }, this),\n                            !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm font-semibold text-red-700 group-hover:text-red-800 transition-all duration-300\",\n                                children: \"تسجيل الخروج\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                                lineNumber: 485,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                        lineNumber: 476,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                    lineNumber: 475,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n                lineNumber: 474,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/Sidebar.tsx\",\n        lineNumber: 331,\n        columnNumber: 5\n    }, this);\n}\n_s(Sidebar, \"8D+h9wKtrXb9BO+L1pAtHWiT9Do=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname\n    ];\n});\n_c = Sidebar;\nvar _c;\n$RefreshReg$(_c, \"Sidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/Sidebar.tsx\n"));

/***/ })

});