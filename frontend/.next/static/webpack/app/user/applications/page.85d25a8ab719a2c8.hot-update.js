"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/user/applications/page",{

/***/ "(app-pages-browser)/./app/user/applications/page.tsx":
/*!****************************************!*\
  !*** ./app/user/applications/page.tsx ***!
  \****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ UserApplicationsPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./components/ui/select.tsx\");\n/* harmony import */ var _components_ui_table__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/table */ \"(app-pages-browser)/./components/ui/table.tsx\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/use-toast */ \"(app-pages-browser)/./components/ui/use-toast.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./lib/api.ts\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Building_Calendar_CheckCircle_Clock_Eye_FileText_Plus_RefreshCw_Search_Star_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Building,Calendar,CheckCircle,Clock,Eye,FileText,Plus,RefreshCw,Search,Star,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Building_Calendar_CheckCircle_Clock_Eye_FileText_Plus_RefreshCw_Search_Star_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Building,Calendar,CheckCircle,Clock,Eye,FileText,Plus,RefreshCw,Search,Star,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Building_Calendar_CheckCircle_Clock_Eye_FileText_Plus_RefreshCw_Search_Star_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Building,Calendar,CheckCircle,Clock,Eye,FileText,Plus,RefreshCw,Search,Star,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Building_Calendar_CheckCircle_Clock_Eye_FileText_Plus_RefreshCw_Search_Star_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Building,Calendar,CheckCircle,Clock,Eye,FileText,Plus,RefreshCw,Search,Star,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Building_Calendar_CheckCircle_Clock_Eye_FileText_Plus_RefreshCw_Search_Star_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Building,Calendar,CheckCircle,Clock,Eye,FileText,Plus,RefreshCw,Search,Star,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Building_Calendar_CheckCircle_Clock_Eye_FileText_Plus_RefreshCw_Search_Star_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Building,Calendar,CheckCircle,Clock,Eye,FileText,Plus,RefreshCw,Search,Star,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x-circle.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Building_Calendar_CheckCircle_Clock_Eye_FileText_Plus_RefreshCw_Search_Star_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Building,Calendar,CheckCircle,Clock,Eye,FileText,Plus,RefreshCw,Search,Star,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Building_Calendar_CheckCircle_Clock_Eye_FileText_Plus_RefreshCw_Search_Star_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Building,Calendar,CheckCircle,Clock,Eye,FileText,Plus,RefreshCw,Search,Star,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bar-chart-3.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Building_Calendar_CheckCircle_Clock_Eye_FileText_Plus_RefreshCw_Search_Star_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Building,Calendar,CheckCircle,Clock,Eye,FileText,Plus,RefreshCw,Search,Star,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Building_Calendar_CheckCircle_Clock_Eye_FileText_Plus_RefreshCw_Search_Star_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Building,Calendar,CheckCircle,Clock,Eye,FileText,Plus,RefreshCw,Search,Star,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Building_Calendar_CheckCircle_Clock_Eye_FileText_Plus_RefreshCw_Search_Star_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Building,Calendar,CheckCircle,Clock,Eye,FileText,Plus,RefreshCw,Search,Star,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Building_Calendar_CheckCircle_Clock_Eye_FileText_Plus_RefreshCw_Search_Star_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Building,Calendar,CheckCircle,Clock,Eye,FileText,Plus,RefreshCw,Search,Star,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Building_Calendar_CheckCircle_Clock_Eye_FileText_Plus_RefreshCw_Search_Star_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Building,Calendar,CheckCircle,Clock,Eye,FileText,Plus,RefreshCw,Search,Star,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nfunction UserApplicationsPage() {\n    _s();\n    const [applications, setApplications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [filteredApplications, setFilteredApplications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [statusFilter, setStatusFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        total: 0,\n        pending: 0,\n        approved: 0,\n        rejected: 0\n    });\n    const { toast } = (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_8__.useToast)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_9__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadApplications();\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        filterApplications();\n    }, [\n        applications,\n        searchTerm,\n        statusFilter\n    ]);\n    const loadApplications = async ()=>{\n        try {\n            setLoading(true);\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_10__[\"default\"].get(\"/users/applications\");\n            if (response.data.success) {\n                setApplications(response.data.data.applications);\n                calculateStats(response.data.data.applications);\n            } else {\n                setApplications([]);\n                calculateStats([]);\n            }\n        } catch (error) {\n            console.error(\"Error loading applications:\", error);\n            toast({\n                title: \"خطأ في التحميل\",\n                description: \"حدث خطأ في تحميل بيانات الطلبات\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    const calculateStats = (applicationsData)=>{\n        const total = applicationsData.length;\n        const pending = applicationsData.filter((app)=>app.status === \"pending\").length;\n        const approved = applicationsData.filter((app)=>app.status === \"approved\").length;\n        const rejected = applicationsData.filter((app)=>app.status === \"rejected\").length;\n        setStats({\n            total,\n            pending,\n            approved,\n            rejected\n        });\n    };\n    const filterApplications = ()=>{\n        let filtered = applications;\n        if (searchTerm) {\n            filtered = filtered.filter((app)=>app.tender.title.toLowerCase().includes(searchTerm.toLowerCase()));\n        }\n        if (statusFilter !== \"all\") {\n            filtered = filtered.filter((app)=>app.status === statusFilter);\n        }\n        setFilteredApplications(filtered);\n    };\n    const formatPrice = (price)=>{\n        return new Intl.NumberFormat(\"ar-SA\", {\n            style: \"currency\",\n            currency: \"SAR\",\n            minimumFractionDigits: 0\n        }).format(price);\n    };\n    const formatDate = (dateString)=>{\n        return new Date(dateString).toLocaleDateString(\"ar-SA\");\n    };\n    const getStatusBadge = (status)=>{\n        switch(status){\n            case \"approved\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                    className: \"bg-green-100 text-green-800\",\n                    children: \"موافق عليه\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/applications/page.tsx\",\n                    lineNumber: 144,\n                    columnNumber: 16\n                }, this);\n            case \"rejected\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                    variant: \"destructive\",\n                    children: \"مرفوض\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/applications/page.tsx\",\n                    lineNumber: 146,\n                    columnNumber: 16\n                }, this);\n            case \"pending\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                    className: \"bg-yellow-100 text-yellow-800\",\n                    children: \"في الانتظار\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/applications/page.tsx\",\n                    lineNumber: 148,\n                    columnNumber: 16\n                }, this);\n            case \"review\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                    className: \"bg-blue-100 text-blue-800\",\n                    children: \"قيد المراجعة\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/applications/page.tsx\",\n                    lineNumber: 150,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                    variant: \"outline\",\n                    children: status\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/applications/page.tsx\",\n                    lineNumber: 152,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    const withdrawApplication = async (applicationId)=>{\n        if (!confirm(\"هل أنت متأكد من سحب هذا الطلب؟\")) return;\n        try {\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_10__[\"default\"].delete(\"/applications/\".concat(applicationId));\n            if (response.data.success) {\n                setApplications(applications.filter((app)=>app._id !== applicationId));\n                toast({\n                    title: \"تم السحب\",\n                    description: \"تم سحب الطلب بنجاح\"\n                });\n            }\n        } catch (error) {\n            var _error_response_data, _error_response;\n            toast({\n                title: \"خطأ في السحب\",\n                description: ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"حدث خطأ في سحب الطلب\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center min-h-96\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/applications/page.tsx\",\n                        lineNumber: 182,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-4 text-gray-600\",\n                        children: \"جاري تحميل طلباتك...\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/applications/page.tsx\",\n                        lineNumber: 183,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/applications/page.tsx\",\n                lineNumber: 181,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/applications/page.tsx\",\n            lineNumber: 180,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gradient-to-r from-green-600 to-emerald-600 text-white rounded-lg p-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-3xl font-bold\",\n                                    children: \"طلبات المناقصات\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/applications/page.tsx\",\n                                    lineNumber: 195,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-green-100 mt-1\",\n                                    children: \"إدارة ومتابعة جميع طلباتك في المناقصات الحكومية\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/applications/page.tsx\",\n                                    lineNumber: 196,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/applications/page.tsx\",\n                            lineNumber: 194,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    onClick: loadApplications,\n                                    variant: \"outline\",\n                                    className: \"bg-white/10 border-white/20 text-white hover:bg-white/20\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Building_Calendar_CheckCircle_Clock_Eye_FileText_Plus_RefreshCw_Search_Star_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"h-4 w-4 ml-2\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/applications/page.tsx\",\n                                            lineNumber: 205,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"تحديث\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/applications/page.tsx\",\n                                    lineNumber: 200,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    onClick: ()=>router.push(\"/tenders\"),\n                                    className: \"bg-white text-green-600 hover:bg-gray-100\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Building_Calendar_CheckCircle_Clock_Eye_FileText_Plus_RefreshCw_Search_Star_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"h-4 w-4 ml-2\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/applications/page.tsx\",\n                                            lineNumber: 212,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"تصفح المناقصات\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/applications/page.tsx\",\n                                    lineNumber: 208,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/applications/page.tsx\",\n                            lineNumber: 198,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/applications/page.tsx\",\n                    lineNumber: 193,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/applications/page.tsx\",\n                lineNumber: 192,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-4 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        className: \"bg-gradient-to-br from-blue-50 to-blue-100 border-blue-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            className: \"p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-blue-600\",\n                                                children: \"إجمالي الطلبات\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/applications/page.tsx\",\n                                                lineNumber: 225,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-3xl font-bold text-blue-900\",\n                                                children: stats.total\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/applications/page.tsx\",\n                                                lineNumber: 226,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/applications/page.tsx\",\n                                        lineNumber: 224,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Building_Calendar_CheckCircle_Clock_Eye_FileText_Plus_RefreshCw_Search_Star_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"h-8 w-8 text-blue-600\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/applications/page.tsx\",\n                                        lineNumber: 228,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/applications/page.tsx\",\n                                lineNumber: 223,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/applications/page.tsx\",\n                            lineNumber: 222,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/applications/page.tsx\",\n                        lineNumber: 221,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        className: \"bg-gradient-to-br from-yellow-50 to-yellow-100 border-yellow-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            className: \"p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-yellow-600\",\n                                                children: \"في الانتظار\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/applications/page.tsx\",\n                                                lineNumber: 237,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-3xl font-bold text-yellow-900\",\n                                                children: stats.pending\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/applications/page.tsx\",\n                                                lineNumber: 238,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/applications/page.tsx\",\n                                        lineNumber: 236,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Building_Calendar_CheckCircle_Clock_Eye_FileText_Plus_RefreshCw_Search_Star_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"h-8 w-8 text-yellow-600\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/applications/page.tsx\",\n                                        lineNumber: 240,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/applications/page.tsx\",\n                                lineNumber: 235,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/applications/page.tsx\",\n                            lineNumber: 234,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/applications/page.tsx\",\n                        lineNumber: 233,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        className: \"bg-gradient-to-br from-green-50 to-green-100 border-green-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            className: \"p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-green-600\",\n                                                children: \"موافق عليها\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/applications/page.tsx\",\n                                                lineNumber: 249,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-3xl font-bold text-green-900\",\n                                                children: stats.approved\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/applications/page.tsx\",\n                                                lineNumber: 250,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/applications/page.tsx\",\n                                        lineNumber: 248,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Building_Calendar_CheckCircle_Clock_Eye_FileText_Plus_RefreshCw_Search_Star_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"h-8 w-8 text-green-600\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/applications/page.tsx\",\n                                        lineNumber: 252,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/applications/page.tsx\",\n                                lineNumber: 247,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/applications/page.tsx\",\n                            lineNumber: 246,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/applications/page.tsx\",\n                        lineNumber: 245,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        className: \"bg-gradient-to-br from-red-50 to-red-100 border-red-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            className: \"p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-red-600\",\n                                                children: \"مرفوضة\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/applications/page.tsx\",\n                                                lineNumber: 261,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-3xl font-bold text-red-900\",\n                                                children: stats.rejected\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/applications/page.tsx\",\n                                                lineNumber: 262,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/applications/page.tsx\",\n                                        lineNumber: 260,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Building_Calendar_CheckCircle_Clock_Eye_FileText_Plus_RefreshCw_Search_Star_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        className: \"h-8 w-8 text-red-600\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/applications/page.tsx\",\n                                        lineNumber: 264,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/applications/page.tsx\",\n                                lineNumber: 259,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/applications/page.tsx\",\n                            lineNumber: 258,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/applications/page.tsx\",\n                        lineNumber: 257,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/applications/page.tsx\",\n                lineNumber: 220,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                    className: \"p-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col md:flex-row gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Building_Calendar_CheckCircle_Clock_Eye_FileText_Plus_RefreshCw_Search_Star_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            className: \"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/applications/page.tsx\",\n                                            lineNumber: 276,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                            placeholder: \"البحث في الطلبات...\",\n                                            value: searchTerm,\n                                            onChange: (e)=>setSearchTerm(e.target.value),\n                                            className: \"pr-10\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/applications/page.tsx\",\n                                            lineNumber: 277,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/applications/page.tsx\",\n                                    lineNumber: 275,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/applications/page.tsx\",\n                                lineNumber: 274,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.Select, {\n                                value: statusFilter,\n                                onValueChange: setStatusFilter,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectTrigger, {\n                                        className: \"w-full md:w-48\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectValue, {\n                                            placeholder: \"فلترة حسب الحالة\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/applications/page.tsx\",\n                                            lineNumber: 287,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/applications/page.tsx\",\n                                        lineNumber: 286,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectContent, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                value: \"all\",\n                                                children: \"جميع الحالات\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/applications/page.tsx\",\n                                                lineNumber: 290,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                value: \"pending\",\n                                                children: \"في الانتظار\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/applications/page.tsx\",\n                                                lineNumber: 291,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                value: \"approved\",\n                                                children: \"موافق عليها\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/applications/page.tsx\",\n                                                lineNumber: 292,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                value: \"rejected\",\n                                                children: \"مرفوضة\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/applications/page.tsx\",\n                                                lineNumber: 293,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                value: \"review\",\n                                                children: \"قيد المراجعة\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/applications/page.tsx\",\n                                                lineNumber: 294,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/applications/page.tsx\",\n                                        lineNumber: 289,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/applications/page.tsx\",\n                                lineNumber: 285,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/applications/page.tsx\",\n                        lineNumber: 273,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/applications/page.tsx\",\n                    lineNumber: 272,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/applications/page.tsx\",\n                lineNumber: 271,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Building_Calendar_CheckCircle_Clock_Eye_FileText_Plus_RefreshCw_Search_Star_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        className: \"h-5 w-5 text-green-600\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/applications/page.tsx\",\n                                        lineNumber: 305,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"قائمة الطلبات\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/applications/page.tsx\",\n                                lineNumber: 304,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                children: [\n                                    \"جميع طلباتك في المناقصات الحكومية (\",\n                                    filteredApplications.length,\n                                    \" من \",\n                                    applications.length,\n                                    \")\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/applications/page.tsx\",\n                                lineNumber: 308,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/applications/page.tsx\",\n                        lineNumber: 303,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        children: filteredApplications.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"overflow-x-auto\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.Table, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableRow, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                    className: \"w-[300px]\",\n                                                    children: \"تفاصيل المناقصة\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/applications/page.tsx\",\n                                                    lineNumber: 318,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                    children: \"الميزانية المقترحة\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/applications/page.tsx\",\n                                                    lineNumber: 319,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                    children: \"الجدول الزمني\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/applications/page.tsx\",\n                                                    lineNumber: 320,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                    children: \"الحالة\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/applications/page.tsx\",\n                                                    lineNumber: 321,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                    children: \"التقييم\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/applications/page.tsx\",\n                                                    lineNumber: 322,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                    className: \"text-center\",\n                                                    children: \"الإجراءات\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/applications/page.tsx\",\n                                                    lineNumber: 323,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/applications/page.tsx\",\n                                            lineNumber: 317,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/applications/page.tsx\",\n                                        lineNumber: 316,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableBody, {\n                                        children: filteredApplications.map((application)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableRow, {\n                                                className: \"hover:bg-gray-50\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                    className: \"font-medium text-gray-900\",\n                                                                    children: application.tender.title\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/applications/page.tsx\",\n                                                                    lineNumber: 331,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-2 text-xs text-gray-500\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Building_Calendar_CheckCircle_Clock_Eye_FileText_Plus_RefreshCw_Search_Star_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                            className: \"h-3 w-3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/applications/page.tsx\",\n                                                                            lineNumber: 333,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        application.tender.organizer.profile.governmentEntity\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/applications/page.tsx\",\n                                                                    lineNumber: 332,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-2 text-xs text-gray-500\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Building_Calendar_CheckCircle_Clock_Eye_FileText_Plus_RefreshCw_Search_Star_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                            className: \"h-3 w-3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/applications/page.tsx\",\n                                                                            lineNumber: 337,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        \"تقدمت في: \",\n                                                                        formatDate(application.submittedAt)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/applications/page.tsx\",\n                                                                    lineNumber: 336,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/applications/page.tsx\",\n                                                            lineNumber: 330,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/applications/page.tsx\",\n                                                        lineNumber: 329,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"font-medium text-green-600\",\n                                                                    children: application.proposedBudget ? formatPrice(application.proposedBudget) : \"-\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/applications/page.tsx\",\n                                                                    lineNumber: 344,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-xs text-gray-500\",\n                                                                    children: [\n                                                                        \"من أصل \",\n                                                                        formatPrice(application.tender.budget)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/applications/page.tsx\",\n                                                                    lineNumber: 347,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/applications/page.tsx\",\n                                                            lineNumber: 343,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/applications/page.tsx\",\n                                                        lineNumber: 342,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm\",\n                                                            children: application.timeline || \"-\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/applications/page.tsx\",\n                                                            lineNumber: 353,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/applications/page.tsx\",\n                                                        lineNumber: 352,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                        children: getStatusBadge(application.status)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/applications/page.tsx\",\n                                                        lineNumber: 355,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                        children: application.score ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Building_Calendar_CheckCircle_Clock_Eye_FileText_Plus_RefreshCw_Search_Star_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-yellow-500\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/applications/page.tsx\",\n                                                                    lineNumber: 361,\n                                                                    columnNumber: 31\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium\",\n                                                                    children: [\n                                                                        application.score,\n                                                                        \"/100\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/applications/page.tsx\",\n                                                                    lineNumber: 362,\n                                                                    columnNumber: 31\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/applications/page.tsx\",\n                                                            lineNumber: 360,\n                                                            columnNumber: 29\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-400\",\n                                                            children: \"-\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/applications/page.tsx\",\n                                                            lineNumber: 365,\n                                                            columnNumber: 29\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/applications/page.tsx\",\n                                                        lineNumber: 358,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                    variant: \"outline\",\n                                                                    size: \"sm\",\n                                                                    onClick: ()=>router.push(\"/tenders/\".concat(application.tender._id)),\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Building_Calendar_CheckCircle_Clock_Eye_FileText_Plus_RefreshCw_Search_Star_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                            className: \"h-4 w-4 ml-1\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/applications/page.tsx\",\n                                                                            lineNumber: 375,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        \"عرض\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/applications/page.tsx\",\n                                                                    lineNumber: 370,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                application.status === \"pending\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                    variant: \"destructive\",\n                                                                    size: \"sm\",\n                                                                    onClick: ()=>withdrawApplication(application._id),\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Building_Calendar_CheckCircle_Clock_Eye_FileText_Plus_RefreshCw_Search_Star_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                            className: \"h-4 w-4 ml-1\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/applications/page.tsx\",\n                                                                            lineNumber: 384,\n                                                                            columnNumber: 33\n                                                                        }, this),\n                                                                        \"سحب\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/applications/page.tsx\",\n                                                                    lineNumber: 379,\n                                                                    columnNumber: 31\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/applications/page.tsx\",\n                                                            lineNumber: 369,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/applications/page.tsx\",\n                                                        lineNumber: 368,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, application._id, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/applications/page.tsx\",\n                                                lineNumber: 328,\n                                                columnNumber: 23\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/applications/page.tsx\",\n                                        lineNumber: 326,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/applications/page.tsx\",\n                                lineNumber: 315,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/applications/page.tsx\",\n                            lineNumber: 314,\n                            columnNumber: 15\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center py-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Building_Calendar_CheckCircle_Clock_Eye_FileText_Plus_RefreshCw_Search_Star_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                    className: \"h-16 w-16 text-gray-400 mx-auto mb-4\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/applications/page.tsx\",\n                                    lineNumber: 397,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-medium text-gray-900 mb-2\",\n                                    children: searchTerm || statusFilter !== \"all\" ? \"لا توجد طلبات تطابق البحث\" : \"لا توجد طلبات\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/applications/page.tsx\",\n                                    lineNumber: 398,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 mb-6\",\n                                    children: searchTerm || statusFilter !== \"all\" ? \"جرب تغيير معايير البحث أو الفلترة\" : \"ابدأ بتصفح المناقصات وتقديم طلباتك\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/applications/page.tsx\",\n                                    lineNumber: 404,\n                                    columnNumber: 17\n                                }, this),\n                                !searchTerm && statusFilter === \"all\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    onClick: ()=>router.push(\"/tenders\"),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Building_Calendar_CheckCircle_Clock_Eye_FileText_Plus_RefreshCw_Search_Star_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"h-4 w-4 ml-2\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/applications/page.tsx\",\n                                            lineNumber: 412,\n                                            columnNumber: 21\n                                        }, this),\n                                        \"تصفح المناقصات\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/applications/page.tsx\",\n                                    lineNumber: 411,\n                                    columnNumber: 19\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/applications/page.tsx\",\n                            lineNumber: 396,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/applications/page.tsx\",\n                        lineNumber: 312,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/applications/page.tsx\",\n                lineNumber: 302,\n                columnNumber: 9\n            }, this),\n            filteredApplications.some((app)=>app.reviewNotes) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                            children: \"ملاحظات المراجعة\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/applications/page.tsx\",\n                            lineNumber: 425,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/applications/page.tsx\",\n                        lineNumber: 424,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: filteredApplications.filter((app)=>app.reviewNotes).map((application)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-4 bg-gray-50 rounded-lg\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between mb-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-medium\",\n                                                    children: application.tender.title\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/applications/page.tsx\",\n                                                    lineNumber: 434,\n                                                    columnNumber: 25\n                                                }, this),\n                                                getStatusBadge(application.status)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/applications/page.tsx\",\n                                            lineNumber: 433,\n                                            columnNumber: 23\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-700 text-sm\",\n                                            children: application.reviewNotes\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/applications/page.tsx\",\n                                            lineNumber: 437,\n                                            columnNumber: 23\n                                        }, this),\n                                        application.score && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-1 mt-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Building_Calendar_CheckCircle_Clock_Eye_FileText_Plus_RefreshCw_Search_Star_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                    className: \"h-4 w-4 text-yellow-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/applications/page.tsx\",\n                                                    lineNumber: 440,\n                                                    columnNumber: 27\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-medium\",\n                                                    children: [\n                                                        \"التقييم: \",\n                                                        application.score,\n                                                        \"/100\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/applications/page.tsx\",\n                                                    lineNumber: 441,\n                                                    columnNumber: 27\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/applications/page.tsx\",\n                                            lineNumber: 439,\n                                            columnNumber: 25\n                                        }, this)\n                                    ]\n                                }, application._id, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/applications/page.tsx\",\n                                    lineNumber: 432,\n                                    columnNumber: 21\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/applications/page.tsx\",\n                            lineNumber: 428,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/applications/page.tsx\",\n                        lineNumber: 427,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/applications/page.tsx\",\n                lineNumber: 423,\n                columnNumber: 11\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/applications/page.tsx\",\n        lineNumber: 190,\n        columnNumber: 5\n    }, this);\n}\n_s(UserApplicationsPage, \"C1iXwJ4IgPSnrwP9Ak8Td6aP6M8=\", false, function() {\n    return [\n        _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_8__.useToast,\n        next_navigation__WEBPACK_IMPORTED_MODULE_9__.useRouter\n    ];\n});\n_c = UserApplicationsPage;\nvar _c;\n$RefreshReg$(_c, \"UserApplicationsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/user/applications/page.tsx\n"));

/***/ })

});