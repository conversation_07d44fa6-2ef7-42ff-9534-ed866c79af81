"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/user/bids/page",{

/***/ "(app-pages-browser)/./components/CurrencySelector.tsx":
/*!*****************************************!*\
  !*** ./components/CurrencySelector.tsx ***!
  \*****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CurrencySelector: function() { return /* binding */ CurrencySelector; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./components/ui/select.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./components/ui/label.tsx\");\n/* harmony import */ var _contexts_CurrencyContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/contexts/CurrencyContext */ \"(app-pages-browser)/./contexts/CurrencyContext.tsx\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./hooks/use-toast.ts\");\n/* __next_internal_client_entry_do_not_use__ CurrencySelector auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nconst currencies = [\n    {\n        code: \"SAR\",\n        symbol: \"ر.س\",\n        name: \"SAR\"\n    },\n    {\n        code: \"EUR\",\n        symbol: \"€\",\n        name: \"EUR\"\n    },\n    {\n        code: \"GBP\",\n        symbol: \"\\xa3\",\n        name: \"GBP\"\n    },\n    {\n        code: \"AED\",\n        symbol: \"د.إ\",\n        name: \"AED\"\n    },\n    {\n        code: \"KWD\",\n        symbol: \"د.ك\",\n        name: \"KWD\"\n    },\n    {\n        code: \"QAR\",\n        symbol: \"ر.ق\",\n        name: \"QAR\"\n    },\n    {\n        code: \"BHD\",\n        symbol: \"د.ب\",\n        name: \"BHD\"\n    },\n    {\n        code: \"OMR\",\n        symbol: \"ر.ع\",\n        name: \"OMR\"\n    }\n];\n// All currencies including USD for reference\nconst allCurrencies = [\n    {\n        code: \"USD\",\n        symbol: \"$\",\n        name: \"USD\"\n    },\n    ...currencies\n];\nfunction CurrencySelector(param) {\n    let { showLabel = true, className = \"\" } = param;\n    _s();\n    const { userCurrency, setUserCurrency, isLoading } = (0,_contexts_CurrencyContext__WEBPACK_IMPORTED_MODULE_4__.useCurrency)();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_5__.useToast)();\n    const handleCurrencyChange = async (newCurrency)=>{\n        try {\n            await setUserCurrency(newCurrency);\n            const selectedCurrency = currencies.find((c)=>c.code === newCurrency);\n            toast({\n                title: \"\\uD83D\\uDCB1 تم تغيير العملة\",\n                description: \"تم التحويل إلى \".concat(selectedCurrency === null || selectedCurrency === void 0 ? void 0 : selectedCurrency.symbol),\n                duration: 3000\n            });\n        } catch (error) {\n            console.error(\"Failed to change currency:\", error);\n            toast({\n                title: \"❌ خطأ في تغيير العملة\",\n                description: \"حدث خطأ أثناء تغيير العملة\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center gap-2 \".concat(className),\n            children: [\n                showLabel && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_3__.Label, {\n                    children: \"العملة:\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/CurrencySelector.tsx\",\n                    lineNumber: 58,\n                    columnNumber: 23\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-32 h-10 bg-gray-200 animate-pulse rounded\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/CurrencySelector.tsx\",\n                    lineNumber: 59,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/CurrencySelector.tsx\",\n            lineNumber: 57,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-center gap-2 \".concat(className),\n        children: [\n            showLabel && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_3__.Label, {\n                htmlFor: \"currency-selector\",\n                children: \"العملة:\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/CurrencySelector.tsx\",\n                lineNumber: 66,\n                columnNumber: 21\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.Select, {\n                value: userCurrency,\n                onValueChange: handleCurrencyChange,\n                disabled: isLoading,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectTrigger, {\n                        id: \"currency-selector\",\n                        className: \"w-32\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectValue, {}, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/CurrencySelector.tsx\",\n                            lineNumber: 73,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/CurrencySelector.tsx\",\n                        lineNumber: 72,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectContent, {\n                        children: currencies.map((currency)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectItem, {\n                                value: currency.code,\n                                children: [\n                                    currency.symbol,\n                                    \" \",\n                                    currency.name\n                                ]\n                            }, currency.code, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/CurrencySelector.tsx\",\n                                lineNumber: 77,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/CurrencySelector.tsx\",\n                        lineNumber: 75,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/CurrencySelector.tsx\",\n                lineNumber: 67,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/CurrencySelector.tsx\",\n        lineNumber: 65,\n        columnNumber: 5\n    }, this);\n}\n_s(CurrencySelector, \"AFLOWcFHjv8huGXTRXA2/cq33X8=\", false, function() {\n    return [\n        _contexts_CurrencyContext__WEBPACK_IMPORTED_MODULE_4__.useCurrency,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_5__.useToast\n    ];\n});\n_c = CurrencySelector;\nvar _c;\n$RefreshReg$(_c, \"CurrencySelector\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/CurrencySelector.tsx\n"));

/***/ })

});