"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/user/leaderboard/page",{

/***/ "(app-pages-browser)/./app/user/leaderboard/page.tsx":
/*!***************************************!*\
  !*** ./app/user/leaderboard/page.tsx ***!
  \***************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ LeaderboardPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/avatar */ \"(app-pages-browser)/./components/ui/avatar.tsx\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,Calendar,Crown,Gavel,Medal,RefreshCw,Star,Target,TrendingUp,Trophy,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/crown.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,Calendar,Crown,Gavel,Medal,RefreshCw,Star,Target,TrendingUp,Trophy,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trophy.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,Calendar,Crown,Gavel,Medal,RefreshCw,Star,Target,TrendingUp,Trophy,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/medal.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,Calendar,Crown,Gavel,Medal,RefreshCw,Star,Target,TrendingUp,Trophy,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,Calendar,Crown,Gavel,Medal,RefreshCw,Star,Target,TrendingUp,Trophy,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,Calendar,Crown,Gavel,Medal,RefreshCw,Star,Target,TrendingUp,Trophy,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,Calendar,Crown,Gavel,Medal,RefreshCw,Star,Target,TrendingUp,Trophy,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,Calendar,Crown,Gavel,Medal,RefreshCw,Star,Target,TrendingUp,Trophy,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,Calendar,Crown,Gavel,Medal,RefreshCw,Star,Target,TrendingUp,Trophy,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,Calendar,Crown,Gavel,Medal,RefreshCw,Star,Target,TrendingUp,Trophy,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,Calendar,Crown,Gavel,Medal,RefreshCw,Star,Target,TrendingUp,Trophy,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/gavel.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,Calendar,Crown,Gavel,Medal,RefreshCw,Star,Target,TrendingUp,Trophy,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/use-toast */ \"(app-pages-browser)/./components/ui/use-toast.tsx\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./lib/api.ts\");\n/* harmony import */ var _contexts_CurrencyContext__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/contexts/CurrencyContext */ \"(app-pages-browser)/./contexts/CurrencyContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nconst getBadgeIcon = (badge)=>{\n    switch(badge){\n        case \"Champion\":\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                className: \"h-4 w-4\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                lineNumber: 50,\n                columnNumber: 14\n            }, undefined);\n        case \"Master\":\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                className: \"h-4 w-4\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                lineNumber: 52,\n                columnNumber: 14\n            }, undefined);\n        case \"Expert\":\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                className: \"h-4 w-4\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                lineNumber: 54,\n                columnNumber: 14\n            }, undefined);\n        case \"Pro\":\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                className: \"h-4 w-4\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                lineNumber: 56,\n                columnNumber: 14\n            }, undefined);\n        case \"Active\":\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                className: \"h-4 w-4\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                lineNumber: 58,\n                columnNumber: 14\n            }, undefined);\n        default:\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                className: \"h-4 w-4\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                lineNumber: 60,\n                columnNumber: 14\n            }, undefined);\n    }\n};\nconst getBadgeColor = (badge)=>{\n    switch(badge){\n        case \"Champion\":\n            return \"bg-yellow-500 text-white\";\n        case \"Master\":\n            return \"bg-purple-500 text-white\";\n        case \"Expert\":\n            return \"bg-blue-500 text-white\";\n        case \"Pro\":\n            return \"bg-green-500 text-white\";\n        case \"Active\":\n            return \"bg-orange-500 text-white\";\n        default:\n            return \"bg-gray-500 text-white\";\n    }\n};\nconst getRankIcon = (rank)=>{\n    if (rank === 1) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n        className: \"h-6 w-6 text-yellow-500\"\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n        lineNumber: 82,\n        columnNumber: 26\n    }, undefined);\n    if (rank === 2) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n        className: \"h-6 w-6 text-gray-400\"\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n        lineNumber: 83,\n        columnNumber: 26\n    }, undefined);\n    if (rank === 3) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n        className: \"h-6 w-6 text-amber-600\"\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n        lineNumber: 84,\n        columnNumber: 26\n    }, undefined);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n        className: \"text-lg font-bold text-gray-600\",\n        children: [\n            \"#\",\n            rank\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n        lineNumber: 85,\n        columnNumber: 10\n    }, undefined);\n};\nfunction LeaderboardPage() {\n    _s();\n    const [leaderboard, setLeaderboard] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [refreshing, setRefreshing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedPeriod, setSelectedPeriod] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all-time\");\n    const { formatAmount } = (0,_contexts_CurrencyContext__WEBPACK_IMPORTED_MODULE_8__.useCurrency)();\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const { toast } = (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_6__.useToast)();\n    const fetchLeaderboard = async function() {\n        let period = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : selectedPeriod;\n        try {\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_7__[\"default\"].get(\"/users/leaderboard?period=\".concat(period, \"&limit=50\"));\n            if (response.data.success) {\n                setLeaderboard(response.data.data.leaderboard);\n                setError(null);\n            } else {\n                throw new Error(\"فشل في تحميل لوحة الصدارة\");\n            }\n        } catch (err) {\n            console.error(\"Leaderboard error:\", err);\n            setError(err instanceof Error ? err.message : \"حدث خطأ غير متوقع\");\n            toast({\n                title: \"خطأ في التحميل\",\n                description: \"حدث خطأ في تحميل بيانات المتصدرين\",\n                variant: \"destructive\"\n            });\n            setLeaderboard([]);\n        } finally{\n            setLoading(false);\n            setRefreshing(false);\n        }\n    };\n    const handleRefresh = ()=>{\n        setRefreshing(true);\n        fetchLeaderboard();\n    };\n    const handlePeriodChange = (period)=>{\n        setSelectedPeriod(period);\n        setLoading(true);\n        fetchLeaderboard(period);\n    };\n    const getPeriodLabel = (period)=>{\n        switch(period){\n            case \"daily\":\n                return \"اليوم\";\n            case \"weekly\":\n                return \"الأسبوع\";\n            case \"monthly\":\n                return \"الشهر\";\n            case \"all-time\":\n                return \"جميع الأوقات\";\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchLeaderboard();\n    }, []);\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center min-h-[400px]\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4 bg-primary/10 rounded-full w-20 h-20 mx-auto mb-6 flex items-center justify-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                            className: \"h-8 w-8 animate-spin text-primary\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                            lineNumber: 154,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                        lineNumber: 153,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold mb-2\",\n                        children: \"جاري تحميل لوحة الصدارة\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                        lineNumber: 156,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"يرجى الانتظار بينما نحضر أحدث البيانات...\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                        lineNumber: 157,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                lineNumber: 152,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n            lineNumber: 151,\n            columnNumber: 7\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center min-h-[400px]\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                className: \"max-w-md mx-auto bg-gradient-to-br from-red-50 to-orange-50 border-red-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                    className: \"pt-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-4 bg-red-100 rounded-full w-20 h-20 mx-auto mb-6 flex items-center justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    className: \"h-10 w-10 text-red-500\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                    lineNumber: 170,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                lineNumber: 169,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-semibold mb-3 text-red-700\",\n                                children: \"خطأ في تحميل البيانات\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                lineNumber: 172,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mb-6\",\n                                children: error\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                lineNumber: 173,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                onClick: handleRefresh,\n                                className: \"w-full bg-gradient-to-r from-red-500 to-orange-500 hover:from-red-600 hover:to-orange-600\",\n                                disabled: refreshing,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2 \".concat(refreshing ? \"animate-spin\" : \"\")\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                        lineNumber: 179,\n                                        columnNumber: 17\n                                    }, this),\n                                    \"إعادة المحاولة\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                lineNumber: 174,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                        lineNumber: 168,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                    lineNumber: 167,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                lineNumber: 166,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n            lineNumber: 165,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gradient-to-r from-yellow-500 to-orange-500 text-white rounded-lg p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-3xl font-bold flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"h-8 w-8 mr-3\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                lineNumber: 196,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"لوحة الصدارة\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                        lineNumber: 195,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"mt-2 opacity-90\",\n                                        children: [\n                                            \"تصنيف أفضل المزايدين في المنصة - \",\n                                            getPeriodLabel(selectedPeriod)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                        lineNumber: 199,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                lineNumber: 194,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                onClick: handleRefresh,\n                                disabled: refreshing,\n                                variant: \"secondary\",\n                                className: \"bg-white/20 hover:bg-white/30 text-white border-white/30\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2 \".concat(refreshing ? \"animate-spin\" : \"\")\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                        lineNumber: 209,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"تحديث\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                lineNumber: 203,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                        lineNumber: 193,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2 space-x-reverse\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                    className: \"h-5 w-5 text-white/80\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                    lineNumber: 217,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm font-medium text-white/90 ml-3\",\n                                    children: \"الفترة الزمنية:\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                    lineNumber: 218,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-2 space-x-reverse\",\n                                    children: [\n                                        \"daily\",\n                                        \"weekly\",\n                                        \"monthly\",\n                                        \"all-time\"\n                                    ].map((period)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            variant: selectedPeriod === period ? \"secondary\" : \"outline\",\n                                            size: \"sm\",\n                                            onClick: ()=>handlePeriodChange(period),\n                                            disabled: loading,\n                                            className: selectedPeriod === period ? \"bg-white text-orange-600 hover:bg-white/90\" : \"bg-white/20 text-white border-white/30 hover:bg-white/30\",\n                                            children: getPeriodLabel(period)\n                                        }, period, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                            lineNumber: 221,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                    lineNumber: 219,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                            lineNumber: 216,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                        lineNumber: 215,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                lineNumber: 192,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-4 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        className: \"bg-gradient-to-br from-blue-50 to-blue-100 border-blue-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            className: \"pt-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-3 bg-blue-500 rounded-full\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            className: \"h-6 w-6 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                            lineNumber: 246,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                        lineNumber: 245,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mr-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-blue-700\",\n                                                children: leaderboard.length\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                lineNumber: 249,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-blue-600 text-sm\",\n                                                children: \"مزايد نشط\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                lineNumber: 250,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                        lineNumber: 248,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                lineNumber: 244,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                            lineNumber: 243,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                        lineNumber: 242,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        className: \"bg-gradient-to-br from-green-50 to-green-100 border-green-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            className: \"pt-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-3 bg-green-500 rounded-full\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                            className: \"h-6 w-6 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                            lineNumber: 260,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                        lineNumber: 259,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mr-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-green-700\",\n                                                children: Array.isArray(leaderboard) ? leaderboard.reduce((sum, entry)=>sum + entry.totalBids, 0) : 0\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                lineNumber: 263,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-green-600 text-sm\",\n                                                children: \"إجمالي المزايدات\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                lineNumber: 266,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                        lineNumber: 262,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                lineNumber: 258,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                            lineNumber: 257,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                        lineNumber: 256,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        className: \"bg-gradient-to-br from-purple-50 to-purple-100 border-purple-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            className: \"pt-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-3 bg-purple-500 rounded-full\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            className: \"h-6 w-6 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                            lineNumber: 276,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                        lineNumber: 275,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mr-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-purple-700\",\n                                                children: Array.isArray(leaderboard) ? leaderboard.reduce((sum, entry)=>sum + entry.points, 0) : 0\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                lineNumber: 279,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-purple-600 text-sm\",\n                                                children: \"إجمالي النقاط\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                lineNumber: 282,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                        lineNumber: 278,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                lineNumber: 274,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                            lineNumber: 273,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                        lineNumber: 272,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        className: \"bg-gradient-to-br from-orange-50 to-orange-100 border-orange-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            className: \"pt-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-3 bg-orange-500 rounded-full\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"h-6 w-6 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                            lineNumber: 292,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                        lineNumber: 291,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mr-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-orange-700\",\n                                                children: Array.isArray(leaderboard) ? formatAmount(leaderboard.reduce((sum, entry)=>sum + entry.totalAmount, 0)) : formatAmount(0)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                lineNumber: 295,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-orange-600 text-sm\",\n                                                children: \"إجمالي المبالغ\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                lineNumber: 298,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                        lineNumber: 294,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                lineNumber: 290,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                            lineNumber: 289,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                        lineNumber: 288,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                lineNumber: 241,\n                columnNumber: 9\n            }, this),\n            leaderboard.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                className: \"bg-gradient-to-br from-gray-50 to-gray-100\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                    className: \"pt-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-4 bg-gray-200 rounded-full w-20 h-20 mx-auto mb-6 flex items-center justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    className: \"h-10 w-10 text-gray-400\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                    lineNumber: 311,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                lineNumber: 310,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-semibold mb-3 text-gray-700\",\n                                children: \"لا توجد بيانات حالياً\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                lineNumber: 313,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mb-6 max-w-md mx-auto\",\n                                children: \"لم يتم العثور على أي مزايدات بعد. ابدأ بالمزايدة لتظهر في لوحة الصدارة!\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                lineNumber: 314,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                className: \"bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                        lineNumber: 318,\n                                        columnNumber: 19\n                                    }, this),\n                                    \"ابدأ المزايدة الآن\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                lineNumber: 317,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                        lineNumber: 309,\n                        columnNumber: 15\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                    lineNumber: 308,\n                    columnNumber: 13\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                lineNumber: 307,\n                columnNumber: 11\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-4\",\n                children: [\n                    leaderboard.slice(0, 3).map((entry, index)=>{\n                        var _entry_user_profile;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            className: \"relative overflow-hidden \".concat(index === 0 ? \"bg-gradient-to-r from-yellow-50 to-orange-50 border-2 border-yellow-300 shadow-lg\" : index === 1 ? \"bg-gradient-to-r from-gray-50 to-slate-50 border-2 border-gray-300 shadow-md\" : \"bg-gradient-to-r from-amber-50 to-orange-50 border-2 border-amber-300 shadow-md\"),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute top-0 right-0 w-16 h-16 \".concat(index === 0 ? \"bg-gradient-to-br from-yellow-400 to-yellow-600\" : index === 1 ? \"bg-gradient-to-br from-gray-400 to-gray-600\" : \"bg-gradient-to-br from-amber-400 to-amber-600\", \" transform rotate-45 translate-x-6 -translate-y-6\"),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute bottom-2 left-2 text-white font-bold text-sm transform -rotate-45\",\n                                        children: [\n                                            \"#\",\n                                            entry.rank\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                        lineNumber: 342,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                    lineNumber: 337,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    className: \"pt-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-4 space-x-reverse\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-shrink-0 w-16 h-16 flex items-center justify-center\",\n                                                        children: getRankIcon(entry.rank)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                        lineNumber: 351,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1 min-w-0 flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__.Avatar, {\n                                                                className: \"mr-4 w-12 h-12 border-2 border-white shadow-md\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__.AvatarImage, {\n                                                                        src: entry.user.profile.avatarUrl,\n                                                                        alt: \"Avatar\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                                        lineNumber: 358,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__.AvatarFallback, {\n                                                                        className: \"bg-gradient-to-br from-blue-400 to-purple-500 text-white\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                            className: \"h-6 w-6\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                                            lineNumber: 360,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                                        lineNumber: 359,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                                lineNumber: 357,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"text-xl font-bold text-gray-900 truncate\",\n                                                                        children: ((_entry_user_profile = entry.user.profile) === null || _entry_user_profile === void 0 ? void 0 : _entry_user_profile.fullName) || entry.user.email\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                                        lineNumber: 364,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center space-x-3 space-x-reverse mt-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                                                                className: \"\".concat(getBadgeColor(entry.badge), \" flex items-center px-3 py-1\"),\n                                                                                children: [\n                                                                                    getBadgeIcon(entry.badge),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"mr-1 font-medium\",\n                                                                                        children: entry.badge\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                                                        lineNumber: 370,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                                                lineNumber: 368,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm font-medium text-gray-600 bg-white px-2 py-1 rounded-full\",\n                                                                                children: [\n                                                                                    entry.points,\n                                                                                    \" نقطة\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                                                lineNumber: 372,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                                        lineNumber: 367,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                                lineNumber: 363,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                        lineNumber: 356,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                lineNumber: 349,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex space-x-8 space-x-reverse text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-white/70 rounded-lg p-3 min-w-[80px]\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-2xl font-bold text-blue-600\",\n                                                                children: entry.totalBids\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                                lineNumber: 383,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-600 font-medium\",\n                                                                children: \"مزايدة\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                                lineNumber: 384,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                        lineNumber: 382,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-white/70 rounded-lg p-3 min-w-[100px]\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-2xl font-bold text-green-600\",\n                                                                children: formatAmount(entry.totalAmount)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                                lineNumber: 387,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-600 font-medium\",\n                                                                children: \"المبلغ\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                                lineNumber: 390,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                        lineNumber: 386,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-white/70 rounded-lg p-3 min-w-[80px]\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-2xl font-bold text-purple-600\",\n                                                                children: entry.wonAuctions\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                                lineNumber: 393,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-600 font-medium\",\n                                                                children: \"فوز\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                                lineNumber: 394,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                        lineNumber: 392,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                lineNumber: 381,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                        lineNumber: 348,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                    lineNumber: 347,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, entry._id, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                            lineNumber: 328,\n                            columnNumber: 15\n                        }, this);\n                    }),\n                    leaderboard.slice(3).map((entry, index)=>{\n                        var _entry_user_profile;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            className: \"hover:shadow-md transition-shadow duration-200\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                className: \"pt-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-4 space-x-reverse\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-shrink-0 w-12 h-12 flex items-center justify-center bg-gray-100 rounded-full\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-lg font-bold text-gray-600\",\n                                                        children: [\n                                                            \"#\",\n                                                            entry.rank\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                        lineNumber: 410,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                    lineNumber: 409,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1 min-w-0 flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__.Avatar, {\n                                                            className: \"mr-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__.AvatarImage, {\n                                                                    src: entry.user.profile.avatarUrl,\n                                                                    alt: \"Avatar\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                                    lineNumber: 416,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__.AvatarFallback, {\n                                                                    className: \"bg-gradient-to-br from-gray-400 to-gray-500 text-white\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                                        lineNumber: 418,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                                    lineNumber: 417,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                            lineNumber: 415,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-lg font-semibold text-gray-900 truncate\",\n                                                                    children: ((_entry_user_profile = entry.user.profile) === null || _entry_user_profile === void 0 ? void 0 : _entry_user_profile.fullName) || entry.user.email\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                                    lineNumber: 422,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-2 space-x-reverse mt-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                                                            className: \"\".concat(getBadgeColor(entry.badge), \" flex items-center\"),\n                                                                            children: [\n                                                                                getBadgeIcon(entry.badge),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"mr-1\",\n                                                                                    children: entry.badge\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                                                    lineNumber: 428,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                                            lineNumber: 426,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: [\n                                                                                entry.points,\n                                                                                \" نقطة\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                                            lineNumber: 430,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                                    lineNumber: 425,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                            lineNumber: 421,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                    lineNumber: 414,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                            lineNumber: 407,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex space-x-6 space-x-reverse text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xl font-bold text-blue-600\",\n                                                            children: entry.totalBids\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                            lineNumber: 441,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-gray-600\",\n                                                            children: \"مزايدة\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                            lineNumber: 442,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                    lineNumber: 440,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xl font-bold text-green-600\",\n                                                            children: formatAmount(entry.totalAmount)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                            lineNumber: 445,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-gray-600\",\n                                                            children: \"المبلغ\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                            lineNumber: 448,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                    lineNumber: 444,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xl font-bold text-purple-600\",\n                                                            children: entry.wonAuctions\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                            lineNumber: 451,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-gray-600\",\n                                                            children: \"فوز\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                            lineNumber: 452,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                    lineNumber: 450,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                            lineNumber: 439,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                    lineNumber: 406,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                lineNumber: 405,\n                                columnNumber: 17\n                            }, this)\n                        }, entry._id, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                            lineNumber: 404,\n                            columnNumber: 15\n                        }, this);\n                    })\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                lineNumber: 325,\n                columnNumber: 11\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                className: \"bg-gradient-to-r from-blue-50 to-purple-50 border-blue-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                    className: \"pt-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-center mb-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"h-5 w-5 text-yellow-500 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                        lineNumber: 467,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"font-semibold text-gray-700\",\n                                        children: \"كيف يتم حساب النقاط؟\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                        lineNumber: 468,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                lineNumber: 466,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-600\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                className: \"h-4 w-4 text-blue-500 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                lineNumber: 472,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"+10 نقاط لكل مزايدة\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                lineNumber: 473,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                        lineNumber: 471,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"h-4 w-4 text-green-500 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                lineNumber: 476,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"+50 نقطة لكل فوز\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                lineNumber: 477,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                        lineNumber: 475,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                className: \"h-4 w-4 text-purple-500 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                lineNumber: 480,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"نقاط إضافية حسب القيمة\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                lineNumber: 481,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                        lineNumber: 479,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                lineNumber: 470,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xs text-gray-500 mt-4\",\n                                children: \"يتم تحديث لوحة الصدارة كل ساعة. النقاط تُحسب بناءً على النشاط والأداء في المزايدات.\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                lineNumber: 484,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                        lineNumber: 465,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                    lineNumber: 464,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                lineNumber: 463,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n        lineNumber: 190,\n        columnNumber: 5\n    }, this);\n}\n_s(LeaderboardPage, \"ZHZbZ+c1MLBPy+d+IX1ZCTrFcGI=\", false, function() {\n    return [\n        _contexts_CurrencyContext__WEBPACK_IMPORTED_MODULE_8__.useCurrency,\n        _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_6__.useToast\n    ];\n});\n_c = LeaderboardPage;\nvar _c;\n$RefreshReg$(_c, \"LeaderboardPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/user/leaderboard/page.tsx\n"));

/***/ })

});