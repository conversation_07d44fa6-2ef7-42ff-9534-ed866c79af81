"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/user/leaderboard/page",{

/***/ "(app-pages-browser)/./app/user/leaderboard/page.tsx":
/*!***************************************!*\
  !*** ./app/user/leaderboard/page.tsx ***!
  \***************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ LeaderboardPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/avatar */ \"(app-pages-browser)/./components/ui/avatar.tsx\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,Calendar,Crown,Gavel,Medal,RefreshCw,Star,Target,TrendingUp,Trophy,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/crown.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,Calendar,Crown,Gavel,Medal,RefreshCw,Star,Target,TrendingUp,Trophy,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trophy.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,Calendar,Crown,Gavel,Medal,RefreshCw,Star,Target,TrendingUp,Trophy,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/medal.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,Calendar,Crown,Gavel,Medal,RefreshCw,Star,Target,TrendingUp,Trophy,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,Calendar,Crown,Gavel,Medal,RefreshCw,Star,Target,TrendingUp,Trophy,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,Calendar,Crown,Gavel,Medal,RefreshCw,Star,Target,TrendingUp,Trophy,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,Calendar,Crown,Gavel,Medal,RefreshCw,Star,Target,TrendingUp,Trophy,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,Calendar,Crown,Gavel,Medal,RefreshCw,Star,Target,TrendingUp,Trophy,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,Calendar,Crown,Gavel,Medal,RefreshCw,Star,Target,TrendingUp,Trophy,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,Calendar,Crown,Gavel,Medal,RefreshCw,Star,Target,TrendingUp,Trophy,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,Calendar,Crown,Gavel,Medal,RefreshCw,Star,Target,TrendingUp,Trophy,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/gavel.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,Calendar,Crown,Gavel,Medal,RefreshCw,Star,Target,TrendingUp,Trophy,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/use-toast */ \"(app-pages-browser)/./components/ui/use-toast.tsx\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./lib/api.ts\");\n/* harmony import */ var _contexts_CurrencyContext__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/contexts/CurrencyContext */ \"(app-pages-browser)/./contexts/CurrencyContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nconst getBadgeIcon = (badge)=>{\n    switch(badge){\n        case \"Champion\":\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                className: \"h-4 w-4\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                lineNumber: 50,\n                columnNumber: 14\n            }, undefined);\n        case \"Master\":\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                className: \"h-4 w-4\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                lineNumber: 52,\n                columnNumber: 14\n            }, undefined);\n        case \"Expert\":\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                className: \"h-4 w-4\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                lineNumber: 54,\n                columnNumber: 14\n            }, undefined);\n        case \"Pro\":\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                className: \"h-4 w-4\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                lineNumber: 56,\n                columnNumber: 14\n            }, undefined);\n        case \"Active\":\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                className: \"h-4 w-4\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                lineNumber: 58,\n                columnNumber: 14\n            }, undefined);\n        default:\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                className: \"h-4 w-4\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                lineNumber: 60,\n                columnNumber: 14\n            }, undefined);\n    }\n};\nconst getBadgeColor = (badge)=>{\n    switch(badge){\n        case \"Champion\":\n            return \"bg-yellow-500 text-white\";\n        case \"Master\":\n            return \"bg-purple-500 text-white\";\n        case \"Expert\":\n            return \"bg-blue-500 text-white\";\n        case \"Pro\":\n            return \"bg-green-500 text-white\";\n        case \"Active\":\n            return \"bg-orange-500 text-white\";\n        default:\n            return \"bg-gray-500 text-white\";\n    }\n};\nconst getRankIcon = (rank)=>{\n    if (rank === 1) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n        className: \"h-6 w-6 text-yellow-500\"\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n        lineNumber: 82,\n        columnNumber: 26\n    }, undefined);\n    if (rank === 2) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n        className: \"h-6 w-6 text-gray-400\"\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n        lineNumber: 83,\n        columnNumber: 26\n    }, undefined);\n    if (rank === 3) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n        className: \"h-6 w-6 text-amber-600\"\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n        lineNumber: 84,\n        columnNumber: 26\n    }, undefined);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n        className: \"text-lg font-bold text-gray-600\",\n        children: [\n            \"#\",\n            rank\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n        lineNumber: 85,\n        columnNumber: 10\n    }, undefined);\n};\nfunction LeaderboardPage() {\n    _s();\n    const [leaderboard, setLeaderboard] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [refreshing, setRefreshing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedPeriod, setSelectedPeriod] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all-time\");\n    const { formatAmount } = (0,_contexts_CurrencyContext__WEBPACK_IMPORTED_MODULE_8__.useCurrency)();\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const { toast } = (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_6__.useToast)();\n    const fetchLeaderboard = async function() {\n        let period = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : selectedPeriod;\n        try {\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_7__[\"default\"].get(\"/users/leaderboard?period=\".concat(period, \"&limit=50\"));\n            if (response.data.success) {\n                setLeaderboard(response.data.data.leaderboard);\n                setError(null);\n            } else {\n                throw new Error(\"فشل في تحميل لوحة الصدارة\");\n            }\n        } catch (err) {\n            console.error(\"Leaderboard error:\", err);\n            setError(err instanceof Error ? err.message : \"حدث خطأ غير متوقع\");\n            toast({\n                title: \"خطأ في التحميل\",\n                description: \"حدث خطأ في تحميل بيانات المتصدرين\",\n                variant: \"destructive\"\n            });\n            setLeaderboard([]);\n        } finally{\n            setLoading(false);\n            setRefreshing(false);\n        }\n    };\n    const handleRefresh = ()=>{\n        setRefreshing(true);\n        fetchLeaderboard();\n    };\n    const handlePeriodChange = (period)=>{\n        setSelectedPeriod(period);\n        setLoading(true);\n        fetchLeaderboard(period);\n    };\n    const getPeriodLabel = (period)=>{\n        switch(period){\n            case \"daily\":\n                return \"اليوم\";\n            case \"weekly\":\n                return \"الأسبوع\";\n            case \"monthly\":\n                return \"الشهر\";\n            case \"all-time\":\n                return \"جميع الأوقات\";\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchLeaderboard();\n    }, []);\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center min-h-[400px]\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4 bg-primary/10 rounded-full w-20 h-20 mx-auto mb-6 flex items-center justify-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                            className: \"h-8 w-8 animate-spin text-primary\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                            lineNumber: 154,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                        lineNumber: 153,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold mb-2\",\n                        children: \"جاري تحميل لوحة الصدارة\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                        lineNumber: 156,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"يرجى الانتظار بينما نحضر أحدث البيانات...\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                        lineNumber: 157,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                lineNumber: 152,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n            lineNumber: 151,\n            columnNumber: 7\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center min-h-[400px]\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                className: \"max-w-md mx-auto bg-gradient-to-br from-red-50 to-orange-50 border-red-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                    className: \"pt-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-4 bg-red-100 rounded-full w-20 h-20 mx-auto mb-6 flex items-center justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    className: \"h-10 w-10 text-red-500\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                    lineNumber: 170,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                lineNumber: 169,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-semibold mb-3 text-red-700\",\n                                children: \"خطأ في تحميل البيانات\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                lineNumber: 172,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mb-6\",\n                                children: error\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                lineNumber: 173,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                onClick: handleRefresh,\n                                className: \"w-full bg-gradient-to-r from-red-500 to-orange-500 hover:from-red-600 hover:to-orange-600\",\n                                disabled: refreshing,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2 \".concat(refreshing ? \"animate-spin\" : \"\")\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                        lineNumber: 179,\n                                        columnNumber: 17\n                                    }, this),\n                                    \"إعادة المحاولة\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                lineNumber: 174,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                        lineNumber: 168,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                    lineNumber: 167,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                lineNumber: 166,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n            lineNumber: 165,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gradient-to-r from-yellow-500 to-orange-500 text-white rounded-lg p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-3xl font-bold flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"h-8 w-8 mr-3\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                lineNumber: 196,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"لوحة الصدارة\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                        lineNumber: 195,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"mt-2 opacity-90\",\n                                        children: [\n                                            \"تصنيف أفضل المزايدين في المنصة - \",\n                                            getPeriodLabel(selectedPeriod)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                        lineNumber: 199,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                lineNumber: 194,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                onClick: handleRefresh,\n                                disabled: refreshing,\n                                variant: \"secondary\",\n                                className: \"bg-white/20 hover:bg-white/30 text-white border-white/30\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2 \".concat(refreshing ? \"animate-spin\" : \"\")\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                        lineNumber: 209,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"تحديث\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                lineNumber: 203,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                        lineNumber: 193,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2 space-x-reverse\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                    className: \"h-5 w-5 text-white/80\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                    lineNumber: 217,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm font-medium text-white/90 ml-3\",\n                                    children: \"الفترة الزمنية:\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                    lineNumber: 218,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-2 space-x-reverse\",\n                                    children: [\n                                        \"daily\",\n                                        \"weekly\",\n                                        \"monthly\",\n                                        \"all-time\"\n                                    ].map((period)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            variant: selectedPeriod === period ? \"secondary\" : \"outline\",\n                                            size: \"sm\",\n                                            onClick: ()=>handlePeriodChange(period),\n                                            disabled: loading,\n                                            className: selectedPeriod === period ? \"bg-white text-orange-600 hover:bg-white/90\" : \"bg-white/20 text-white border-white/30 hover:bg-white/30\",\n                                            children: getPeriodLabel(period)\n                                        }, period, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                            lineNumber: 221,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                    lineNumber: 219,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                            lineNumber: 216,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                        lineNumber: 215,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                lineNumber: 192,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-4 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        className: \"bg-gradient-to-br from-blue-50 to-blue-100 border-blue-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            className: \"pt-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-3 bg-blue-500 rounded-full\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            className: \"h-6 w-6 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                            lineNumber: 246,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                        lineNumber: 245,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mr-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-blue-700\",\n                                                children: leaderboard.length\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                lineNumber: 249,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-blue-600 text-sm\",\n                                                children: \"مزايد نشط\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                lineNumber: 250,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                        lineNumber: 248,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                lineNumber: 244,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                            lineNumber: 243,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                        lineNumber: 242,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        className: \"bg-gradient-to-br from-green-50 to-green-100 border-green-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            className: \"pt-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-3 bg-green-500 rounded-full\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                            className: \"h-6 w-6 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                            lineNumber: 260,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                        lineNumber: 259,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mr-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-green-700\",\n                                                children: Array.isArray(leaderboard) ? leaderboard.reduce((sum, entry)=>sum + entry.totalBids, 0) : 0\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                lineNumber: 263,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-green-600 text-sm\",\n                                                children: \"إجمالي المزايدات\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                lineNumber: 266,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                        lineNumber: 262,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                lineNumber: 258,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                            lineNumber: 257,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                        lineNumber: 256,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        className: \"bg-gradient-to-br from-purple-50 to-purple-100 border-purple-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            className: \"pt-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-3 bg-purple-500 rounded-full\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            className: \"h-6 w-6 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                            lineNumber: 276,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                        lineNumber: 275,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mr-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-purple-700\",\n                                                children: Array.isArray(leaderboard) ? leaderboard.reduce((sum, entry)=>sum + entry.points, 0) : 0\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                lineNumber: 279,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-purple-600 text-sm\",\n                                                children: \"إجمالي النقاط\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                lineNumber: 282,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                        lineNumber: 278,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                lineNumber: 274,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                            lineNumber: 273,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                        lineNumber: 272,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        className: \"bg-gradient-to-br from-orange-50 to-orange-100 border-orange-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            className: \"pt-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-3 bg-orange-500 rounded-full\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"h-6 w-6 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                            lineNumber: 292,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                        lineNumber: 291,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mr-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-orange-700\",\n                                                children: Array.isArray(leaderboard) ? formatAmount(leaderboard.reduce((sum, entry)=>sum + entry.totalAmount, 0)) : formatAmount(0)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                lineNumber: 295,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-orange-600 text-sm\",\n                                                children: \"إجمالي المبالغ\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                lineNumber: 298,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                        lineNumber: 294,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                lineNumber: 290,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                            lineNumber: 289,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                        lineNumber: 288,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                lineNumber: 241,\n                columnNumber: 9\n            }, this),\n            leaderboard.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                className: \"bg-gradient-to-br from-gray-50 to-gray-100\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                    className: \"pt-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-4 bg-gray-200 rounded-full w-20 h-20 mx-auto mb-6 flex items-center justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    className: \"h-10 w-10 text-gray-400\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                    lineNumber: 311,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                lineNumber: 310,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-semibold mb-3 text-gray-700\",\n                                children: \"لا توجد بيانات حالياً\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                lineNumber: 313,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mb-6 max-w-md mx-auto\",\n                                children: \"لم يتم العثور على أي مزايدات بعد. ابدأ بالمزايدة لتظهر في لوحة الصدارة!\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                lineNumber: 314,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                className: \"bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                        lineNumber: 318,\n                                        columnNumber: 19\n                                    }, this),\n                                    \"ابدأ المزايدة الآن\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                lineNumber: 317,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                        lineNumber: 309,\n                        columnNumber: 15\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                    lineNumber: 308,\n                    columnNumber: 13\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                lineNumber: 307,\n                columnNumber: 11\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-4\",\n                children: [\n                    leaderboard.slice(0, 3).map((entry, index)=>{\n                        var _entry_user_profile;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            className: \"relative overflow-hidden \".concat(index === 0 ? \"bg-gradient-to-r from-yellow-50 to-orange-50 border-2 border-yellow-300 shadow-lg\" : index === 1 ? \"bg-gradient-to-r from-gray-50 to-slate-50 border-2 border-gray-300 shadow-md\" : \"bg-gradient-to-r from-amber-50 to-orange-50 border-2 border-amber-300 shadow-md\"),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute top-0 right-0 w-16 h-16 \".concat(index === 0 ? \"bg-gradient-to-br from-yellow-400 to-yellow-600\" : index === 1 ? \"bg-gradient-to-br from-gray-400 to-gray-600\" : \"bg-gradient-to-br from-amber-400 to-amber-600\", \" transform rotate-45 translate-x-6 -translate-y-6\"),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute bottom-2 left-2 text-white font-bold text-sm transform -rotate-45\",\n                                        children: [\n                                            \"#\",\n                                            entry.rank\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                        lineNumber: 342,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                    lineNumber: 337,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    className: \"pt-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-4 space-x-reverse\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-shrink-0 w-16 h-16 flex items-center justify-center\",\n                                                        children: getRankIcon(entry.rank)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                        lineNumber: 351,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1 min-w-0 flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__.Avatar, {\n                                                                className: \"mr-4 w-12 h-12 border-2 border-white shadow-md\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__.AvatarImage, {\n                                                                        src: entry.user.profile.avatarUrl,\n                                                                        alt: \"Avatar\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                                        lineNumber: 358,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__.AvatarFallback, {\n                                                                        className: \"bg-gradient-to-br from-blue-400 to-purple-500 text-white\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                            className: \"h-6 w-6\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                                            lineNumber: 360,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                                        lineNumber: 359,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                                lineNumber: 357,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"text-xl font-bold text-gray-900 truncate\",\n                                                                        children: ((_entry_user_profile = entry.user.profile) === null || _entry_user_profile === void 0 ? void 0 : _entry_user_profile.fullName) || entry.user.email\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                                        lineNumber: 364,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center space-x-3 space-x-reverse mt-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                                                                className: \"\".concat(getBadgeColor(entry.badge), \" flex items-center px-3 py-1\"),\n                                                                                children: [\n                                                                                    getBadgeIcon(entry.badge),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"mr-1 font-medium\",\n                                                                                        children: entry.badge\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                                                        lineNumber: 370,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                                                lineNumber: 368,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm font-medium text-gray-600 bg-white px-2 py-1 rounded-full\",\n                                                                                children: [\n                                                                                    entry.points,\n                                                                                    \" نقطة\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                                                lineNumber: 372,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                                        lineNumber: 367,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                                lineNumber: 363,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                        lineNumber: 356,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                lineNumber: 349,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex space-x-8 space-x-reverse text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-white/70 rounded-lg p-3 min-w-[80px]\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-2xl font-bold text-blue-600\",\n                                                                children: entry.totalBids\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                                lineNumber: 383,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-600 font-medium\",\n                                                                children: \"مزايدة\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                                lineNumber: 384,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                        lineNumber: 382,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-white/70 rounded-lg p-3 min-w-[100px]\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-2xl font-bold text-green-600\",\n                                                                children: formatAmount(entry.totalAmount)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                                lineNumber: 387,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-600 font-medium\",\n                                                                children: \"المبلغ\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                                lineNumber: 390,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                        lineNumber: 386,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-white/70 rounded-lg p-3 min-w-[80px]\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-2xl font-bold text-purple-600\",\n                                                                children: entry.wonAuctions\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                                lineNumber: 393,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-600 font-medium\",\n                                                                children: \"فوز\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                                lineNumber: 394,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                        lineNumber: 392,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                lineNumber: 381,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                        lineNumber: 348,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                    lineNumber: 347,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, entry._id, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                            lineNumber: 328,\n                            columnNumber: 15\n                        }, this);\n                    }),\n                    leaderboard.slice(3).map((entry, index)=>{\n                        var _entry_user_profile;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            className: \"hover:shadow-md transition-shadow duration-200\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                className: \"pt-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-4 space-x-reverse\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-shrink-0 w-12 h-12 flex items-center justify-center bg-gray-100 rounded-full\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-lg font-bold text-gray-600\",\n                                                        children: [\n                                                            \"#\",\n                                                            entry.rank\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                        lineNumber: 410,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                    lineNumber: 409,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1 min-w-0 flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__.Avatar, {\n                                                            className: \"mr-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__.AvatarImage, {\n                                                                    src: entry.user.profile.avatarUrl,\n                                                                    alt: \"Avatar\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                                    lineNumber: 416,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__.AvatarFallback, {\n                                                                    className: \"bg-gradient-to-br from-gray-400 to-gray-500 text-white\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                                        lineNumber: 418,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                                    lineNumber: 417,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                            lineNumber: 415,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-lg font-semibold text-gray-900 truncate\",\n                                                                    children: ((_entry_user_profile = entry.user.profile) === null || _entry_user_profile === void 0 ? void 0 : _entry_user_profile.fullName) || entry.user.email\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                                    lineNumber: 422,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-2 space-x-reverse mt-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                                                            className: \"\".concat(getBadgeColor(entry.badge), \" flex items-center\"),\n                                                                            children: [\n                                                                                getBadgeIcon(entry.badge),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"mr-1\",\n                                                                                    children: entry.badge\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                                                    lineNumber: 428,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                                            lineNumber: 426,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: [\n                                                                                entry.points,\n                                                                                \" نقطة\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                                            lineNumber: 430,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                                    lineNumber: 425,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                            lineNumber: 421,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                    lineNumber: 414,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                            lineNumber: 407,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex space-x-6 space-x-reverse text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xl font-bold text-blue-600\",\n                                                            children: entry.totalBids\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                            lineNumber: 441,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-gray-600\",\n                                                            children: \"مزايدة\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                            lineNumber: 442,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                    lineNumber: 440,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xl font-bold text-green-600\",\n                                                            children: entry.totalAmount.toLocaleString(\"ar-SA\")\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                            lineNumber: 445,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-gray-600\",\n                                                            children: \"ر.س\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                            lineNumber: 448,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                    lineNumber: 444,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xl font-bold text-purple-600\",\n                                                            children: entry.wonAuctions\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                            lineNumber: 451,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-gray-600\",\n                                                            children: \"فوز\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                            lineNumber: 452,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                    lineNumber: 450,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                            lineNumber: 439,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                    lineNumber: 406,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                lineNumber: 405,\n                                columnNumber: 17\n                            }, this)\n                        }, entry._id, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                            lineNumber: 404,\n                            columnNumber: 15\n                        }, this);\n                    })\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                lineNumber: 325,\n                columnNumber: 11\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                className: \"bg-gradient-to-r from-blue-50 to-purple-50 border-blue-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                    className: \"pt-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-center mb-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"h-5 w-5 text-yellow-500 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                        lineNumber: 467,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"font-semibold text-gray-700\",\n                                        children: \"كيف يتم حساب النقاط؟\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                        lineNumber: 468,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                lineNumber: 466,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-600\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                className: \"h-4 w-4 text-blue-500 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                lineNumber: 472,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"+10 نقاط لكل مزايدة\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                lineNumber: 473,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                        lineNumber: 471,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"h-4 w-4 text-green-500 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                lineNumber: 476,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"+50 نقطة لكل فوز\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                lineNumber: 477,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                        lineNumber: 475,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                className: \"h-4 w-4 text-purple-500 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                lineNumber: 480,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"نقاط إضافية حسب القيمة\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                lineNumber: 481,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                        lineNumber: 479,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                lineNumber: 470,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xs text-gray-500 mt-4\",\n                                children: \"يتم تحديث لوحة الصدارة كل ساعة. النقاط تُحسب بناءً على النشاط والأداء في المزايدات.\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                lineNumber: 484,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                        lineNumber: 465,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                    lineNumber: 464,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                lineNumber: 463,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n        lineNumber: 190,\n        columnNumber: 5\n    }, this);\n}\n_s(LeaderboardPage, \"ZHZbZ+c1MLBPy+d+IX1ZCTrFcGI=\", false, function() {\n    return [\n        _contexts_CurrencyContext__WEBPACK_IMPORTED_MODULE_8__.useCurrency,\n        _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_6__.useToast\n    ];\n});\n_c = LeaderboardPage;\nvar _c;\n$RefreshReg$(_c, \"LeaderboardPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/user/leaderboard/page.tsx\n"));

/***/ })

});