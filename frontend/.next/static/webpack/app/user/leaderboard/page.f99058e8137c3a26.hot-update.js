"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/user/leaderboard/page",{

/***/ "(app-pages-browser)/./app/user/leaderboard/page.tsx":
/*!***************************************!*\
  !*** ./app/user/leaderboard/page.tsx ***!
  \***************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ LeaderboardPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/avatar */ \"(app-pages-browser)/./components/ui/avatar.tsx\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,Calendar,Crown,Gavel,Medal,RefreshCw,Star,Target,TrendingUp,Trophy,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/crown.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,Calendar,Crown,Gavel,Medal,RefreshCw,Star,Target,TrendingUp,Trophy,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trophy.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,Calendar,Crown,Gavel,Medal,RefreshCw,Star,Target,TrendingUp,Trophy,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/medal.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,Calendar,Crown,Gavel,Medal,RefreshCw,Star,Target,TrendingUp,Trophy,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,Calendar,Crown,Gavel,Medal,RefreshCw,Star,Target,TrendingUp,Trophy,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,Calendar,Crown,Gavel,Medal,RefreshCw,Star,Target,TrendingUp,Trophy,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,Calendar,Crown,Gavel,Medal,RefreshCw,Star,Target,TrendingUp,Trophy,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,Calendar,Crown,Gavel,Medal,RefreshCw,Star,Target,TrendingUp,Trophy,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,Calendar,Crown,Gavel,Medal,RefreshCw,Star,Target,TrendingUp,Trophy,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,Calendar,Crown,Gavel,Medal,RefreshCw,Star,Target,TrendingUp,Trophy,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,Calendar,Crown,Gavel,Medal,RefreshCw,Star,Target,TrendingUp,Trophy,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/gavel.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,Calendar,Crown,Gavel,Medal,RefreshCw,Star,Target,TrendingUp,Trophy,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/use-toast */ \"(app-pages-browser)/./components/ui/use-toast.tsx\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./lib/api.ts\");\n/* harmony import */ var _contexts_CurrencyContext__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/contexts/CurrencyContext */ \"(app-pages-browser)/./contexts/CurrencyContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nconst getBadgeIcon = (badge)=>{\n    switch(badge){\n        case \"Champion\":\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                className: \"h-4 w-4\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                lineNumber: 50,\n                columnNumber: 14\n            }, undefined);\n        case \"Master\":\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                className: \"h-4 w-4\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                lineNumber: 52,\n                columnNumber: 14\n            }, undefined);\n        case \"Expert\":\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                className: \"h-4 w-4\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                lineNumber: 54,\n                columnNumber: 14\n            }, undefined);\n        case \"Pro\":\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                className: \"h-4 w-4\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                lineNumber: 56,\n                columnNumber: 14\n            }, undefined);\n        case \"Active\":\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                className: \"h-4 w-4\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                lineNumber: 58,\n                columnNumber: 14\n            }, undefined);\n        default:\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                className: \"h-4 w-4\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                lineNumber: 60,\n                columnNumber: 14\n            }, undefined);\n    }\n};\nconst getBadgeColor = (badge)=>{\n    switch(badge){\n        case \"Champion\":\n            return \"bg-yellow-500 text-white\";\n        case \"Master\":\n            return \"bg-purple-500 text-white\";\n        case \"Expert\":\n            return \"bg-blue-500 text-white\";\n        case \"Pro\":\n            return \"bg-green-500 text-white\";\n        case \"Active\":\n            return \"bg-orange-500 text-white\";\n        default:\n            return \"bg-gray-500 text-white\";\n    }\n};\nconst getRankIcon = (rank)=>{\n    if (rank === 1) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n        className: \"h-6 w-6 text-yellow-500\"\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n        lineNumber: 82,\n        columnNumber: 26\n    }, undefined);\n    if (rank === 2) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n        className: \"h-6 w-6 text-gray-400\"\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n        lineNumber: 83,\n        columnNumber: 26\n    }, undefined);\n    if (rank === 3) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n        className: \"h-6 w-6 text-amber-600\"\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n        lineNumber: 84,\n        columnNumber: 26\n    }, undefined);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n        className: \"text-lg font-bold text-gray-600\",\n        children: [\n            \"#\",\n            rank\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n        lineNumber: 85,\n        columnNumber: 10\n    }, undefined);\n};\nfunction LeaderboardPage() {\n    _s();\n    const [leaderboard, setLeaderboard] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [refreshing, setRefreshing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedPeriod, setSelectedPeriod] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all-time\");\n    const { formatAmount } = (0,_contexts_CurrencyContext__WEBPACK_IMPORTED_MODULE_8__.useCurrency)();\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const { toast } = (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_6__.useToast)();\n    const fetchLeaderboard = async function() {\n        let period = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : selectedPeriod;\n        try {\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_7__[\"default\"].get(\"/users/leaderboard?period=\".concat(period, \"&limit=50\"));\n            if (response.data.success) {\n                setLeaderboard(response.data.data.leaderboard);\n                setError(null);\n            } else {\n                throw new Error(\"فشل في تحميل لوحة الصدارة\");\n            }\n        } catch (err) {\n            console.error(\"Leaderboard error:\", err);\n            setError(err instanceof Error ? err.message : \"حدث خطأ غير متوقع\");\n            toast({\n                title: \"خطأ في التحميل\",\n                description: \"حدث خطأ في تحميل بيانات المتصدرين\",\n                variant: \"destructive\"\n            });\n            setLeaderboard([]);\n        } finally{\n            setLoading(false);\n            setRefreshing(false);\n        }\n    };\n    const handleRefresh = ()=>{\n        setRefreshing(true);\n        fetchLeaderboard();\n    };\n    const handlePeriodChange = (period)=>{\n        setSelectedPeriod(period);\n        setLoading(true);\n        fetchLeaderboard(period);\n    };\n    const getPeriodLabel = (period)=>{\n        switch(period){\n            case \"daily\":\n                return \"اليوم\";\n            case \"weekly\":\n                return \"الأسبوع\";\n            case \"monthly\":\n                return \"الشهر\";\n            case \"all-time\":\n                return \"جميع الأوقات\";\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchLeaderboard();\n    }, []);\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center min-h-[400px]\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4 bg-primary/10 rounded-full w-20 h-20 mx-auto mb-6 flex items-center justify-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                            className: \"h-8 w-8 animate-spin text-primary\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                            lineNumber: 154,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                        lineNumber: 153,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold mb-2\",\n                        children: \"جاري تحميل لوحة الصدارة\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                        lineNumber: 156,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"يرجى الانتظار بينما نحضر أحدث البيانات...\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                        lineNumber: 157,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                lineNumber: 152,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n            lineNumber: 151,\n            columnNumber: 7\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center min-h-[400px]\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                className: \"max-w-md mx-auto bg-gradient-to-br from-red-50 to-orange-50 border-red-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                    className: \"pt-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-4 bg-red-100 rounded-full w-20 h-20 mx-auto mb-6 flex items-center justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    className: \"h-10 w-10 text-red-500\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                    lineNumber: 170,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                lineNumber: 169,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-semibold mb-3 text-red-700\",\n                                children: \"خطأ في تحميل البيانات\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                lineNumber: 172,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mb-6\",\n                                children: error\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                lineNumber: 173,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                onClick: handleRefresh,\n                                className: \"w-full bg-gradient-to-r from-red-500 to-orange-500 hover:from-red-600 hover:to-orange-600\",\n                                disabled: refreshing,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2 \".concat(refreshing ? \"animate-spin\" : \"\")\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                        lineNumber: 179,\n                                        columnNumber: 17\n                                    }, this),\n                                    \"إعادة المحاولة\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                lineNumber: 174,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                        lineNumber: 168,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                    lineNumber: 167,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                lineNumber: 166,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n            lineNumber: 165,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gradient-to-r from-yellow-500 to-orange-500 text-white rounded-lg p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-3xl font-bold flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"h-8 w-8 mr-3\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                lineNumber: 196,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"لوحة الصدارة\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                        lineNumber: 195,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"mt-2 opacity-90\",\n                                        children: [\n                                            \"تصنيف أفضل المزايدين في المنصة - \",\n                                            getPeriodLabel(selectedPeriod)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                        lineNumber: 199,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                lineNumber: 194,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                onClick: handleRefresh,\n                                disabled: refreshing,\n                                variant: \"secondary\",\n                                className: \"bg-white/20 hover:bg-white/30 text-white border-white/30\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2 \".concat(refreshing ? \"animate-spin\" : \"\")\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                        lineNumber: 209,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"تحديث\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                lineNumber: 203,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                        lineNumber: 193,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2 space-x-reverse\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                    className: \"h-5 w-5 text-white/80\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                    lineNumber: 217,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm font-medium text-white/90 ml-3\",\n                                    children: \"الفترة الزمنية:\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                    lineNumber: 218,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-2 space-x-reverse\",\n                                    children: [\n                                        \"daily\",\n                                        \"weekly\",\n                                        \"monthly\",\n                                        \"all-time\"\n                                    ].map((period)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            variant: selectedPeriod === period ? \"secondary\" : \"outline\",\n                                            size: \"sm\",\n                                            onClick: ()=>handlePeriodChange(period),\n                                            disabled: loading,\n                                            className: selectedPeriod === period ? \"bg-white text-orange-600 hover:bg-white/90\" : \"bg-white/20 text-white border-white/30 hover:bg-white/30\",\n                                            children: getPeriodLabel(period)\n                                        }, period, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                            lineNumber: 221,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                    lineNumber: 219,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                            lineNumber: 216,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                        lineNumber: 215,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                lineNumber: 192,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-4 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        className: \"bg-gradient-to-br from-blue-50 to-blue-100 border-blue-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            className: \"pt-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-3 bg-blue-500 rounded-full\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            className: \"h-6 w-6 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                            lineNumber: 246,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                        lineNumber: 245,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mr-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-blue-700\",\n                                                children: leaderboard.length\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                lineNumber: 249,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-blue-600 text-sm\",\n                                                children: \"مزايد نشط\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                lineNumber: 250,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                        lineNumber: 248,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                lineNumber: 244,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                            lineNumber: 243,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                        lineNumber: 242,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        className: \"bg-gradient-to-br from-green-50 to-green-100 border-green-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            className: \"pt-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-3 bg-green-500 rounded-full\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                            className: \"h-6 w-6 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                            lineNumber: 260,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                        lineNumber: 259,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mr-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-green-700\",\n                                                children: Array.isArray(leaderboard) ? leaderboard.reduce((sum, entry)=>sum + entry.totalBids, 0) : 0\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                lineNumber: 263,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-green-600 text-sm\",\n                                                children: \"إجمالي المزايدات\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                lineNumber: 266,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                        lineNumber: 262,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                lineNumber: 258,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                            lineNumber: 257,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                        lineNumber: 256,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        className: \"bg-gradient-to-br from-purple-50 to-purple-100 border-purple-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            className: \"pt-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-3 bg-purple-500 rounded-full\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            className: \"h-6 w-6 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                            lineNumber: 276,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                        lineNumber: 275,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mr-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-purple-700\",\n                                                children: Array.isArray(leaderboard) ? leaderboard.reduce((sum, entry)=>sum + entry.points, 0) : 0\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                lineNumber: 279,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-purple-600 text-sm\",\n                                                children: \"إجمالي النقاط\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                lineNumber: 282,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                        lineNumber: 278,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                lineNumber: 274,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                            lineNumber: 273,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                        lineNumber: 272,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        className: \"bg-gradient-to-br from-orange-50 to-orange-100 border-orange-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            className: \"pt-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-3 bg-orange-500 rounded-full\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"h-6 w-6 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                            lineNumber: 292,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                        lineNumber: 291,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mr-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-orange-700\",\n                                                children: Array.isArray(leaderboard) ? formatAmount(leaderboard.reduce((sum, entry)=>sum + entry.totalAmount, 0)) : formatAmount(0)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                lineNumber: 295,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-orange-600 text-sm\",\n                                                children: \"إجمالي المبالغ\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                lineNumber: 298,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                        lineNumber: 294,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                lineNumber: 290,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                            lineNumber: 289,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                        lineNumber: 288,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                lineNumber: 241,\n                columnNumber: 9\n            }, this),\n            leaderboard.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                className: \"bg-gradient-to-br from-gray-50 to-gray-100\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                    className: \"pt-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-4 bg-gray-200 rounded-full w-20 h-20 mx-auto mb-6 flex items-center justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    className: \"h-10 w-10 text-gray-400\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                    lineNumber: 311,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                lineNumber: 310,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-semibold mb-3 text-gray-700\",\n                                children: \"لا توجد بيانات حالياً\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                lineNumber: 313,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mb-6 max-w-md mx-auto\",\n                                children: \"لم يتم العثور على أي مزايدات بعد. ابدأ بالمزايدة لتظهر في لوحة الصدارة!\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                lineNumber: 314,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                className: \"bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                        lineNumber: 318,\n                                        columnNumber: 19\n                                    }, this),\n                                    \"ابدأ المزايدة الآن\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                lineNumber: 317,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                        lineNumber: 309,\n                        columnNumber: 15\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                    lineNumber: 308,\n                    columnNumber: 13\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                lineNumber: 307,\n                columnNumber: 11\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-4\",\n                children: [\n                    leaderboard.slice(0, 3).map((entry, index)=>{\n                        var _entry_user_profile;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            className: \"relative overflow-hidden \".concat(index === 0 ? \"bg-gradient-to-r from-yellow-50 to-orange-50 border-2 border-yellow-300 shadow-lg\" : index === 1 ? \"bg-gradient-to-r from-gray-50 to-slate-50 border-2 border-gray-300 shadow-md\" : \"bg-gradient-to-r from-amber-50 to-orange-50 border-2 border-amber-300 shadow-md\"),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute top-0 right-0 w-16 h-16 \".concat(index === 0 ? \"bg-gradient-to-br from-yellow-400 to-yellow-600\" : index === 1 ? \"bg-gradient-to-br from-gray-400 to-gray-600\" : \"bg-gradient-to-br from-amber-400 to-amber-600\", \" transform rotate-45 translate-x-6 -translate-y-6\"),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute bottom-2 left-2 text-white font-bold text-sm transform -rotate-45\",\n                                        children: [\n                                            \"#\",\n                                            entry.rank\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                        lineNumber: 342,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                    lineNumber: 337,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    className: \"pt-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-4 space-x-reverse\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-shrink-0 w-16 h-16 flex items-center justify-center\",\n                                                        children: getRankIcon(entry.rank)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                        lineNumber: 351,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1 min-w-0 flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__.Avatar, {\n                                                                className: \"mr-4 w-12 h-12 border-2 border-white shadow-md\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__.AvatarImage, {\n                                                                        src: entry.user.profile.avatarUrl,\n                                                                        alt: \"Avatar\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                                        lineNumber: 358,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__.AvatarFallback, {\n                                                                        className: \"bg-gradient-to-br from-blue-400 to-purple-500 text-white\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                            className: \"h-6 w-6\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                                            lineNumber: 360,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                                        lineNumber: 359,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                                lineNumber: 357,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"text-xl font-bold text-gray-900 truncate\",\n                                                                        children: ((_entry_user_profile = entry.user.profile) === null || _entry_user_profile === void 0 ? void 0 : _entry_user_profile.fullName) || entry.user.email\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                                        lineNumber: 364,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center space-x-3 space-x-reverse mt-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                                                                className: \"\".concat(getBadgeColor(entry.badge), \" flex items-center px-3 py-1\"),\n                                                                                children: [\n                                                                                    getBadgeIcon(entry.badge),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"mr-1 font-medium\",\n                                                                                        children: entry.badge\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                                                        lineNumber: 370,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                                                lineNumber: 368,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm font-medium text-gray-600 bg-white px-2 py-1 rounded-full\",\n                                                                                children: [\n                                                                                    entry.points,\n                                                                                    \" نقطة\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                                                lineNumber: 372,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                                        lineNumber: 367,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                                lineNumber: 363,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                        lineNumber: 356,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                lineNumber: 349,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex space-x-8 space-x-reverse text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-white/70 rounded-lg p-3 min-w-[80px]\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-2xl font-bold text-blue-600\",\n                                                                children: entry.totalBids\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                                lineNumber: 383,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-600 font-medium\",\n                                                                children: \"مزايدة\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                                lineNumber: 384,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                        lineNumber: 382,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-white/70 rounded-lg p-3 min-w-[100px]\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-2xl font-bold text-green-600\",\n                                                                children: entry.totalAmount.toLocaleString(\"ar-SA\")\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                                lineNumber: 387,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-600 font-medium\",\n                                                                children: \"ر.س\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                                lineNumber: 390,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                        lineNumber: 386,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-white/70 rounded-lg p-3 min-w-[80px]\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-2xl font-bold text-purple-600\",\n                                                                children: entry.wonAuctions\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                                lineNumber: 393,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-600 font-medium\",\n                                                                children: \"فوز\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                                lineNumber: 394,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                        lineNumber: 392,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                lineNumber: 381,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                        lineNumber: 348,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                    lineNumber: 347,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, entry._id, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                            lineNumber: 328,\n                            columnNumber: 15\n                        }, this);\n                    }),\n                    leaderboard.slice(3).map((entry, index)=>{\n                        var _entry_user_profile;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            className: \"hover:shadow-md transition-shadow duration-200\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                className: \"pt-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-4 space-x-reverse\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-shrink-0 w-12 h-12 flex items-center justify-center bg-gray-100 rounded-full\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-lg font-bold text-gray-600\",\n                                                        children: [\n                                                            \"#\",\n                                                            entry.rank\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                        lineNumber: 410,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                    lineNumber: 409,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1 min-w-0 flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__.Avatar, {\n                                                            className: \"mr-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__.AvatarImage, {\n                                                                    src: entry.user.profile.avatarUrl,\n                                                                    alt: \"Avatar\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                                    lineNumber: 416,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__.AvatarFallback, {\n                                                                    className: \"bg-gradient-to-br from-gray-400 to-gray-500 text-white\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                                        lineNumber: 418,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                                    lineNumber: 417,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                            lineNumber: 415,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-lg font-semibold text-gray-900 truncate\",\n                                                                    children: ((_entry_user_profile = entry.user.profile) === null || _entry_user_profile === void 0 ? void 0 : _entry_user_profile.fullName) || entry.user.email\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                                    lineNumber: 422,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-2 space-x-reverse mt-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                                                            className: \"\".concat(getBadgeColor(entry.badge), \" flex items-center\"),\n                                                                            children: [\n                                                                                getBadgeIcon(entry.badge),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"mr-1\",\n                                                                                    children: entry.badge\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                                                    lineNumber: 428,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                                            lineNumber: 426,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: [\n                                                                                entry.points,\n                                                                                \" نقطة\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                                            lineNumber: 430,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                                    lineNumber: 425,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                            lineNumber: 421,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                    lineNumber: 414,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                            lineNumber: 407,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex space-x-6 space-x-reverse text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xl font-bold text-blue-600\",\n                                                            children: entry.totalBids\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                            lineNumber: 441,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-gray-600\",\n                                                            children: \"مزايدة\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                            lineNumber: 442,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                    lineNumber: 440,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xl font-bold text-green-600\",\n                                                            children: entry.totalAmount.toLocaleString(\"ar-SA\")\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                            lineNumber: 445,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-gray-600\",\n                                                            children: \"ر.س\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                            lineNumber: 448,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                    lineNumber: 444,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xl font-bold text-purple-600\",\n                                                            children: entry.wonAuctions\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                            lineNumber: 451,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-gray-600\",\n                                                            children: \"فوز\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                            lineNumber: 452,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                    lineNumber: 450,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                            lineNumber: 439,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                    lineNumber: 406,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                lineNumber: 405,\n                                columnNumber: 17\n                            }, this)\n                        }, entry._id, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                            lineNumber: 404,\n                            columnNumber: 15\n                        }, this);\n                    })\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                lineNumber: 325,\n                columnNumber: 11\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                className: \"bg-gradient-to-r from-blue-50 to-purple-50 border-blue-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                    className: \"pt-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-center mb-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"h-5 w-5 text-yellow-500 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                        lineNumber: 467,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"font-semibold text-gray-700\",\n                                        children: \"كيف يتم حساب النقاط؟\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                        lineNumber: 468,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                lineNumber: 466,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-600\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                className: \"h-4 w-4 text-blue-500 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                lineNumber: 472,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"+10 نقاط لكل مزايدة\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                lineNumber: 473,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                        lineNumber: 471,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"h-4 w-4 text-green-500 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                lineNumber: 476,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"+50 نقطة لكل فوز\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                lineNumber: 477,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                        lineNumber: 475,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_Crown_Gavel_Medal_RefreshCw_Star_Target_TrendingUp_Trophy_User_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                className: \"h-4 w-4 text-purple-500 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                lineNumber: 480,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"نقاط إضافية حسب القيمة\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                                lineNumber: 481,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                        lineNumber: 479,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                lineNumber: 470,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xs text-gray-500 mt-4\",\n                                children: \"يتم تحديث لوحة الصدارة كل ساعة. النقاط تُحسب بناءً على النشاط والأداء في المزايدات.\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                                lineNumber: 484,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                        lineNumber: 465,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                    lineNumber: 464,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n                lineNumber: 463,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/leaderboard/page.tsx\",\n        lineNumber: 190,\n        columnNumber: 5\n    }, this);\n}\n_s(LeaderboardPage, \"ZHZbZ+c1MLBPy+d+IX1ZCTrFcGI=\", false, function() {\n    return [\n        _contexts_CurrencyContext__WEBPACK_IMPORTED_MODULE_8__.useCurrency,\n        _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_6__.useToast\n    ];\n});\n_c = LeaderboardPage;\nvar _c;\n$RefreshReg$(_c, \"LeaderboardPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/user/leaderboard/page.tsx\n"));

/***/ })

});