"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/user/notifications/page",{

/***/ "(app-pages-browser)/./app/user/notifications/page.tsx":
/*!*****************************************!*\
  !*** ./app/user/notifications/page.tsx ***!
  \*****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ NotificationsPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _barrel_optimize_names_Bell_Check_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Check,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Check_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Check,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Check_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Check,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var socket_io_client__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! socket.io-client */ \"(app-pages-browser)/./node_modules/socket.io-client/build/esm/index.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction NotificationsPage() {\n    _s();\n    const [notifications, setNotifications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [unreadCount, setUnreadCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [socket, setSocket] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const token = localStorage.getItem(\"token\");\n        if (!token) return;\n        // Initialize socket connection\n        const socketConnection = (0,socket_io_client__WEBPACK_IMPORTED_MODULE_4__.io)(\"http://localhost:5000/api\" || 0, {\n            auth: {\n                token\n            },\n            reconnection: true,\n            reconnectionAttempts: 5,\n            reconnectionDelay: 1000\n        });\n        setSocket(socketConnection);\n        // Listen for notifications\n        socketConnection.on(\"notification\", (notification)=>{\n            const newNotification = {\n                id: Date.now().toString(),\n                title: notification.title,\n                message: notification.message,\n                type: notification.type || \"notification\",\n                isRead: false,\n                timestamp: new Date(),\n                data: notification.data\n            };\n            setNotifications((prev)=>[\n                    newNotification,\n                    ...prev\n                ]);\n            setUnreadCount((prev)=>prev + 1);\n        });\n        // Listen for auction updates\n        socketConnection.on(\"auction_update\", (data)=>{\n            const notification = {\n                id: Date.now().toString(),\n                title: \"تحديث المزاد\",\n                message: data.message || \"تم تحديث المزاد\",\n                type: \"auction_update\",\n                isRead: false,\n                timestamp: new Date(),\n                data\n            };\n            setNotifications((prev)=>[\n                    notification,\n                    ...prev\n                ]);\n            setUnreadCount((prev)=>prev + 1);\n        });\n        // Listen for bid updates\n        socketConnection.on(\"bid_placed\", (data)=>{\n            const notification = {\n                id: Date.now().toString(),\n                title: \"مزايدة جديدة\",\n                message: data.message || \"تم وضع مزايدة جديدة\",\n                type: \"bid_placed\",\n                isRead: false,\n                timestamp: new Date(),\n                data\n            };\n            setNotifications((prev)=>[\n                    notification,\n                    ...prev\n                ]);\n            setUnreadCount((prev)=>prev + 1);\n        });\n        // Listen for tender updates\n        socketConnection.on(\"tender_update\", (data)=>{\n            const notification = {\n                id: Date.now().toString(),\n                title: \"تحديث المناقصة\",\n                message: data.message || \"تم تحديث المناقصة\",\n                type: \"tender_update\",\n                isRead: false,\n                timestamp: new Date(),\n                data\n            };\n            setNotifications((prev)=>[\n                    notification,\n                    ...prev\n                ]);\n            setUnreadCount((prev)=>prev + 1);\n        });\n        return ()=>{\n            socketConnection.disconnect();\n        };\n    }, []);\n    const markAsRead = (id)=>{\n        setNotifications((prev)=>prev.map((notification)=>notification.id === id ? {\n                    ...notification,\n                    isRead: true\n                } : notification));\n        setUnreadCount((prev)=>Math.max(0, prev - 1));\n    };\n    const markAllAsRead = ()=>{\n        setNotifications((prev)=>prev.map((notification)=>({\n                    ...notification,\n                    isRead: true\n                })));\n        setUnreadCount(0);\n    };\n    const removeNotification = (id)=>{\n        const notification = notifications.find((n)=>n.id === id);\n        if (notification && !notification.isRead) {\n            setUnreadCount((prev)=>Math.max(0, prev - 1));\n        }\n        setNotifications((prev)=>prev.filter((n)=>n.id !== id));\n    };\n    const getNotificationIcon = (type)=>{\n        switch(type){\n            case \"auction_update\":\n                return \"\\uD83C\\uDFF7️\";\n            case \"bid_placed\":\n                return \"\\uD83D\\uDCB0\";\n            case \"tender_update\":\n                return \"\\uD83D\\uDCCB\";\n            default:\n                return \"\\uD83D\\uDD14\";\n        }\n    };\n    const formatTimeAgo = (timestamp)=>{\n        const now = new Date();\n        const diffInMinutes = Math.floor((now.getTime() - timestamp.getTime()) / (1000 * 60));\n        if (diffInMinutes < 1) return \"الآن\";\n        if (diffInMinutes < 60) return \"منذ \".concat(diffInMinutes, \" دقيقة\");\n        const diffInHours = Math.floor(diffInMinutes / 60);\n        if (diffInHours < 24) return \"منذ \".concat(diffInHours, \" ساعة\");\n        const diffInDays = Math.floor(diffInHours / 24);\n        return \"منذ \".concat(diffInDays, \" يوم\");\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DashboardLayout, {\n        allowedRoles: [\n            \"individual\",\n            \"company\",\n            \"government\"\n        ],\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-3xl font-bold flex items-center gap-2\",\n                                    children: [\n                                        \"الإشعارات\",\n                                        unreadCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"bg-red-500 text-white text-sm px-2 py-1 rounded-full\",\n                                            children: unreadCount\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/notifications/page.tsx\",\n                                            lineNumber: 169,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/notifications/page.tsx\",\n                                    lineNumber: 166,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-muted-foreground\",\n                                    children: \"تابع آخر تنبيهاتك وإشعاراتك هنا\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/notifications/page.tsx\",\n                                    lineNumber: 174,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/notifications/page.tsx\",\n                            lineNumber: 165,\n                            columnNumber: 11\n                        }, this),\n                        notifications.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex gap-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"outline\",\n                                size: \"sm\",\n                                onClick: markAllAsRead,\n                                disabled: unreadCount === 0,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Check_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/notifications/page.tsx\",\n                                        lineNumber: 185,\n                                        columnNumber: 17\n                                    }, this),\n                                    \"قراءة الكل\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/notifications/page.tsx\",\n                                lineNumber: 179,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/notifications/page.tsx\",\n                            lineNumber: 178,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/notifications/page.tsx\",\n                    lineNumber: 164,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                children: \"الإشعارات المباشرة\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/notifications/page.tsx\",\n                                lineNumber: 194,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/notifications/page.tsx\",\n                            lineNumber: 193,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            children: notifications.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-8 text-gray-500\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Check_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"h-12 w-12 mx-auto mb-4 opacity-50\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/notifications/page.tsx\",\n                                        lineNumber: 199,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: \"لا توجد إشعارات حالياً\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/notifications/page.tsx\",\n                                        lineNumber: 200,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm\",\n                                        children: \"ستظهر الإشعارات الجديدة هنا تلقائياً\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/notifications/page.tsx\",\n                                        lineNumber: 201,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/notifications/page.tsx\",\n                                lineNumber: 198,\n                                columnNumber: 15\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-3\",\n                                children: notifications.map((notification)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-4 border rounded-lg transition-all duration-200 \".concat(!notification.isRead ? \"bg-blue-50 border-blue-200 shadow-sm\" : \"bg-gray-50 border-gray-200\"),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-start justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-start gap-3 flex-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-2xl\",\n                                                            children: getNotificationIcon(notification.type)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/notifications/page.tsx\",\n                                                            lineNumber: 216,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                    className: \"font-medium text-gray-900 mb-1\",\n                                                                    children: notification.title\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/notifications/page.tsx\",\n                                                                    lineNumber: 220,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-gray-600 mb-2\",\n                                                                    children: notification.message\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/notifications/page.tsx\",\n                                                                    lineNumber: 223,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-gray-500\",\n                                                                    children: formatTimeAgo(notification.timestamp)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/notifications/page.tsx\",\n                                                                    lineNumber: 226,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/notifications/page.tsx\",\n                                                            lineNumber: 219,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/notifications/page.tsx\",\n                                                    lineNumber: 215,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        !notification.isRead && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                            variant: \"ghost\",\n                                                            size: \"sm\",\n                                                            onClick: ()=>markAsRead(notification.id),\n                                                            className: \"text-blue-600 hover:text-blue-800\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Check_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/notifications/page.tsx\",\n                                                                lineNumber: 240,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/notifications/page.tsx\",\n                                                            lineNumber: 234,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                            variant: \"ghost\",\n                                                            size: \"sm\",\n                                                            onClick: ()=>removeNotification(notification.id),\n                                                            className: \"text-red-600 hover:text-red-800\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Check_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/notifications/page.tsx\",\n                                                                lineNumber: 249,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/notifications/page.tsx\",\n                                                            lineNumber: 243,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/notifications/page.tsx\",\n                                                    lineNumber: 232,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/notifications/page.tsx\",\n                                            lineNumber: 214,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, notification.id, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/notifications/page.tsx\",\n                                        lineNumber: 206,\n                                        columnNumber: 19\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/notifications/page.tsx\",\n                                lineNumber: 204,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/notifications/page.tsx\",\n                            lineNumber: 196,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/notifications/page.tsx\",\n                    lineNumber: 192,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/notifications/page.tsx\",\n            lineNumber: 163,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/notifications/page.tsx\",\n        lineNumber: 162,\n        columnNumber: 5\n    }, this);\n}\n_s(NotificationsPage, \"y5SaqQbS/PEjVXhMYkCzUx8kkV0=\");\n_c = NotificationsPage;\nvar _c;\n$RefreshReg$(_c, \"NotificationsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/user/notifications/page.tsx\n"));

/***/ })

});