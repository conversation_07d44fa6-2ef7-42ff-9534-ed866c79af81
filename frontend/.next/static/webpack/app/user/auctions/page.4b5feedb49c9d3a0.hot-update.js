"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/user/auctions/page",{

/***/ "(app-pages-browser)/./app/user/auctions/page.tsx":
/*!************************************!*\
  !*** ./app/user/auctions/page.tsx ***!
  \************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ UserAuctionsPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./components/ui/dialog.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./components/ui/label.tsx\");\n/* harmony import */ var _barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,DollarSign,Eye,Gavel,Heart,Pause,Play,RefreshCw,Search,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,DollarSign,Eye,Gavel,Heart,Pause,Play,RefreshCw,Search,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pause.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,DollarSign,Eye,Gavel,Heart,Pause,Play,RefreshCw,Search,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,DollarSign,Eye,Gavel,Heart,Pause,Play,RefreshCw,Search,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,DollarSign,Eye,Gavel,Heart,Pause,Play,RefreshCw,Search,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/gavel.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,DollarSign,Eye,Gavel,Heart,Pause,Play,RefreshCw,Search,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,DollarSign,Eye,Gavel,Heart,Pause,Play,RefreshCw,Search,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,DollarSign,Eye,Gavel,Heart,Pause,Play,RefreshCw,Search,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,DollarSign,Eye,Gavel,Heart,Pause,Play,RefreshCw,Search,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,DollarSign,Eye,Gavel,Heart,Pause,Play,RefreshCw,Search,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./lib/api.ts\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/use-toast */ \"(app-pages-browser)/./components/ui/use-toast.tsx\");\n/* harmony import */ var _contexts_CurrencyContext__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/contexts/CurrencyContext */ \"(app-pages-browser)/./contexts/CurrencyContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nfunction UserAuctionsPage() {\n    _s();\n    const [auctions, setAuctions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [filteredAuctions, setFilteredAuctions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [savedAuctions, setSavedAuctions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Bidding modal state\n    const [showBidModal, setShowBidModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedAuction, setSelectedAuction] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [bidAmount, setBidAmount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [bidLoading, setBidLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Filter and sort state\n    const [sortBy, setSortBy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"endDate\");\n    const [filterCategory, setFilterCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // Auto-refresh state\n    const [autoRefresh, setAutoRefresh] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [lastRefresh, setLastRefresh] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Date());\n    // Optimistic locking\n    const [auctionVersions, setAuctionVersions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_9__.useRouter)();\n    const { toast } = (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_10__.useToast)();\n    const { userCurrency, formatAmount, convertAmount } = (0,_contexts_CurrencyContext__WEBPACK_IMPORTED_MODULE_11__.useCurrency)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Get user data and check permissions\n        const userData = localStorage.getItem(\"user\");\n        if (userData) {\n            const parsedUser = JSON.parse(userData);\n            setUser(parsedUser);\n            // Redirect admins to their proper dashboard\n            if (parsedUser.role === \"admin\" || parsedUser.role === \"super_admin\") {\n                toast({\n                    title: \"وصول غير مسموح\",\n                    description: \"المديرون يجب أن يستخدموا لوحة إدارة المزادات\",\n                    variant: \"destructive\"\n                });\n                router.push(\"/admin/auctions\");\n                return;\n            }\n            // Redirect government users to their dashboard\n            if (parsedUser.role === \"government\") {\n                toast({\n                    title: \"وصول غير مسموح\",\n                    description: \"الجهات الحكومية تستخدم نظام المناقصات\",\n                    variant: \"destructive\"\n                });\n                router.push(\"/government/dashboard\");\n                return;\n            }\n        }\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Load data\n        loadAuctions();\n        loadSavedAuctions();\n    }, []);\n    // Load auctions when currency changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (userCurrency) {\n            loadAuctions();\n        }\n    }, [\n        userCurrency\n    ]);\n    // Auto-refresh auctions every 30 seconds\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!autoRefresh) return;\n        const interval = setInterval(async ()=>{\n            try {\n                await loadAuctions(false) // Silent refresh without loading state\n                ;\n                setLastRefresh(new Date());\n                console.log(\"\\uD83D\\uDD04 Auto-refreshed auction data\");\n            } catch (error) {\n                console.error(\"Auto-refresh failed:\", error);\n            }\n        }, 30000) // 30 seconds\n        ;\n        return ()=>clearInterval(interval);\n    }, [\n        autoRefresh\n    ]);\n    const loadAuctions = async function() {\n        let showLoading = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : true;\n        try {\n            if (showLoading) {\n                setLoading(true);\n            }\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_8__.auctionAPI.getAll();\n            console.log(\"Auctions API response:\", response.data);\n            // Handle the API response structure properly\n            let auctionData = [];\n            if (response.data && response.data.success) {\n                var _response_data_data;\n                // If the response has a success field, extract the auctions from data.data.auctions\n                auctionData = ((_response_data_data = response.data.data) === null || _response_data_data === void 0 ? void 0 : _response_data_data.auctions) || [];\n            } else if (response.data && Array.isArray(response.data)) {\n                // If response.data is directly an array\n                auctionData = response.data;\n            } else if (response.data && response.data.data && Array.isArray(response.data.data)) {\n                // If the auctions are in response.data.data\n                auctionData = response.data.data;\n            }\n            // Transform and convert currency for each auction\n            const transformedData = await Promise.all(auctionData.map(async (auction)=>{\n                var _auction_bids, _auction_seller_profile, _auction_seller, _auction_seller_profile1, _auction_seller1, _auction_seller2, _auction_seller3;\n                const baseAuction = {\n                    id: auction._id || auction.id,\n                    title: auction.title,\n                    description: auction.description,\n                    currentBid: auction.currentPrice || auction.currentBid || auction.startingPrice || 0,\n                    endDate: auction.endDate,\n                    bidsCount: ((_auction_bids = auction.bids) === null || _auction_bids === void 0 ? void 0 : _auction_bids.length) || auction.bidsCount || 0,\n                    seller: ((_auction_seller = auction.seller) === null || _auction_seller === void 0 ? void 0 : (_auction_seller_profile = _auction_seller.profile) === null || _auction_seller_profile === void 0 ? void 0 : _auction_seller_profile.fullName) || ((_auction_seller1 = auction.seller) === null || _auction_seller1 === void 0 ? void 0 : (_auction_seller_profile1 = _auction_seller1.profile) === null || _auction_seller_profile1 === void 0 ? void 0 : _auction_seller_profile1.companyName) || ((_auction_seller2 = auction.seller) === null || _auction_seller2 === void 0 ? void 0 : _auction_seller2.name) || ((_auction_seller3 = auction.seller) === null || _auction_seller3 === void 0 ? void 0 : _auction_seller3._id) || \"غير محدد\",\n                    category: auction.category,\n                    status: auction.status,\n                    views: auction.views || 0,\n                    location: auction.location,\n                    currency: auction.currency || \"SAR\",\n                    version: auction.version || 0\n                };\n                // Store version for optimistic locking\n                setAuctionVersions((prev)=>({\n                        ...prev,\n                        [baseAuction.id]: baseAuction.version\n                    }));\n                // Convert currency if needed\n                try {\n                    if (baseAuction.currency !== userCurrency) {\n                        const convertedBid = await convertAmount(baseAuction.currentBid, baseAuction.currency, userCurrency);\n                        return {\n                            ...baseAuction,\n                            currentBid: Math.round(convertedBid),\n                            originalCurrency: baseAuction.currency,\n                            currency: userCurrency\n                        };\n                    }\n                } catch (conversionError) {\n                    console.warn(\"Currency conversion failed for auction:\", baseAuction.id, conversionError);\n                }\n                return baseAuction;\n            }));\n            setAuctions(transformedData);\n            setFilteredAuctions(transformedData);\n            console.log(\"✅ Set auctions and filteredAuctions:\", transformedData.length);\n        } catch (error) {\n            console.error(\"Error loading auctions:\", error);\n            setAuctions([]);\n            setFilteredAuctions([]);\n            toast({\n                title: \"فشل في تحميل المزادات\",\n                description: \"حدث خطأ أثناء تحميل المزادات. يرجى المحاولة مرة أخرى.\",\n                variant: \"destructive\"\n            });\n        } finally{\n            if (showLoading) {\n                setLoading(false);\n            }\n            console.log(\"\\uD83C\\uDFC1 Loading finished. Auctions loaded:\", auctions.length);\n        }\n    };\n    const loadSavedAuctions = async ()=>{\n        try {\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_8__.favoritesAPI.getFavorites({\n                type: \"auction\"\n            });\n            // Handle different response structures\n            let favoritesData = [];\n            if (response.data && response.data.success) {\n                favoritesData = response.data.data || [];\n            } else if (response.data && Array.isArray(response.data)) {\n                favoritesData = response.data;\n            } else if (response.data && response.data.favorites && Array.isArray(response.data.favorites)) {\n                favoritesData = response.data.favorites;\n            }\n            // Check if favoritesData is an array before mapping\n            if (Array.isArray(favoritesData)) {\n                const savedIds = new Set(favoritesData.map((fav)=>fav.itemId || fav._id || fav.id));\n                setSavedAuctions(savedIds);\n            } else {\n                console.warn(\"Favorites data is not an array:\", favoritesData);\n                setSavedAuctions(new Set());\n            }\n        } catch (error) {\n            console.error(\"Error loading saved auctions:\", error);\n            // Don't show error toast for favorites as it's not critical\n            setSavedAuctions(new Set());\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Ensure auctions is an array before filtering\n        if (Array.isArray(auctions)) {\n            let filtered = auctions.filter((auction)=>{\n                var _auction_title, _auction_description;\n                const matchesSearch = ((_auction_title = auction.title) === null || _auction_title === void 0 ? void 0 : _auction_title.toLowerCase().includes(searchTerm.toLowerCase())) || ((_auction_description = auction.description) === null || _auction_description === void 0 ? void 0 : _auction_description.toLowerCase().includes(searchTerm.toLowerCase()));\n                const matchesCategory = !filterCategory || auction.category === filterCategory;\n                return matchesSearch && matchesCategory;\n            });\n            // Sort the filtered results\n            filtered = filtered.sort((a, b)=>{\n                switch(sortBy){\n                    case \"endDate\":\n                        return new Date(a.endDate).getTime() - new Date(b.endDate).getTime();\n                    case \"currentBid\":\n                        return (b.currentBid || 0) - (a.currentBid || 0);\n                    case \"bidsCount\":\n                        return (b.bidsCount || 0) - (a.bidsCount || 0);\n                    case \"title\":\n                        return a.title.localeCompare(b.title);\n                    default:\n                        return 0;\n                }\n            });\n            setFilteredAuctions(filtered);\n            console.log(\"\\uD83D\\uDD0D Filtered auctions:\", filtered.length, \"from\", auctions.length);\n        } else {\n            setFilteredAuctions([]);\n        }\n    }, [\n        searchTerm,\n        auctions,\n        filterCategory,\n        sortBy\n    ]);\n    const getTimeRemaining = (endDate)=>{\n        const now = new Date();\n        const end = new Date(endDate);\n        const diff = end.getTime() - now.getTime();\n        if (diff <= 0) return \"انتهى\";\n        const hours = Math.floor(diff / (1000 * 60 * 60));\n        const days = Math.floor(hours / 24);\n        if (days > 0) return \"\".concat(days, \" يوم\");\n        return \"\".concat(hours, \" ساعة\");\n    };\n    // Check if user can bid\n    const canUserBid = (auction)=>{\n        var _user_profile;\n        if (!user) return false;\n        // Only individuals and companies can bid\n        if (user.role !== \"individual\" && user.role !== \"company\") {\n            return false;\n        }\n        // Companies cannot bid on their own auctions\n        if (user.role === \"company\" && auction.seller === ((_user_profile = user.profile) === null || _user_profile === void 0 ? void 0 : _user_profile.companyName)) {\n            return false;\n        }\n        return true;\n    };\n    const handleBid = async (auctionId)=>{\n        console.log(\"\\uD83C\\uDFAF Bid button clicked for auction:\", auctionId);\n        try {\n            var _freshAuction_bids, _freshAuction_seller_profile, _freshAuction_seller, _freshAuction_seller_profile1, _freshAuction_seller1, _freshAuction_seller2;\n            // Fetch fresh auction data before opening modal\n            setLoading(true);\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_8__.auctionAPI.getById(auctionId);\n            let freshAuction = null;\n            if (response.data && response.data.success) {\n                freshAuction = response.data.data.auction;\n            } else if (response.data && response.data.auction) {\n                freshAuction = response.data.auction;\n            }\n            if (!freshAuction) {\n                toast({\n                    title: \"خطأ\",\n                    description: \"لم يتم العثور على المزاد\",\n                    variant: \"destructive\"\n                });\n                return;\n            }\n            // Transform fresh data to match our format\n            const transformedAuction = {\n                id: freshAuction._id || freshAuction.id,\n                title: freshAuction.title,\n                description: freshAuction.description,\n                currentBid: freshAuction.currentPrice || freshAuction.currentBid || freshAuction.startingPrice || 0,\n                endDate: freshAuction.endDate,\n                bidsCount: ((_freshAuction_bids = freshAuction.bids) === null || _freshAuction_bids === void 0 ? void 0 : _freshAuction_bids.length) || freshAuction.bidsCount || 0,\n                seller: ((_freshAuction_seller = freshAuction.seller) === null || _freshAuction_seller === void 0 ? void 0 : (_freshAuction_seller_profile = _freshAuction_seller.profile) === null || _freshAuction_seller_profile === void 0 ? void 0 : _freshAuction_seller_profile.fullName) || ((_freshAuction_seller1 = freshAuction.seller) === null || _freshAuction_seller1 === void 0 ? void 0 : (_freshAuction_seller_profile1 = _freshAuction_seller1.profile) === null || _freshAuction_seller_profile1 === void 0 ? void 0 : _freshAuction_seller_profile1.companyName) || ((_freshAuction_seller2 = freshAuction.seller) === null || _freshAuction_seller2 === void 0 ? void 0 : _freshAuction_seller2.name) || \"غير محدد\",\n                category: freshAuction.category,\n                status: freshAuction.status\n            };\n            // Check if user can bid\n            if (!canUserBid(transformedAuction)) {\n                if ((user === null || user === void 0 ? void 0 : user.role) === \"admin\" || (user === null || user === void 0 ? void 0 : user.role) === \"super_admin\") {\n                    toast({\n                        title: \"وصول غير مسموح\",\n                        description: \"المديرون لا يمكنهم المزايدة على المزادات\",\n                        variant: \"destructive\"\n                    });\n                } else if ((user === null || user === void 0 ? void 0 : user.role) === \"government\") {\n                    toast({\n                        title: \"وصول غير مسموح\",\n                        description: \"الجهات الحكومية لا تزايد على المزادات\",\n                        variant: \"destructive\"\n                    });\n                } else if ((user === null || user === void 0 ? void 0 : user.role) === \"company\") {\n                    toast({\n                        title: \"وصول غير مسموح\",\n                        description: \"لا يمكنك المزايدة على مزادك الخاص\",\n                        variant: \"destructive\"\n                    });\n                } else {\n                    toast({\n                        title: \"وصول غير مسموح\",\n                        description: \"غير مسموح لك بالمزايدة\",\n                        variant: \"destructive\"\n                    });\n                }\n                return;\n            }\n            // Open bidding modal with fresh data\n            setSelectedAuction(transformedAuction);\n            setBidAmount(String((transformedAuction.currentBid || 0) + 1000));\n            setShowBidModal(true);\n            toast({\n                title: \"\\uD83D\\uDCCA تم تحديث بيانات المزاد\",\n                description: \"المزايدة الحالية: \".concat(formatAmount(transformedAuction.currentBid || 0))\n            });\n        } catch (error) {\n            console.error(\"Error fetching fresh auction data:\", error);\n            toast({\n                title: \"خطأ في تحديث البيانات\",\n                description: \"سيتم استخدام البيانات المحفوظة محلياً\",\n                variant: \"destructive\"\n            });\n            // Fallback to cached data\n            const currentAuction = auctions.find((a)=>a.id === auctionId);\n            if (currentAuction && canUserBid(currentAuction)) {\n                setSelectedAuction(currentAuction);\n                setBidAmount(String((currentAuction.currentBid || 0) + 1000));\n                setShowBidModal(true);\n            }\n        } finally{\n            setLoading(false);\n        }\n    };\n    const submitBid = async ()=>{\n        if (!selectedAuction) return;\n        const bidAmountNum = parseFloat(bidAmount);\n        if (isNaN(bidAmountNum) || bidAmountNum <= (selectedAuction.currentBid || 0)) {\n            toast({\n                title: \"مزايدة غير صالحة\",\n                description: \"يجب أن تكون المزايدة أكبر من المزايدة الحالية\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        try {\n            setBidLoading(true);\n            // Get auction version for optimistic locking\n            const auctionVersion = auctionVersions[selectedAuction.id];\n            // Prepare bid data with currency and version\n            const bidData = {\n                bidAmount: bidAmountNum,\n                currency: userCurrency,\n                version: auctionVersion\n            };\n            // Submit bid and wait for server response\n            const response = await fetch(\"/api/auctions/\".concat(selectedAuction.id, \"/bid\"), {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    \"Authorization\": \"Bearer \".concat(localStorage.getItem(\"token\"))\n                },\n                body: JSON.stringify(bidData)\n            });\n            const result = await response.json();\n            if (!response.ok) {\n                // Handle specific error cases\n                if (response.status === 409) {\n                    // Optimistic locking conflict\n                    toast({\n                        title: \"تم تحديث المزاد ⚠️\",\n                        description: \"قام شخص آخر بتحديث المزاد. سيتم تحديث البيانات.\",\n                        variant: \"destructive\"\n                    });\n                    await loadAuctions();\n                    setShowBidModal(false);\n                    setBidAmount(\"\");\n                    setSelectedAuction(null);\n                    return;\n                }\n                throw new Error(result.message || \"Bid failed\");\n            }\n            // Close modal first\n            setShowBidModal(false);\n            setBidAmount(\"\");\n            setSelectedAuction(null);\n            // Reload fresh data from server\n            await loadAuctions();\n            // Show success message with currency formatting\n            const formattedAmount = formatAmount(bidAmountNum);\n            toast({\n                title: \"تم تقديم المزايدة بنجاح! \\uD83C\\uDF89\",\n                description: \"مقدار المزايدة: \".concat(formattedAmount)\n            });\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error(\"Error placing bid:\", error);\n            // Close modal on error too\n            setShowBidModal(false);\n            setBidAmount(\"\");\n            setSelectedAuction(null);\n            // Show specific error message\n            const errorMessage = ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"يرجى المحاولة مرة أخرى\";\n            if (errorMessage.includes(\"higher than current price\") || errorMessage.includes(\"أكبر من السعر الحالي\")) {\n                toast({\n                    title: \"مزايدة منخفضة ⚠️\",\n                    description: \"قام شخص آخر بمزايدة أعلى. يرجى المحاولة بمبلغ أكبر.\",\n                    variant: \"destructive\"\n                });\n            } else if (errorMessage.includes(\"ended\") || errorMessage.includes(\"انتهى\")) {\n                toast({\n                    title: \"انتهى المزاد ⏰\",\n                    description: \"لقد انتهى هذا المزاد ولا يمكن قبول مزايدات جديدة.\",\n                    variant: \"destructive\"\n                });\n            } else {\n                toast({\n                    title: \"فشل في تقديم المزايدة\",\n                    description: errorMessage,\n                    variant: \"destructive\"\n                });\n            }\n            // Always reload data on error to sync with server\n            await loadAuctions();\n        } finally{\n            setBidLoading(false);\n        }\n    };\n    const handleSaveAuction = async (auctionId)=>{\n        console.log(\"❤️ Save button clicked for auction:\", auctionId);\n        // Show immediate feedback that button was clicked\n        toast({\n            title: \"\\uD83D\\uDC86 تم الضغط على زر الحفظ\",\n            description: \"جاري معالجة الطلب...\"\n        });\n        try {\n            const auction = auctions.find((a)=>a.id === auctionId);\n            if (!auction) {\n                toast({\n                    title: \"خطأ\",\n                    description: \"لم يتم العثور على المزاد\",\n                    variant: \"destructive\"\n                });\n                return;\n            }\n            const isCurrentlySaved = savedAuctions.has(auctionId);\n            // Optimistic UI update\n            setSavedAuctions((prev)=>{\n                const newSet = new Set(prev);\n                if (isCurrentlySaved) {\n                    newSet.delete(auctionId);\n                } else {\n                    newSet.add(auctionId);\n                }\n                return newSet;\n            });\n            if (isCurrentlySaved) {\n                // Remove from favorites\n                await _lib_api__WEBPACK_IMPORTED_MODULE_8__.favoritesAPI.removeFavorite(\"auction\", auctionId);\n                toast({\n                    title: \"❤️ تم إزالة من المفضلة\",\n                    description: 'تم إزالة \"'.concat(auction.title, '\" من المفضلة')\n                });\n            } else {\n                // Add to favorites\n                await _lib_api__WEBPACK_IMPORTED_MODULE_8__.favoritesAPI.addFavorite({\n                    itemType: \"auction\",\n                    itemId: auctionId,\n                    notifications: {\n                        bidUpdates: true,\n                        statusChanges: true,\n                        endingSoon: true\n                    }\n                });\n                toast({\n                    title: \"\\uD83D\\uDC96 تم إضافة إلى المفضلة\",\n                    description: 'تم إضافة \"'.concat(auction.title, '\" إلى المفضلة')\n                });\n            }\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error(\"Error saving auction:\", error);\n            // Revert optimistic update on error\n            setSavedAuctions((prev)=>{\n                const newSet = new Set(prev);\n                if (savedAuctions.has(auctionId)) {\n                    newSet.add(auctionId);\n                } else {\n                    newSet.delete(auctionId);\n                }\n                return newSet;\n            });\n            toast({\n                title: \"فشل في حفظ المزاد\",\n                description: ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"يرجى المحاولة مرة أخرى\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleViewAuction = async (auctionId)=>{\n        console.log(\"\\uD83D\\uDC41️ View button clicked for auction:\", auctionId);\n        try {\n            const auction = auctions.find((a)=>a.id === auctionId);\n            if (!auction) {\n                toast({\n                    title: \"خطأ\",\n                    description: \"لم يتم العثور على المزاد\",\n                    variant: \"destructive\"\n                });\n                return;\n            }\n            // Navigate to auction detail page\n            router.push(\"/auctions/\".concat(auctionId));\n            toast({\n                title: \"\\uD83D\\uDC41️ جاري تحميل التفاصيل\",\n                description: \"سيتم عرض تفاصيل المزاد\"\n            });\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error(\"Error viewing auction:\", error);\n            toast({\n                title: \"فشل في عرض تفاصيل المزاد\",\n                description: ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"يرجى المحاولة مرة أخرى\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-3xl font-bold\",\n                                    children: \"المزادات المتاحة\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 612,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-muted-foreground\",\n                                    children: [\n                                        \"استكشف المزادات الحالية وشارك في المزايدة\",\n                                        autoRefresh && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs text-green-600 ml-2\",\n                                            children: \"• تحديث تلقائي كل 30 ثانية\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                            lineNumber: 616,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 613,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                            lineNumber: 611,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    onClick: async ()=>{\n                                        await loadAuctions(true) // Show loading state\n                                        ;\n                                        setLastRefresh(new Date());\n                                        toast({\n                                            title: \"\\uD83D\\uDD04 تم تحديث البيانات\",\n                                            description: \"تم العثور على \".concat(filteredAuctions.length, \" مزاد متاح\")\n                                        });\n                                    },\n                                    variant: \"outline\",\n                                    disabled: loading,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"h-4 w-4 ml-2 \".concat(loading ? \"animate-spin\" : \"\")\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                            lineNumber: 636,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"تحديث\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 624,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    onClick: ()=>{\n                                        setAutoRefresh(!autoRefresh);\n                                        toast({\n                                            title: autoRefresh ? \"⏸️ تم إيقاف التحديث التلقائي\" : \"▶️ تم تشغيل التحديث التلقائي\",\n                                            description: autoRefresh ? \"يمكنك التحديث يدوياً\" : \"سيتم التحديث كل 30 ثانية\"\n                                        });\n                                    },\n                                    variant: autoRefresh ? \"default\" : \"outline\",\n                                    size: \"icon\",\n                                    children: autoRefresh ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 650,\n                                        columnNumber: 32\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 650,\n                                        columnNumber: 64\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 639,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                            lineNumber: 622,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                    lineNumber: 610,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                lineNumber: 609,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                    className: \"pt-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"absolute right-3 top-3 h-4 w-4 text-muted-foreground\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 661,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                        placeholder: \"ابحث في المزادات...\",\n                                        value: searchTerm,\n                                        onChange: (e)=>setSearchTerm(e.target.value),\n                                        className: \"pr-10\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 662,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                lineNumber: 660,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col sm:flex-row gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                htmlFor: \"category\",\n                                                className: \"text-sm font-medium\",\n                                                children: \"الفئة\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                lineNumber: 672,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                id: \"category\",\n                                                value: filterCategory,\n                                                onChange: (e)=>setFilterCategory(e.target.value),\n                                                className: \"w-full mt-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"\",\n                                                        children: \"جميع الفئات\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 679,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"electronics\",\n                                                        children: \"إلكترونيات\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 680,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"vehicles\",\n                                                        children: \"سيارات\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 681,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"real_estate\",\n                                                        children: \"عقارات\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 682,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"art_collectibles\",\n                                                        children: \"فنون ومقتنيات\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 683,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"machinery\",\n                                                        children: \"معدات\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 684,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"furniture\",\n                                                        children: \"أثاث\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 685,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"jewelry\",\n                                                        children: \"مجوهرات\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 686,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"books_media\",\n                                                        children: \"كتب ووسائط\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 687,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"clothing\",\n                                                        children: \"ملابس\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 688,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"sports\",\n                                                        children: \"رياضة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 689,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"other\",\n                                                        children: \"أخرى\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 690,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                lineNumber: 673,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 671,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                htmlFor: \"sort\",\n                                                className: \"text-sm font-medium\",\n                                                children: \"ترتيب حسب\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                lineNumber: 695,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                id: \"sort\",\n                                                value: sortBy,\n                                                onChange: (e)=>setSortBy(e.target.value),\n                                                className: \"w-full mt-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"endDate\",\n                                                        children: \"تاريخ الانتهاء\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 702,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"currentBid\",\n                                                        children: \"أعلى مزايدة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 703,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"bidsCount\",\n                                                        children: \"عدد المزايدات\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 704,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"title\",\n                                                        children: \"الاسم\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 705,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                lineNumber: 696,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 694,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                lineNumber: 670,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                        lineNumber: 659,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                    lineNumber: 658,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                lineNumber: 657,\n                columnNumber: 9\n            }, this),\n            Array.isArray(filteredAuctions) && filteredAuctions.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                    className: \"text-lg\",\n                                    children: \"إجمالي المزادات\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 718,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                    className: \"text-2xl font-bold text-blue-600\",\n                                    children: filteredAuctions.length\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 719,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                            lineNumber: 717,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                        lineNumber: 716,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                    className: \"text-lg\",\n                                    children: \"المزايدات النشطة\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 726,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                    className: \"text-2xl font-bold text-green-600\",\n                                    children: filteredAuctions.reduce((sum, a)=>sum + (a.bidsCount || 0), 0)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 727,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                            lineNumber: 725,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                        lineNumber: 724,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                    className: \"text-lg\",\n                                    children: \"أعلى مزايدة\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 734,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                    className: \"text-2xl font-bold text-purple-600\",\n                                    children: filteredAuctions.length > 0 ? formatAmount(Math.max(...filteredAuctions.map((a)=>a.currentBid || 0))) : formatAmount(0)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 735,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                            lineNumber: 733,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                        lineNumber: 732,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                lineNumber: 715,\n                columnNumber: 11\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                children: Array.isArray(filteredAuctions) && filteredAuctions.map((auction)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        className: \"hover:shadow-lg transition-shadow cursor-pointer\",\n                        onClick: ()=>handleViewAuction(auction.id),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"aspect-video bg-gradient-to-br from-gray-100 to-gray-200 rounded-lg mb-4 flex items-center justify-center relative overflow-hidden\",\n                                        children: [\n                                            auction.images && auction.images.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: auction.images[0].url,\n                                                alt: auction.title,\n                                                className: \"w-full h-full object-cover\",\n                                                onError: (e)=>{\n                                                    e.currentTarget.style.display = \"none\";\n                                                    e.currentTarget.nextElementSibling.style.display = \"flex\";\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                lineNumber: 753,\n                                                columnNumber: 21\n                                            }, this) : null,\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-center w-full h-full text-gray-500\",\n                                                style: {\n                                                    display: auction.images && auction.images.length > 0 ? \"none\" : \"flex\"\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                            className: \"h-8 w-8 mx-auto mb-2 text-gray-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                            lineNumber: 765,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm\",\n                                                            children: \"صورة المزاد\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                            lineNumber: 766,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                    lineNumber: 764,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                lineNumber: 763,\n                                                columnNumber: 19\n                                            }, this),\n                                            auction.status === \"active\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                className: \"absolute top-2 right-2 bg-green-500\",\n                                                children: \"نشط\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                lineNumber: 770,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 751,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                        className: \"text-lg line-clamp-2\",\n                                        children: auction.title\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 775,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                        className: \"line-clamp-2\",\n                                        children: auction.description\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 776,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                lineNumber: 750,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-muted-foreground\",\n                                                        children: \"أعلى مزايدة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 783,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xl font-bold text-green-600\",\n                                                        children: [\n                                                            formatAmount(auction.currentBid || 0),\n                                                            auction.originalCurrency && auction.originalCurrency !== userCurrency && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs text-gray-500 ml-2\",\n                                                                children: [\n                                                                    \"(من \",\n                                                                    auction.originalCurrency,\n                                                                    \")\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                                lineNumber: 787,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 784,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                lineNumber: 782,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-muted-foreground\",\n                                                        children: \"المزايدات\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 794,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-lg font-semibold\",\n                                                        children: auction.bidsCount || 0\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 795,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                lineNumber: 793,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 781,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                className: \"h-4 w-4 text-muted-foreground\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                lineNumber: 800,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-muted-foreground\",\n                                                children: [\n                                                    \"ينتهي خلال: \",\n                                                    getTimeRemaining(auction.endDate)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                lineNumber: 801,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 799,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-muted-foreground\",\n                                        children: [\n                                            \"البائع: \",\n                                            auction.seller\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 806,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                onClick: (e)=>{\n                                                    e.preventDefault();\n                                                    e.stopPropagation();\n                                                    handleBid(auction.id);\n                                                },\n                                                className: \"flex-1\",\n                                                disabled: loading || bidLoading,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                        className: \"h-4 w-4 ml-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 820,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    bidLoading ? \"جاري المزايدة...\" : \"مزايدة\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                lineNumber: 811,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                variant: savedAuctions.has(auction.id) ? \"default\" : \"outline\",\n                                                size: \"icon\",\n                                                onClick: (e)=>{\n                                                    e.preventDefault();\n                                                    e.stopPropagation();\n                                                    handleSaveAuction(auction.id);\n                                                },\n                                                disabled: loading,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                    className: \"h-4 w-4 \".concat(savedAuctions.has(auction.id) ? \"fill-current\" : \"\")\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                    lineNumber: 833,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                lineNumber: 823,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                variant: \"outline\",\n                                                size: \"icon\",\n                                                onClick: (e)=>{\n                                                    e.preventDefault();\n                                                    e.stopPropagation();\n                                                    handleViewAuction(auction.id);\n                                                },\n                                                disabled: loading,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                    lineNumber: 845,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                lineNumber: 835,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 810,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                lineNumber: 780,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, auction.id, true, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                        lineNumber: 749,\n                        columnNumber: 13\n                    }, this))\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                lineNumber: 747,\n                columnNumber: 9\n            }, this),\n            !loading && (!Array.isArray(filteredAuctions) || filteredAuctions.length === 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                    className: \"py-16 text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col items-center gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-16 h-16 rounded-full bg-gray-100 flex items-center justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                    className: \"h-8 w-8 text-gray-400\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 859,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                lineNumber: 858,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold mb-2\",\n                                        children: searchTerm ? \"لا توجد مزادات تطابق البحث\" : \"لا توجد مزادات متاحة حالياً\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 862,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-muted-foreground\",\n                                        children: searchTerm ? \"جرب البحث بكلمات مختلفة أو امسح مربع البحث لعرض جميع المزادات\" : \"سيتم عرض المزادات المتاحة هنا عند إضافتها من قبل الشركات\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 865,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                lineNumber: 861,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                        lineNumber: 857,\n                        columnNumber: 15\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                    lineNumber: 856,\n                    columnNumber: 13\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                lineNumber: 855,\n                columnNumber: 11\n            }, this),\n            loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                    className: \"py-16 text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col items-center gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                lineNumber: 882,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-muted-foreground\",\n                                children: \"جاري تحميل المزادات...\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                lineNumber: 883,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                        lineNumber: 881,\n                        columnNumber: 15\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                    lineNumber: 880,\n                    columnNumber: 13\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                lineNumber: 879,\n                columnNumber: 11\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__.Dialog, {\n                open: showBidModal,\n                onOpenChange: setShowBidModal,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__.DialogContent, {\n                    className: \"sm:max-w-md\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__.DialogHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__.DialogTitle, {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 894,\n                                        columnNumber: 17\n                                    }, this),\n                                    \"تقديم مزايدة\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                lineNumber: 893,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                            lineNumber: 892,\n                            columnNumber: 13\n                        }, this),\n                        selectedAuction && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gray-50 p-4 rounded-lg\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"font-semibold mb-2\",\n                                            children: selectedAuction.title\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                            lineNumber: 902,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-4 text-sm text-gray-600\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                            lineNumber: 905,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: [\n                                                                \"المزايدة الحالية: \",\n                                                                formatAmount(selectedAuction.currentBid || 0)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                            lineNumber: 906,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                    lineNumber: 904,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                            lineNumber: 909,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: [\n                                                                selectedAuction.bidsCount || 0,\n                                                                \" مزايدة\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                            lineNumber: 910,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                    lineNumber: 908,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                            lineNumber: 903,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 901,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                            htmlFor: \"bidAmount\",\n                                            children: [\n                                                \"مقدار المزايدة (\",\n                                                userCurrency,\n                                                \")\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                            lineNumber: 916,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                            id: \"bidAmount\",\n                                            type: \"number\",\n                                            value: bidAmount,\n                                            onChange: (e)=>setBidAmount(e.target.value),\n                                            placeholder: \"أدخل مقدار المزايدة\",\n                                            min: (selectedAuction.currentBid || 0) + 1\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                            lineNumber: 917,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-gray-500\",\n                                            children: [\n                                                \"الحد الأدنى: \",\n                                                formatAmount((selectedAuction.currentBid || 0) + 1)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                            lineNumber: 925,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 915,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                            lineNumber: 900,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__.DialogFooter, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"outline\",\n                                    onClick: ()=>{\n                                        setShowBidModal(false);\n                                        setBidAmount(\"\");\n                                        setSelectedAuction(null);\n                                    },\n                                    disabled: bidLoading,\n                                    children: \"إلغاء\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 933,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    onClick: submitBid,\n                                    disabled: bidLoading || !bidAmount,\n                                    className: \"bg-green-600 hover:bg-green-700\",\n                                    children: bidLoading ? \"جاري التقديم...\" : \"تقديم المزايدة\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 944,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                            lineNumber: 932,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                    lineNumber: 891,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                lineNumber: 890,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n        lineNumber: 608,\n        columnNumber: 5\n    }, this);\n}\n_s(UserAuctionsPage, \"0C/dfCNanwYtXVZSEIcjKhB8Lbk=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_9__.useRouter,\n        _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_10__.useToast,\n        _contexts_CurrencyContext__WEBPACK_IMPORTED_MODULE_11__.useCurrency\n    ];\n});\n_c = UserAuctionsPage;\nvar _c;\n$RefreshReg$(_c, \"UserAuctionsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/user/auctions/page.tsx\n"));

/***/ })

});