"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/user/auctions/page",{

/***/ "(app-pages-browser)/./app/user/auctions/page.tsx":
/*!************************************!*\
  !*** ./app/user/auctions/page.tsx ***!
  \************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ UserAuctionsPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./components/ui/dialog.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./components/ui/label.tsx\");\n/* harmony import */ var _barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,DollarSign,Eye,Gavel,Heart,Pause,Play,RefreshCw,Search,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,DollarSign,Eye,Gavel,Heart,Pause,Play,RefreshCw,Search,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pause.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,DollarSign,Eye,Gavel,Heart,Pause,Play,RefreshCw,Search,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,DollarSign,Eye,Gavel,Heart,Pause,Play,RefreshCw,Search,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,DollarSign,Eye,Gavel,Heart,Pause,Play,RefreshCw,Search,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/gavel.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,DollarSign,Eye,Gavel,Heart,Pause,Play,RefreshCw,Search,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,DollarSign,Eye,Gavel,Heart,Pause,Play,RefreshCw,Search,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,DollarSign,Eye,Gavel,Heart,Pause,Play,RefreshCw,Search,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,DollarSign,Eye,Gavel,Heart,Pause,Play,RefreshCw,Search,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,DollarSign,Eye,Gavel,Heart,Pause,Play,RefreshCw,Search,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./lib/api.ts\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/use-toast */ \"(app-pages-browser)/./components/ui/use-toast.tsx\");\n/* harmony import */ var _contexts_CurrencyContext__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/contexts/CurrencyContext */ \"(app-pages-browser)/./contexts/CurrencyContext.tsx\");\n/* harmony import */ var _components_CurrencySelector__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/CurrencySelector */ \"(app-pages-browser)/./components/CurrencySelector.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction UserAuctionsPage() {\n    _s();\n    const [auctions, setAuctions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [filteredAuctions, setFilteredAuctions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [savedAuctions, setSavedAuctions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Bidding modal state\n    const [showBidModal, setShowBidModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedAuction, setSelectedAuction] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [bidAmount, setBidAmount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [bidLoading, setBidLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Filter and sort state\n    const [sortBy, setSortBy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"endDate\");\n    const [filterCategory, setFilterCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // Auto-refresh state\n    const [autoRefresh, setAutoRefresh] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [lastRefresh, setLastRefresh] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Date());\n    // Optimistic locking\n    const [auctionVersions, setAuctionVersions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_9__.useRouter)();\n    const { toast } = (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_10__.useToast)();\n    const { userCurrency, formatAmount, convertAmount } = (0,_contexts_CurrencyContext__WEBPACK_IMPORTED_MODULE_11__.useCurrency)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Get user data and check permissions\n        const userData = localStorage.getItem(\"user\");\n        if (userData) {\n            const parsedUser = JSON.parse(userData);\n            setUser(parsedUser);\n            // Redirect admins to their proper dashboard\n            if (parsedUser.role === \"admin\" || parsedUser.role === \"super_admin\") {\n                toast({\n                    title: \"وصول غير مسموح\",\n                    description: \"المديرون يجب أن يستخدموا لوحة إدارة المزادات\",\n                    variant: \"destructive\"\n                });\n                router.push(\"/admin/auctions\");\n                return;\n            }\n            // Redirect government users to their dashboard\n            if (parsedUser.role === \"government\") {\n                toast({\n                    title: \"وصول غير مسموح\",\n                    description: \"الجهات الحكومية تستخدم نظام المناقصات\",\n                    variant: \"destructive\"\n                });\n                router.push(\"/government/dashboard\");\n                return;\n            }\n        }\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Load data\n        loadAuctions();\n        loadSavedAuctions();\n    }, []);\n    // Load auctions when currency changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (userCurrency) {\n            loadAuctions();\n        }\n    }, [\n        userCurrency\n    ]);\n    // Auto-refresh auctions every 30 seconds\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!autoRefresh) return;\n        const interval = setInterval(async ()=>{\n            try {\n                await loadAuctions(false) // Silent refresh without loading state\n                ;\n                setLastRefresh(new Date());\n                console.log(\"\\uD83D\\uDD04 Auto-refreshed auction data\");\n            } catch (error) {\n                console.error(\"Auto-refresh failed:\", error);\n            }\n        }, 30000) // 30 seconds\n        ;\n        return ()=>clearInterval(interval);\n    }, [\n        autoRefresh\n    ]);\n    const loadAuctions = async function() {\n        let showLoading = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : true;\n        try {\n            if (showLoading) {\n                setLoading(true);\n            }\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_8__.auctionAPI.getAll();\n            console.log(\"Auctions API response:\", response.data);\n            // Handle the API response structure properly\n            let auctionData = [];\n            if (response.data && response.data.success) {\n                var _response_data_data;\n                // If the response has a success field, extract the auctions from data.data.auctions\n                auctionData = ((_response_data_data = response.data.data) === null || _response_data_data === void 0 ? void 0 : _response_data_data.auctions) || [];\n            } else if (response.data && Array.isArray(response.data)) {\n                // If response.data is directly an array\n                auctionData = response.data;\n            } else if (response.data && response.data.data && Array.isArray(response.data.data)) {\n                // If the auctions are in response.data.data\n                auctionData = response.data.data;\n            }\n            // Transform and convert currency for each auction\n            const transformedData = await Promise.all(auctionData.map(async (auction)=>{\n                var _auction_bids, _auction_seller_profile, _auction_seller, _auction_seller_profile1, _auction_seller1, _auction_seller2, _auction_seller3;\n                const baseAuction = {\n                    id: auction._id || auction.id,\n                    title: auction.title,\n                    description: auction.description,\n                    currentBid: auction.currentPrice || auction.currentBid || auction.startingPrice || 0,\n                    endDate: auction.endDate,\n                    bidsCount: ((_auction_bids = auction.bids) === null || _auction_bids === void 0 ? void 0 : _auction_bids.length) || auction.bidsCount || 0,\n                    seller: ((_auction_seller = auction.seller) === null || _auction_seller === void 0 ? void 0 : (_auction_seller_profile = _auction_seller.profile) === null || _auction_seller_profile === void 0 ? void 0 : _auction_seller_profile.fullName) || ((_auction_seller1 = auction.seller) === null || _auction_seller1 === void 0 ? void 0 : (_auction_seller_profile1 = _auction_seller1.profile) === null || _auction_seller_profile1 === void 0 ? void 0 : _auction_seller_profile1.companyName) || ((_auction_seller2 = auction.seller) === null || _auction_seller2 === void 0 ? void 0 : _auction_seller2.name) || ((_auction_seller3 = auction.seller) === null || _auction_seller3 === void 0 ? void 0 : _auction_seller3._id) || \"غير محدد\",\n                    category: auction.category,\n                    status: auction.status,\n                    views: auction.views || 0,\n                    location: auction.location,\n                    currency: auction.currency || \"SAR\",\n                    version: auction.version || 0\n                };\n                // Store version for optimistic locking\n                setAuctionVersions((prev)=>({\n                        ...prev,\n                        [baseAuction.id]: baseAuction.version\n                    }));\n                // Convert currency if needed\n                try {\n                    if (baseAuction.currency !== userCurrency) {\n                        const convertedBid = await convertAmount(baseAuction.currentBid, baseAuction.currency, userCurrency);\n                        return {\n                            ...baseAuction,\n                            currentBid: Math.round(convertedBid),\n                            originalCurrency: baseAuction.currency,\n                            currency: userCurrency\n                        };\n                    }\n                } catch (conversionError) {\n                    console.warn(\"Currency conversion failed for auction:\", baseAuction.id, conversionError);\n                }\n                return baseAuction;\n            }));\n            setAuctions(transformedData);\n            setFilteredAuctions(transformedData);\n            console.log(\"✅ Set auctions and filteredAuctions:\", transformedData.length);\n        } catch (error) {\n            console.error(\"Error loading auctions:\", error);\n            setAuctions([]);\n            setFilteredAuctions([]);\n            toast({\n                title: \"فشل في تحميل المزادات\",\n                description: \"حدث خطأ أثناء تحميل المزادات. يرجى المحاولة مرة أخرى.\",\n                variant: \"destructive\"\n            });\n        } finally{\n            if (showLoading) {\n                setLoading(false);\n            }\n            console.log(\"\\uD83C\\uDFC1 Loading finished. Auctions loaded:\", auctions.length);\n        }\n    };\n    const loadSavedAuctions = async ()=>{\n        try {\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_8__.favoritesAPI.getFavorites({\n                type: \"auction\"\n            });\n            // Handle different response structures\n            let favoritesData = [];\n            if (response.data && response.data.success) {\n                favoritesData = response.data.data || [];\n            } else if (response.data && Array.isArray(response.data)) {\n                favoritesData = response.data;\n            } else if (response.data && response.data.favorites && Array.isArray(response.data.favorites)) {\n                favoritesData = response.data.favorites;\n            }\n            // Check if favoritesData is an array before mapping\n            if (Array.isArray(favoritesData)) {\n                const savedIds = new Set(favoritesData.map((fav)=>fav.itemId || fav._id || fav.id));\n                setSavedAuctions(savedIds);\n            } else {\n                console.warn(\"Favorites data is not an array:\", favoritesData);\n                setSavedAuctions(new Set());\n            }\n        } catch (error) {\n            console.error(\"Error loading saved auctions:\", error);\n            // Don't show error toast for favorites as it's not critical\n            setSavedAuctions(new Set());\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Ensure auctions is an array before filtering\n        if (Array.isArray(auctions)) {\n            let filtered = auctions.filter((auction)=>{\n                var _auction_title, _auction_description;\n                const matchesSearch = ((_auction_title = auction.title) === null || _auction_title === void 0 ? void 0 : _auction_title.toLowerCase().includes(searchTerm.toLowerCase())) || ((_auction_description = auction.description) === null || _auction_description === void 0 ? void 0 : _auction_description.toLowerCase().includes(searchTerm.toLowerCase()));\n                const matchesCategory = !filterCategory || auction.category === filterCategory;\n                return matchesSearch && matchesCategory;\n            });\n            // Sort the filtered results\n            filtered = filtered.sort((a, b)=>{\n                switch(sortBy){\n                    case \"endDate\":\n                        return new Date(a.endDate).getTime() - new Date(b.endDate).getTime();\n                    case \"currentBid\":\n                        return (b.currentBid || 0) - (a.currentBid || 0);\n                    case \"bidsCount\":\n                        return (b.bidsCount || 0) - (a.bidsCount || 0);\n                    case \"title\":\n                        return a.title.localeCompare(b.title);\n                    default:\n                        return 0;\n                }\n            });\n            setFilteredAuctions(filtered);\n            console.log(\"\\uD83D\\uDD0D Filtered auctions:\", filtered.length, \"from\", auctions.length);\n        } else {\n            setFilteredAuctions([]);\n        }\n    }, [\n        searchTerm,\n        auctions,\n        filterCategory,\n        sortBy\n    ]);\n    const getTimeRemaining = (endDate)=>{\n        const now = new Date();\n        const end = new Date(endDate);\n        const diff = end.getTime() - now.getTime();\n        if (diff <= 0) return \"انتهى\";\n        const hours = Math.floor(diff / (1000 * 60 * 60));\n        const days = Math.floor(hours / 24);\n        if (days > 0) return \"\".concat(days, \" يوم\");\n        return \"\".concat(hours, \" ساعة\");\n    };\n    // Check if user can bid\n    const canUserBid = (auction)=>{\n        var _user_profile;\n        if (!user) return false;\n        // Only individuals and companies can bid\n        if (user.role !== \"individual\" && user.role !== \"company\") {\n            return false;\n        }\n        // Companies cannot bid on their own auctions\n        if (user.role === \"company\" && auction.seller === ((_user_profile = user.profile) === null || _user_profile === void 0 ? void 0 : _user_profile.companyName)) {\n            return false;\n        }\n        return true;\n    };\n    const handleBid = async (auctionId)=>{\n        console.log(\"\\uD83C\\uDFAF Bid button clicked for auction:\", auctionId);\n        try {\n            var _freshAuction_bids, _freshAuction_seller_profile, _freshAuction_seller, _freshAuction_seller_profile1, _freshAuction_seller1, _freshAuction_seller2;\n            // Fetch fresh auction data before opening modal\n            setLoading(true);\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_8__.auctionAPI.getById(auctionId);\n            let freshAuction = null;\n            if (response.data && response.data.success) {\n                freshAuction = response.data.data.auction;\n            } else if (response.data && response.data.auction) {\n                freshAuction = response.data.auction;\n            }\n            if (!freshAuction) {\n                toast({\n                    title: \"خطأ\",\n                    description: \"لم يتم العثور على المزاد\",\n                    variant: \"destructive\"\n                });\n                return;\n            }\n            // Transform fresh data to match our format\n            const transformedAuction = {\n                id: freshAuction._id || freshAuction.id,\n                title: freshAuction.title,\n                description: freshAuction.description,\n                currentBid: freshAuction.currentPrice || freshAuction.currentBid || freshAuction.startingPrice || 0,\n                endDate: freshAuction.endDate,\n                bidsCount: ((_freshAuction_bids = freshAuction.bids) === null || _freshAuction_bids === void 0 ? void 0 : _freshAuction_bids.length) || freshAuction.bidsCount || 0,\n                seller: ((_freshAuction_seller = freshAuction.seller) === null || _freshAuction_seller === void 0 ? void 0 : (_freshAuction_seller_profile = _freshAuction_seller.profile) === null || _freshAuction_seller_profile === void 0 ? void 0 : _freshAuction_seller_profile.fullName) || ((_freshAuction_seller1 = freshAuction.seller) === null || _freshAuction_seller1 === void 0 ? void 0 : (_freshAuction_seller_profile1 = _freshAuction_seller1.profile) === null || _freshAuction_seller_profile1 === void 0 ? void 0 : _freshAuction_seller_profile1.companyName) || ((_freshAuction_seller2 = freshAuction.seller) === null || _freshAuction_seller2 === void 0 ? void 0 : _freshAuction_seller2.name) || \"غير محدد\",\n                category: freshAuction.category,\n                status: freshAuction.status\n            };\n            // Check if user can bid\n            if (!canUserBid(transformedAuction)) {\n                if ((user === null || user === void 0 ? void 0 : user.role) === \"admin\" || (user === null || user === void 0 ? void 0 : user.role) === \"super_admin\") {\n                    toast({\n                        title: \"وصول غير مسموح\",\n                        description: \"المديرون لا يمكنهم المزايدة على المزادات\",\n                        variant: \"destructive\"\n                    });\n                } else if ((user === null || user === void 0 ? void 0 : user.role) === \"government\") {\n                    toast({\n                        title: \"وصول غير مسموح\",\n                        description: \"الجهات الحكومية لا تزايد على المزادات\",\n                        variant: \"destructive\"\n                    });\n                } else if ((user === null || user === void 0 ? void 0 : user.role) === \"company\") {\n                    toast({\n                        title: \"وصول غير مسموح\",\n                        description: \"لا يمكنك المزايدة على مزادك الخاص\",\n                        variant: \"destructive\"\n                    });\n                } else {\n                    toast({\n                        title: \"وصول غير مسموح\",\n                        description: \"غير مسموح لك بالمزايدة\",\n                        variant: \"destructive\"\n                    });\n                }\n                return;\n            }\n            // Open bidding modal with fresh data\n            setSelectedAuction(transformedAuction);\n            setBidAmount(String((transformedAuction.currentBid || 0) + 1000));\n            setShowBidModal(true);\n            toast({\n                title: \"\\uD83D\\uDCCA تم تحديث بيانات المزاد\",\n                description: \"المزايدة الحالية: \".concat(formatAmount(transformedAuction.currentBid || 0))\n            });\n        } catch (error) {\n            console.error(\"Error fetching fresh auction data:\", error);\n            toast({\n                title: \"خطأ في تحديث البيانات\",\n                description: \"سيتم استخدام البيانات المحفوظة محلياً\",\n                variant: \"destructive\"\n            });\n            // Fallback to cached data\n            const currentAuction = auctions.find((a)=>a.id === auctionId);\n            if (currentAuction && canUserBid(currentAuction)) {\n                setSelectedAuction(currentAuction);\n                setBidAmount(String((currentAuction.currentBid || 0) + 1000));\n                setShowBidModal(true);\n            }\n        } finally{\n            setLoading(false);\n        }\n    };\n    const submitBid = async ()=>{\n        if (!selectedAuction) return;\n        const bidAmountNum = parseFloat(bidAmount);\n        if (isNaN(bidAmountNum) || bidAmountNum <= (selectedAuction.currentBid || 0)) {\n            toast({\n                title: \"مزايدة غير صالحة\",\n                description: \"يجب أن تكون المزايدة أكبر من المزايدة الحالية\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        try {\n            setBidLoading(true);\n            // Get auction version for optimistic locking\n            const auctionVersion = auctionVersions[selectedAuction.id];\n            // Prepare bid data with currency and version\n            const bidData = {\n                bidAmount: bidAmountNum,\n                currency: userCurrency,\n                version: auctionVersion\n            };\n            // Submit bid and wait for server response\n            const response = await fetch(\"/api/auctions/\".concat(selectedAuction.id, \"/bid\"), {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    \"Authorization\": \"Bearer \".concat(localStorage.getItem(\"token\"))\n                },\n                body: JSON.stringify(bidData)\n            });\n            const result = await response.json();\n            if (!response.ok) {\n                // Handle specific error cases\n                if (response.status === 409) {\n                    // Optimistic locking conflict\n                    toast({\n                        title: \"تم تحديث المزاد ⚠️\",\n                        description: \"قام شخص آخر بتحديث المزاد. سيتم تحديث البيانات.\",\n                        variant: \"destructive\"\n                    });\n                    await loadAuctions();\n                    setShowBidModal(false);\n                    setBidAmount(\"\");\n                    setSelectedAuction(null);\n                    return;\n                }\n                throw new Error(result.message || \"Bid failed\");\n            }\n            // Close modal first\n            setShowBidModal(false);\n            setBidAmount(\"\");\n            setSelectedAuction(null);\n            // Reload fresh data from server\n            await loadAuctions();\n            // Show success message with currency formatting\n            const formattedAmount = formatAmount(bidAmountNum);\n            toast({\n                title: \"تم تقديم المزايدة بنجاح! \\uD83C\\uDF89\",\n                description: \"مقدار المزايدة: \".concat(formattedAmount)\n            });\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error(\"Error placing bid:\", error);\n            // Close modal on error too\n            setShowBidModal(false);\n            setBidAmount(\"\");\n            setSelectedAuction(null);\n            // Show specific error message\n            const errorMessage = ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"يرجى المحاولة مرة أخرى\";\n            if (errorMessage.includes(\"higher than current price\") || errorMessage.includes(\"أكبر من السعر الحالي\")) {\n                toast({\n                    title: \"مزايدة منخفضة ⚠️\",\n                    description: \"قام شخص آخر بمزايدة أعلى. يرجى المحاولة بمبلغ أكبر.\",\n                    variant: \"destructive\"\n                });\n            } else if (errorMessage.includes(\"ended\") || errorMessage.includes(\"انتهى\")) {\n                toast({\n                    title: \"انتهى المزاد ⏰\",\n                    description: \"لقد انتهى هذا المزاد ولا يمكن قبول مزايدات جديدة.\",\n                    variant: \"destructive\"\n                });\n            } else {\n                toast({\n                    title: \"فشل في تقديم المزايدة\",\n                    description: errorMessage,\n                    variant: \"destructive\"\n                });\n            }\n            // Always reload data on error to sync with server\n            await loadAuctions();\n        } finally{\n            setBidLoading(false);\n        }\n    };\n    const handleSaveAuction = async (auctionId)=>{\n        console.log(\"❤️ Save button clicked for auction:\", auctionId);\n        // Show immediate feedback that button was clicked\n        toast({\n            title: \"\\uD83D\\uDC86 تم الضغط على زر الحفظ\",\n            description: \"جاري معالجة الطلب...\"\n        });\n        try {\n            const auction = auctions.find((a)=>a.id === auctionId);\n            if (!auction) {\n                toast({\n                    title: \"خطأ\",\n                    description: \"لم يتم العثور على المزاد\",\n                    variant: \"destructive\"\n                });\n                return;\n            }\n            const isCurrentlySaved = savedAuctions.has(auctionId);\n            // Optimistic UI update\n            setSavedAuctions((prev)=>{\n                const newSet = new Set(prev);\n                if (isCurrentlySaved) {\n                    newSet.delete(auctionId);\n                } else {\n                    newSet.add(auctionId);\n                }\n                return newSet;\n            });\n            if (isCurrentlySaved) {\n                // Remove from favorites\n                await _lib_api__WEBPACK_IMPORTED_MODULE_8__.favoritesAPI.removeFavorite(\"auction\", auctionId);\n                toast({\n                    title: \"❤️ تم إزالة من المفضلة\",\n                    description: 'تم إزالة \"'.concat(auction.title, '\" من المفضلة')\n                });\n            } else {\n                // Add to favorites\n                await _lib_api__WEBPACK_IMPORTED_MODULE_8__.favoritesAPI.addFavorite({\n                    itemType: \"auction\",\n                    itemId: auctionId,\n                    notifications: {\n                        bidUpdates: true,\n                        statusChanges: true,\n                        endingSoon: true\n                    }\n                });\n                toast({\n                    title: \"\\uD83D\\uDC96 تم إضافة إلى المفضلة\",\n                    description: 'تم إضافة \"'.concat(auction.title, '\" إلى المفضلة')\n                });\n            }\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error(\"Error saving auction:\", error);\n            // Revert optimistic update on error\n            setSavedAuctions((prev)=>{\n                const newSet = new Set(prev);\n                if (savedAuctions.has(auctionId)) {\n                    newSet.add(auctionId);\n                } else {\n                    newSet.delete(auctionId);\n                }\n                return newSet;\n            });\n            toast({\n                title: \"فشل في حفظ المزاد\",\n                description: ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"يرجى المحاولة مرة أخرى\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleViewAuction = async (auctionId)=>{\n        console.log(\"\\uD83D\\uDC41️ View button clicked for auction:\", auctionId);\n        try {\n            const auction = auctions.find((a)=>a.id === auctionId);\n            if (!auction) {\n                toast({\n                    title: \"خطأ\",\n                    description: \"لم يتم العثور على المزاد\",\n                    variant: \"destructive\"\n                });\n                return;\n            }\n            // Navigate to auction detail page\n            router.push(\"/auctions/\".concat(auctionId));\n            toast({\n                title: \"\\uD83D\\uDC41️ جاري تحميل التفاصيل\",\n                description: \"سيتم عرض تفاصيل المزاد\"\n            });\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error(\"Error viewing auction:\", error);\n            toast({\n                title: \"فشل في عرض تفاصيل المزاد\",\n                description: ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"يرجى المحاولة مرة أخرى\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-3xl font-bold\",\n                                    children: \"المزادات المتاحة\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 612,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-muted-foreground\",\n                                    children: [\n                                        \"استكشف المزادات الحالية وشارك في المزايدة\",\n                                        autoRefresh && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs text-green-600 ml-2\",\n                                            children: \"• تحديث تلقائي كل 30 ثانية\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                            lineNumber: 616,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 613,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                            lineNumber: 611,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_CurrencySelector__WEBPACK_IMPORTED_MODULE_12__.CurrencySelector, {}, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 623,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    onClick: async ()=>{\n                                        await loadAuctions(true) // Show loading state\n                                        ;\n                                        setLastRefresh(new Date());\n                                        toast({\n                                            title: \"\\uD83D\\uDD04 تم تحديث البيانات\",\n                                            description: \"تم العثور على \".concat(filteredAuctions.length, \" مزاد متاح\")\n                                        });\n                                    },\n                                    variant: \"outline\",\n                                    disabled: loading,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"h-4 w-4 ml-2 \".concat(loading ? \"animate-spin\" : \"\")\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                            lineNumber: 636,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"تحديث\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 624,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    onClick: ()=>{\n                                        setAutoRefresh(!autoRefresh);\n                                        toast({\n                                            title: autoRefresh ? \"⏸️ تم إيقاف التحديث التلقائي\" : \"▶️ تم تشغيل التحديث التلقائي\",\n                                            description: autoRefresh ? \"يمكنك التحديث يدوياً\" : \"سيتم التحديث كل 30 ثانية\"\n                                        });\n                                    },\n                                    variant: autoRefresh ? \"default\" : \"outline\",\n                                    size: \"icon\",\n                                    children: autoRefresh ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 650,\n                                        columnNumber: 32\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 650,\n                                        columnNumber: 64\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 639,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                            lineNumber: 622,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                    lineNumber: 610,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                lineNumber: 609,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                    className: \"pt-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        className: \"absolute right-3 top-3 h-4 w-4 text-muted-foreground\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 661,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                        placeholder: \"ابحث في المزادات...\",\n                                        value: searchTerm,\n                                        onChange: (e)=>setSearchTerm(e.target.value),\n                                        className: \"pr-10\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 662,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                lineNumber: 660,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col sm:flex-row gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                htmlFor: \"category\",\n                                                className: \"text-sm font-medium\",\n                                                children: \"الفئة\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                lineNumber: 672,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                id: \"category\",\n                                                value: filterCategory,\n                                                onChange: (e)=>setFilterCategory(e.target.value),\n                                                className: \"w-full mt-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"\",\n                                                        children: \"جميع الفئات\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 679,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"electronics\",\n                                                        children: \"إلكترونيات\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 680,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"vehicles\",\n                                                        children: \"سيارات\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 681,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"real_estate\",\n                                                        children: \"عقارات\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 682,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"art_collectibles\",\n                                                        children: \"فنون ومقتنيات\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 683,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"machinery\",\n                                                        children: \"معدات\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 684,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"furniture\",\n                                                        children: \"أثاث\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 685,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"jewelry\",\n                                                        children: \"مجوهرات\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 686,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"books_media\",\n                                                        children: \"كتب ووسائط\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 687,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"clothing\",\n                                                        children: \"ملابس\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 688,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"sports\",\n                                                        children: \"رياضة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 689,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"other\",\n                                                        children: \"أخرى\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 690,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                lineNumber: 673,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 671,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                htmlFor: \"sort\",\n                                                className: \"text-sm font-medium\",\n                                                children: \"ترتيب حسب\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                lineNumber: 695,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                id: \"sort\",\n                                                value: sortBy,\n                                                onChange: (e)=>setSortBy(e.target.value),\n                                                className: \"w-full mt-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"endDate\",\n                                                        children: \"تاريخ الانتهاء\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 702,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"currentBid\",\n                                                        children: \"أعلى مزايدة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 703,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"bidsCount\",\n                                                        children: \"عدد المزايدات\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 704,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"title\",\n                                                        children: \"الاسم\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 705,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                lineNumber: 696,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 694,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                lineNumber: 670,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                        lineNumber: 659,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                    lineNumber: 658,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                lineNumber: 657,\n                columnNumber: 9\n            }, this),\n            Array.isArray(filteredAuctions) && filteredAuctions.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                    className: \"text-lg\",\n                                    children: \"إجمالي المزادات\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 718,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                    className: \"text-2xl font-bold text-blue-600\",\n                                    children: filteredAuctions.length\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 719,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                            lineNumber: 717,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                        lineNumber: 716,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                    className: \"text-lg\",\n                                    children: \"المزايدات النشطة\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 726,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                    className: \"text-2xl font-bold text-green-600\",\n                                    children: filteredAuctions.reduce((sum, a)=>sum + (a.bidsCount || 0), 0)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 727,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                            lineNumber: 725,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                        lineNumber: 724,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                    className: \"text-lg\",\n                                    children: \"أعلى مزايدة\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 734,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                    className: \"text-2xl font-bold text-purple-600\",\n                                    children: filteredAuctions.length > 0 ? formatAmount(Math.max(...filteredAuctions.map((a)=>a.currentBid || 0))) : formatAmount(0)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 735,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                            lineNumber: 733,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                        lineNumber: 732,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                lineNumber: 715,\n                columnNumber: 11\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                children: Array.isArray(filteredAuctions) && filteredAuctions.map((auction)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        className: \"hover:shadow-lg transition-shadow cursor-pointer\",\n                        onClick: ()=>handleViewAuction(auction.id),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"aspect-video bg-gradient-to-br from-gray-100 to-gray-200 rounded-lg mb-4 flex items-center justify-center relative overflow-hidden\",\n                                        children: [\n                                            auction.images && auction.images.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: auction.images[0].url,\n                                                alt: auction.title,\n                                                className: \"w-full h-full object-cover\",\n                                                onError: (e)=>{\n                                                    e.currentTarget.style.display = \"none\";\n                                                    e.currentTarget.nextElementSibling.style.display = \"flex\";\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                lineNumber: 753,\n                                                columnNumber: 21\n                                            }, this) : null,\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-center w-full h-full text-gray-500\",\n                                                style: {\n                                                    display: auction.images && auction.images.length > 0 ? \"none\" : \"flex\"\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                            className: \"h-8 w-8 mx-auto mb-2 text-gray-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                            lineNumber: 765,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm\",\n                                                            children: \"صورة المزاد\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                            lineNumber: 766,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                    lineNumber: 764,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                lineNumber: 763,\n                                                columnNumber: 19\n                                            }, this),\n                                            auction.status === \"active\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                className: \"absolute top-2 right-2 bg-green-500\",\n                                                children: \"نشط\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                lineNumber: 770,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 751,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                        className: \"text-lg line-clamp-2\",\n                                        children: auction.title\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 775,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                        className: \"line-clamp-2\",\n                                        children: auction.description\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 776,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                lineNumber: 750,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-muted-foreground\",\n                                                        children: \"أعلى مزايدة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 783,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xl font-bold text-green-600\",\n                                                        children: [\n                                                            formatAmount(auction.currentBid || 0),\n                                                            auction.originalCurrency && auction.originalCurrency !== userCurrency && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs text-gray-500 ml-2\",\n                                                                children: [\n                                                                    \"(من \",\n                                                                    auction.originalCurrency,\n                                                                    \")\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                                lineNumber: 787,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 784,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                lineNumber: 782,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-muted-foreground\",\n                                                        children: \"المزايدات\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 794,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-lg font-semibold\",\n                                                        children: auction.bidsCount || 0\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 795,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                lineNumber: 793,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 781,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                className: \"h-4 w-4 text-muted-foreground\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                lineNumber: 800,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-muted-foreground\",\n                                                children: [\n                                                    \"ينتهي خلال: \",\n                                                    getTimeRemaining(auction.endDate)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                lineNumber: 801,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 799,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-muted-foreground\",\n                                        children: [\n                                            \"البائع: \",\n                                            auction.seller\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 806,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                onClick: (e)=>{\n                                                    e.preventDefault();\n                                                    e.stopPropagation();\n                                                    handleBid(auction.id);\n                                                },\n                                                className: \"flex-1\",\n                                                disabled: loading || bidLoading,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        className: \"h-4 w-4 ml-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 820,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    bidLoading ? \"جاري المزايدة...\" : \"مزايدة\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                lineNumber: 811,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                variant: savedAuctions.has(auction.id) ? \"default\" : \"outline\",\n                                                size: \"icon\",\n                                                onClick: (e)=>{\n                                                    e.preventDefault();\n                                                    e.stopPropagation();\n                                                    handleSaveAuction(auction.id);\n                                                },\n                                                disabled: loading,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                    className: \"h-4 w-4 \".concat(savedAuctions.has(auction.id) ? \"fill-current\" : \"\")\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                    lineNumber: 833,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                lineNumber: 823,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                variant: \"outline\",\n                                                size: \"icon\",\n                                                onClick: (e)=>{\n                                                    e.preventDefault();\n                                                    e.stopPropagation();\n                                                    handleViewAuction(auction.id);\n                                                },\n                                                disabled: loading,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                    lineNumber: 845,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                lineNumber: 835,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 810,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                lineNumber: 780,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, auction.id, true, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                        lineNumber: 749,\n                        columnNumber: 13\n                    }, this))\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                lineNumber: 747,\n                columnNumber: 9\n            }, this),\n            !loading && (!Array.isArray(filteredAuctions) || filteredAuctions.length === 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                    className: \"py-16 text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col items-center gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-16 h-16 rounded-full bg-gray-100 flex items-center justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                    className: \"h-8 w-8 text-gray-400\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 859,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                lineNumber: 858,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold mb-2\",\n                                        children: searchTerm ? \"لا توجد مزادات تطابق البحث\" : \"لا توجد مزادات متاحة حالياً\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 862,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-muted-foreground\",\n                                        children: searchTerm ? \"جرب البحث بكلمات مختلفة أو امسح مربع البحث لعرض جميع المزادات\" : \"سيتم عرض المزادات المتاحة هنا عند إضافتها من قبل الشركات\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 865,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                lineNumber: 861,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                        lineNumber: 857,\n                        columnNumber: 15\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                    lineNumber: 856,\n                    columnNumber: 13\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                lineNumber: 855,\n                columnNumber: 11\n            }, this),\n            loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                    className: \"py-16 text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col items-center gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                lineNumber: 882,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-muted-foreground\",\n                                children: \"جاري تحميل المزادات...\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                lineNumber: 883,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                        lineNumber: 881,\n                        columnNumber: 15\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                    lineNumber: 880,\n                    columnNumber: 13\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                lineNumber: 879,\n                columnNumber: 11\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__.Dialog, {\n                open: showBidModal,\n                onOpenChange: setShowBidModal,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__.DialogContent, {\n                    className: \"sm:max-w-md\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__.DialogHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__.DialogTitle, {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 894,\n                                        columnNumber: 17\n                                    }, this),\n                                    \"تقديم مزايدة\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                lineNumber: 893,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                            lineNumber: 892,\n                            columnNumber: 13\n                        }, this),\n                        selectedAuction && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gray-50 p-4 rounded-lg\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"font-semibold mb-2\",\n                                            children: selectedAuction.title\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                            lineNumber: 902,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-4 text-sm text-gray-600\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                            lineNumber: 905,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: [\n                                                                \"المزايدة الحالية: \",\n                                                                formatAmount(selectedAuction.currentBid || 0)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                            lineNumber: 906,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                    lineNumber: 904,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                            lineNumber: 909,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: [\n                                                                selectedAuction.bidsCount || 0,\n                                                                \" مزايدة\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                            lineNumber: 910,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                    lineNumber: 908,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                            lineNumber: 903,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 901,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                            htmlFor: \"bidAmount\",\n                                            children: [\n                                                \"مقدار المزايدة (\",\n                                                userCurrency,\n                                                \")\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                            lineNumber: 916,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                            id: \"bidAmount\",\n                                            type: \"number\",\n                                            value: bidAmount,\n                                            onChange: (e)=>setBidAmount(e.target.value),\n                                            placeholder: \"أدخل مقدار المزايدة\",\n                                            min: (selectedAuction.currentBid || 0) + 1\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                            lineNumber: 917,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-gray-500\",\n                                            children: [\n                                                \"الحد الأدنى: \",\n                                                ((selectedAuction.currentBid || 0) + 1).toLocaleString(),\n                                                \" ر.س\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                            lineNumber: 925,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 915,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                            lineNumber: 900,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__.DialogFooter, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"outline\",\n                                    onClick: ()=>{\n                                        setShowBidModal(false);\n                                        setBidAmount(\"\");\n                                        setSelectedAuction(null);\n                                    },\n                                    disabled: bidLoading,\n                                    children: \"إلغاء\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 933,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    onClick: submitBid,\n                                    disabled: bidLoading || !bidAmount,\n                                    className: \"bg-green-600 hover:bg-green-700\",\n                                    children: bidLoading ? \"جاري التقديم...\" : \"تقديم المزايدة\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 944,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                            lineNumber: 932,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                    lineNumber: 891,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                lineNumber: 890,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n        lineNumber: 608,\n        columnNumber: 5\n    }, this);\n}\n_s(UserAuctionsPage, \"0C/dfCNanwYtXVZSEIcjKhB8Lbk=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_9__.useRouter,\n        _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_10__.useToast,\n        _contexts_CurrencyContext__WEBPACK_IMPORTED_MODULE_11__.useCurrency\n    ];\n});\n_c = UserAuctionsPage;\nvar _c;\n$RefreshReg$(_c, \"UserAuctionsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/user/auctions/page.tsx\n"));

/***/ })

});