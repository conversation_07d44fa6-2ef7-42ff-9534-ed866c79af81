"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/user/auctions/page",{

/***/ "(app-pages-browser)/./app/user/auctions/page.tsx":
/*!************************************!*\
  !*** ./app/user/auctions/page.tsx ***!
  \************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ UserAuctionsPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _barrel_optimize_names_Clock_Eye_Heart_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Eye,Heart,Search,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Eye_Heart_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Eye,Heart,Search,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Eye_Heart_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Eye,Heart,Search,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Eye_Heart_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Eye,Heart,Search,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Eye_Heart_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Eye,Heart,Search,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./lib/api.ts\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/use-toast */ \"(app-pages-browser)/./components/ui/use-toast.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction UserAuctionsPage() {\n    _s();\n    const [auctions, setAuctions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [filteredAuctions, setFilteredAuctions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [savedAuctions, setSavedAuctions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_6__.useRouter)();\n    const { toast } = (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_7__.useToast)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Get user data and check permissions\n        const userData = localStorage.getItem(\"user\");\n        if (userData) {\n            const parsedUser = JSON.parse(userData);\n            setUser(parsedUser);\n            // Redirect admins to their proper dashboard\n            if (parsedUser.role === \"admin\" || parsedUser.role === \"super_admin\") {\n                toast({\n                    title: \"وصول غير مسموح\",\n                    description: \"المديرون يجب أن يستخدموا لوحة إدارة المزادات\",\n                    variant: \"destructive\"\n                });\n                router.push(\"/admin/auctions\");\n                return;\n            }\n            // Redirect government users to their dashboard\n            if (parsedUser.role === \"government\") {\n                toast({\n                    title: \"وصول غير مسموح\",\n                    description: \"الجهات الحكومية تستخدم نظام المناقصات\",\n                    variant: \"destructive\"\n                });\n                router.push(\"/government/dashboard\");\n                return;\n            }\n        }\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadAuctions();\n        loadSavedAuctions();\n    }, []);\n    const loadAuctions = async ()=>{\n        try {\n            setLoading(true);\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_5__.auctionAPI.getAll();\n            console.log(\"Auctions API response:\", response.data);\n            // Handle the API response structure properly\n            let auctionData = [];\n            if (response.data && response.data.success) {\n                var _response_data_data;\n                // If the response has a success field, extract the auctions from data.data.auctions\n                auctionData = ((_response_data_data = response.data.data) === null || _response_data_data === void 0 ? void 0 : _response_data_data.auctions) || [];\n            } else if (response.data && Array.isArray(response.data)) {\n                // If response.data is directly an array\n                auctionData = response.data;\n            } else if (response.data && response.data.data && Array.isArray(response.data.data)) {\n                // If the auctions are in response.data.data\n                auctionData = response.data.data;\n            }\n            // Transform the data to match the expected format\n            const transformedData = auctionData.map((auction)=>{\n                var _auction_bids, _auction_seller_profile, _auction_seller, _auction_seller_profile1, _auction_seller1, _auction_seller2, _auction_seller3;\n                return {\n                    id: auction._id || auction.id,\n                    title: auction.title,\n                    description: auction.description,\n                    currentBid: auction.currentPrice || auction.currentBid || auction.startingPrice || 0,\n                    endDate: auction.endDate,\n                    bidsCount: ((_auction_bids = auction.bids) === null || _auction_bids === void 0 ? void 0 : _auction_bids.length) || auction.bidsCount || 0,\n                    seller: ((_auction_seller = auction.seller) === null || _auction_seller === void 0 ? void 0 : (_auction_seller_profile = _auction_seller.profile) === null || _auction_seller_profile === void 0 ? void 0 : _auction_seller_profile.fullName) || ((_auction_seller1 = auction.seller) === null || _auction_seller1 === void 0 ? void 0 : (_auction_seller_profile1 = _auction_seller1.profile) === null || _auction_seller_profile1 === void 0 ? void 0 : _auction_seller_profile1.companyName) || ((_auction_seller2 = auction.seller) === null || _auction_seller2 === void 0 ? void 0 : _auction_seller2.name) || ((_auction_seller3 = auction.seller) === null || _auction_seller3 === void 0 ? void 0 : _auction_seller3._id) || \"غير محدد\",\n                    category: auction.category,\n                    status: auction.status,\n                    views: auction.views || 0,\n                    location: auction.location\n                };\n            });\n            setAuctions(transformedData);\n            setFilteredAuctions(transformedData);\n        } catch (error) {\n            console.error(\"Error loading auctions:\", error);\n            setAuctions([]);\n            setFilteredAuctions([]);\n            toast({\n                title: \"فشل في تحميل المزادات\",\n                description: \"حدث خطأ أثناء تحميل المزادات. يرجى المحاولة مرة أخرى.\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    const loadSavedAuctions = async ()=>{\n        try {\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_5__.favoritesAPI.getFavorites({\n                type: \"auction\"\n            });\n            const savedIds = new Set(response.data.map((fav)=>fav.itemId));\n            setSavedAuctions(savedIds);\n        } catch (error) {\n            console.error(\"Error loading saved auctions:\", error);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Ensure auctions is an array before filtering\n        if (Array.isArray(auctions)) {\n            const filtered = auctions.filter((auction)=>{\n                var _auction_title, _auction_description;\n                return ((_auction_title = auction.title) === null || _auction_title === void 0 ? void 0 : _auction_title.toLowerCase().includes(searchTerm.toLowerCase())) || ((_auction_description = auction.description) === null || _auction_description === void 0 ? void 0 : _auction_description.toLowerCase().includes(searchTerm.toLowerCase()));\n            });\n            setFilteredAuctions(filtered);\n        } else {\n            setFilteredAuctions([]);\n        }\n    }, [\n        searchTerm,\n        auctions\n    ]);\n    const getTimeRemaining = (endDate)=>{\n        const now = new Date();\n        const end = new Date(endDate);\n        const diff = end.getTime() - now.getTime();\n        if (diff <= 0) return \"انتهى\";\n        const hours = Math.floor(diff / (1000 * 60 * 60));\n        const days = Math.floor(hours / 24);\n        if (days > 0) return \"\".concat(days, \" يوم\");\n        return \"\".concat(hours, \" ساعة\");\n    };\n    // Check if user can bid\n    const canUserBid = (auction)=>{\n        var _user_profile;\n        if (!user) return false;\n        // Only individuals and companies can bid\n        if (user.role !== \"individual\" && user.role !== \"company\") {\n            return false;\n        }\n        // Companies cannot bid on their own auctions\n        if (user.role === \"company\" && auction.seller === ((_user_profile = user.profile) === null || _user_profile === void 0 ? void 0 : _user_profile.companyName)) {\n            return false;\n        }\n        return true;\n    };\n    const handleBid = async (auctionId)=>{\n        console.log(\"\\uD83C\\uDFAF Bid button clicked for auction:\", auctionId);\n        try {\n            var _currentAuction_currentBid;\n            const currentAuction = auctions.find((a)=>a.id === auctionId);\n            if (!currentAuction) {\n                toast({\n                    title: \"خطأ\",\n                    description: \"لم يتم العثور على المزاد\",\n                    variant: \"destructive\"\n                });\n                return;\n            }\n            // Check if user can bid\n            if (!canUserBid(currentAuction)) {\n                if ((user === null || user === void 0 ? void 0 : user.role) === \"admin\" || (user === null || user === void 0 ? void 0 : user.role) === \"super_admin\") {\n                    toast({\n                        title: \"وصول غير مسموح\",\n                        description: \"المديرون لا يمكنهم المزايدة على المزادات\",\n                        variant: \"destructive\"\n                    });\n                } else if ((user === null || user === void 0 ? void 0 : user.role) === \"government\") {\n                    toast({\n                        title: \"وصول غير مسموح\",\n                        description: \"الجهات الحكومية لا تزايد على المزادات\",\n                        variant: \"destructive\"\n                    });\n                } else if ((user === null || user === void 0 ? void 0 : user.role) === \"company\") {\n                    toast({\n                        title: \"وصول غير مسموح\",\n                        description: \"لا يمكنك المزايدة على مزادك الخاص\",\n                        variant: \"destructive\"\n                    });\n                } else {\n                    toast({\n                        title: \"وصول غير مسموح\",\n                        description: \"غير مسموح لك بالمزايدة\",\n                        variant: \"destructive\"\n                    });\n                }\n                return;\n            }\n            // Show immediate feedback that button was clicked\n            toast({\n                title: \"\\uD83D\\uDC86 تم الضغط على زر المزايدة\",\n                description: \"جاري معالجة المزايدة...\"\n            });\n            // Prompt user for bid amount\n            const bidAmountStr = prompt(\"المزايدة الحالية: \".concat(((_currentAuction_currentBid = currentAuction.currentBid) === null || _currentAuction_currentBid === void 0 ? void 0 : _currentAuction_currentBid.toLocaleString()) || 0, \" ر.س\\n\\nأدخل مقدار مزايدتك:\"), String((currentAuction.currentBid || 0) + 1000));\n            if (!bidAmountStr) return; // User cancelled\n            const bidAmount = parseFloat(bidAmountStr);\n            if (isNaN(bidAmount) || bidAmount <= (currentAuction.currentBid || 0)) {\n                toast({\n                    title: \"مزايدة غير صالحة\",\n                    description: \"يجب أن تكون المزايدة أكبر من المزايدة الحالية\",\n                    variant: \"destructive\"\n                });\n                return;\n            }\n            setLoading(true);\n            await _lib_api__WEBPACK_IMPORTED_MODULE_5__.auctionAPI.placeBid(auctionId, bidAmount);\n            // Update local state optimistically\n            setAuctions((prev)=>prev.map((auction)=>auction.id === auctionId ? {\n                        ...auction,\n                        currentBid: bidAmount,\n                        bidsCount: (auction.bidsCount || 0) + 1\n                    } : auction));\n            setFilteredAuctions((prev)=>prev.map((auction)=>auction.id === auctionId ? {\n                        ...auction,\n                        currentBid: bidAmount,\n                        bidsCount: (auction.bidsCount || 0) + 1\n                    } : auction));\n            toast({\n                title: \"تم تقديم المزايدة بنجاح! \\uD83C\\uDF89\",\n                description: \"مقدار المزايدة: \".concat(bidAmount.toLocaleString(), \" ر.س\")\n            });\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error(\"Error placing bid:\", error);\n            toast({\n                title: \"فشل في تقديم المزايدة\",\n                description: ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"يرجى المحاولة مرة أخرى\",\n                variant: \"destructive\"\n            });\n            // Reload data on error to sync with server\n            loadAuctions();\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleSaveAuction = async (auctionId)=>{\n        console.log(\"❤️ Save button clicked for auction:\", auctionId);\n        // Show immediate feedback that button was clicked\n        toast({\n            title: \"\\uD83D\\uDC86 تم الضغط على زر الحفظ\",\n            description: \"جاري معالجة الطلب...\"\n        });\n        try {\n            const auction = auctions.find((a)=>a.id === auctionId);\n            if (!auction) {\n                toast({\n                    title: \"خطأ\",\n                    description: \"لم يتم العثور على المزاد\",\n                    variant: \"destructive\"\n                });\n                return;\n            }\n            const isCurrentlySaved = savedAuctions.has(auctionId);\n            // Optimistic UI update\n            setSavedAuctions((prev)=>{\n                const newSet = new Set(prev);\n                if (isCurrentlySaved) {\n                    newSet.delete(auctionId);\n                } else {\n                    newSet.add(auctionId);\n                }\n                return newSet;\n            });\n            if (isCurrentlySaved) {\n                // Remove from favorites\n                await _lib_api__WEBPACK_IMPORTED_MODULE_5__.favoritesAPI.removeFavorite(\"auction\", auctionId);\n                toast({\n                    title: \"❤️ تم إزالة من المفضلة\",\n                    description: 'تم إزالة \"'.concat(auction.title, '\" من المفضلة')\n                });\n            } else {\n                // Add to favorites\n                await _lib_api__WEBPACK_IMPORTED_MODULE_5__.favoritesAPI.addFavorite({\n                    itemType: \"auction\",\n                    itemId: auctionId,\n                    notifications: {\n                        bidUpdates: true,\n                        statusChanges: true,\n                        endingSoon: true\n                    }\n                });\n                toast({\n                    title: \"\\uD83D\\uDC96 تم إضافة إلى المفضلة\",\n                    description: 'تم إضافة \"'.concat(auction.title, '\" إلى المفضلة')\n                });\n            }\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error(\"Error saving auction:\", error);\n            // Revert optimistic update on error\n            setSavedAuctions((prev)=>{\n                const newSet = new Set(prev);\n                if (savedAuctions.has(auctionId)) {\n                    newSet.add(auctionId);\n                } else {\n                    newSet.delete(auctionId);\n                }\n                return newSet;\n            });\n            toast({\n                title: \"فشل في حفظ المزاد\",\n                description: ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"يرجى المحاولة مرة أخرى\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleViewAuction = async (auctionId)=>{\n        console.log(\"\\uD83D\\uDC41️ View button clicked for auction:\", auctionId);\n        // Show immediate feedback that button was clicked\n        toast({\n            title: \"\\uD83D\\uDC86 تم الضغط على زر العرض\",\n            description: \"جاري تحميل التفاصيل...\"\n        });\n        try {\n            const auction = auctions.find((a)=>a.id === auctionId);\n            if (!auction) {\n                toast({\n                    title: \"خطأ\",\n                    description: \"لم يتم العثور على المزاد\",\n                    variant: \"destructive\"\n                });\n                return;\n            }\n            // For now, show auction details in an alert - in real app this would navigate to detail page\n            const details = \"عنوان المزاد: \".concat(auction.title, \"\\n\") + \"الوصف: \".concat(auction.description || \"غير متوفر\", \"\\n\") + \"المزايدة الحالية: \".concat((auction.currentBid || 0).toLocaleString(), \" ر.س\\n\") + \"عدد المزايدات: \".concat(auction.bidsCount || 0, \"\\n\") + \"البائع: \".concat(auction.seller || \"غير معروف\", \"\\n\") + \"ينتهي: \".concat(auction.endDate || \"غير محدد\");\n            alert(details);\n            // Try to fetch fresh data from API\n            try {\n                const response = await _lib_api__WEBPACK_IMPORTED_MODULE_5__.auctionAPI.getById(auctionId);\n                console.log(\"Fresh auction data:\", response.data);\n                toast({\n                    title: \"\\uD83D\\uDC41️ تم عرض التفاصيل\",\n                    description: \"تم تحديث بيانات المزاد من الخادم\"\n                });\n            } catch (apiError) {\n                console.log(\"Could not fetch fresh data, showing cached data\");\n                toast({\n                    title: \"\\uD83D\\uDC41️ عرض التفاصيل\",\n                    description: \"يتم عرض البيانات المحفوظة محلياً\"\n                });\n            }\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error(\"Error viewing auction:\", error);\n            toast({\n                title: \"فشل في عرض تفاصيل المزاد\",\n                description: ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"يرجى المحاولة مرة أخرى\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DashboardLayout, {\n        allowedRoles: [\n            \"individual\"\n        ],\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-3xl font-bold\",\n                                        children: \"المزادات المتاحة\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 414,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-muted-foreground\",\n                                        children: \"استكشف المزادات الحالية وشارك في المزايدة\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 415,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                lineNumber: 413,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                onClick: ()=>{\n                                    alert(\"\\uD83C\\uDF89 Test button works! Buttons are functional.\");\n                                    console.log(\"Test button clicked successfully!\");\n                                    toast({\n                                        title: \"✅ الأزرار تعمل بشكل طبيعي\",\n                                        description: \"يمكنك الآن استخدام أزرار المزادات\"\n                                    });\n                                },\n                                variant: \"outline\",\n                                children: \"\\uD83E\\uDDEA اختبار الأزرار\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                lineNumber: 417,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                        lineNumber: 412,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                    lineNumber: 411,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        className: \"pt-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Eye_Heart_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"absolute right-3 top-3 h-4 w-4 text-muted-foreground\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 437,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                    placeholder: \"ابحث في المزادات...\",\n                                    value: searchTerm,\n                                    onChange: (e)=>setSearchTerm(e.target.value),\n                                    className: \"pr-10\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 438,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                            lineNumber: 436,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                        lineNumber: 435,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                    lineNumber: 434,\n                    columnNumber: 9\n                }, this),\n                Array.isArray(filteredAuctions) && filteredAuctions.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                        className: \"text-lg\",\n                                        children: \"إجمالي المزادات\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 453,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                        className: \"text-2xl font-bold text-blue-600\",\n                                        children: filteredAuctions.length\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 454,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                lineNumber: 452,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                            lineNumber: 451,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                        className: \"text-lg\",\n                                        children: \"المزايدات النشطة\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 461,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                        className: \"text-2xl font-bold text-green-600\",\n                                        children: filteredAuctions.reduce((sum, a)=>sum + (a.bidsCount || 0), 0)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 462,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                lineNumber: 460,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                            lineNumber: 459,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                        className: \"text-lg\",\n                                        children: \"أعلى مزايدة\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 469,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                        className: \"text-2xl font-bold text-purple-600\",\n                                        children: filteredAuctions.length > 0 ? Math.max(...filteredAuctions.map((a)=>a.currentBid || 0)).toLocaleString() + \" ر.س\" : \"0 ر.س\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 470,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                lineNumber: 468,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                            lineNumber: 467,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                    lineNumber: 450,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                    children: Array.isArray(filteredAuctions) && filteredAuctions.map((auction)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            className: \"hover:shadow-lg transition-shadow\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"aspect-video bg-gray-200 rounded-lg mb-4 flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-500\",\n                                                children: \"صورة المنتج\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                lineNumber: 487,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                            lineNumber: 486,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                            className: \"text-lg line-clamp-2\",\n                                            children: auction.title\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                            lineNumber: 489,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                            className: \"line-clamp-2\",\n                                            children: auction.description\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                            lineNumber: 490,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 485,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-muted-foreground\",\n                                                            children: \"أعلى مزايدة\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                            lineNumber: 497,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xl font-bold text-green-600\",\n                                                            children: [\n                                                                (auction.currentBid || 0).toLocaleString(),\n                                                                \" ر.س\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                            lineNumber: 498,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                    lineNumber: 496,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-muted-foreground\",\n                                                            children: \"المزايدات\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                            lineNumber: 503,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-lg font-semibold\",\n                                                            children: auction.bidsCount || 0\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                            lineNumber: 504,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                    lineNumber: 502,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                            lineNumber: 495,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Eye_Heart_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"h-4 w-4 text-muted-foreground\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                    lineNumber: 509,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-muted-foreground\",\n                                                    children: [\n                                                        \"ينتهي خلال: \",\n                                                        getTimeRemaining(auction.endDate)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                    lineNumber: 510,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                            lineNumber: 508,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs text-muted-foreground\",\n                                            children: [\n                                                \"البائع: \",\n                                                auction.seller\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                            lineNumber: 515,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    onClick: (e)=>{\n                                                        e.preventDefault();\n                                                        e.stopPropagation();\n                                                        alert(\"Bid button clicked for auction: \" + auction.id);\n                                                        console.log(\"BID BUTTON CLICKED for auction:\", auction.id);\n                                                        handleBid(auction.id);\n                                                    },\n                                                    className: \"flex-1\",\n                                                    disabled: loading,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Eye_Heart_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                            className: \"h-4 w-4 ml-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                            lineNumber: 531,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        loading ? \"جاري المزايدة...\" : \"مزايدة\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                    lineNumber: 520,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    variant: savedAuctions.has(auction.id) ? \"default\" : \"outline\",\n                                                    size: \"icon\",\n                                                    onClick: (e)=>{\n                                                        e.preventDefault();\n                                                        e.stopPropagation();\n                                                        alert(\"Save button clicked for auction: \" + auction.id);\n                                                        console.log(\"SAVE BUTTON CLICKED for auction:\", auction.id);\n                                                        handleSaveAuction(auction.id);\n                                                    },\n                                                    disabled: loading,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Eye_Heart_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"h-4 w-4 \".concat(savedAuctions.has(auction.id) ? \"fill-current\" : \"\")\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 546,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                    lineNumber: 534,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    variant: \"outline\",\n                                                    size: \"icon\",\n                                                    onClick: (e)=>{\n                                                        e.preventDefault();\n                                                        e.stopPropagation();\n                                                        alert(\"View button clicked for auction: \" + auction.id);\n                                                        console.log(\"VIEW BUTTON CLICKED for auction:\", auction.id);\n                                                        handleViewAuction(auction.id);\n                                                    },\n                                                    disabled: loading,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Eye_Heart_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 560,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                    lineNumber: 548,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                            lineNumber: 519,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 494,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, auction.id, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                            lineNumber: 484,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                    lineNumber: 482,\n                    columnNumber: 9\n                }, this),\n                !loading && (!Array.isArray(filteredAuctions) || filteredAuctions.length === 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        className: \"py-16 text-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col items-center gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-16 h-16 rounded-full bg-gray-100 flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Eye_Heart_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"h-8 w-8 text-gray-400\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 574,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 573,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold mb-2\",\n                                            children: searchTerm ? \"لا توجد مزادات تطابق البحث\" : \"لا توجد مزادات متاحة حالياً\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                            lineNumber: 577,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-muted-foreground\",\n                                            children: searchTerm ? \"جرب البحث بكلمات مختلفة أو امسح مربع البحث لعرض جميع المزادات\" : \"سيتم عرض المزادات المتاحة هنا عند إضافتها من قبل الشركات\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                            lineNumber: 580,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 576,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                            lineNumber: 572,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                        lineNumber: 571,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                    lineNumber: 570,\n                    columnNumber: 11\n                }, this),\n                loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        className: \"py-16 text-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col items-center gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 597,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-muted-foreground\",\n                                    children: \"جاري تحميل المزادات...\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 598,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                            lineNumber: 596,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                        lineNumber: 595,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                    lineNumber: 594,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n            lineNumber: 410,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n        lineNumber: 409,\n        columnNumber: 5\n    }, this);\n}\n_s(UserAuctionsPage, \"6SomYB603PhCI9dVoioSOuqDBi4=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_6__.useRouter,\n        _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_7__.useToast\n    ];\n});\n_c = UserAuctionsPage;\nvar _c;\n$RefreshReg$(_c, \"UserAuctionsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/user/auctions/page.tsx\n"));

/***/ })

});