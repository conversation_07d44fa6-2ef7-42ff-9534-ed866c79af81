"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/user/auctions/page",{

/***/ "(app-pages-browser)/./app/user/auctions/page.tsx":
/*!************************************!*\
  !*** ./app/user/auctions/page.tsx ***!
  \************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ UserAuctionsPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./components/ui/dialog.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./components/ui/label.tsx\");\n/* harmony import */ var _barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,DollarSign,Eye,Gavel,Heart,Pause,Play,RefreshCw,Search,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,DollarSign,Eye,Gavel,Heart,Pause,Play,RefreshCw,Search,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pause.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,DollarSign,Eye,Gavel,Heart,Pause,Play,RefreshCw,Search,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,DollarSign,Eye,Gavel,Heart,Pause,Play,RefreshCw,Search,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,DollarSign,Eye,Gavel,Heart,Pause,Play,RefreshCw,Search,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/gavel.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,DollarSign,Eye,Gavel,Heart,Pause,Play,RefreshCw,Search,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,DollarSign,Eye,Gavel,Heart,Pause,Play,RefreshCw,Search,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,DollarSign,Eye,Gavel,Heart,Pause,Play,RefreshCw,Search,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,DollarSign,Eye,Gavel,Heart,Pause,Play,RefreshCw,Search,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,DollarSign,Eye,Gavel,Heart,Pause,Play,RefreshCw,Search,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./lib/api.ts\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/use-toast */ \"(app-pages-browser)/./components/ui/use-toast.tsx\");\n/* harmony import */ var _lib_currencyService__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/lib/currencyService */ \"(app-pages-browser)/./lib/currencyService.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nfunction UserAuctionsPage() {\n    _s();\n    const [auctions, setAuctions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [filteredAuctions, setFilteredAuctions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [savedAuctions, setSavedAuctions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Bidding modal state\n    const [showBidModal, setShowBidModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedAuction, setSelectedAuction] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [bidAmount, setBidAmount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [bidLoading, setBidLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Filter and sort state\n    const [sortBy, setSortBy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"endDate\");\n    const [filterCategory, setFilterCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // Auto-refresh state\n    const [autoRefresh, setAutoRefresh] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [lastRefresh, setLastRefresh] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Date());\n    // Currency state\n    const [userCurrency, setUserCurrency] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"SAR\");\n    const [exchangeRates, setExchangeRates] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    // Optimistic locking\n    const [auctionVersions, setAuctionVersions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_9__.useRouter)();\n    const { toast } = (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_10__.useToast)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Get user data and check permissions\n        const userData = localStorage.getItem(\"user\");\n        if (userData) {\n            const parsedUser = JSON.parse(userData);\n            setUser(parsedUser);\n            // Redirect admins to their proper dashboard\n            if (parsedUser.role === \"admin\" || parsedUser.role === \"super_admin\") {\n                toast({\n                    title: \"وصول غير مسموح\",\n                    description: \"المديرون يجب أن يستخدموا لوحة إدارة المزادات\",\n                    variant: \"destructive\"\n                });\n                router.push(\"/admin/auctions\");\n                return;\n            }\n            // Redirect government users to their dashboard\n            if (parsedUser.role === \"government\") {\n                toast({\n                    title: \"وصول غير مسموح\",\n                    description: \"الجهات الحكومية تستخدم نظام المناقصات\",\n                    variant: \"destructive\"\n                });\n                router.push(\"/government/dashboard\");\n                return;\n            }\n        }\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Initialize currency first\n        const savedCurrency = _lib_currencyService__WEBPACK_IMPORTED_MODULE_11__[\"default\"].getUserCurrency();\n        console.log(\"\\uD83D\\uDD27 Initializing currency:\", savedCurrency);\n        setUserCurrency(savedCurrency);\n        // Load data after currency is set\n        setTimeout(()=>{\n            loadAuctions();\n            loadSavedAuctions();\n            loadExchangeRates();\n        }, 100) // Small delay to ensure state update\n        ;\n    }, []);\n    const loadExchangeRates = async ()=>{\n        try {\n            const rates = await _lib_currencyService__WEBPACK_IMPORTED_MODULE_11__[\"default\"].getExchangeRates();\n            setExchangeRates(rates);\n        } catch (error) {\n            console.error(\"Failed to load exchange rates:\", error);\n        }\n    };\n    // Auto-refresh auctions every 30 seconds\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!autoRefresh) return;\n        const interval = setInterval(async ()=>{\n            try {\n                await loadAuctions(false) // Silent refresh without loading state\n                ;\n                setLastRefresh(new Date());\n                console.log(\"\\uD83D\\uDD04 Auto-refreshed auction data\");\n            } catch (error) {\n                console.error(\"Auto-refresh failed:\", error);\n            }\n        }, 30000) // 30 seconds\n        ;\n        return ()=>clearInterval(interval);\n    }, [\n        autoRefresh\n    ]);\n    const loadAuctions = async function() {\n        let showLoading = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : true;\n        try {\n            console.log(\"\\uD83D\\uDD04 Starting loadAuctions...\", showLoading ? \"with loading\" : \"silent\");\n            if (showLoading) {\n                setLoading(true);\n            }\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_8__.auctionAPI.getAll();\n            console.log(\"Auctions API response:\", response.data);\n            // Handle the API response structure properly\n            let auctionData = [];\n            if (response.data && response.data.success) {\n                var _response_data_data;\n                // If the response has a success field, extract the auctions from data.data.auctions\n                auctionData = ((_response_data_data = response.data.data) === null || _response_data_data === void 0 ? void 0 : _response_data_data.auctions) || [];\n            } else if (response.data && Array.isArray(response.data)) {\n                // If response.data is directly an array\n                auctionData = response.data;\n            } else if (response.data && response.data.data && Array.isArray(response.data.data)) {\n                // If the auctions are in response.data.data\n                auctionData = response.data.data;\n            }\n            // Transform and convert currency for each auction\n            const transformedData = await Promise.all(auctionData.map(async (auction)=>{\n                var _auction_bids, _auction_seller_profile, _auction_seller, _auction_seller_profile1, _auction_seller1, _auction_seller2, _auction_seller3;\n                const baseAuction = {\n                    id: auction._id || auction.id,\n                    title: auction.title,\n                    description: auction.description,\n                    currentBid: auction.currentPrice || auction.currentBid || auction.startingPrice || 0,\n                    endDate: auction.endDate,\n                    bidsCount: ((_auction_bids = auction.bids) === null || _auction_bids === void 0 ? void 0 : _auction_bids.length) || auction.bidsCount || 0,\n                    seller: ((_auction_seller = auction.seller) === null || _auction_seller === void 0 ? void 0 : (_auction_seller_profile = _auction_seller.profile) === null || _auction_seller_profile === void 0 ? void 0 : _auction_seller_profile.fullName) || ((_auction_seller1 = auction.seller) === null || _auction_seller1 === void 0 ? void 0 : (_auction_seller_profile1 = _auction_seller1.profile) === null || _auction_seller_profile1 === void 0 ? void 0 : _auction_seller_profile1.companyName) || ((_auction_seller2 = auction.seller) === null || _auction_seller2 === void 0 ? void 0 : _auction_seller2.name) || ((_auction_seller3 = auction.seller) === null || _auction_seller3 === void 0 ? void 0 : _auction_seller3._id) || \"غير محدد\",\n                    category: auction.category,\n                    status: auction.status,\n                    views: auction.views || 0,\n                    location: auction.location,\n                    currency: auction.currency || \"SAR\",\n                    version: auction.version || 0\n                };\n                // Store version for optimistic locking\n                setAuctionVersions((prev)=>({\n                        ...prev,\n                        [baseAuction.id]: baseAuction.version\n                    }));\n                // Convert currency if needed\n                console.log(\"\\uD83D\\uDD0D Checking conversion: \".concat(baseAuction.currency, \" vs \").concat(userCurrency, \" for auction \").concat(baseAuction.id));\n                console.log(\"\\uD83D\\uDCB0 Current userCurrency state: \".concat(userCurrency));\n                try {\n                    if (baseAuction.currency !== userCurrency) {\n                        console.log(\"\\uD83D\\uDCB1 Converting \".concat(baseAuction.currentBid, \" \").concat(baseAuction.currency, \" → \").concat(userCurrency));\n                        const convertedBid = await _lib_currencyService__WEBPACK_IMPORTED_MODULE_11__[\"default\"].convertCurrency(baseAuction.currentBid, baseAuction.currency, userCurrency);\n                        console.log(\"\\uD83D\\uDCB1 Converted result: \".concat(Math.round(convertedBid), \" \").concat(userCurrency));\n                        return {\n                            ...baseAuction,\n                            currentBid: Math.round(convertedBid),\n                            originalCurrency: baseAuction.currency,\n                            currency: userCurrency\n                        };\n                    } else {\n                        console.log(\"⏭️ No conversion needed: currencies match (\".concat(baseAuction.currency, \")\"));\n                    }\n                } catch (conversionError) {\n                    console.warn(\"Currency conversion failed for auction:\", baseAuction.id, conversionError);\n                }\n                return baseAuction;\n            }));\n            setAuctions(transformedData);\n            setFilteredAuctions(transformedData);\n            console.log(\"✅ Set auctions and filteredAuctions:\", transformedData.length);\n        } catch (error) {\n            console.error(\"Error loading auctions:\", error);\n            setAuctions([]);\n            setFilteredAuctions([]);\n            toast({\n                title: \"فشل في تحميل المزادات\",\n                description: \"حدث خطأ أثناء تحميل المزادات. يرجى المحاولة مرة أخرى.\",\n                variant: \"destructive\"\n            });\n        } finally{\n            if (showLoading) {\n                setLoading(false);\n            }\n            console.log(\"\\uD83C\\uDFC1 Loading finished. Auctions loaded:\", auctions.length);\n        }\n    };\n    const loadSavedAuctions = async ()=>{\n        try {\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_8__.favoritesAPI.getFavorites({\n                type: \"auction\"\n            });\n            // Handle different response structures\n            let favoritesData = [];\n            if (response.data && response.data.success) {\n                favoritesData = response.data.data || [];\n            } else if (response.data && Array.isArray(response.data)) {\n                favoritesData = response.data;\n            } else if (response.data && response.data.favorites && Array.isArray(response.data.favorites)) {\n                favoritesData = response.data.favorites;\n            }\n            // Check if favoritesData is an array before mapping\n            if (Array.isArray(favoritesData)) {\n                const savedIds = new Set(favoritesData.map((fav)=>fav.itemId || fav._id || fav.id));\n                setSavedAuctions(savedIds);\n            } else {\n                console.warn(\"Favorites data is not an array:\", favoritesData);\n                setSavedAuctions(new Set());\n            }\n        } catch (error) {\n            console.error(\"Error loading saved auctions:\", error);\n            // Don't show error toast for favorites as it's not critical\n            setSavedAuctions(new Set());\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Ensure auctions is an array before filtering\n        if (Array.isArray(auctions)) {\n            let filtered = auctions.filter((auction)=>{\n                var _auction_title, _auction_description;\n                const matchesSearch = ((_auction_title = auction.title) === null || _auction_title === void 0 ? void 0 : _auction_title.toLowerCase().includes(searchTerm.toLowerCase())) || ((_auction_description = auction.description) === null || _auction_description === void 0 ? void 0 : _auction_description.toLowerCase().includes(searchTerm.toLowerCase()));\n                const matchesCategory = !filterCategory || auction.category === filterCategory;\n                return matchesSearch && matchesCategory;\n            });\n            // Sort the filtered results\n            filtered = filtered.sort((a, b)=>{\n                switch(sortBy){\n                    case \"endDate\":\n                        return new Date(a.endDate).getTime() - new Date(b.endDate).getTime();\n                    case \"currentBid\":\n                        return (b.currentBid || 0) - (a.currentBid || 0);\n                    case \"bidsCount\":\n                        return (b.bidsCount || 0) - (a.bidsCount || 0);\n                    case \"title\":\n                        return a.title.localeCompare(b.title);\n                    default:\n                        return 0;\n                }\n            });\n            setFilteredAuctions(filtered);\n            console.log(\"\\uD83D\\uDD0D Filtered auctions:\", filtered.length, \"from\", auctions.length);\n        } else {\n            setFilteredAuctions([]);\n        }\n    }, [\n        searchTerm,\n        auctions,\n        filterCategory,\n        sortBy\n    ]);\n    const getTimeRemaining = (endDate)=>{\n        const now = new Date();\n        const end = new Date(endDate);\n        const diff = end.getTime() - now.getTime();\n        if (diff <= 0) return \"انتهى\";\n        const hours = Math.floor(diff / (1000 * 60 * 60));\n        const days = Math.floor(hours / 24);\n        if (days > 0) return \"\".concat(days, \" يوم\");\n        return \"\".concat(hours, \" ساعة\");\n    };\n    // Check if user can bid\n    const canUserBid = (auction)=>{\n        var _user_profile;\n        if (!user) return false;\n        // Only individuals and companies can bid\n        if (user.role !== \"individual\" && user.role !== \"company\") {\n            return false;\n        }\n        // Companies cannot bid on their own auctions\n        if (user.role === \"company\" && auction.seller === ((_user_profile = user.profile) === null || _user_profile === void 0 ? void 0 : _user_profile.companyName)) {\n            return false;\n        }\n        return true;\n    };\n    const handleBid = async (auctionId)=>{\n        console.log(\"\\uD83C\\uDFAF Bid button clicked for auction:\", auctionId);\n        try {\n            var _freshAuction_bids, _freshAuction_seller_profile, _freshAuction_seller, _freshAuction_seller_profile1, _freshAuction_seller1, _freshAuction_seller2;\n            // Fetch fresh auction data before opening modal\n            setLoading(true);\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_8__.auctionAPI.getById(auctionId);\n            let freshAuction = null;\n            if (response.data && response.data.success) {\n                freshAuction = response.data.data.auction;\n            } else if (response.data && response.data.auction) {\n                freshAuction = response.data.auction;\n            }\n            if (!freshAuction) {\n                toast({\n                    title: \"خطأ\",\n                    description: \"لم يتم العثور على المزاد\",\n                    variant: \"destructive\"\n                });\n                return;\n            }\n            // Transform fresh data to match our format\n            const transformedAuction = {\n                id: freshAuction._id || freshAuction.id,\n                title: freshAuction.title,\n                description: freshAuction.description,\n                currentBid: freshAuction.currentPrice || freshAuction.currentBid || freshAuction.startingPrice || 0,\n                endDate: freshAuction.endDate,\n                bidsCount: ((_freshAuction_bids = freshAuction.bids) === null || _freshAuction_bids === void 0 ? void 0 : _freshAuction_bids.length) || freshAuction.bidsCount || 0,\n                seller: ((_freshAuction_seller = freshAuction.seller) === null || _freshAuction_seller === void 0 ? void 0 : (_freshAuction_seller_profile = _freshAuction_seller.profile) === null || _freshAuction_seller_profile === void 0 ? void 0 : _freshAuction_seller_profile.fullName) || ((_freshAuction_seller1 = freshAuction.seller) === null || _freshAuction_seller1 === void 0 ? void 0 : (_freshAuction_seller_profile1 = _freshAuction_seller1.profile) === null || _freshAuction_seller_profile1 === void 0 ? void 0 : _freshAuction_seller_profile1.companyName) || ((_freshAuction_seller2 = freshAuction.seller) === null || _freshAuction_seller2 === void 0 ? void 0 : _freshAuction_seller2.name) || \"غير محدد\",\n                category: freshAuction.category,\n                status: freshAuction.status\n            };\n            // Check if user can bid\n            if (!canUserBid(transformedAuction)) {\n                if ((user === null || user === void 0 ? void 0 : user.role) === \"admin\" || (user === null || user === void 0 ? void 0 : user.role) === \"super_admin\") {\n                    toast({\n                        title: \"وصول غير مسموح\",\n                        description: \"المديرون لا يمكنهم المزايدة على المزادات\",\n                        variant: \"destructive\"\n                    });\n                } else if ((user === null || user === void 0 ? void 0 : user.role) === \"government\") {\n                    toast({\n                        title: \"وصول غير مسموح\",\n                        description: \"الجهات الحكومية لا تزايد على المزادات\",\n                        variant: \"destructive\"\n                    });\n                } else if ((user === null || user === void 0 ? void 0 : user.role) === \"company\") {\n                    toast({\n                        title: \"وصول غير مسموح\",\n                        description: \"لا يمكنك المزايدة على مزادك الخاص\",\n                        variant: \"destructive\"\n                    });\n                } else {\n                    toast({\n                        title: \"وصول غير مسموح\",\n                        description: \"غير مسموح لك بالمزايدة\",\n                        variant: \"destructive\"\n                    });\n                }\n                return;\n            }\n            // Open bidding modal with fresh data\n            setSelectedAuction(transformedAuction);\n            setBidAmount(String((transformedAuction.currentBid || 0) + 1000));\n            setShowBidModal(true);\n            toast({\n                title: \"\\uD83D\\uDCCA تم تحديث بيانات المزاد\",\n                description: \"المزايدة الحالية: \".concat((transformedAuction.currentBid || 0).toLocaleString(), \" ر.س\")\n            });\n        } catch (error) {\n            console.error(\"Error fetching fresh auction data:\", error);\n            toast({\n                title: \"خطأ في تحديث البيانات\",\n                description: \"سيتم استخدام البيانات المحفوظة محلياً\",\n                variant: \"destructive\"\n            });\n            // Fallback to cached data\n            const currentAuction = auctions.find((a)=>a.id === auctionId);\n            if (currentAuction && canUserBid(currentAuction)) {\n                setSelectedAuction(currentAuction);\n                setBidAmount(String((currentAuction.currentBid || 0) + 1000));\n                setShowBidModal(true);\n            }\n        } finally{\n            setLoading(false);\n        }\n    };\n    const submitBid = async ()=>{\n        if (!selectedAuction) return;\n        const bidAmountNum = parseFloat(bidAmount);\n        if (isNaN(bidAmountNum) || bidAmountNum <= (selectedAuction.currentBid || 0)) {\n            toast({\n                title: \"مزايدة غير صالحة\",\n                description: \"يجب أن تكون المزايدة أكبر من المزايدة الحالية\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        try {\n            setBidLoading(true);\n            // Get auction version for optimistic locking\n            const auctionVersion = auctionVersions[selectedAuction.id];\n            // Prepare bid data with currency and version\n            const bidData = {\n                bidAmount: bidAmountNum,\n                currency: userCurrency,\n                version: auctionVersion\n            };\n            // Submit bid and wait for server response\n            const response = await fetch(\"/api/auctions/\".concat(selectedAuction.id, \"/bid\"), {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    \"Authorization\": \"Bearer \".concat(localStorage.getItem(\"token\"))\n                },\n                body: JSON.stringify(bidData)\n            });\n            const result = await response.json();\n            if (!response.ok) {\n                // Handle specific error cases\n                if (response.status === 409) {\n                    // Optimistic locking conflict\n                    toast({\n                        title: \"تم تحديث المزاد ⚠️\",\n                        description: \"قام شخص آخر بتحديث المزاد. سيتم تحديث البيانات.\",\n                        variant: \"destructive\"\n                    });\n                    await loadAuctions();\n                    setShowBidModal(false);\n                    setBidAmount(\"\");\n                    setSelectedAuction(null);\n                    return;\n                }\n                throw new Error(result.message || \"Bid failed\");\n            }\n            // Close modal first\n            setShowBidModal(false);\n            setBidAmount(\"\");\n            setSelectedAuction(null);\n            // Reload fresh data from server\n            await loadAuctions();\n            // Show success message with currency formatting\n            const formattedAmount = _lib_currencyService__WEBPACK_IMPORTED_MODULE_11__[\"default\"].formatAmount(bidAmountNum, userCurrency);\n            toast({\n                title: \"تم تقديم المزايدة بنجاح! \\uD83C\\uDF89\",\n                description: \"مقدار المزايدة: \".concat(formattedAmount)\n            });\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error(\"Error placing bid:\", error);\n            // Close modal on error too\n            setShowBidModal(false);\n            setBidAmount(\"\");\n            setSelectedAuction(null);\n            // Show specific error message\n            const errorMessage = ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"يرجى المحاولة مرة أخرى\";\n            if (errorMessage.includes(\"higher than current price\") || errorMessage.includes(\"أكبر من السعر الحالي\")) {\n                toast({\n                    title: \"مزايدة منخفضة ⚠️\",\n                    description: \"قام شخص آخر بمزايدة أعلى. يرجى المحاولة بمبلغ أكبر.\",\n                    variant: \"destructive\"\n                });\n            } else if (errorMessage.includes(\"ended\") || errorMessage.includes(\"انتهى\")) {\n                toast({\n                    title: \"انتهى المزاد ⏰\",\n                    description: \"لقد انتهى هذا المزاد ولا يمكن قبول مزايدات جديدة.\",\n                    variant: \"destructive\"\n                });\n            } else {\n                toast({\n                    title: \"فشل في تقديم المزايدة\",\n                    description: errorMessage,\n                    variant: \"destructive\"\n                });\n            }\n            // Always reload data on error to sync with server\n            await loadAuctions();\n        } finally{\n            setBidLoading(false);\n        }\n    };\n    const handleSaveAuction = async (auctionId)=>{\n        console.log(\"❤️ Save button clicked for auction:\", auctionId);\n        // Show immediate feedback that button was clicked\n        toast({\n            title: \"\\uD83D\\uDC86 تم الضغط على زر الحفظ\",\n            description: \"جاري معالجة الطلب...\"\n        });\n        try {\n            const auction = auctions.find((a)=>a.id === auctionId);\n            if (!auction) {\n                toast({\n                    title: \"خطأ\",\n                    description: \"لم يتم العثور على المزاد\",\n                    variant: \"destructive\"\n                });\n                return;\n            }\n            const isCurrentlySaved = savedAuctions.has(auctionId);\n            // Optimistic UI update\n            setSavedAuctions((prev)=>{\n                const newSet = new Set(prev);\n                if (isCurrentlySaved) {\n                    newSet.delete(auctionId);\n                } else {\n                    newSet.add(auctionId);\n                }\n                return newSet;\n            });\n            if (isCurrentlySaved) {\n                // Remove from favorites\n                await _lib_api__WEBPACK_IMPORTED_MODULE_8__.favoritesAPI.removeFavorite(\"auction\", auctionId);\n                toast({\n                    title: \"❤️ تم إزالة من المفضلة\",\n                    description: 'تم إزالة \"'.concat(auction.title, '\" من المفضلة')\n                });\n            } else {\n                // Add to favorites\n                await _lib_api__WEBPACK_IMPORTED_MODULE_8__.favoritesAPI.addFavorite({\n                    itemType: \"auction\",\n                    itemId: auctionId,\n                    notifications: {\n                        bidUpdates: true,\n                        statusChanges: true,\n                        endingSoon: true\n                    }\n                });\n                toast({\n                    title: \"\\uD83D\\uDC96 تم إضافة إلى المفضلة\",\n                    description: 'تم إضافة \"'.concat(auction.title, '\" إلى المفضلة')\n                });\n            }\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error(\"Error saving auction:\", error);\n            // Revert optimistic update on error\n            setSavedAuctions((prev)=>{\n                const newSet = new Set(prev);\n                if (savedAuctions.has(auctionId)) {\n                    newSet.add(auctionId);\n                } else {\n                    newSet.delete(auctionId);\n                }\n                return newSet;\n            });\n            toast({\n                title: \"فشل في حفظ المزاد\",\n                description: ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"يرجى المحاولة مرة أخرى\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleViewAuction = async (auctionId)=>{\n        console.log(\"\\uD83D\\uDC41️ View button clicked for auction:\", auctionId);\n        try {\n            const auction = auctions.find((a)=>a.id === auctionId);\n            if (!auction) {\n                toast({\n                    title: \"خطأ\",\n                    description: \"لم يتم العثور على المزاد\",\n                    variant: \"destructive\"\n                });\n                return;\n            }\n            // Navigate to auction detail page\n            router.push(\"/auctions/\".concat(auctionId));\n            toast({\n                title: \"\\uD83D\\uDC41️ جاري تحميل التفاصيل\",\n                description: \"سيتم عرض تفاصيل المزاد\"\n            });\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error(\"Error viewing auction:\", error);\n            toast({\n                title: \"فشل في عرض تفاصيل المزاد\",\n                description: ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"يرجى المحاولة مرة أخرى\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-3xl font-bold\",\n                                    children: \"المزادات المتاحة\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 628,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-muted-foreground\",\n                                    children: [\n                                        \"استكشف المزادات الحالية وشارك في المزايدة\",\n                                        autoRefresh && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs text-green-600 ml-2\",\n                                            children: \"• تحديث تلقائي كل 30 ثانية\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                            lineNumber: 632,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 629,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                            lineNumber: 627,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                            htmlFor: \"currency\",\n                                            className: \"text-sm font-medium\",\n                                            children: \"العملة:\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                            lineNumber: 640,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            id: \"currency\",\n                                            value: userCurrency,\n                                            onChange: async (e)=>{\n                                                const newCurrency = e.target.value;\n                                                setUserCurrency(newCurrency);\n                                                _lib_currencyService__WEBPACK_IMPORTED_MODULE_11__[\"default\"].setUserCurrency(newCurrency);\n                                                setLoading(true);\n                                                await loadAuctions();\n                                                setLoading(false);\n                                                toast({\n                                                    title: \"\\uD83D\\uDCB1 تم تغيير العملة\",\n                                                    description: \"تم التحويل إلى \".concat(_lib_currencyService__WEBPACK_IMPORTED_MODULE_11__[\"default\"].getCurrencySymbol(newCurrency))\n                                                });\n                                            },\n                                            className: \"px-2 py-1 border border-gray-300 rounded text-sm focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                            children: _lib_currencyService__WEBPACK_IMPORTED_MODULE_11__[\"default\"].getSupportedCurrencies().map((currency)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: currency.code,\n                                                    children: [\n                                                        currency.symbol,\n                                                        \" \",\n                                                        currency.code\n                                                    ]\n                                                }, currency.code, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                    lineNumber: 659,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                            lineNumber: 641,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 639,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    onClick: async ()=>{\n                                        await loadAuctions(true) // Show loading state\n                                        ;\n                                        setLastRefresh(new Date());\n                                        toast({\n                                            title: \"\\uD83D\\uDD04 تم تحديث البيانات\",\n                                            description: \"تم العثور على \".concat(filteredAuctions.length, \" مزاد متاح\")\n                                        });\n                                    },\n                                    variant: \"outline\",\n                                    disabled: loading,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"h-4 w-4 ml-2 \".concat(loading ? \"animate-spin\" : \"\")\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                            lineNumber: 677,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"تحديث\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 665,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    onClick: ()=>{\n                                        setAutoRefresh(!autoRefresh);\n                                        toast({\n                                            title: autoRefresh ? \"⏸️ تم إيقاف التحديث التلقائي\" : \"▶️ تم تشغيل التحديث التلقائي\",\n                                            description: autoRefresh ? \"يمكنك التحديث يدوياً\" : \"سيتم التحديث كل 30 ثانية\"\n                                        });\n                                    },\n                                    variant: autoRefresh ? \"default\" : \"outline\",\n                                    size: \"icon\",\n                                    children: autoRefresh ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 691,\n                                        columnNumber: 32\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 691,\n                                        columnNumber: 64\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 680,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                            lineNumber: 638,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                    lineNumber: 626,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                lineNumber: 625,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                    className: \"pt-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"absolute right-3 top-3 h-4 w-4 text-muted-foreground\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 702,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                        placeholder: \"ابحث في المزادات...\",\n                                        value: searchTerm,\n                                        onChange: (e)=>setSearchTerm(e.target.value),\n                                        className: \"pr-10\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 703,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                lineNumber: 701,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col sm:flex-row gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                htmlFor: \"category\",\n                                                className: \"text-sm font-medium\",\n                                                children: \"الفئة\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                lineNumber: 713,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                id: \"category\",\n                                                value: filterCategory,\n                                                onChange: (e)=>setFilterCategory(e.target.value),\n                                                className: \"w-full mt-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"\",\n                                                        children: \"جميع الفئات\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 720,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"electronics\",\n                                                        children: \"إلكترونيات\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 721,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"vehicles\",\n                                                        children: \"سيارات\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 722,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"real_estate\",\n                                                        children: \"عقارات\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 723,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"art_collectibles\",\n                                                        children: \"فنون ومقتنيات\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 724,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"machinery\",\n                                                        children: \"معدات\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 725,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"furniture\",\n                                                        children: \"أثاث\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 726,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"jewelry\",\n                                                        children: \"مجوهرات\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 727,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"books_media\",\n                                                        children: \"كتب ووسائط\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 728,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"clothing\",\n                                                        children: \"ملابس\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 729,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"sports\",\n                                                        children: \"رياضة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 730,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"other\",\n                                                        children: \"أخرى\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 731,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                lineNumber: 714,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 712,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                htmlFor: \"sort\",\n                                                className: \"text-sm font-medium\",\n                                                children: \"ترتيب حسب\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                lineNumber: 736,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                id: \"sort\",\n                                                value: sortBy,\n                                                onChange: (e)=>setSortBy(e.target.value),\n                                                className: \"w-full mt-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"endDate\",\n                                                        children: \"تاريخ الانتهاء\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 743,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"currentBid\",\n                                                        children: \"أعلى مزايدة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 744,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"bidsCount\",\n                                                        children: \"عدد المزايدات\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 745,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"title\",\n                                                        children: \"الاسم\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 746,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                lineNumber: 737,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 735,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                lineNumber: 711,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                        lineNumber: 700,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                    lineNumber: 699,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                lineNumber: 698,\n                columnNumber: 9\n            }, this),\n            Array.isArray(filteredAuctions) && filteredAuctions.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                    className: \"text-lg\",\n                                    children: \"إجمالي المزادات\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 759,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                    className: \"text-2xl font-bold text-blue-600\",\n                                    children: filteredAuctions.length\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 760,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                            lineNumber: 758,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                        lineNumber: 757,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                    className: \"text-lg\",\n                                    children: \"المزايدات النشطة\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 767,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                    className: \"text-2xl font-bold text-green-600\",\n                                    children: filteredAuctions.reduce((sum, a)=>sum + (a.bidsCount || 0), 0)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 768,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                            lineNumber: 766,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                        lineNumber: 765,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                    className: \"text-lg\",\n                                    children: \"أعلى مزايدة\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 775,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                    className: \"text-2xl font-bold text-purple-600\",\n                                    children: filteredAuctions.length > 0 ? _lib_currencyService__WEBPACK_IMPORTED_MODULE_11__[\"default\"].formatAmount(Math.max(...filteredAuctions.map((a)=>a.currentBid || 0)), userCurrency) : _lib_currencyService__WEBPACK_IMPORTED_MODULE_11__[\"default\"].formatAmount(0, userCurrency)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 776,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                            lineNumber: 774,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                        lineNumber: 773,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                lineNumber: 756,\n                columnNumber: 11\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                children: Array.isArray(filteredAuctions) && filteredAuctions.map((auction)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        className: \"hover:shadow-lg transition-shadow cursor-pointer\",\n                        onClick: ()=>handleViewAuction(auction.id),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"aspect-video bg-gradient-to-br from-gray-100 to-gray-200 rounded-lg mb-4 flex items-center justify-center relative overflow-hidden\",\n                                        children: [\n                                            auction.images && auction.images.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: auction.images[0].url,\n                                                alt: auction.title,\n                                                className: \"w-full h-full object-cover\",\n                                                onError: (e)=>{\n                                                    e.currentTarget.style.display = \"none\";\n                                                    e.currentTarget.nextElementSibling.style.display = \"flex\";\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                lineNumber: 797,\n                                                columnNumber: 21\n                                            }, this) : null,\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-center w-full h-full text-gray-500\",\n                                                style: {\n                                                    display: auction.images && auction.images.length > 0 ? \"none\" : \"flex\"\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                            className: \"h-8 w-8 mx-auto mb-2 text-gray-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                            lineNumber: 809,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm\",\n                                                            children: \"صورة المزاد\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                            lineNumber: 810,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                    lineNumber: 808,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                lineNumber: 807,\n                                                columnNumber: 19\n                                            }, this),\n                                            auction.status === \"active\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                className: \"absolute top-2 right-2 bg-green-500\",\n                                                children: \"نشط\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                lineNumber: 814,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 795,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                        className: \"text-lg line-clamp-2\",\n                                        children: auction.title\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 819,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                        className: \"line-clamp-2\",\n                                        children: auction.description\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 820,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                lineNumber: 794,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-muted-foreground\",\n                                                        children: \"أعلى مزايدة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 827,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xl font-bold text-green-600\",\n                                                        children: [\n                                                            _lib_currencyService__WEBPACK_IMPORTED_MODULE_11__[\"default\"].formatAmount(auction.currentBid || 0, userCurrency),\n                                                            auction.originalCurrency && auction.originalCurrency !== userCurrency && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs text-gray-500 ml-2\",\n                                                                children: [\n                                                                    \"(من \",\n                                                                    auction.originalCurrency,\n                                                                    \")\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                                lineNumber: 831,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 828,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                lineNumber: 826,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-muted-foreground\",\n                                                        children: \"المزايدات\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 838,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-lg font-semibold\",\n                                                        children: auction.bidsCount || 0\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 839,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                lineNumber: 837,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 825,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                className: \"h-4 w-4 text-muted-foreground\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                lineNumber: 844,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-muted-foreground\",\n                                                children: [\n                                                    \"ينتهي خلال: \",\n                                                    getTimeRemaining(auction.endDate)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                lineNumber: 845,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 843,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-muted-foreground\",\n                                        children: [\n                                            \"البائع: \",\n                                            auction.seller\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 850,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                onClick: (e)=>{\n                                                    e.preventDefault();\n                                                    e.stopPropagation();\n                                                    handleBid(auction.id);\n                                                },\n                                                className: \"flex-1\",\n                                                disabled: loading || bidLoading,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                        className: \"h-4 w-4 ml-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 864,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    bidLoading ? \"جاري المزايدة...\" : \"مزايدة\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                lineNumber: 855,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                variant: savedAuctions.has(auction.id) ? \"default\" : \"outline\",\n                                                size: \"icon\",\n                                                onClick: (e)=>{\n                                                    e.preventDefault();\n                                                    e.stopPropagation();\n                                                    handleSaveAuction(auction.id);\n                                                },\n                                                disabled: loading,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                    className: \"h-4 w-4 \".concat(savedAuctions.has(auction.id) ? \"fill-current\" : \"\")\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                    lineNumber: 877,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                lineNumber: 867,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                variant: \"outline\",\n                                                size: \"icon\",\n                                                onClick: (e)=>{\n                                                    e.preventDefault();\n                                                    e.stopPropagation();\n                                                    handleViewAuction(auction.id);\n                                                },\n                                                disabled: loading,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                    lineNumber: 889,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                lineNumber: 879,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 854,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                lineNumber: 824,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, auction.id, true, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                        lineNumber: 793,\n                        columnNumber: 13\n                    }, this))\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                lineNumber: 791,\n                columnNumber: 9\n            }, this),\n            !loading && (!Array.isArray(filteredAuctions) || filteredAuctions.length === 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                    className: \"py-16 text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col items-center gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-16 h-16 rounded-full bg-gray-100 flex items-center justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                    className: \"h-8 w-8 text-gray-400\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 903,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                lineNumber: 902,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold mb-2\",\n                                        children: searchTerm ? \"لا توجد مزادات تطابق البحث\" : \"لا توجد مزادات متاحة حالياً\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 906,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-muted-foreground\",\n                                        children: searchTerm ? \"جرب البحث بكلمات مختلفة أو امسح مربع البحث لعرض جميع المزادات\" : \"سيتم عرض المزادات المتاحة هنا عند إضافتها من قبل الشركات\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 909,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                lineNumber: 905,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                        lineNumber: 901,\n                        columnNumber: 15\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                    lineNumber: 900,\n                    columnNumber: 13\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                lineNumber: 899,\n                columnNumber: 11\n            }, this),\n            loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                    className: \"py-16 text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col items-center gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                lineNumber: 926,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-muted-foreground\",\n                                children: \"جاري تحميل المزادات...\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                lineNumber: 927,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xs text-gray-400\",\n                                children: [\n                                    \"Loading: \",\n                                    loading.toString(),\n                                    \", Auctions: \",\n                                    auctions.length,\n                                    \", Filtered: \",\n                                    filteredAuctions.length\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                lineNumber: 928,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                        lineNumber: 925,\n                        columnNumber: 15\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                    lineNumber: 924,\n                    columnNumber: 13\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                lineNumber: 923,\n                columnNumber: 11\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__.Dialog, {\n                open: showBidModal,\n                onOpenChange: setShowBidModal,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__.DialogContent, {\n                    className: \"sm:max-w-md\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__.DialogHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__.DialogTitle, {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 939,\n                                        columnNumber: 17\n                                    }, this),\n                                    \"تقديم مزايدة\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                lineNumber: 938,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                            lineNumber: 937,\n                            columnNumber: 13\n                        }, this),\n                        selectedAuction && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gray-50 p-4 rounded-lg\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"font-semibold mb-2\",\n                                            children: selectedAuction.title\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                            lineNumber: 947,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-4 text-sm text-gray-600\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                            lineNumber: 950,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: [\n                                                                \"المزايدة الحالية: \",\n                                                                (selectedAuction.currentBid || 0).toLocaleString(),\n                                                                \" ر.س\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                            lineNumber: 951,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                    lineNumber: 949,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                            lineNumber: 954,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: [\n                                                                selectedAuction.bidsCount || 0,\n                                                                \" مزايدة\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                            lineNumber: 955,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                    lineNumber: 953,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                            lineNumber: 948,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 946,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                            htmlFor: \"bidAmount\",\n                                            children: \"مقدار المزايدة (ر.س)\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                            lineNumber: 961,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                            id: \"bidAmount\",\n                                            type: \"number\",\n                                            value: bidAmount,\n                                            onChange: (e)=>setBidAmount(e.target.value),\n                                            placeholder: \"أدخل مقدار المزايدة\",\n                                            min: (selectedAuction.currentBid || 0) + 1\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                            lineNumber: 962,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-gray-500\",\n                                            children: [\n                                                \"الحد الأدنى: \",\n                                                ((selectedAuction.currentBid || 0) + 1).toLocaleString(),\n                                                \" ر.س\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                            lineNumber: 970,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 960,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                            lineNumber: 945,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__.DialogFooter, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"outline\",\n                                    onClick: ()=>{\n                                        setShowBidModal(false);\n                                        setBidAmount(\"\");\n                                        setSelectedAuction(null);\n                                    },\n                                    disabled: bidLoading,\n                                    children: \"إلغاء\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 978,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    onClick: submitBid,\n                                    disabled: bidLoading || !bidAmount,\n                                    className: \"bg-green-600 hover:bg-green-700\",\n                                    children: bidLoading ? \"جاري التقديم...\" : \"تقديم المزايدة\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 989,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                            lineNumber: 977,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                    lineNumber: 936,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                lineNumber: 935,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n        lineNumber: 624,\n        columnNumber: 5\n    }, this);\n}\n_s(UserAuctionsPage, \"z+c0gDlNDBuJNNkaYMi/S/91rKQ=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_9__.useRouter,\n        _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_10__.useToast\n    ];\n});\n_c = UserAuctionsPage;\nvar _c;\n$RefreshReg$(_c, \"UserAuctionsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FwcC91c2VyL2F1Y3Rpb25zL3BhZ2UudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBRWtEO0FBQzhDO0FBQ2pEO0FBQ0Y7QUFDQTtBQUMwRDtBQUMxRDtBQUVtRjtBQUM1RTtBQUNUO0FBQ1M7QUFDRDtBQUdwQyxTQUFTZ0M7O0lBQ3RCLE1BQU0sQ0FBQ0MsVUFBVUMsWUFBWSxHQUFHakMsK0NBQVFBLENBQVEsRUFBRTtJQUNsRCxNQUFNLENBQUNrQyxZQUFZQyxjQUFjLEdBQUduQywrQ0FBUUEsQ0FBQztJQUM3QyxNQUFNLENBQUNvQyxrQkFBa0JDLG9CQUFvQixHQUFHckMsK0NBQVFBLENBQVEsRUFBRTtJQUNsRSxNQUFNLENBQUNzQyxTQUFTQyxXQUFXLEdBQUd2QywrQ0FBUUEsQ0FBQztJQUN2QyxNQUFNLENBQUN3QyxlQUFlQyxpQkFBaUIsR0FBR3pDLCtDQUFRQSxDQUFjLElBQUkwQztJQUNwRSxNQUFNLENBQUNDLE1BQU1DLFFBQVEsR0FBRzVDLCtDQUFRQSxDQUFNO0lBRXRDLHNCQUFzQjtJQUN0QixNQUFNLENBQUM2QyxjQUFjQyxnQkFBZ0IsR0FBRzlDLCtDQUFRQSxDQUFDO0lBQ2pELE1BQU0sQ0FBQytDLGlCQUFpQkMsbUJBQW1CLEdBQUdoRCwrQ0FBUUEsQ0FBTTtJQUM1RCxNQUFNLENBQUNpRCxXQUFXQyxhQUFhLEdBQUdsRCwrQ0FBUUEsQ0FBQztJQUMzQyxNQUFNLENBQUNtRCxZQUFZQyxjQUFjLEdBQUdwRCwrQ0FBUUEsQ0FBQztJQUU3Qyx3QkFBd0I7SUFDeEIsTUFBTSxDQUFDcUQsUUFBUUMsVUFBVSxHQUFHdEQsK0NBQVFBLENBQUM7SUFDckMsTUFBTSxDQUFDdUQsZ0JBQWdCQyxrQkFBa0IsR0FBR3hELCtDQUFRQSxDQUFDO0lBRXJELHFCQUFxQjtJQUNyQixNQUFNLENBQUN5RCxhQUFhQyxlQUFlLEdBQUcxRCwrQ0FBUUEsQ0FBQztJQUMvQyxNQUFNLENBQUMyRCxhQUFhQyxlQUFlLEdBQUc1RCwrQ0FBUUEsQ0FBTyxJQUFJNkQ7SUFFekQsaUJBQWlCO0lBQ2pCLE1BQU0sQ0FBQ0MsY0FBY0MsZ0JBQWdCLEdBQUcvRCwrQ0FBUUEsQ0FBQztJQUNqRCxNQUFNLENBQUNnRSxlQUFlQyxpQkFBaUIsR0FBR2pFLCtDQUFRQSxDQUFNLENBQUM7SUFFekQscUJBQXFCO0lBQ3JCLE1BQU0sQ0FBQ2tFLGlCQUFpQkMsbUJBQW1CLEdBQUduRSwrQ0FBUUEsQ0FBMEIsQ0FBQztJQUVqRixNQUFNb0UsU0FBU3hDLDBEQUFTQTtJQUN4QixNQUFNLEVBQUV5QyxLQUFLLEVBQUUsR0FBR3hDLG1FQUFRQTtJQUUxQjVCLGdEQUFTQSxDQUFDO1FBQ1Isc0NBQXNDO1FBQ3RDLE1BQU1xRSxXQUFXQyxhQUFhQyxPQUFPLENBQUM7UUFDdEMsSUFBSUYsVUFBVTtZQUNaLE1BQU1HLGFBQWFDLEtBQUtDLEtBQUssQ0FBQ0w7WUFDOUIxQixRQUFRNkI7WUFFUiw0Q0FBNEM7WUFDNUMsSUFBSUEsV0FBV0csSUFBSSxLQUFLLFdBQVdILFdBQVdHLElBQUksS0FBSyxlQUFlO2dCQUNwRVAsTUFBTTtvQkFDSlEsT0FBTztvQkFDUEMsYUFBYTtvQkFDYkMsU0FBUztnQkFDWDtnQkFDQVgsT0FBT1ksSUFBSSxDQUFDO2dCQUNaO1lBQ0Y7WUFFQSwrQ0FBK0M7WUFDL0MsSUFBSVAsV0FBV0csSUFBSSxLQUFLLGNBQWM7Z0JBQ3BDUCxNQUFNO29CQUNKUSxPQUFPO29CQUNQQyxhQUFhO29CQUNiQyxTQUFTO2dCQUNYO2dCQUNBWCxPQUFPWSxJQUFJLENBQUM7Z0JBQ1o7WUFDRjtRQUNGO0lBQ0YsR0FBRyxFQUFFO0lBRUwvRSxnREFBU0EsQ0FBQztRQUNSLDRCQUE0QjtRQUM1QixNQUFNZ0YsZ0JBQWdCbkQsNkRBQWVBLENBQUNvRCxlQUFlO1FBQ3JEQyxRQUFRQyxHQUFHLENBQUMsdUNBQTZCSDtRQUN6Q2xCLGdCQUFnQmtCO1FBRWhCLGtDQUFrQztRQUNsQ0ksV0FBVztZQUNUQztZQUNBQztZQUNBQztRQUNGLEdBQUcsS0FBSyxxQ0FBcUM7O0lBQy9DLEdBQUcsRUFBRTtJQUVMLE1BQU1BLG9CQUFvQjtRQUN4QixJQUFJO1lBQ0YsTUFBTUMsUUFBUSxNQUFNM0QsNkRBQWVBLENBQUM0RCxnQkFBZ0I7WUFDcER6QixpQkFBaUJ3QjtRQUNuQixFQUFFLE9BQU9FLE9BQU87WUFDZFIsUUFBUVEsS0FBSyxDQUFDLGtDQUFrQ0E7UUFDbEQ7SUFDRjtJQUVBLHlDQUF5QztJQUN6QzFGLGdEQUFTQSxDQUFDO1FBQ1IsSUFBSSxDQUFDd0QsYUFBYTtRQUVsQixNQUFNbUMsV0FBV0MsWUFBWTtZQUMzQixJQUFJO2dCQUNGLE1BQU1QLGFBQWEsT0FBTyx1Q0FBdUM7O2dCQUNqRTFCLGVBQWUsSUFBSUM7Z0JBQ25Cc0IsUUFBUUMsR0FBRyxDQUFDO1lBQ2QsRUFBRSxPQUFPTyxPQUFPO2dCQUNkUixRQUFRUSxLQUFLLENBQUMsd0JBQXdCQTtZQUN4QztRQUNGLEdBQUcsT0FBTyxhQUFhOztRQUV2QixPQUFPLElBQU1HLGNBQWNGO0lBQzdCLEdBQUc7UUFBQ25DO0tBQVk7SUFFaEIsTUFBTTZCLGVBQWU7WUFBT1MsK0VBQWM7UUFDeEMsSUFBSTtZQUNGWixRQUFRQyxHQUFHLENBQUMseUNBQStCVyxjQUFjLGlCQUFpQjtZQUMxRSxJQUFJQSxhQUFhO2dCQUNmeEQsV0FBVztZQUNiO1lBQ0EsTUFBTXlELFdBQVcsTUFBTXRFLGdEQUFVQSxDQUFDdUUsTUFBTTtZQUV4Q2QsUUFBUUMsR0FBRyxDQUFDLDBCQUEwQlksU0FBU0UsSUFBSTtZQUVuRCw2Q0FBNkM7WUFDN0MsSUFBSUMsY0FBYyxFQUFFO1lBQ3BCLElBQUlILFNBQVNFLElBQUksSUFBSUYsU0FBU0UsSUFBSSxDQUFDRSxPQUFPLEVBQUU7b0JBRTVCSjtnQkFEZCxvRkFBb0Y7Z0JBQ3BGRyxjQUFjSCxFQUFBQSxzQkFBQUEsU0FBU0UsSUFBSSxDQUFDQSxJQUFJLGNBQWxCRiwwQ0FBQUEsb0JBQW9CaEUsUUFBUSxLQUFJLEVBQUU7WUFDbEQsT0FBTyxJQUFJZ0UsU0FBU0UsSUFBSSxJQUFJRyxNQUFNQyxPQUFPLENBQUNOLFNBQVNFLElBQUksR0FBRztnQkFDeEQsd0NBQXdDO2dCQUN4Q0MsY0FBY0gsU0FBU0UsSUFBSTtZQUM3QixPQUFPLElBQUlGLFNBQVNFLElBQUksSUFBSUYsU0FBU0UsSUFBSSxDQUFDQSxJQUFJLElBQUlHLE1BQU1DLE9BQU8sQ0FBQ04sU0FBU0UsSUFBSSxDQUFDQSxJQUFJLEdBQUc7Z0JBQ25GLDRDQUE0QztnQkFDNUNDLGNBQWNILFNBQVNFLElBQUksQ0FBQ0EsSUFBSTtZQUNsQztZQUVBLGtEQUFrRDtZQUNsRCxNQUFNSyxrQkFBa0IsTUFBTUMsUUFBUUMsR0FBRyxDQUFDTixZQUFZTyxHQUFHLENBQUMsT0FBT0M7b0JBT2xEQSxlQUNIQSx5QkFBQUEsaUJBQXFDQSwwQkFBQUEsa0JBQXdDQSxrQkFBd0JBO2dCQVAvRyxNQUFNQyxjQUFjO29CQUNsQkMsSUFBSUYsUUFBUUcsR0FBRyxJQUFJSCxRQUFRRSxFQUFFO29CQUM3QmhDLE9BQU84QixRQUFROUIsS0FBSztvQkFDcEJDLGFBQWE2QixRQUFRN0IsV0FBVztvQkFDaENpQyxZQUFZSixRQUFRSyxZQUFZLElBQUlMLFFBQVFJLFVBQVUsSUFBSUosUUFBUU0sYUFBYSxJQUFJO29CQUNuRkMsU0FBU1AsUUFBUU8sT0FBTztvQkFDeEJDLFdBQVdSLEVBQUFBLGdCQUFBQSxRQUFRUyxJQUFJLGNBQVpULG9DQUFBQSxjQUFjVSxNQUFNLEtBQUlWLFFBQVFRLFNBQVMsSUFBSTtvQkFDeERHLFFBQVFYLEVBQUFBLGtCQUFBQSxRQUFRVyxNQUFNLGNBQWRYLHVDQUFBQSwwQkFBQUEsZ0JBQWdCWSxPQUFPLGNBQXZCWiw4Q0FBQUEsd0JBQXlCYSxRQUFRLE9BQUliLG1CQUFBQSxRQUFRVyxNQUFNLGNBQWRYLHdDQUFBQSwyQkFBQUEsaUJBQWdCWSxPQUFPLGNBQXZCWiwrQ0FBQUEseUJBQXlCYyxXQUFXLE9BQUlkLG1CQUFBQSxRQUFRVyxNQUFNLGNBQWRYLHVDQUFBQSxpQkFBZ0JlLElBQUksT0FBSWYsbUJBQUFBLFFBQVFXLE1BQU0sY0FBZFgsdUNBQUFBLGlCQUFnQkcsR0FBRyxLQUFJO29CQUNwSWEsVUFBVWhCLFFBQVFnQixRQUFRO29CQUMxQkMsUUFBUWpCLFFBQVFpQixNQUFNO29CQUN0QkMsT0FBT2xCLFFBQVFrQixLQUFLLElBQUk7b0JBQ3hCQyxVQUFVbkIsUUFBUW1CLFFBQVE7b0JBQzFCQyxVQUFVcEIsUUFBUW9CLFFBQVEsSUFBSTtvQkFDOUJDLFNBQVNyQixRQUFRcUIsT0FBTyxJQUFJO2dCQUM5QjtnQkFFQSx1Q0FBdUM7Z0JBQ3ZDN0QsbUJBQW1COEQsQ0FBQUEsT0FBUzt3QkFDMUIsR0FBR0EsSUFBSTt3QkFDUCxDQUFDckIsWUFBWUMsRUFBRSxDQUFDLEVBQUVELFlBQVlvQixPQUFPO29CQUN2QztnQkFFQSw2QkFBNkI7Z0JBQzdCN0MsUUFBUUMsR0FBRyxDQUFDLHFDQUFzRHRCLE9BQTNCOEMsWUFBWW1CLFFBQVEsRUFBQyxRQUFrQ25CLE9BQTVCOUMsY0FBYSxpQkFBOEIsT0FBZjhDLFlBQVlDLEVBQUU7Z0JBQzVHMUIsUUFBUUMsR0FBRyxDQUFDLDRDQUErQyxPQUFidEI7Z0JBQzlDLElBQUk7b0JBQ0YsSUFBSThDLFlBQVltQixRQUFRLEtBQUtqRSxjQUFjO3dCQUN6Q3FCLFFBQVFDLEdBQUcsQ0FBQywyQkFBMkN3QixPQUExQkEsWUFBWUcsVUFBVSxFQUFDLEtBQTZCakQsT0FBMUI4QyxZQUFZbUIsUUFBUSxFQUFDLE9BQWtCLE9BQWJqRTt3QkFDakYsTUFBTW9FLGVBQWUsTUFBTXBHLDZEQUFlQSxDQUFDcUcsZUFBZSxDQUN4RHZCLFlBQVlHLFVBQVUsRUFDdEJILFlBQVltQixRQUFRLEVBQ3BCakU7d0JBRUZxQixRQUFRQyxHQUFHLENBQUMsa0NBQW9EdEIsT0FBNUJzRSxLQUFLQyxLQUFLLENBQUNILGVBQWMsS0FBZ0IsT0FBYnBFO3dCQUNoRSxPQUFPOzRCQUNMLEdBQUc4QyxXQUFXOzRCQUNkRyxZQUFZcUIsS0FBS0MsS0FBSyxDQUFDSDs0QkFDdkJJLGtCQUFrQjFCLFlBQVltQixRQUFROzRCQUN0Q0EsVUFBVWpFO3dCQUNaO29CQUNGLE9BQU87d0JBQ0xxQixRQUFRQyxHQUFHLENBQUMsOENBQW1FLE9BQXJCd0IsWUFBWW1CLFFBQVEsRUFBQztvQkFDakY7Z0JBQ0YsRUFBRSxPQUFPUSxpQkFBaUI7b0JBQ3hCcEQsUUFBUXFELElBQUksQ0FBQywyQ0FBMkM1QixZQUFZQyxFQUFFLEVBQUUwQjtnQkFDMUU7Z0JBRUEsT0FBTzNCO1lBQ1Q7WUFFQTNFLFlBQVlzRTtZQUNabEUsb0JBQW9Ca0U7WUFDcEJwQixRQUFRQyxHQUFHLENBQUMsd0NBQXdDbUIsZ0JBQWdCYyxNQUFNO1FBQzVFLEVBQUUsT0FBTzFCLE9BQU87WUFDZFIsUUFBUVEsS0FBSyxDQUFDLDJCQUEyQkE7WUFDekMxRCxZQUFZLEVBQUU7WUFDZEksb0JBQW9CLEVBQUU7WUFFdEJnQyxNQUFNO2dCQUNKUSxPQUFPO2dCQUNQQyxhQUFhO2dCQUNiQyxTQUFTO1lBQ1g7UUFDRixTQUFVO1lBQ1IsSUFBSWdCLGFBQWE7Z0JBQ2Z4RCxXQUFXO1lBQ2I7WUFDQTRDLFFBQVFDLEdBQUcsQ0FBQyxtREFBeUNwRCxTQUFTcUYsTUFBTTtRQUN0RTtJQUNGO0lBRUEsTUFBTTlCLG9CQUFvQjtRQUN4QixJQUFJO1lBQ0YsTUFBTVMsV0FBVyxNQUFNckUsa0RBQVlBLENBQUM4RyxZQUFZLENBQUM7Z0JBQUVDLE1BQU07WUFBVTtZQUVuRSx1Q0FBdUM7WUFDdkMsSUFBSUMsZ0JBQWdCLEVBQUU7WUFDdEIsSUFBSTNDLFNBQVNFLElBQUksSUFBSUYsU0FBU0UsSUFBSSxDQUFDRSxPQUFPLEVBQUU7Z0JBQzFDdUMsZ0JBQWdCM0MsU0FBU0UsSUFBSSxDQUFDQSxJQUFJLElBQUksRUFBRTtZQUMxQyxPQUFPLElBQUlGLFNBQVNFLElBQUksSUFBSUcsTUFBTUMsT0FBTyxDQUFDTixTQUFTRSxJQUFJLEdBQUc7Z0JBQ3hEeUMsZ0JBQWdCM0MsU0FBU0UsSUFBSTtZQUMvQixPQUFPLElBQUlGLFNBQVNFLElBQUksSUFBSUYsU0FBU0UsSUFBSSxDQUFDMEMsU0FBUyxJQUFJdkMsTUFBTUMsT0FBTyxDQUFDTixTQUFTRSxJQUFJLENBQUMwQyxTQUFTLEdBQUc7Z0JBQzdGRCxnQkFBZ0IzQyxTQUFTRSxJQUFJLENBQUMwQyxTQUFTO1lBQ3pDO1lBRUEsb0RBQW9EO1lBQ3BELElBQUl2QyxNQUFNQyxPQUFPLENBQUNxQyxnQkFBZ0I7Z0JBQ2hDLE1BQU1FLFdBQVcsSUFBSW5HLElBQVlpRyxjQUFjakMsR0FBRyxDQUFDLENBQUNvQyxNQUFhQSxJQUFJQyxNQUFNLElBQUlELElBQUloQyxHQUFHLElBQUlnQyxJQUFJakMsRUFBRTtnQkFDaEdwRSxpQkFBaUJvRztZQUNuQixPQUFPO2dCQUNMMUQsUUFBUXFELElBQUksQ0FBQyxtQ0FBbUNHO2dCQUNoRGxHLGlCQUFpQixJQUFJQztZQUN2QjtRQUNGLEVBQUUsT0FBT2lELE9BQU87WUFDZFIsUUFBUVEsS0FBSyxDQUFDLGlDQUFpQ0E7WUFDL0MsNERBQTREO1lBQzVEbEQsaUJBQWlCLElBQUlDO1FBQ3ZCO0lBQ0Y7SUFFQXpDLGdEQUFTQSxDQUFDO1FBQ1IsK0NBQStDO1FBQy9DLElBQUlvRyxNQUFNQyxPQUFPLENBQUN0RSxXQUFXO1lBQzNCLElBQUlnSCxXQUFXaEgsU0FBU2lILE1BQU0sQ0FBQ3RDLENBQUFBO29CQUNQQSxnQkFDREE7Z0JBRHJCLE1BQU11QyxnQkFBZ0J2QyxFQUFBQSxpQkFBQUEsUUFBUTlCLEtBQUssY0FBYjhCLHFDQUFBQSxlQUFld0MsV0FBVyxHQUFHQyxRQUFRLENBQUNsSCxXQUFXaUgsV0FBVyxVQUM3RHhDLHVCQUFBQSxRQUFRN0IsV0FBVyxjQUFuQjZCLDJDQUFBQSxxQkFBcUJ3QyxXQUFXLEdBQUdDLFFBQVEsQ0FBQ2xILFdBQVdpSCxXQUFXO2dCQUN2RixNQUFNRSxrQkFBa0IsQ0FBQzlGLGtCQUFrQm9ELFFBQVFnQixRQUFRLEtBQUtwRTtnQkFDaEUsT0FBTzJGLGlCQUFpQkc7WUFDMUI7WUFFQSw0QkFBNEI7WUFDNUJMLFdBQVdBLFNBQVNNLElBQUksQ0FBQyxDQUFDQyxHQUFHQztnQkFDM0IsT0FBUW5HO29CQUNOLEtBQUs7d0JBQ0gsT0FBTyxJQUFJUSxLQUFLMEYsRUFBRXJDLE9BQU8sRUFBRXVDLE9BQU8sS0FBSyxJQUFJNUYsS0FBSzJGLEVBQUV0QyxPQUFPLEVBQUV1QyxPQUFPO29CQUNwRSxLQUFLO3dCQUNILE9BQU8sQ0FBQ0QsRUFBRXpDLFVBQVUsSUFBSSxLQUFNd0MsQ0FBQUEsRUFBRXhDLFVBQVUsSUFBSTtvQkFDaEQsS0FBSzt3QkFDSCxPQUFPLENBQUN5QyxFQUFFckMsU0FBUyxJQUFJLEtBQU1vQyxDQUFBQSxFQUFFcEMsU0FBUyxJQUFJO29CQUM5QyxLQUFLO3dCQUNILE9BQU9vQyxFQUFFMUUsS0FBSyxDQUFDNkUsYUFBYSxDQUFDRixFQUFFM0UsS0FBSztvQkFDdEM7d0JBQ0UsT0FBTztnQkFDWDtZQUNGO1lBRUF4QyxvQkFBb0IyRztZQUNwQjdELFFBQVFDLEdBQUcsQ0FBQyxtQ0FBeUI0RCxTQUFTM0IsTUFBTSxFQUFFLFFBQVFyRixTQUFTcUYsTUFBTTtRQUMvRSxPQUFPO1lBQ0xoRixvQkFBb0IsRUFBRTtRQUN4QjtJQUNGLEdBQUc7UUFBQ0g7UUFBWUY7UUFBVXVCO1FBQWdCRjtLQUFPO0lBRWpELE1BQU1zRyxtQkFBbUIsQ0FBQ3pDO1FBQ3hCLE1BQU0wQyxNQUFNLElBQUkvRjtRQUNoQixNQUFNZ0csTUFBTSxJQUFJaEcsS0FBS3FEO1FBQ3JCLE1BQU00QyxPQUFPRCxJQUFJSixPQUFPLEtBQUtHLElBQUlILE9BQU87UUFFeEMsSUFBSUssUUFBUSxHQUFHLE9BQU87UUFFdEIsTUFBTUMsUUFBUTNCLEtBQUs0QixLQUFLLENBQUNGLE9BQVEsUUFBTyxLQUFLLEVBQUM7UUFDOUMsTUFBTUcsT0FBTzdCLEtBQUs0QixLQUFLLENBQUNELFFBQVE7UUFFaEMsSUFBSUUsT0FBTyxHQUFHLE9BQU8sR0FBUSxPQUFMQSxNQUFLO1FBQzdCLE9BQU8sR0FBUyxPQUFORixPQUFNO0lBQ2xCO0lBRUEsd0JBQXdCO0lBQ3hCLE1BQU1HLGFBQWEsQ0FBQ3ZEO1lBU2dDaEU7UUFSbEQsSUFBSSxDQUFDQSxNQUFNLE9BQU87UUFFbEIseUNBQXlDO1FBQ3pDLElBQUlBLEtBQUtpQyxJQUFJLEtBQUssZ0JBQWdCakMsS0FBS2lDLElBQUksS0FBSyxXQUFXO1lBQ3pELE9BQU87UUFDVDtRQUVBLDZDQUE2QztRQUM3QyxJQUFJakMsS0FBS2lDLElBQUksS0FBSyxhQUFhK0IsUUFBUVcsTUFBTSxPQUFLM0UsZ0JBQUFBLEtBQUs0RSxPQUFPLGNBQVo1RSxvQ0FBQUEsY0FBYzhFLFdBQVcsR0FBRTtZQUMzRSxPQUFPO1FBQ1Q7UUFFQSxPQUFPO0lBQ1Q7SUFFQSxNQUFNMEMsWUFBWSxPQUFPQztRQUN2QmpGLFFBQVFDLEdBQUcsQ0FBQyxnREFBc0NnRjtRQUVsRCxJQUFJO2dCQTRCV0Msb0JBQ0hBLDhCQUFBQSxzQkFBMENBLCtCQUFBQSx1QkFBNkNBO1lBNUJqRyxnREFBZ0Q7WUFDaEQ5SCxXQUFXO1lBQ1gsTUFBTXlELFdBQVcsTUFBTXRFLGdEQUFVQSxDQUFDNEksT0FBTyxDQUFDRjtZQUUxQyxJQUFJQyxlQUFlO1lBQ25CLElBQUlyRSxTQUFTRSxJQUFJLElBQUlGLFNBQVNFLElBQUksQ0FBQ0UsT0FBTyxFQUFFO2dCQUMxQ2lFLGVBQWVyRSxTQUFTRSxJQUFJLENBQUNBLElBQUksQ0FBQ1MsT0FBTztZQUMzQyxPQUFPLElBQUlYLFNBQVNFLElBQUksSUFBSUYsU0FBU0UsSUFBSSxDQUFDUyxPQUFPLEVBQUU7Z0JBQ2pEMEQsZUFBZXJFLFNBQVNFLElBQUksQ0FBQ1MsT0FBTztZQUN0QztZQUVBLElBQUksQ0FBQzBELGNBQWM7Z0JBQ2pCaEcsTUFBTTtvQkFDSlEsT0FBTztvQkFDUEMsYUFBYTtvQkFDYkMsU0FBUztnQkFDWDtnQkFDQTtZQUNGO1lBRUEsMkNBQTJDO1lBQzNDLE1BQU13RixxQkFBcUI7Z0JBQ3pCMUQsSUFBSXdELGFBQWF2RCxHQUFHLElBQUl1RCxhQUFheEQsRUFBRTtnQkFDdkNoQyxPQUFPd0YsYUFBYXhGLEtBQUs7Z0JBQ3pCQyxhQUFhdUYsYUFBYXZGLFdBQVc7Z0JBQ3JDaUMsWUFBWXNELGFBQWFyRCxZQUFZLElBQUlxRCxhQUFhdEQsVUFBVSxJQUFJc0QsYUFBYXBELGFBQWEsSUFBSTtnQkFDbEdDLFNBQVNtRCxhQUFhbkQsT0FBTztnQkFDN0JDLFdBQVdrRCxFQUFBQSxxQkFBQUEsYUFBYWpELElBQUksY0FBakJpRCx5Q0FBQUEsbUJBQW1CaEQsTUFBTSxLQUFJZ0QsYUFBYWxELFNBQVMsSUFBSTtnQkFDbEVHLFFBQVErQyxFQUFBQSx1QkFBQUEsYUFBYS9DLE1BQU0sY0FBbkIrQyw0Q0FBQUEsK0JBQUFBLHFCQUFxQjlDLE9BQU8sY0FBNUI4QyxtREFBQUEsNkJBQThCN0MsUUFBUSxPQUFJNkMsd0JBQUFBLGFBQWEvQyxNQUFNLGNBQW5CK0MsNkNBQUFBLGdDQUFBQSxzQkFBcUI5QyxPQUFPLGNBQTVCOEMsb0RBQUFBLDhCQUE4QjVDLFdBQVcsT0FBSTRDLHdCQUFBQSxhQUFhL0MsTUFBTSxjQUFuQitDLDRDQUFBQSxzQkFBcUIzQyxJQUFJLEtBQUk7Z0JBQzVIQyxVQUFVMEMsYUFBYTFDLFFBQVE7Z0JBQy9CQyxRQUFReUMsYUFBYXpDLE1BQU07WUFDN0I7WUFFQSx3QkFBd0I7WUFDeEIsSUFBSSxDQUFDc0MsV0FBV0sscUJBQXFCO2dCQUNuQyxJQUFJNUgsQ0FBQUEsaUJBQUFBLDJCQUFBQSxLQUFNaUMsSUFBSSxNQUFLLFdBQVdqQyxDQUFBQSxpQkFBQUEsMkJBQUFBLEtBQU1pQyxJQUFJLE1BQUssZUFBZTtvQkFDMURQLE1BQU07d0JBQ0pRLE9BQU87d0JBQ1BDLGFBQWE7d0JBQ2JDLFNBQVM7b0JBQ1g7Z0JBQ0YsT0FBTyxJQUFJcEMsQ0FBQUEsaUJBQUFBLDJCQUFBQSxLQUFNaUMsSUFBSSxNQUFLLGNBQWM7b0JBQ3RDUCxNQUFNO3dCQUNKUSxPQUFPO3dCQUNQQyxhQUFhO3dCQUNiQyxTQUFTO29CQUNYO2dCQUNGLE9BQU8sSUFBSXBDLENBQUFBLGlCQUFBQSwyQkFBQUEsS0FBTWlDLElBQUksTUFBSyxXQUFXO29CQUNuQ1AsTUFBTTt3QkFDSlEsT0FBTzt3QkFDUEMsYUFBYTt3QkFDYkMsU0FBUztvQkFDWDtnQkFDRixPQUFPO29CQUNMVixNQUFNO3dCQUNKUSxPQUFPO3dCQUNQQyxhQUFhO3dCQUNiQyxTQUFTO29CQUNYO2dCQUNGO2dCQUNBO1lBQ0Y7WUFFQSxxQ0FBcUM7WUFDckMvQixtQkFBbUJ1SDtZQUNuQnJILGFBQWFzSCxPQUFPLENBQUNELG1CQUFtQnhELFVBQVUsSUFBSSxLQUFLO1lBQzNEakUsZ0JBQWdCO1lBRWhCdUIsTUFBTTtnQkFDSlEsT0FBTztnQkFDUEMsYUFBYSxxQkFBMkUsT0FBdEQsQ0FBQ3lGLG1CQUFtQnhELFVBQVUsSUFBSSxHQUFHMEQsY0FBYyxJQUFHO1lBQzFGO1FBRUYsRUFBRSxPQUFPOUUsT0FBWTtZQUNuQlIsUUFBUVEsS0FBSyxDQUFDLHNDQUFzQ0E7WUFDcER0QixNQUFNO2dCQUNKUSxPQUFPO2dCQUNQQyxhQUFhO2dCQUNiQyxTQUFTO1lBQ1g7WUFFQSwwQkFBMEI7WUFDMUIsTUFBTTJGLGlCQUFpQjFJLFNBQVMySSxJQUFJLENBQUNwQixDQUFBQSxJQUFLQSxFQUFFMUMsRUFBRSxLQUFLdUQ7WUFDbkQsSUFBSU0sa0JBQWtCUixXQUFXUSxpQkFBaUI7Z0JBQ2hEMUgsbUJBQW1CMEg7Z0JBQ25CeEgsYUFBYXNILE9BQU8sQ0FBQ0UsZUFBZTNELFVBQVUsSUFBSSxLQUFLO2dCQUN2RGpFLGdCQUFnQjtZQUNsQjtRQUNGLFNBQVU7WUFDUlAsV0FBVztRQUNiO0lBQ0Y7SUFFQSxNQUFNcUksWUFBWTtRQUNoQixJQUFJLENBQUM3SCxpQkFBaUI7UUFFdEIsTUFBTThILGVBQWVDLFdBQVc3SDtRQUNoQyxJQUFJOEgsTUFBTUYsaUJBQWlCQSxnQkFBaUI5SCxDQUFBQSxnQkFBZ0JnRSxVQUFVLElBQUksSUFBSTtZQUM1RTFDLE1BQU07Z0JBQ0pRLE9BQU87Z0JBQ1BDLGFBQWE7Z0JBQ2JDLFNBQVM7WUFDWDtZQUNBO1FBQ0Y7UUFFQSxJQUFJO1lBQ0YzQixjQUFjO1lBRWQsNkNBQTZDO1lBQzdDLE1BQU00SCxpQkFBaUI5RyxlQUFlLENBQUNuQixnQkFBZ0I4RCxFQUFFLENBQUM7WUFFMUQsNkNBQTZDO1lBQzdDLE1BQU1vRSxVQUFVO2dCQUNkaEksV0FBVzRIO2dCQUNYOUMsVUFBVWpFO2dCQUNWa0UsU0FBU2dEO1lBQ1g7WUFFQSwwQ0FBMEM7WUFDMUMsTUFBTWhGLFdBQVcsTUFBTWtGLE1BQU0saUJBQW9DLE9BQW5CbkksZ0JBQWdCOEQsRUFBRSxFQUFDLFNBQU87Z0JBQ3RFc0UsUUFBUTtnQkFDUkMsU0FBUztvQkFDUCxnQkFBZ0I7b0JBQ2hCLGlCQUFpQixVQUF3QyxPQUE5QjdHLGFBQWFDLE9BQU8sQ0FBQztnQkFDbEQ7Z0JBQ0E2RyxNQUFNM0csS0FBSzRHLFNBQVMsQ0FBQ0w7WUFDdkI7WUFFQSxNQUFNTSxTQUFTLE1BQU12RixTQUFTd0YsSUFBSTtZQUVsQyxJQUFJLENBQUN4RixTQUFTeUYsRUFBRSxFQUFFO2dCQUNoQiw4QkFBOEI7Z0JBQzlCLElBQUl6RixTQUFTNEIsTUFBTSxLQUFLLEtBQUs7b0JBQzNCLDhCQUE4QjtvQkFDOUJ2RCxNQUFNO3dCQUNKUSxPQUFPO3dCQUNQQyxhQUFhO3dCQUNiQyxTQUFTO29CQUNYO29CQUNBLE1BQU1PO29CQUNOeEMsZ0JBQWdCO29CQUNoQkksYUFBYTtvQkFDYkYsbUJBQW1CO29CQUNuQjtnQkFDRjtnQkFDQSxNQUFNLElBQUkwSSxNQUFNSCxPQUFPSSxPQUFPLElBQUk7WUFDcEM7WUFFQSxvQkFBb0I7WUFDcEI3SSxnQkFBZ0I7WUFDaEJJLGFBQWE7WUFDYkYsbUJBQW1CO1lBRW5CLGdDQUFnQztZQUNoQyxNQUFNc0M7WUFFTixnREFBZ0Q7WUFDaEQsTUFBTXNHLGtCQUFrQjlKLDZEQUFlQSxDQUFDK0osWUFBWSxDQUFDaEIsY0FBYy9HO1lBQ25FTyxNQUFNO2dCQUNKUSxPQUFPO2dCQUNQQyxhQUFhLG1CQUFtQyxPQUFoQjhHO1lBQ2xDO1FBRUYsRUFBRSxPQUFPakcsT0FBWTtnQkFTRUEsc0JBQUFBO1lBUnJCUixRQUFRUSxLQUFLLENBQUMsc0JBQXNCQTtZQUVwQywyQkFBMkI7WUFDM0I3QyxnQkFBZ0I7WUFDaEJJLGFBQWE7WUFDYkYsbUJBQW1CO1lBRW5CLDhCQUE4QjtZQUM5QixNQUFNOEksZUFBZW5HLEVBQUFBLGtCQUFBQSxNQUFNSyxRQUFRLGNBQWRMLHVDQUFBQSx1QkFBQUEsZ0JBQWdCTyxJQUFJLGNBQXBCUCwyQ0FBQUEscUJBQXNCZ0csT0FBTyxLQUFJO1lBRXRELElBQUlHLGFBQWExQyxRQUFRLENBQUMsZ0NBQWdDMEMsYUFBYTFDLFFBQVEsQ0FBQyx5QkFBeUI7Z0JBQ3ZHL0UsTUFBTTtvQkFDSlEsT0FBTztvQkFDUEMsYUFBYTtvQkFDYkMsU0FBUztnQkFDWDtZQUNGLE9BQU8sSUFBSStHLGFBQWExQyxRQUFRLENBQUMsWUFBWTBDLGFBQWExQyxRQUFRLENBQUMsVUFBVTtnQkFDM0UvRSxNQUFNO29CQUNKUSxPQUFPO29CQUNQQyxhQUFhO29CQUNiQyxTQUFTO2dCQUNYO1lBQ0YsT0FBTztnQkFDTFYsTUFBTTtvQkFDSlEsT0FBTztvQkFDUEMsYUFBYWdIO29CQUNiL0csU0FBUztnQkFDWDtZQUNGO1lBRUEsa0RBQWtEO1lBQ2xELE1BQU1PO1FBQ1IsU0FBVTtZQUNSbEMsY0FBYztRQUNoQjtJQUNGO0lBRUEsTUFBTTJJLG9CQUFvQixPQUFPM0I7UUFDL0JqRixRQUFRQyxHQUFHLENBQUMsdUNBQXVDZ0Y7UUFFbkQsa0RBQWtEO1FBQ2xEL0YsTUFBTTtZQUNKUSxPQUFPO1lBQ1BDLGFBQWE7UUFDZjtRQUVBLElBQUk7WUFDRixNQUFNNkIsVUFBVTNFLFNBQVMySSxJQUFJLENBQUNwQixDQUFBQSxJQUFLQSxFQUFFMUMsRUFBRSxLQUFLdUQ7WUFDNUMsSUFBSSxDQUFDekQsU0FBUztnQkFDWnRDLE1BQU07b0JBQ0pRLE9BQU87b0JBQ1BDLGFBQWE7b0JBQ2JDLFNBQVM7Z0JBQ1g7Z0JBQ0E7WUFDRjtZQUVBLE1BQU1pSCxtQkFBbUJ4SixjQUFjeUosR0FBRyxDQUFDN0I7WUFFM0MsdUJBQXVCO1lBQ3ZCM0gsaUJBQWlCd0YsQ0FBQUE7Z0JBQ2YsTUFBTWlFLFNBQVMsSUFBSXhKLElBQUl1RjtnQkFDdkIsSUFBSStELGtCQUFrQjtvQkFDcEJFLE9BQU9DLE1BQU0sQ0FBQy9CO2dCQUNoQixPQUFPO29CQUNMOEIsT0FBT0UsR0FBRyxDQUFDaEM7Z0JBQ2I7Z0JBQ0EsT0FBTzhCO1lBQ1Q7WUFFQSxJQUFJRixrQkFBa0I7Z0JBQ3BCLHdCQUF3QjtnQkFDeEIsTUFBTXJLLGtEQUFZQSxDQUFDMEssY0FBYyxDQUFDLFdBQVdqQztnQkFDN0MvRixNQUFNO29CQUNKUSxPQUFPO29CQUNQQyxhQUFhLGFBQTJCLE9BQWQ2QixRQUFROUIsS0FBSyxFQUFDO2dCQUMxQztZQUNGLE9BQU87Z0JBQ0wsbUJBQW1CO2dCQUNuQixNQUFNbEQsa0RBQVlBLENBQUMySyxXQUFXLENBQUM7b0JBQzdCQyxVQUFVO29CQUNWeEQsUUFBUXFCO29CQUNSb0MsZUFBZTt3QkFDYkMsWUFBWTt3QkFDWkMsZUFBZTt3QkFDZkMsWUFBWTtvQkFDZDtnQkFDRjtnQkFDQXRJLE1BQU07b0JBQ0pRLE9BQU87b0JBQ1BDLGFBQWEsYUFBMkIsT0FBZDZCLFFBQVE5QixLQUFLLEVBQUM7Z0JBQzFDO1lBQ0Y7UUFDRixFQUFFLE9BQU9jLE9BQVk7Z0JBY0pBLHNCQUFBQTtZQWJmUixRQUFRUSxLQUFLLENBQUMseUJBQXlCQTtZQUN2QyxvQ0FBb0M7WUFDcENsRCxpQkFBaUJ3RixDQUFBQTtnQkFDZixNQUFNaUUsU0FBUyxJQUFJeEosSUFBSXVGO2dCQUN2QixJQUFJekYsY0FBY3lKLEdBQUcsQ0FBQzdCLFlBQVk7b0JBQ2hDOEIsT0FBT0UsR0FBRyxDQUFDaEM7Z0JBQ2IsT0FBTztvQkFDTDhCLE9BQU9DLE1BQU0sQ0FBQy9CO2dCQUNoQjtnQkFDQSxPQUFPOEI7WUFDVDtZQUNBN0gsTUFBTTtnQkFDSlEsT0FBTztnQkFDUEMsYUFBYWEsRUFBQUEsa0JBQUFBLE1BQU1LLFFBQVEsY0FBZEwsdUNBQUFBLHVCQUFBQSxnQkFBZ0JPLElBQUksY0FBcEJQLDJDQUFBQSxxQkFBc0JnRyxPQUFPLEtBQUk7Z0JBQzlDNUcsU0FBUztZQUNYO1FBQ0Y7SUFDRjtJQUVBLE1BQU02SCxvQkFBb0IsT0FBT3hDO1FBQy9CakYsUUFBUUMsR0FBRyxDQUFDLGtEQUF3Q2dGO1FBRXBELElBQUk7WUFDRixNQUFNekQsVUFBVTNFLFNBQVMySSxJQUFJLENBQUNwQixDQUFBQSxJQUFLQSxFQUFFMUMsRUFBRSxLQUFLdUQ7WUFDNUMsSUFBSSxDQUFDekQsU0FBUztnQkFDWnRDLE1BQU07b0JBQ0pRLE9BQU87b0JBQ1BDLGFBQWE7b0JBQ2JDLFNBQVM7Z0JBQ1g7Z0JBQ0E7WUFDRjtZQUVBLGtDQUFrQztZQUNsQ1gsT0FBT1ksSUFBSSxDQUFDLGFBQXVCLE9BQVZvRjtZQUV6Qi9GLE1BQU07Z0JBQ0pRLE9BQU87Z0JBQ1BDLGFBQWE7WUFDZjtRQUNGLEVBQUUsT0FBT2EsT0FBWTtnQkFJSkEsc0JBQUFBO1lBSGZSLFFBQVFRLEtBQUssQ0FBQywwQkFBMEJBO1lBQ3hDdEIsTUFBTTtnQkFDSlEsT0FBTztnQkFDUEMsYUFBYWEsRUFBQUEsa0JBQUFBLE1BQU1LLFFBQVEsY0FBZEwsdUNBQUFBLHVCQUFBQSxnQkFBZ0JPLElBQUksY0FBcEJQLDJDQUFBQSxxQkFBc0JnRyxPQUFPLEtBQUk7Z0JBQzlDNUcsU0FBUztZQUNYO1FBQ0Y7SUFDRjtJQUVBLHFCQUNFLDhEQUFDOEg7UUFBSUMsV0FBVTs7MEJBQ1gsOERBQUNDOzBCQUNDLDRFQUFDRjtvQkFBSUMsV0FBVTs7c0NBQ2IsOERBQUNEOzs4Q0FDQyw4REFBQ0c7b0NBQUdGLFdBQVU7OENBQXFCOzs7Ozs7OENBQ25DLDhEQUFDRztvQ0FBRUgsV0FBVTs7d0NBQXdCO3dDQUVsQ3JKLDZCQUNDLDhEQUFDeUo7NENBQUtKLFdBQVU7c0RBQThCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7c0NBTXBELDhEQUFDRDs0QkFBSUMsV0FBVTs7OENBQ2IsOERBQUNEO29DQUFJQyxXQUFVOztzREFDYiw4REFBQy9MLHVEQUFLQTs0Q0FBQ29NLFNBQVE7NENBQVdMLFdBQVU7c0RBQXNCOzs7Ozs7c0RBQzFELDhEQUFDTTs0Q0FDQ3ZHLElBQUc7NENBQ0h3RyxPQUFPdko7NENBQ1B3SixVQUFVLE9BQU9DO2dEQUNmLE1BQU1DLGNBQWNELEVBQUVFLE1BQU0sQ0FBQ0osS0FBSztnREFDbEN0SixnQkFBZ0J5SjtnREFDaEIxTCw2REFBZUEsQ0FBQ2lDLGVBQWUsQ0FBQ3lKO2dEQUNoQ2pMLFdBQVc7Z0RBQ1gsTUFBTStDO2dEQUNOL0MsV0FBVztnREFDWDhCLE1BQU07b0RBQ0pRLE9BQU87b0RBQ1BDLGFBQWEsa0JBQWlFLE9BQS9DaEQsNkRBQWVBLENBQUM0TCxpQkFBaUIsQ0FBQ0Y7Z0RBQ25FOzRDQUNGOzRDQUNBVixXQUFVO3NEQUVUaEwsNkRBQWVBLENBQUM2TCxzQkFBc0IsR0FBR2pILEdBQUcsQ0FBQ3FCLENBQUFBLHlCQUM1Qyw4REFBQzZGO29EQUEyQlAsT0FBT3RGLFNBQVM4RixJQUFJOzt3REFDN0M5RixTQUFTK0YsTUFBTTt3REFBQzt3REFBRS9GLFNBQVM4RixJQUFJOzttREFEckI5RixTQUFTOEYsSUFBSTs7Ozs7Ozs7Ozs7Ozs7Ozs4Q0FNaEMsOERBQUN0Tix5REFBTUE7b0NBQ0x3TixTQUFTO3dDQUNQLE1BQU16SSxhQUFhLE1BQU0scUJBQXFCOzt3Q0FDOUMxQixlQUFlLElBQUlDO3dDQUNuQlEsTUFBTTs0Q0FDSlEsT0FBTzs0Q0FDUEMsYUFBYSxpQkFBeUMsT0FBeEIxQyxpQkFBaUJpRixNQUFNLEVBQUM7d0NBQ3hEO29DQUNGO29DQUNBdEMsU0FBUTtvQ0FDUmlKLFVBQVUxTDs7c0RBRVYsOERBQUNmLG9KQUFTQTs0Q0FBQ3VMLFdBQVcsZ0JBQThDLE9BQTlCeEssVUFBVSxpQkFBaUI7Ozs7Ozt3Q0FBUTs7Ozs7Ozs4Q0FHM0UsOERBQUMvQix5REFBTUE7b0NBQ0x3TixTQUFTO3dDQUNQckssZUFBZSxDQUFDRDt3Q0FDaEJZLE1BQU07NENBQ0pRLE9BQU9wQixjQUFjLGlDQUFpQzs0Q0FDdERxQixhQUFhckIsY0FBYyx5QkFBeUI7d0NBQ3REO29DQUNGO29DQUNBc0IsU0FBU3RCLGNBQWMsWUFBWTtvQ0FDbkN3SyxNQUFLOzhDQUVKeEssNEJBQWMsOERBQUNqQyxvSkFBS0E7d0NBQUNzTCxXQUFVOzs7Ozs2REFBZSw4REFBQ3JMLG9KQUFJQTt3Q0FBQ3FMLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBT3ZFLDhEQUFDNU0scURBQUlBOzBCQUNILDRFQUFDQyw0REFBV0E7b0JBQUMyTSxXQUFVOzhCQUNyQiw0RUFBQ0Q7d0JBQUlDLFdBQVU7OzBDQUNiLDhEQUFDRDtnQ0FBSUMsV0FBVTs7a0RBQ2IsOERBQUM5TCxvSkFBTUE7d0NBQUM4TCxXQUFVOzs7Ozs7a0RBQ2xCLDhEQUFDck0sdURBQUtBO3dDQUNKeU4sYUFBWTt3Q0FDWmIsT0FBT25MO3dDQUNQb0wsVUFBVSxDQUFDQyxJQUFNcEwsY0FBY29MLEVBQUVFLE1BQU0sQ0FBQ0osS0FBSzt3Q0FDN0NQLFdBQVU7Ozs7Ozs7Ozs7OzswQ0FJZCw4REFBQ0Q7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDRDt3Q0FBSUMsV0FBVTs7MERBQ2IsOERBQUMvTCx1REFBS0E7Z0RBQUNvTSxTQUFRO2dEQUFXTCxXQUFVOzBEQUFzQjs7Ozs7OzBEQUMxRCw4REFBQ007Z0RBQ0N2RyxJQUFHO2dEQUNId0csT0FBTzlKO2dEQUNQK0osVUFBVSxDQUFDQyxJQUFNL0osa0JBQWtCK0osRUFBRUUsTUFBTSxDQUFDSixLQUFLO2dEQUNqRFAsV0FBVTs7a0VBRVYsOERBQUNjO3dEQUFPUCxPQUFNO2tFQUFHOzs7Ozs7a0VBQ2pCLDhEQUFDTzt3REFBT1AsT0FBTTtrRUFBYzs7Ozs7O2tFQUM1Qiw4REFBQ087d0RBQU9QLE9BQU07a0VBQVc7Ozs7OztrRUFDekIsOERBQUNPO3dEQUFPUCxPQUFNO2tFQUFjOzs7Ozs7a0VBQzVCLDhEQUFDTzt3REFBT1AsT0FBTTtrRUFBbUI7Ozs7OztrRUFDakMsOERBQUNPO3dEQUFPUCxPQUFNO2tFQUFZOzs7Ozs7a0VBQzFCLDhEQUFDTzt3REFBT1AsT0FBTTtrRUFBWTs7Ozs7O2tFQUMxQiw4REFBQ087d0RBQU9QLE9BQU07a0VBQVU7Ozs7OztrRUFDeEIsOERBQUNPO3dEQUFPUCxPQUFNO2tFQUFjOzs7Ozs7a0VBQzVCLDhEQUFDTzt3REFBT1AsT0FBTTtrRUFBVzs7Ozs7O2tFQUN6Qiw4REFBQ087d0RBQU9QLE9BQU07a0VBQVM7Ozs7OztrRUFDdkIsOERBQUNPO3dEQUFPUCxPQUFNO2tFQUFROzs7Ozs7Ozs7Ozs7Ozs7Ozs7a0RBSTFCLDhEQUFDUjt3Q0FBSUMsV0FBVTs7MERBQ2IsOERBQUMvTCx1REFBS0E7Z0RBQUNvTSxTQUFRO2dEQUFPTCxXQUFVOzBEQUFzQjs7Ozs7OzBEQUN0RCw4REFBQ007Z0RBQ0N2RyxJQUFHO2dEQUNId0csT0FBT2hLO2dEQUNQaUssVUFBVSxDQUFDQyxJQUFNakssVUFBVWlLLEVBQUVFLE1BQU0sQ0FBQ0osS0FBSztnREFDekNQLFdBQVU7O2tFQUVWLDhEQUFDYzt3REFBT1AsT0FBTTtrRUFBVTs7Ozs7O2tFQUN4Qiw4REFBQ087d0RBQU9QLE9BQU07a0VBQWE7Ozs7OztrRUFDM0IsOERBQUNPO3dEQUFPUCxPQUFNO2tFQUFZOzs7Ozs7a0VBQzFCLDhEQUFDTzt3REFBT1AsT0FBTTtrRUFBUTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztZQVNqQ2hILE1BQU1DLE9BQU8sQ0FBQ2xFLHFCQUFxQkEsaUJBQWlCaUYsTUFBTSxHQUFHLG1CQUM1RCw4REFBQ3dGO2dCQUFJQyxXQUFVOztrQ0FDYiw4REFBQzVNLHFEQUFJQTtrQ0FDSCw0RUFBQ0UsMkRBQVVBOzs4Q0FDVCw4REFBQ0MsMERBQVNBO29DQUFDeU0sV0FBVTs4Q0FBVTs7Ozs7OzhDQUMvQiw4REFBQ3hNLGdFQUFlQTtvQ0FBQ3dNLFdBQVU7OENBQ3hCMUssaUJBQWlCaUYsTUFBTTs7Ozs7Ozs7Ozs7Ozs7Ozs7a0NBSTlCLDhEQUFDbkgscURBQUlBO2tDQUNILDRFQUFDRSwyREFBVUE7OzhDQUNULDhEQUFDQywwREFBU0E7b0NBQUN5TSxXQUFVOzhDQUFVOzs7Ozs7OENBQy9CLDhEQUFDeE0sZ0VBQWVBO29DQUFDd00sV0FBVTs4Q0FDeEIxSyxpQkFBaUIrTCxNQUFNLENBQUMsQ0FBQ0MsS0FBSzdFLElBQU02RSxNQUFPN0UsQ0FBQUEsRUFBRXBDLFNBQVMsSUFBSSxJQUFJOzs7Ozs7Ozs7Ozs7Ozs7OztrQ0FJckUsOERBQUNqSCxxREFBSUE7a0NBQ0gsNEVBQUNFLDJEQUFVQTs7OENBQ1QsOERBQUNDLDBEQUFTQTtvQ0FBQ3lNLFdBQVU7OENBQVU7Ozs7Ozs4Q0FDL0IsOERBQUN4TSxnRUFBZUE7b0NBQUN3TSxXQUFVOzhDQUN4QjFLLGlCQUFpQmlGLE1BQU0sR0FBRyxJQUN2QnZGLDZEQUFlQSxDQUFDK0osWUFBWSxDQUMxQnpELEtBQUtpRyxHQUFHLElBQUlqTSxpQkFBaUJzRSxHQUFHLENBQUM2QyxDQUFBQSxJQUFLQSxFQUFFeEMsVUFBVSxJQUFJLEtBQ3REakQsZ0JBRUZoQyw2REFBZUEsQ0FBQytKLFlBQVksQ0FBQyxHQUFHL0g7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQVM5Qyw4REFBQytJO2dCQUFJQyxXQUFVOzBCQUNaekcsTUFBTUMsT0FBTyxDQUFDbEUscUJBQXFCQSxpQkFBaUJzRSxHQUFHLENBQUMsQ0FBQ0Msd0JBQ3hELDhEQUFDekcscURBQUlBO3dCQUFrQjRNLFdBQVU7d0JBQW1EaUIsU0FBUyxJQUFNbkIsa0JBQWtCakcsUUFBUUUsRUFBRTs7MENBQzdILDhEQUFDekcsMkRBQVVBOztrREFDVCw4REFBQ3lNO3dDQUFJQyxXQUFVOzs0Q0FDWm5HLFFBQVEySCxNQUFNLElBQUkzSCxRQUFRMkgsTUFBTSxDQUFDakgsTUFBTSxHQUFHLGtCQUN6Qyw4REFBQ2tIO2dEQUNDQyxLQUFLN0gsUUFBUTJILE1BQU0sQ0FBQyxFQUFFLENBQUNHLEdBQUc7Z0RBQzFCQyxLQUFLL0gsUUFBUTlCLEtBQUs7Z0RBQ2xCaUksV0FBVTtnREFDVjZCLFNBQVMsQ0FBQ3BCO29EQUNSQSxFQUFFcUIsYUFBYSxDQUFDQyxLQUFLLENBQUNDLE9BQU8sR0FBRztvREFDaEN2QixFQUFFcUIsYUFBYSxDQUFDRyxrQkFBa0IsQ0FBQ0YsS0FBSyxDQUFDQyxPQUFPLEdBQUc7Z0RBQ3JEOzs7Ozt1REFFQTswREFDSiw4REFBQ2pDO2dEQUFJQyxXQUFVO2dEQUErRCtCLE9BQU87b0RBQUNDLFNBQVNuSSxRQUFRMkgsTUFBTSxJQUFJM0gsUUFBUTJILE1BQU0sQ0FBQ2pILE1BQU0sR0FBRyxJQUFJLFNBQVM7Z0RBQU07MERBQzFKLDRFQUFDd0Y7b0RBQUlDLFdBQVU7O3NFQUNiLDhEQUFDekwsb0pBQUtBOzREQUFDeUwsV0FBVTs7Ozs7O3NFQUNqQiw4REFBQ0k7NERBQUtKLFdBQVU7c0VBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7OzRDQUc3Qm5HLFFBQVFpQixNQUFNLEtBQUssMEJBQ2xCLDhEQUFDcEgsdURBQUtBO2dEQUFDc00sV0FBVTswREFBc0M7Ozs7Ozs7Ozs7OztrREFLM0QsOERBQUN6TSwwREFBU0E7d0NBQUN5TSxXQUFVO2tEQUF3Qm5HLFFBQVE5QixLQUFLOzs7Ozs7a0RBQzFELDhEQUFDdkUsZ0VBQWVBO3dDQUFDd00sV0FBVTtrREFDeEJuRyxRQUFRN0IsV0FBVzs7Ozs7Ozs7Ozs7OzBDQUd4Qiw4REFBQzNFLDREQUFXQTtnQ0FBQzJNLFdBQVU7O2tEQUNyQiw4REFBQ0Q7d0NBQUlDLFdBQVU7OzBEQUNiLDhEQUFDRDs7a0VBQ0MsOERBQUNJO3dEQUFFSCxXQUFVO2tFQUFnQzs7Ozs7O2tFQUM3Qyw4REFBQ0c7d0RBQUVILFdBQVU7OzREQUNWaEwsNkRBQWVBLENBQUMrSixZQUFZLENBQUNsRixRQUFRSSxVQUFVLElBQUksR0FBR2pEOzREQUN0RDZDLFFBQVEyQixnQkFBZ0IsSUFBSTNCLFFBQVEyQixnQkFBZ0IsS0FBS3hFLDhCQUN4RCw4REFBQ29KO2dFQUFLSixXQUFVOztvRUFBNkI7b0VBQ3RDbkcsUUFBUTJCLGdCQUFnQjtvRUFBQzs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswREFLdEMsOERBQUN1RTtnREFBSUMsV0FBVTs7a0VBQ2IsOERBQUNHO3dEQUFFSCxXQUFVO2tFQUFnQzs7Ozs7O2tFQUM3Qyw4REFBQ0c7d0RBQUVILFdBQVU7a0VBQXlCbkcsUUFBUVEsU0FBUyxJQUFJOzs7Ozs7Ozs7Ozs7Ozs7Ozs7a0RBSS9ELDhEQUFDMEY7d0NBQUlDLFdBQVU7OzBEQUNiLDhEQUFDM0wsb0pBQUtBO2dEQUFDMkwsV0FBVTs7Ozs7OzBEQUNqQiw4REFBQ0k7Z0RBQUtKLFdBQVU7O29EQUFnQztvREFDakNuRCxpQkFBaUJoRCxRQUFRTyxPQUFPOzs7Ozs7Ozs7Ozs7O2tEQUlqRCw4REFBQzJGO3dDQUFJQyxXQUFVOzs0Q0FBZ0M7NENBQ3BDbkcsUUFBUVcsTUFBTTs7Ozs7OztrREFHekIsOERBQUN1Rjt3Q0FBSUMsV0FBVTs7MERBQ2IsOERBQUN2TSx5REFBTUE7Z0RBQ0x3TixTQUFTLENBQUNSO29EQUNSQSxFQUFFeUIsY0FBYztvREFDaEJ6QixFQUFFMEIsZUFBZTtvREFDakI5RSxVQUFVeEQsUUFBUUUsRUFBRTtnREFDdEI7Z0RBQ0FpRyxXQUFVO2dEQUNWa0IsVUFBVTFMLFdBQVdhOztrRUFFckIsOERBQUMvQixvSkFBVUE7d0RBQUMwTCxXQUFVOzs7Ozs7b0RBQ3JCM0osYUFBYSxxQkFBcUI7Ozs7Ozs7MERBRXJDLDhEQUFDNUMseURBQU1BO2dEQUNMd0UsU0FBU3ZDLGNBQWN5SixHQUFHLENBQUN0RixRQUFRRSxFQUFFLElBQUksWUFBWTtnREFDckRvSCxNQUFLO2dEQUNMRixTQUFTLENBQUNSO29EQUNSQSxFQUFFeUIsY0FBYztvREFDaEJ6QixFQUFFMEIsZUFBZTtvREFDakJsRCxrQkFBa0JwRixRQUFRRSxFQUFFO2dEQUM5QjtnREFDQW1ILFVBQVUxTDswREFFViw0RUFBQ3BCLG9KQUFLQTtvREFBQzRMLFdBQVcsV0FBK0QsT0FBcER0SyxjQUFjeUosR0FBRyxDQUFDdEYsUUFBUUUsRUFBRSxJQUFJLGlCQUFpQjs7Ozs7Ozs7Ozs7MERBRWhGLDhEQUFDdEcseURBQU1BO2dEQUNMd0UsU0FBUTtnREFDUmtKLE1BQUs7Z0RBQ0xGLFNBQVMsQ0FBQ1I7b0RBQ1JBLEVBQUV5QixjQUFjO29EQUNoQnpCLEVBQUUwQixlQUFlO29EQUNqQnJDLGtCQUFrQmpHLFFBQVFFLEVBQUU7Z0RBQzlCO2dEQUNBbUgsVUFBVTFMOzBEQUVWLDRFQUFDckIsb0pBQUdBO29EQUFDNkwsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O3VCQWhHWm5HLFFBQVFFLEVBQUU7Ozs7Ozs7Ozs7WUF5R3hCLENBQUN2RSxXQUFZLEVBQUMrRCxNQUFNQyxPQUFPLENBQUNsRSxxQkFBcUJBLGlCQUFpQmlGLE1BQU0sS0FBSyxvQkFDNUUsOERBQUNuSCxxREFBSUE7MEJBQ0gsNEVBQUNDLDREQUFXQTtvQkFBQzJNLFdBQVU7OEJBQ3JCLDRFQUFDRDt3QkFBSUMsV0FBVTs7MENBQ2IsOERBQUNEO2dDQUFJQyxXQUFVOzBDQUNiLDRFQUFDMUwsb0pBQVVBO29DQUFDMEwsV0FBVTs7Ozs7Ozs7Ozs7MENBRXhCLDhEQUFDRDs7a0RBQ0MsOERBQUNxQzt3Q0FBR3BDLFdBQVU7a0RBQ1g1SyxhQUFhLCtCQUErQjs7Ozs7O2tEQUUvQyw4REFBQytLO3dDQUFFSCxXQUFVO2tEQUNWNUssYUFDRyxrRUFDQTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztZQVVmSSx5QkFDQyw4REFBQ3BDLHFEQUFJQTswQkFDSCw0RUFBQ0MsNERBQVdBO29CQUFDMk0sV0FBVTs4QkFDckIsNEVBQUNEO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQ0Q7Z0NBQUlDLFdBQVU7Ozs7OzswQ0FDZiw4REFBQ0c7Z0NBQUVILFdBQVU7MENBQXdCOzs7Ozs7MENBQ3JDLDhEQUFDRztnQ0FBRUgsV0FBVTs7b0NBQXdCO29DQUFVeEssUUFBUTZNLFFBQVE7b0NBQUc7b0NBQWFuTixTQUFTcUYsTUFBTTtvQ0FBQztvQ0FBYWpGLGlCQUFpQmlGLE1BQU07Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQU8zSSw4REFBQzNHLHlEQUFNQTtnQkFBQzBPLE1BQU12TTtnQkFBY3dNLGNBQWN2TTswQkFDeEMsNEVBQUNuQyxnRUFBYUE7b0JBQUNtTSxXQUFVOztzQ0FDdkIsOERBQUNsTSwrREFBWUE7c0NBQ1gsNEVBQUNDLDhEQUFXQTtnQ0FBQ2lNLFdBQVU7O2tEQUNyQiw4REFBQ3pMLG9KQUFLQTt3Q0FBQ3lMLFdBQVU7Ozs7OztvQ0FBWTs7Ozs7Ozs7Ozs7O3dCQUtoQy9KLGlDQUNDLDhEQUFDOEo7NEJBQUlDLFdBQVU7OzhDQUNiLDhEQUFDRDtvQ0FBSUMsV0FBVTs7c0RBQ2IsOERBQUN3Qzs0Q0FBR3hDLFdBQVU7c0RBQXNCL0osZ0JBQWdCOEIsS0FBSzs7Ozs7O3NEQUN6RCw4REFBQ2dJOzRDQUFJQyxXQUFVOzs4REFDYiw4REFBQ0Q7b0RBQUlDLFdBQVU7O3NFQUNiLDhEQUFDeEwsb0pBQVVBOzREQUFDd0wsV0FBVTs7Ozs7O3NFQUN0Qiw4REFBQ0k7O2dFQUFLO2dFQUFvQm5LLENBQUFBLGdCQUFnQmdFLFVBQVUsSUFBSSxHQUFHMEQsY0FBYztnRUFBRzs7Ozs7Ozs7Ozs7Ozs4REFFOUUsOERBQUNvQztvREFBSUMsV0FBVTs7c0VBQ2IsOERBQUMxTCxvSkFBVUE7NERBQUMwTCxXQUFVOzs7Ozs7c0VBQ3RCLDhEQUFDSTs7Z0VBQU1uSyxnQkFBZ0JvRSxTQUFTLElBQUk7Z0VBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OENBSzVDLDhEQUFDMEY7b0NBQUlDLFdBQVU7O3NEQUNiLDhEQUFDL0wsdURBQUtBOzRDQUFDb00sU0FBUTtzREFBWTs7Ozs7O3NEQUMzQiw4REFBQzFNLHVEQUFLQTs0Q0FDSm9HLElBQUc7NENBQ0g2QixNQUFLOzRDQUNMMkUsT0FBT3BLOzRDQUNQcUssVUFBVSxDQUFDQyxJQUFNckssYUFBYXFLLEVBQUVFLE1BQU0sQ0FBQ0osS0FBSzs0Q0FDNUNhLGFBQVk7NENBQ1pxQixLQUFLLENBQUN4TSxnQkFBZ0JnRSxVQUFVLElBQUksS0FBSzs7Ozs7O3NEQUUzQyw4REFBQ2tHOzRDQUFFSCxXQUFVOztnREFBd0I7Z0RBQ3BCLEVBQUMvSixnQkFBZ0JnRSxVQUFVLElBQUksS0FBSyxHQUFHMEQsY0FBYztnREFBRzs7Ozs7Ozs7Ozs7Ozs7Ozs7OztzQ0FNL0UsOERBQUMzSiwrREFBWUE7OzhDQUNYLDhEQUFDUCx5REFBTUE7b0NBQ0x3RSxTQUFRO29DQUNSZ0osU0FBUzt3Q0FDUGpMLGdCQUFnQjt3Q0FDaEJJLGFBQWE7d0NBQ2JGLG1CQUFtQjtvQ0FDckI7b0NBQ0FnTCxVQUFVN0s7OENBQ1g7Ozs7Ozs4Q0FHRCw4REFBQzVDLHlEQUFNQTtvQ0FDTHdOLFNBQVNuRDtvQ0FDVG9ELFVBQVU3SyxjQUFjLENBQUNGO29DQUN6QjZKLFdBQVU7OENBRVQzSixhQUFhLG9CQUFvQjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFPbEQ7R0F2OUJ3QnBCOztRQTZCUEgsc0RBQVNBO1FBQ05DLCtEQUFRQTs7O0tBOUJKRSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9hcHAvdXNlci9hdWN0aW9ucy9wYWdlLnRzeD80ZWZkIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50J1xuXG5pbXBvcnQgUmVhY3QsIHsgdXNlU3RhdGUsIHVzZUVmZmVjdCB9IGZyb20gJ3JlYWN0J1xuaW1wb3J0IHsgQ2FyZCwgQ2FyZENvbnRlbnQsIENhcmRIZWFkZXIsIENhcmRUaXRsZSwgQ2FyZERlc2NyaXB0aW9uIH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL2NhcmQnXG5pbXBvcnQgeyBCdXR0b24gfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvYnV0dG9uJ1xuaW1wb3J0IHsgQmFkZ2UgfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvYmFkZ2UnXG5pbXBvcnQgeyBJbnB1dCB9IGZyb20gJ0AvY29tcG9uZW50cy91aS9pbnB1dCdcbmltcG9ydCB7IERpYWxvZywgRGlhbG9nQ29udGVudCwgRGlhbG9nSGVhZGVyLCBEaWFsb2dUaXRsZSwgRGlhbG9nRm9vdGVyIH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL2RpYWxvZydcbmltcG9ydCB7IExhYmVsIH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL2xhYmVsJ1xuXG5pbXBvcnQgeyBTZWFyY2gsIEV5ZSwgSGVhcnQsIENsb2NrLCBUcmVuZGluZ1VwLCBHYXZlbCwgRG9sbGFyU2lnbiwgRmlsdGVyLCBTb3J0QXNjLCBSZWZyZXNoQ3csIFBhdXNlLCBQbGF5IH0gZnJvbSAnbHVjaWRlLXJlYWN0J1xuaW1wb3J0IHsgYXVjdGlvbkFQSSwgZmF2b3JpdGVzQVBJIH0gZnJvbSAnQC9saWIvYXBpJ1xuaW1wb3J0IHsgdXNlUm91dGVyIH0gZnJvbSAnbmV4dC9uYXZpZ2F0aW9uJ1xuaW1wb3J0IHsgdXNlVG9hc3QgfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvdXNlLXRvYXN0J1xuaW1wb3J0IGN1cnJlbmN5U2VydmljZSBmcm9tICdAL2xpYi9jdXJyZW5jeVNlcnZpY2UnXG5cblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gVXNlckF1Y3Rpb25zUGFnZSgpIHtcbiAgY29uc3QgW2F1Y3Rpb25zLCBzZXRBdWN0aW9uc10gPSB1c2VTdGF0ZTxhbnlbXT4oW10pXG4gIGNvbnN0IFtzZWFyY2hUZXJtLCBzZXRTZWFyY2hUZXJtXSA9IHVzZVN0YXRlKCcnKVxuICBjb25zdCBbZmlsdGVyZWRBdWN0aW9ucywgc2V0RmlsdGVyZWRBdWN0aW9uc10gPSB1c2VTdGF0ZTxhbnlbXT4oW10pXG4gIGNvbnN0IFtsb2FkaW5nLCBzZXRMb2FkaW5nXSA9IHVzZVN0YXRlKGZhbHNlKVxuICBjb25zdCBbc2F2ZWRBdWN0aW9ucywgc2V0U2F2ZWRBdWN0aW9uc10gPSB1c2VTdGF0ZTxTZXQ8c3RyaW5nPj4obmV3IFNldCgpKVxuICBjb25zdCBbdXNlciwgc2V0VXNlcl0gPSB1c2VTdGF0ZTxhbnk+KG51bGwpXG5cbiAgLy8gQmlkZGluZyBtb2RhbCBzdGF0ZVxuICBjb25zdCBbc2hvd0JpZE1vZGFsLCBzZXRTaG93QmlkTW9kYWxdID0gdXNlU3RhdGUoZmFsc2UpXG4gIGNvbnN0IFtzZWxlY3RlZEF1Y3Rpb24sIHNldFNlbGVjdGVkQXVjdGlvbl0gPSB1c2VTdGF0ZTxhbnk+KG51bGwpXG4gIGNvbnN0IFtiaWRBbW91bnQsIHNldEJpZEFtb3VudF0gPSB1c2VTdGF0ZSgnJylcbiAgY29uc3QgW2JpZExvYWRpbmcsIHNldEJpZExvYWRpbmddID0gdXNlU3RhdGUoZmFsc2UpXG5cbiAgLy8gRmlsdGVyIGFuZCBzb3J0IHN0YXRlXG4gIGNvbnN0IFtzb3J0QnksIHNldFNvcnRCeV0gPSB1c2VTdGF0ZSgnZW5kRGF0ZScpXG4gIGNvbnN0IFtmaWx0ZXJDYXRlZ29yeSwgc2V0RmlsdGVyQ2F0ZWdvcnldID0gdXNlU3RhdGUoJycpXG5cbiAgLy8gQXV0by1yZWZyZXNoIHN0YXRlXG4gIGNvbnN0IFthdXRvUmVmcmVzaCwgc2V0QXV0b1JlZnJlc2hdID0gdXNlU3RhdGUodHJ1ZSlcbiAgY29uc3QgW2xhc3RSZWZyZXNoLCBzZXRMYXN0UmVmcmVzaF0gPSB1c2VTdGF0ZTxEYXRlPihuZXcgRGF0ZSgpKVxuXG4gIC8vIEN1cnJlbmN5IHN0YXRlXG4gIGNvbnN0IFt1c2VyQ3VycmVuY3ksIHNldFVzZXJDdXJyZW5jeV0gPSB1c2VTdGF0ZSgnU0FSJylcbiAgY29uc3QgW2V4Y2hhbmdlUmF0ZXMsIHNldEV4Y2hhbmdlUmF0ZXNdID0gdXNlU3RhdGU8YW55Pih7fSlcblxuICAvLyBPcHRpbWlzdGljIGxvY2tpbmdcbiAgY29uc3QgW2F1Y3Rpb25WZXJzaW9ucywgc2V0QXVjdGlvblZlcnNpb25zXSA9IHVzZVN0YXRlPHtba2V5OiBzdHJpbmddOiBudW1iZXJ9Pih7fSlcblxuICBjb25zdCByb3V0ZXIgPSB1c2VSb3V0ZXIoKVxuICBjb25zdCB7IHRvYXN0IH0gPSB1c2VUb2FzdCgpXG5cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICAvLyBHZXQgdXNlciBkYXRhIGFuZCBjaGVjayBwZXJtaXNzaW9uc1xuICAgIGNvbnN0IHVzZXJEYXRhID0gbG9jYWxTdG9yYWdlLmdldEl0ZW0oJ3VzZXInKVxuICAgIGlmICh1c2VyRGF0YSkge1xuICAgICAgY29uc3QgcGFyc2VkVXNlciA9IEpTT04ucGFyc2UodXNlckRhdGEpXG4gICAgICBzZXRVc2VyKHBhcnNlZFVzZXIpXG5cbiAgICAgIC8vIFJlZGlyZWN0IGFkbWlucyB0byB0aGVpciBwcm9wZXIgZGFzaGJvYXJkXG4gICAgICBpZiAocGFyc2VkVXNlci5yb2xlID09PSAnYWRtaW4nIHx8IHBhcnNlZFVzZXIucm9sZSA9PT0gJ3N1cGVyX2FkbWluJykge1xuICAgICAgICB0b2FzdCh7XG4gICAgICAgICAgdGl0bGU6ICfZiNi12YjZhCDYutmK2LEg2YXYs9mF2YjYrScsXG4gICAgICAgICAgZGVzY3JpcHRpb246ICfYp9mE2YXYr9mK2LHZiNmGINmK2KzYqCDYo9mGINmK2LPYqtiu2K/ZhdmI2Kcg2YTZiNit2Kkg2KXYr9in2LHYqSDYp9mE2YXYstin2K/Yp9iqJyxcbiAgICAgICAgICB2YXJpYW50OiAnZGVzdHJ1Y3RpdmUnXG4gICAgICAgIH0pXG4gICAgICAgIHJvdXRlci5wdXNoKCcvYWRtaW4vYXVjdGlvbnMnKVxuICAgICAgICByZXR1cm5cbiAgICAgIH1cblxuICAgICAgLy8gUmVkaXJlY3QgZ292ZXJubWVudCB1c2VycyB0byB0aGVpciBkYXNoYm9hcmRcbiAgICAgIGlmIChwYXJzZWRVc2VyLnJvbGUgPT09ICdnb3Zlcm5tZW50Jykge1xuICAgICAgICB0b2FzdCh7XG4gICAgICAgICAgdGl0bGU6ICfZiNi12YjZhCDYutmK2LEg2YXYs9mF2YjYrScsXG4gICAgICAgICAgZGVzY3JpcHRpb246ICfYp9mE2KzZh9in2Kog2KfZhNit2YPZiNmF2YrYqSDYqtiz2KrYrtiv2YUg2YbYuNin2YUg2KfZhNmF2YbYp9mC2LXYp9iqJyxcbiAgICAgICAgICB2YXJpYW50OiAnZGVzdHJ1Y3RpdmUnXG4gICAgICAgIH0pXG4gICAgICAgIHJvdXRlci5wdXNoKCcvZ292ZXJubWVudC9kYXNoYm9hcmQnKVxuICAgICAgICByZXR1cm5cbiAgICAgIH1cbiAgICB9XG4gIH0sIFtdKVxuXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgLy8gSW5pdGlhbGl6ZSBjdXJyZW5jeSBmaXJzdFxuICAgIGNvbnN0IHNhdmVkQ3VycmVuY3kgPSBjdXJyZW5jeVNlcnZpY2UuZ2V0VXNlckN1cnJlbmN5KClcbiAgICBjb25zb2xlLmxvZygn8J+UpyBJbml0aWFsaXppbmcgY3VycmVuY3k6Jywgc2F2ZWRDdXJyZW5jeSlcbiAgICBzZXRVc2VyQ3VycmVuY3koc2F2ZWRDdXJyZW5jeSlcblxuICAgIC8vIExvYWQgZGF0YSBhZnRlciBjdXJyZW5jeSBpcyBzZXRcbiAgICBzZXRUaW1lb3V0KCgpID0+IHtcbiAgICAgIGxvYWRBdWN0aW9ucygpXG4gICAgICBsb2FkU2F2ZWRBdWN0aW9ucygpXG4gICAgICBsb2FkRXhjaGFuZ2VSYXRlcygpXG4gICAgfSwgMTAwKSAvLyBTbWFsbCBkZWxheSB0byBlbnN1cmUgc3RhdGUgdXBkYXRlXG4gIH0sIFtdKVxuXG4gIGNvbnN0IGxvYWRFeGNoYW5nZVJhdGVzID0gYXN5bmMgKCkgPT4ge1xuICAgIHRyeSB7XG4gICAgICBjb25zdCByYXRlcyA9IGF3YWl0IGN1cnJlbmN5U2VydmljZS5nZXRFeGNoYW5nZVJhdGVzKClcbiAgICAgIHNldEV4Y2hhbmdlUmF0ZXMocmF0ZXMpXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0ZhaWxlZCB0byBsb2FkIGV4Y2hhbmdlIHJhdGVzOicsIGVycm9yKVxuICAgIH1cbiAgfVxuXG4gIC8vIEF1dG8tcmVmcmVzaCBhdWN0aW9ucyBldmVyeSAzMCBzZWNvbmRzXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgaWYgKCFhdXRvUmVmcmVzaCkgcmV0dXJuXG5cbiAgICBjb25zdCBpbnRlcnZhbCA9IHNldEludGVydmFsKGFzeW5jICgpID0+IHtcbiAgICAgIHRyeSB7XG4gICAgICAgIGF3YWl0IGxvYWRBdWN0aW9ucyhmYWxzZSkgLy8gU2lsZW50IHJlZnJlc2ggd2l0aG91dCBsb2FkaW5nIHN0YXRlXG4gICAgICAgIHNldExhc3RSZWZyZXNoKG5ldyBEYXRlKCkpXG4gICAgICAgIGNvbnNvbGUubG9nKCfwn5SEIEF1dG8tcmVmcmVzaGVkIGF1Y3Rpb24gZGF0YScpXG4gICAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICBjb25zb2xlLmVycm9yKCdBdXRvLXJlZnJlc2ggZmFpbGVkOicsIGVycm9yKVxuICAgICAgfVxuICAgIH0sIDMwMDAwKSAvLyAzMCBzZWNvbmRzXG5cbiAgICByZXR1cm4gKCkgPT4gY2xlYXJJbnRlcnZhbChpbnRlcnZhbClcbiAgfSwgW2F1dG9SZWZyZXNoXSlcblxuICBjb25zdCBsb2FkQXVjdGlvbnMgPSBhc3luYyAoc2hvd0xvYWRpbmcgPSB0cnVlKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnNvbGUubG9nKCfwn5SEIFN0YXJ0aW5nIGxvYWRBdWN0aW9ucy4uLicsIHNob3dMb2FkaW5nID8gJ3dpdGggbG9hZGluZycgOiAnc2lsZW50JylcbiAgICAgIGlmIChzaG93TG9hZGluZykge1xuICAgICAgICBzZXRMb2FkaW5nKHRydWUpXG4gICAgICB9XG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGF1Y3Rpb25BUEkuZ2V0QWxsKClcblxuICAgICAgY29uc29sZS5sb2coJ0F1Y3Rpb25zIEFQSSByZXNwb25zZTonLCByZXNwb25zZS5kYXRhKVxuXG4gICAgICAvLyBIYW5kbGUgdGhlIEFQSSByZXNwb25zZSBzdHJ1Y3R1cmUgcHJvcGVybHlcbiAgICAgIGxldCBhdWN0aW9uRGF0YSA9IFtdXG4gICAgICBpZiAocmVzcG9uc2UuZGF0YSAmJiByZXNwb25zZS5kYXRhLnN1Y2Nlc3MpIHtcbiAgICAgICAgLy8gSWYgdGhlIHJlc3BvbnNlIGhhcyBhIHN1Y2Nlc3MgZmllbGQsIGV4dHJhY3QgdGhlIGF1Y3Rpb25zIGZyb20gZGF0YS5kYXRhLmF1Y3Rpb25zXG4gICAgICAgIGF1Y3Rpb25EYXRhID0gcmVzcG9uc2UuZGF0YS5kYXRhPy5hdWN0aW9ucyB8fCBbXVxuICAgICAgfSBlbHNlIGlmIChyZXNwb25zZS5kYXRhICYmIEFycmF5LmlzQXJyYXkocmVzcG9uc2UuZGF0YSkpIHtcbiAgICAgICAgLy8gSWYgcmVzcG9uc2UuZGF0YSBpcyBkaXJlY3RseSBhbiBhcnJheVxuICAgICAgICBhdWN0aW9uRGF0YSA9IHJlc3BvbnNlLmRhdGFcbiAgICAgIH0gZWxzZSBpZiAocmVzcG9uc2UuZGF0YSAmJiByZXNwb25zZS5kYXRhLmRhdGEgJiYgQXJyYXkuaXNBcnJheShyZXNwb25zZS5kYXRhLmRhdGEpKSB7XG4gICAgICAgIC8vIElmIHRoZSBhdWN0aW9ucyBhcmUgaW4gcmVzcG9uc2UuZGF0YS5kYXRhXG4gICAgICAgIGF1Y3Rpb25EYXRhID0gcmVzcG9uc2UuZGF0YS5kYXRhXG4gICAgICB9XG5cbiAgICAgIC8vIFRyYW5zZm9ybSBhbmQgY29udmVydCBjdXJyZW5jeSBmb3IgZWFjaCBhdWN0aW9uXG4gICAgICBjb25zdCB0cmFuc2Zvcm1lZERhdGEgPSBhd2FpdCBQcm9taXNlLmFsbChhdWN0aW9uRGF0YS5tYXAoYXN5bmMgKGF1Y3Rpb246IGFueSkgPT4ge1xuICAgICAgICBjb25zdCBiYXNlQXVjdGlvbiA9IHtcbiAgICAgICAgICBpZDogYXVjdGlvbi5faWQgfHwgYXVjdGlvbi5pZCxcbiAgICAgICAgICB0aXRsZTogYXVjdGlvbi50aXRsZSxcbiAgICAgICAgICBkZXNjcmlwdGlvbjogYXVjdGlvbi5kZXNjcmlwdGlvbixcbiAgICAgICAgICBjdXJyZW50QmlkOiBhdWN0aW9uLmN1cnJlbnRQcmljZSB8fCBhdWN0aW9uLmN1cnJlbnRCaWQgfHwgYXVjdGlvbi5zdGFydGluZ1ByaWNlIHx8IDAsXG4gICAgICAgICAgZW5kRGF0ZTogYXVjdGlvbi5lbmREYXRlLFxuICAgICAgICAgIGJpZHNDb3VudDogYXVjdGlvbi5iaWRzPy5sZW5ndGggfHwgYXVjdGlvbi5iaWRzQ291bnQgfHwgMCxcbiAgICAgICAgICBzZWxsZXI6IGF1Y3Rpb24uc2VsbGVyPy5wcm9maWxlPy5mdWxsTmFtZSB8fCBhdWN0aW9uLnNlbGxlcj8ucHJvZmlsZT8uY29tcGFueU5hbWUgfHwgYXVjdGlvbi5zZWxsZXI/Lm5hbWUgfHwgYXVjdGlvbi5zZWxsZXI/Ll9pZCB8fCAn2LrZitixINmF2K3Yr9ivJyxcbiAgICAgICAgICBjYXRlZ29yeTogYXVjdGlvbi5jYXRlZ29yeSxcbiAgICAgICAgICBzdGF0dXM6IGF1Y3Rpb24uc3RhdHVzLFxuICAgICAgICAgIHZpZXdzOiBhdWN0aW9uLnZpZXdzIHx8IDAsXG4gICAgICAgICAgbG9jYXRpb246IGF1Y3Rpb24ubG9jYXRpb24sXG4gICAgICAgICAgY3VycmVuY3k6IGF1Y3Rpb24uY3VycmVuY3kgfHwgJ1NBUicsXG4gICAgICAgICAgdmVyc2lvbjogYXVjdGlvbi52ZXJzaW9uIHx8IDBcbiAgICAgICAgfVxuXG4gICAgICAgIC8vIFN0b3JlIHZlcnNpb24gZm9yIG9wdGltaXN0aWMgbG9ja2luZ1xuICAgICAgICBzZXRBdWN0aW9uVmVyc2lvbnMocHJldiA9PiAoe1xuICAgICAgICAgIC4uLnByZXYsXG4gICAgICAgICAgW2Jhc2VBdWN0aW9uLmlkXTogYmFzZUF1Y3Rpb24udmVyc2lvblxuICAgICAgICB9KSlcblxuICAgICAgICAvLyBDb252ZXJ0IGN1cnJlbmN5IGlmIG5lZWRlZFxuICAgICAgICBjb25zb2xlLmxvZyhg8J+UjSBDaGVja2luZyBjb252ZXJzaW9uOiAke2Jhc2VBdWN0aW9uLmN1cnJlbmN5fSB2cyAke3VzZXJDdXJyZW5jeX0gZm9yIGF1Y3Rpb24gJHtiYXNlQXVjdGlvbi5pZH1gKVxuICAgICAgICBjb25zb2xlLmxvZyhg8J+SsCBDdXJyZW50IHVzZXJDdXJyZW5jeSBzdGF0ZTogJHt1c2VyQ3VycmVuY3l9YClcbiAgICAgICAgdHJ5IHtcbiAgICAgICAgICBpZiAoYmFzZUF1Y3Rpb24uY3VycmVuY3kgIT09IHVzZXJDdXJyZW5jeSkge1xuICAgICAgICAgICAgY29uc29sZS5sb2coYPCfkrEgQ29udmVydGluZyAke2Jhc2VBdWN0aW9uLmN1cnJlbnRCaWR9ICR7YmFzZUF1Y3Rpb24uY3VycmVuY3l9IOKGkiAke3VzZXJDdXJyZW5jeX1gKVxuICAgICAgICAgICAgY29uc3QgY29udmVydGVkQmlkID0gYXdhaXQgY3VycmVuY3lTZXJ2aWNlLmNvbnZlcnRDdXJyZW5jeShcbiAgICAgICAgICAgICAgYmFzZUF1Y3Rpb24uY3VycmVudEJpZCxcbiAgICAgICAgICAgICAgYmFzZUF1Y3Rpb24uY3VycmVuY3ksXG4gICAgICAgICAgICAgIHVzZXJDdXJyZW5jeVxuICAgICAgICAgICAgKVxuICAgICAgICAgICAgY29uc29sZS5sb2coYPCfkrEgQ29udmVydGVkIHJlc3VsdDogJHtNYXRoLnJvdW5kKGNvbnZlcnRlZEJpZCl9ICR7dXNlckN1cnJlbmN5fWApXG4gICAgICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgICAuLi5iYXNlQXVjdGlvbixcbiAgICAgICAgICAgICAgY3VycmVudEJpZDogTWF0aC5yb3VuZChjb252ZXJ0ZWRCaWQpLFxuICAgICAgICAgICAgICBvcmlnaW5hbEN1cnJlbmN5OiBiYXNlQXVjdGlvbi5jdXJyZW5jeSxcbiAgICAgICAgICAgICAgY3VycmVuY3k6IHVzZXJDdXJyZW5jeVxuICAgICAgICAgICAgfVxuICAgICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICBjb25zb2xlLmxvZyhg4o+t77iPIE5vIGNvbnZlcnNpb24gbmVlZGVkOiBjdXJyZW5jaWVzIG1hdGNoICgke2Jhc2VBdWN0aW9uLmN1cnJlbmN5fSlgKVxuICAgICAgICAgIH1cbiAgICAgICAgfSBjYXRjaCAoY29udmVyc2lvbkVycm9yKSB7XG4gICAgICAgICAgY29uc29sZS53YXJuKCdDdXJyZW5jeSBjb252ZXJzaW9uIGZhaWxlZCBmb3IgYXVjdGlvbjonLCBiYXNlQXVjdGlvbi5pZCwgY29udmVyc2lvbkVycm9yKVxuICAgICAgICB9XG5cbiAgICAgICAgcmV0dXJuIGJhc2VBdWN0aW9uXG4gICAgICB9KSlcblxuICAgICAgc2V0QXVjdGlvbnModHJhbnNmb3JtZWREYXRhKVxuICAgICAgc2V0RmlsdGVyZWRBdWN0aW9ucyh0cmFuc2Zvcm1lZERhdGEpXG4gICAgICBjb25zb2xlLmxvZygn4pyFIFNldCBhdWN0aW9ucyBhbmQgZmlsdGVyZWRBdWN0aW9uczonLCB0cmFuc2Zvcm1lZERhdGEubGVuZ3RoKVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBsb2FkaW5nIGF1Y3Rpb25zOicsIGVycm9yKVxuICAgICAgc2V0QXVjdGlvbnMoW10pXG4gICAgICBzZXRGaWx0ZXJlZEF1Y3Rpb25zKFtdKVxuXG4gICAgICB0b2FzdCh7XG4gICAgICAgIHRpdGxlOiAn2YHYtNmEINmB2Yog2KrYrdmF2YrZhCDYp9mE2YXYstin2K/Yp9iqJyxcbiAgICAgICAgZGVzY3JpcHRpb246ICfYrdiv2Ksg2K7Yt9ijINij2KvZhtin2KEg2KrYrdmF2YrZhCDYp9mE2YXYstin2K/Yp9iqLiDZitix2KzZiSDYp9mE2YXYrdin2YjZhNipINmF2LHYqSDYo9iu2LHZiS4nLFxuICAgICAgICB2YXJpYW50OiAnZGVzdHJ1Y3RpdmUnXG4gICAgICB9KVxuICAgIH0gZmluYWxseSB7XG4gICAgICBpZiAoc2hvd0xvYWRpbmcpIHtcbiAgICAgICAgc2V0TG9hZGluZyhmYWxzZSlcbiAgICAgIH1cbiAgICAgIGNvbnNvbGUubG9nKCfwn4+BIExvYWRpbmcgZmluaXNoZWQuIEF1Y3Rpb25zIGxvYWRlZDonLCBhdWN0aW9ucy5sZW5ndGgpXG4gICAgfVxuICB9XG5cbiAgY29uc3QgbG9hZFNhdmVkQXVjdGlvbnMgPSBhc3luYyAoKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmF2b3JpdGVzQVBJLmdldEZhdm9yaXRlcyh7IHR5cGU6ICdhdWN0aW9uJyB9KVxuXG4gICAgICAvLyBIYW5kbGUgZGlmZmVyZW50IHJlc3BvbnNlIHN0cnVjdHVyZXNcbiAgICAgIGxldCBmYXZvcml0ZXNEYXRhID0gW11cbiAgICAgIGlmIChyZXNwb25zZS5kYXRhICYmIHJlc3BvbnNlLmRhdGEuc3VjY2Vzcykge1xuICAgICAgICBmYXZvcml0ZXNEYXRhID0gcmVzcG9uc2UuZGF0YS5kYXRhIHx8IFtdXG4gICAgICB9IGVsc2UgaWYgKHJlc3BvbnNlLmRhdGEgJiYgQXJyYXkuaXNBcnJheShyZXNwb25zZS5kYXRhKSkge1xuICAgICAgICBmYXZvcml0ZXNEYXRhID0gcmVzcG9uc2UuZGF0YVxuICAgICAgfSBlbHNlIGlmIChyZXNwb25zZS5kYXRhICYmIHJlc3BvbnNlLmRhdGEuZmF2b3JpdGVzICYmIEFycmF5LmlzQXJyYXkocmVzcG9uc2UuZGF0YS5mYXZvcml0ZXMpKSB7XG4gICAgICAgIGZhdm9yaXRlc0RhdGEgPSByZXNwb25zZS5kYXRhLmZhdm9yaXRlc1xuICAgICAgfVxuXG4gICAgICAvLyBDaGVjayBpZiBmYXZvcml0ZXNEYXRhIGlzIGFuIGFycmF5IGJlZm9yZSBtYXBwaW5nXG4gICAgICBpZiAoQXJyYXkuaXNBcnJheShmYXZvcml0ZXNEYXRhKSkge1xuICAgICAgICBjb25zdCBzYXZlZElkcyA9IG5ldyBTZXQ8c3RyaW5nPihmYXZvcml0ZXNEYXRhLm1hcCgoZmF2OiBhbnkpID0+IGZhdi5pdGVtSWQgfHwgZmF2Ll9pZCB8fCBmYXYuaWQpKVxuICAgICAgICBzZXRTYXZlZEF1Y3Rpb25zKHNhdmVkSWRzKVxuICAgICAgfSBlbHNlIHtcbiAgICAgICAgY29uc29sZS53YXJuKCdGYXZvcml0ZXMgZGF0YSBpcyBub3QgYW4gYXJyYXk6JywgZmF2b3JpdGVzRGF0YSlcbiAgICAgICAgc2V0U2F2ZWRBdWN0aW9ucyhuZXcgU2V0KCkpXG4gICAgICB9XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGxvYWRpbmcgc2F2ZWQgYXVjdGlvbnM6JywgZXJyb3IpXG4gICAgICAvLyBEb24ndCBzaG93IGVycm9yIHRvYXN0IGZvciBmYXZvcml0ZXMgYXMgaXQncyBub3QgY3JpdGljYWxcbiAgICAgIHNldFNhdmVkQXVjdGlvbnMobmV3IFNldCgpKVxuICAgIH1cbiAgfVxuXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgLy8gRW5zdXJlIGF1Y3Rpb25zIGlzIGFuIGFycmF5IGJlZm9yZSBmaWx0ZXJpbmdcbiAgICBpZiAoQXJyYXkuaXNBcnJheShhdWN0aW9ucykpIHtcbiAgICAgIGxldCBmaWx0ZXJlZCA9IGF1Y3Rpb25zLmZpbHRlcihhdWN0aW9uID0+IHtcbiAgICAgICAgY29uc3QgbWF0Y2hlc1NlYXJjaCA9IGF1Y3Rpb24udGl0bGU/LnRvTG93ZXJDYXNlKCkuaW5jbHVkZXMoc2VhcmNoVGVybS50b0xvd2VyQ2FzZSgpKSB8fFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICBhdWN0aW9uLmRlc2NyaXB0aW9uPy50b0xvd2VyQ2FzZSgpLmluY2x1ZGVzKHNlYXJjaFRlcm0udG9Mb3dlckNhc2UoKSlcbiAgICAgICAgY29uc3QgbWF0Y2hlc0NhdGVnb3J5ID0gIWZpbHRlckNhdGVnb3J5IHx8IGF1Y3Rpb24uY2F0ZWdvcnkgPT09IGZpbHRlckNhdGVnb3J5XG4gICAgICAgIHJldHVybiBtYXRjaGVzU2VhcmNoICYmIG1hdGNoZXNDYXRlZ29yeVxuICAgICAgfSlcblxuICAgICAgLy8gU29ydCB0aGUgZmlsdGVyZWQgcmVzdWx0c1xuICAgICAgZmlsdGVyZWQgPSBmaWx0ZXJlZC5zb3J0KChhLCBiKSA9PiB7XG4gICAgICAgIHN3aXRjaCAoc29ydEJ5KSB7XG4gICAgICAgICAgY2FzZSAnZW5kRGF0ZSc6XG4gICAgICAgICAgICByZXR1cm4gbmV3IERhdGUoYS5lbmREYXRlKS5nZXRUaW1lKCkgLSBuZXcgRGF0ZShiLmVuZERhdGUpLmdldFRpbWUoKVxuICAgICAgICAgIGNhc2UgJ2N1cnJlbnRCaWQnOlxuICAgICAgICAgICAgcmV0dXJuIChiLmN1cnJlbnRCaWQgfHwgMCkgLSAoYS5jdXJyZW50QmlkIHx8IDApXG4gICAgICAgICAgY2FzZSAnYmlkc0NvdW50JzpcbiAgICAgICAgICAgIHJldHVybiAoYi5iaWRzQ291bnQgfHwgMCkgLSAoYS5iaWRzQ291bnQgfHwgMClcbiAgICAgICAgICBjYXNlICd0aXRsZSc6XG4gICAgICAgICAgICByZXR1cm4gYS50aXRsZS5sb2NhbGVDb21wYXJlKGIudGl0bGUpXG4gICAgICAgICAgZGVmYXVsdDpcbiAgICAgICAgICAgIHJldHVybiAwXG4gICAgICAgIH1cbiAgICAgIH0pXG5cbiAgICAgIHNldEZpbHRlcmVkQXVjdGlvbnMoZmlsdGVyZWQpXG4gICAgICBjb25zb2xlLmxvZygn8J+UjSBGaWx0ZXJlZCBhdWN0aW9uczonLCBmaWx0ZXJlZC5sZW5ndGgsICdmcm9tJywgYXVjdGlvbnMubGVuZ3RoKVxuICAgIH0gZWxzZSB7XG4gICAgICBzZXRGaWx0ZXJlZEF1Y3Rpb25zKFtdKVxuICAgIH1cbiAgfSwgW3NlYXJjaFRlcm0sIGF1Y3Rpb25zLCBmaWx0ZXJDYXRlZ29yeSwgc29ydEJ5XSlcblxuICBjb25zdCBnZXRUaW1lUmVtYWluaW5nID0gKGVuZERhdGU6IHN0cmluZykgPT4ge1xuICAgIGNvbnN0IG5vdyA9IG5ldyBEYXRlKClcbiAgICBjb25zdCBlbmQgPSBuZXcgRGF0ZShlbmREYXRlKVxuICAgIGNvbnN0IGRpZmYgPSBlbmQuZ2V0VGltZSgpIC0gbm93LmdldFRpbWUoKVxuXG4gICAgaWYgKGRpZmYgPD0gMCkgcmV0dXJuICfYp9mG2KrZh9mJJ1xuXG4gICAgY29uc3QgaG91cnMgPSBNYXRoLmZsb29yKGRpZmYgLyAoMTAwMCAqIDYwICogNjApKVxuICAgIGNvbnN0IGRheXMgPSBNYXRoLmZsb29yKGhvdXJzIC8gMjQpXG5cbiAgICBpZiAoZGF5cyA+IDApIHJldHVybiBgJHtkYXlzfSDZitmI2YVgXG4gICAgcmV0dXJuIGAke2hvdXJzfSDYs9in2LnYqWBcbiAgfVxuXG4gIC8vIENoZWNrIGlmIHVzZXIgY2FuIGJpZFxuICBjb25zdCBjYW5Vc2VyQmlkID0gKGF1Y3Rpb246IGFueSkgPT4ge1xuICAgIGlmICghdXNlcikgcmV0dXJuIGZhbHNlXG5cbiAgICAvLyBPbmx5IGluZGl2aWR1YWxzIGFuZCBjb21wYW5pZXMgY2FuIGJpZFxuICAgIGlmICh1c2VyLnJvbGUgIT09ICdpbmRpdmlkdWFsJyAmJiB1c2VyLnJvbGUgIT09ICdjb21wYW55Jykge1xuICAgICAgcmV0dXJuIGZhbHNlXG4gICAgfVxuXG4gICAgLy8gQ29tcGFuaWVzIGNhbm5vdCBiaWQgb24gdGhlaXIgb3duIGF1Y3Rpb25zXG4gICAgaWYgKHVzZXIucm9sZSA9PT0gJ2NvbXBhbnknICYmIGF1Y3Rpb24uc2VsbGVyID09PSB1c2VyLnByb2ZpbGU/LmNvbXBhbnlOYW1lKSB7XG4gICAgICByZXR1cm4gZmFsc2VcbiAgICB9XG5cbiAgICByZXR1cm4gdHJ1ZVxuICB9XG5cbiAgY29uc3QgaGFuZGxlQmlkID0gYXN5bmMgKGF1Y3Rpb25JZDogc3RyaW5nKSA9PiB7XG4gICAgY29uc29sZS5sb2coJ/Cfjq8gQmlkIGJ1dHRvbiBjbGlja2VkIGZvciBhdWN0aW9uOicsIGF1Y3Rpb25JZClcblxuICAgIHRyeSB7XG4gICAgICAvLyBGZXRjaCBmcmVzaCBhdWN0aW9uIGRhdGEgYmVmb3JlIG9wZW5pbmcgbW9kYWxcbiAgICAgIHNldExvYWRpbmcodHJ1ZSlcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgYXVjdGlvbkFQSS5nZXRCeUlkKGF1Y3Rpb25JZClcblxuICAgICAgbGV0IGZyZXNoQXVjdGlvbiA9IG51bGxcbiAgICAgIGlmIChyZXNwb25zZS5kYXRhICYmIHJlc3BvbnNlLmRhdGEuc3VjY2Vzcykge1xuICAgICAgICBmcmVzaEF1Y3Rpb24gPSByZXNwb25zZS5kYXRhLmRhdGEuYXVjdGlvblxuICAgICAgfSBlbHNlIGlmIChyZXNwb25zZS5kYXRhICYmIHJlc3BvbnNlLmRhdGEuYXVjdGlvbikge1xuICAgICAgICBmcmVzaEF1Y3Rpb24gPSByZXNwb25zZS5kYXRhLmF1Y3Rpb25cbiAgICAgIH1cblxuICAgICAgaWYgKCFmcmVzaEF1Y3Rpb24pIHtcbiAgICAgICAgdG9hc3Qoe1xuICAgICAgICAgIHRpdGxlOiAn2K7Yt9ijJyxcbiAgICAgICAgICBkZXNjcmlwdGlvbjogJ9mE2YUg2YrYqtmFINin2YTYudir2YjYsSDYudmE2Ykg2KfZhNmF2LLYp9ivJyxcbiAgICAgICAgICB2YXJpYW50OiAnZGVzdHJ1Y3RpdmUnXG4gICAgICAgIH0pXG4gICAgICAgIHJldHVyblxuICAgICAgfVxuXG4gICAgICAvLyBUcmFuc2Zvcm0gZnJlc2ggZGF0YSB0byBtYXRjaCBvdXIgZm9ybWF0XG4gICAgICBjb25zdCB0cmFuc2Zvcm1lZEF1Y3Rpb24gPSB7XG4gICAgICAgIGlkOiBmcmVzaEF1Y3Rpb24uX2lkIHx8IGZyZXNoQXVjdGlvbi5pZCxcbiAgICAgICAgdGl0bGU6IGZyZXNoQXVjdGlvbi50aXRsZSxcbiAgICAgICAgZGVzY3JpcHRpb246IGZyZXNoQXVjdGlvbi5kZXNjcmlwdGlvbixcbiAgICAgICAgY3VycmVudEJpZDogZnJlc2hBdWN0aW9uLmN1cnJlbnRQcmljZSB8fCBmcmVzaEF1Y3Rpb24uY3VycmVudEJpZCB8fCBmcmVzaEF1Y3Rpb24uc3RhcnRpbmdQcmljZSB8fCAwLFxuICAgICAgICBlbmREYXRlOiBmcmVzaEF1Y3Rpb24uZW5kRGF0ZSxcbiAgICAgICAgYmlkc0NvdW50OiBmcmVzaEF1Y3Rpb24uYmlkcz8ubGVuZ3RoIHx8IGZyZXNoQXVjdGlvbi5iaWRzQ291bnQgfHwgMCxcbiAgICAgICAgc2VsbGVyOiBmcmVzaEF1Y3Rpb24uc2VsbGVyPy5wcm9maWxlPy5mdWxsTmFtZSB8fCBmcmVzaEF1Y3Rpb24uc2VsbGVyPy5wcm9maWxlPy5jb21wYW55TmFtZSB8fCBmcmVzaEF1Y3Rpb24uc2VsbGVyPy5uYW1lIHx8ICfYutmK2LEg2YXYrdiv2K8nLFxuICAgICAgICBjYXRlZ29yeTogZnJlc2hBdWN0aW9uLmNhdGVnb3J5LFxuICAgICAgICBzdGF0dXM6IGZyZXNoQXVjdGlvbi5zdGF0dXNcbiAgICAgIH1cblxuICAgICAgLy8gQ2hlY2sgaWYgdXNlciBjYW4gYmlkXG4gICAgICBpZiAoIWNhblVzZXJCaWQodHJhbnNmb3JtZWRBdWN0aW9uKSkge1xuICAgICAgICBpZiAodXNlcj8ucm9sZSA9PT0gJ2FkbWluJyB8fCB1c2VyPy5yb2xlID09PSAnc3VwZXJfYWRtaW4nKSB7XG4gICAgICAgICAgdG9hc3Qoe1xuICAgICAgICAgICAgdGl0bGU6ICfZiNi12YjZhCDYutmK2LEg2YXYs9mF2YjYrScsXG4gICAgICAgICAgICBkZXNjcmlwdGlvbjogJ9in2YTZhdiv2YrYsdmI2YYg2YTYpyDZitmF2YPZhtmH2YUg2KfZhNmF2LLYp9mK2K/YqSDYudmE2Ykg2KfZhNmF2LLYp9iv2KfYqicsXG4gICAgICAgICAgICB2YXJpYW50OiAnZGVzdHJ1Y3RpdmUnXG4gICAgICAgICAgfSlcbiAgICAgICAgfSBlbHNlIGlmICh1c2VyPy5yb2xlID09PSAnZ292ZXJubWVudCcpIHtcbiAgICAgICAgICB0b2FzdCh7XG4gICAgICAgICAgICB0aXRsZTogJ9mI2LXZiNmEINi62YrYsSDZhdiz2YXZiNitJyxcbiAgICAgICAgICAgIGRlc2NyaXB0aW9uOiAn2KfZhNis2YfYp9iqINin2YTYrdmD2YjZhdmK2Kkg2YTYpyDYqtiy2KfZitivINi52YTZiSDYp9mE2YXYstin2K/Yp9iqJyxcbiAgICAgICAgICAgIHZhcmlhbnQ6ICdkZXN0cnVjdGl2ZSdcbiAgICAgICAgICB9KVxuICAgICAgICB9IGVsc2UgaWYgKHVzZXI/LnJvbGUgPT09ICdjb21wYW55Jykge1xuICAgICAgICAgIHRvYXN0KHtcbiAgICAgICAgICAgIHRpdGxlOiAn2YjYtdmI2YQg2LrZitixINmF2LPZhdmI2K0nLFxuICAgICAgICAgICAgZGVzY3JpcHRpb246ICfZhNinINmK2YXZg9mG2YMg2KfZhNmF2LLYp9mK2K/YqSDYudmE2Ykg2YXYstin2K/ZgyDYp9mE2K7Yp9i1JyxcbiAgICAgICAgICAgIHZhcmlhbnQ6ICdkZXN0cnVjdGl2ZSdcbiAgICAgICAgICB9KVxuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgIHRvYXN0KHtcbiAgICAgICAgICAgIHRpdGxlOiAn2YjYtdmI2YQg2LrZitixINmF2LPZhdmI2K0nLFxuICAgICAgICAgICAgZGVzY3JpcHRpb246ICfYutmK2LEg2YXYs9mF2YjYrSDZhNmDINio2KfZhNmF2LLYp9mK2K/YqScsXG4gICAgICAgICAgICB2YXJpYW50OiAnZGVzdHJ1Y3RpdmUnXG4gICAgICAgICAgfSlcbiAgICAgICAgfVxuICAgICAgICByZXR1cm5cbiAgICAgIH1cblxuICAgICAgLy8gT3BlbiBiaWRkaW5nIG1vZGFsIHdpdGggZnJlc2ggZGF0YVxuICAgICAgc2V0U2VsZWN0ZWRBdWN0aW9uKHRyYW5zZm9ybWVkQXVjdGlvbilcbiAgICAgIHNldEJpZEFtb3VudChTdHJpbmcoKHRyYW5zZm9ybWVkQXVjdGlvbi5jdXJyZW50QmlkIHx8IDApICsgMTAwMCkpXG4gICAgICBzZXRTaG93QmlkTW9kYWwodHJ1ZSlcblxuICAgICAgdG9hc3Qoe1xuICAgICAgICB0aXRsZTogJ/Cfk4og2KrZhSDYqtit2K/ZitirINio2YrYp9mG2KfYqiDYp9mE2YXYstin2K8nLFxuICAgICAgICBkZXNjcmlwdGlvbjogYNin2YTZhdiy2KfZitiv2Kkg2KfZhNit2KfZhNmK2Kk6ICR7KHRyYW5zZm9ybWVkQXVjdGlvbi5jdXJyZW50QmlkIHx8IDApLnRvTG9jYWxlU3RyaW5nKCl9INixLtizYFxuICAgICAgfSlcblxuICAgIH0gY2F0Y2ggKGVycm9yOiBhbnkpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGZldGNoaW5nIGZyZXNoIGF1Y3Rpb24gZGF0YTonLCBlcnJvcilcbiAgICAgIHRvYXN0KHtcbiAgICAgICAgdGl0bGU6ICfYrti32KMg2YHZiiDYqtit2K/ZitirINin2YTYqNmK2KfZhtin2KonLFxuICAgICAgICBkZXNjcmlwdGlvbjogJ9iz2YrYqtmFINin2LPYqtiu2K/Yp9mFINin2YTYqNmK2KfZhtin2Kog2KfZhNmF2K3ZgdmI2LjYqSDZhdit2YTZitin2YsnLFxuICAgICAgICB2YXJpYW50OiAnZGVzdHJ1Y3RpdmUnXG4gICAgICB9KVxuXG4gICAgICAvLyBGYWxsYmFjayB0byBjYWNoZWQgZGF0YVxuICAgICAgY29uc3QgY3VycmVudEF1Y3Rpb24gPSBhdWN0aW9ucy5maW5kKGEgPT4gYS5pZCA9PT0gYXVjdGlvbklkKVxuICAgICAgaWYgKGN1cnJlbnRBdWN0aW9uICYmIGNhblVzZXJCaWQoY3VycmVudEF1Y3Rpb24pKSB7XG4gICAgICAgIHNldFNlbGVjdGVkQXVjdGlvbihjdXJyZW50QXVjdGlvbilcbiAgICAgICAgc2V0QmlkQW1vdW50KFN0cmluZygoY3VycmVudEF1Y3Rpb24uY3VycmVudEJpZCB8fCAwKSArIDEwMDApKVxuICAgICAgICBzZXRTaG93QmlkTW9kYWwodHJ1ZSlcbiAgICAgIH1cbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0TG9hZGluZyhmYWxzZSlcbiAgICB9XG4gIH1cblxuICBjb25zdCBzdWJtaXRCaWQgPSBhc3luYyAoKSA9PiB7XG4gICAgaWYgKCFzZWxlY3RlZEF1Y3Rpb24pIHJldHVyblxuXG4gICAgY29uc3QgYmlkQW1vdW50TnVtID0gcGFyc2VGbG9hdChiaWRBbW91bnQpXG4gICAgaWYgKGlzTmFOKGJpZEFtb3VudE51bSkgfHwgYmlkQW1vdW50TnVtIDw9IChzZWxlY3RlZEF1Y3Rpb24uY3VycmVudEJpZCB8fCAwKSkge1xuICAgICAgdG9hc3Qoe1xuICAgICAgICB0aXRsZTogJ9mF2LLYp9mK2K/YqSDYutmK2LEg2LXYp9mE2K3YqScsXG4gICAgICAgIGRlc2NyaXB0aW9uOiAn2YrYrNioINij2YYg2KrZg9mI2YYg2KfZhNmF2LLYp9mK2K/YqSDYo9mD2KjYsSDZhdmGINin2YTZhdiy2KfZitiv2Kkg2KfZhNit2KfZhNmK2KknLFxuICAgICAgICB2YXJpYW50OiAnZGVzdHJ1Y3RpdmUnXG4gICAgICB9KVxuICAgICAgcmV0dXJuXG4gICAgfVxuXG4gICAgdHJ5IHtcbiAgICAgIHNldEJpZExvYWRpbmcodHJ1ZSlcblxuICAgICAgLy8gR2V0IGF1Y3Rpb24gdmVyc2lvbiBmb3Igb3B0aW1pc3RpYyBsb2NraW5nXG4gICAgICBjb25zdCBhdWN0aW9uVmVyc2lvbiA9IGF1Y3Rpb25WZXJzaW9uc1tzZWxlY3RlZEF1Y3Rpb24uaWRdXG5cbiAgICAgIC8vIFByZXBhcmUgYmlkIGRhdGEgd2l0aCBjdXJyZW5jeSBhbmQgdmVyc2lvblxuICAgICAgY29uc3QgYmlkRGF0YSA9IHtcbiAgICAgICAgYmlkQW1vdW50OiBiaWRBbW91bnROdW0sXG4gICAgICAgIGN1cnJlbmN5OiB1c2VyQ3VycmVuY3ksXG4gICAgICAgIHZlcnNpb246IGF1Y3Rpb25WZXJzaW9uXG4gICAgICB9XG5cbiAgICAgIC8vIFN1Ym1pdCBiaWQgYW5kIHdhaXQgZm9yIHNlcnZlciByZXNwb25zZVxuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaChgL2FwaS9hdWN0aW9ucy8ke3NlbGVjdGVkQXVjdGlvbi5pZH0vYmlkYCwge1xuICAgICAgICBtZXRob2Q6ICdQT1NUJyxcbiAgICAgICAgaGVhZGVyczoge1xuICAgICAgICAgICdDb250ZW50LVR5cGUnOiAnYXBwbGljYXRpb24vanNvbicsXG4gICAgICAgICAgJ0F1dGhvcml6YXRpb24nOiBgQmVhcmVyICR7bG9jYWxTdG9yYWdlLmdldEl0ZW0oJ3Rva2VuJyl9YFxuICAgICAgICB9LFxuICAgICAgICBib2R5OiBKU09OLnN0cmluZ2lmeShiaWREYXRhKVxuICAgICAgfSlcblxuICAgICAgY29uc3QgcmVzdWx0ID0gYXdhaXQgcmVzcG9uc2UuanNvbigpXG5cbiAgICAgIGlmICghcmVzcG9uc2Uub2spIHtcbiAgICAgICAgLy8gSGFuZGxlIHNwZWNpZmljIGVycm9yIGNhc2VzXG4gICAgICAgIGlmIChyZXNwb25zZS5zdGF0dXMgPT09IDQwOSkge1xuICAgICAgICAgIC8vIE9wdGltaXN0aWMgbG9ja2luZyBjb25mbGljdFxuICAgICAgICAgIHRvYXN0KHtcbiAgICAgICAgICAgIHRpdGxlOiAn2KrZhSDYqtit2K/ZitirINin2YTZhdiy2KfYryDimqDvuI8nLFxuICAgICAgICAgICAgZGVzY3JpcHRpb246ICfZgtin2YUg2LTYrti1INii2K7YsSDYqNiq2K3Yr9mK2Ksg2KfZhNmF2LLYp9ivLiDYs9mK2KrZhSDYqtit2K/ZitirINin2YTYqNmK2KfZhtin2KouJyxcbiAgICAgICAgICAgIHZhcmlhbnQ6ICdkZXN0cnVjdGl2ZSdcbiAgICAgICAgICB9KVxuICAgICAgICAgIGF3YWl0IGxvYWRBdWN0aW9ucygpXG4gICAgICAgICAgc2V0U2hvd0JpZE1vZGFsKGZhbHNlKVxuICAgICAgICAgIHNldEJpZEFtb3VudCgnJylcbiAgICAgICAgICBzZXRTZWxlY3RlZEF1Y3Rpb24obnVsbClcbiAgICAgICAgICByZXR1cm5cbiAgICAgICAgfVxuICAgICAgICB0aHJvdyBuZXcgRXJyb3IocmVzdWx0Lm1lc3NhZ2UgfHwgJ0JpZCBmYWlsZWQnKVxuICAgICAgfVxuXG4gICAgICAvLyBDbG9zZSBtb2RhbCBmaXJzdFxuICAgICAgc2V0U2hvd0JpZE1vZGFsKGZhbHNlKVxuICAgICAgc2V0QmlkQW1vdW50KCcnKVxuICAgICAgc2V0U2VsZWN0ZWRBdWN0aW9uKG51bGwpXG5cbiAgICAgIC8vIFJlbG9hZCBmcmVzaCBkYXRhIGZyb20gc2VydmVyXG4gICAgICBhd2FpdCBsb2FkQXVjdGlvbnMoKVxuXG4gICAgICAvLyBTaG93IHN1Y2Nlc3MgbWVzc2FnZSB3aXRoIGN1cnJlbmN5IGZvcm1hdHRpbmdcbiAgICAgIGNvbnN0IGZvcm1hdHRlZEFtb3VudCA9IGN1cnJlbmN5U2VydmljZS5mb3JtYXRBbW91bnQoYmlkQW1vdW50TnVtLCB1c2VyQ3VycmVuY3kpXG4gICAgICB0b2FzdCh7XG4gICAgICAgIHRpdGxlOiAn2KrZhSDYqtmC2K/ZitmFINin2YTZhdiy2KfZitiv2Kkg2KjZhtis2KfYrSEg8J+OiScsXG4gICAgICAgIGRlc2NyaXB0aW9uOiBg2YXZgtiv2KfYsSDYp9mE2YXYstin2YrYr9ipOiAke2Zvcm1hdHRlZEFtb3VudH1gXG4gICAgICB9KVxuXG4gICAgfSBjYXRjaCAoZXJyb3I6IGFueSkge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgcGxhY2luZyBiaWQ6JywgZXJyb3IpXG5cbiAgICAgIC8vIENsb3NlIG1vZGFsIG9uIGVycm9yIHRvb1xuICAgICAgc2V0U2hvd0JpZE1vZGFsKGZhbHNlKVxuICAgICAgc2V0QmlkQW1vdW50KCcnKVxuICAgICAgc2V0U2VsZWN0ZWRBdWN0aW9uKG51bGwpXG5cbiAgICAgIC8vIFNob3cgc3BlY2lmaWMgZXJyb3IgbWVzc2FnZVxuICAgICAgY29uc3QgZXJyb3JNZXNzYWdlID0gZXJyb3IucmVzcG9uc2U/LmRhdGE/Lm1lc3NhZ2UgfHwgJ9mK2LHYrNmJINin2YTZhdit2KfZiNmE2Kkg2YXYsdipINij2K7YsdmJJ1xuXG4gICAgICBpZiAoZXJyb3JNZXNzYWdlLmluY2x1ZGVzKCdoaWdoZXIgdGhhbiBjdXJyZW50IHByaWNlJykgfHwgZXJyb3JNZXNzYWdlLmluY2x1ZGVzKCfYo9mD2KjYsSDZhdmGINin2YTYs9i52LEg2KfZhNit2KfZhNmKJykpIHtcbiAgICAgICAgdG9hc3Qoe1xuICAgICAgICAgIHRpdGxlOiAn2YXYstin2YrYr9ipINmF2YbYrtmB2LbYqSDimqDvuI8nLFxuICAgICAgICAgIGRlc2NyaXB0aW9uOiAn2YLYp9mFINi02K7YtSDYotiu2LEg2KjZhdiy2KfZitiv2Kkg2KPYudmE2YkuINmK2LHYrNmJINin2YTZhdit2KfZiNmE2Kkg2KjZhdio2YTYuiDYo9mD2KjYsS4nLFxuICAgICAgICAgIHZhcmlhbnQ6ICdkZXN0cnVjdGl2ZSdcbiAgICAgICAgfSlcbiAgICAgIH0gZWxzZSBpZiAoZXJyb3JNZXNzYWdlLmluY2x1ZGVzKCdlbmRlZCcpIHx8IGVycm9yTWVzc2FnZS5pbmNsdWRlcygn2KfZhtiq2YfZiScpKSB7XG4gICAgICAgIHRvYXN0KHtcbiAgICAgICAgICB0aXRsZTogJ9in2YbYqtmH2Ykg2KfZhNmF2LLYp9ivIOKPsCcsXG4gICAgICAgICAgZGVzY3JpcHRpb246ICfZhNmC2K8g2KfZhtiq2YfZiSDZh9iw2Kcg2KfZhNmF2LLYp9ivINmI2YTYpyDZitmF2YPZhiDZgtio2YjZhCDZhdiy2KfZitiv2KfYqiDYrNiv2YrYr9ipLicsXG4gICAgICAgICAgdmFyaWFudDogJ2Rlc3RydWN0aXZlJ1xuICAgICAgICB9KVxuICAgICAgfSBlbHNlIHtcbiAgICAgICAgdG9hc3Qoe1xuICAgICAgICAgIHRpdGxlOiAn2YHYtNmEINmB2Yog2KrZgtiv2YrZhSDYp9mE2YXYstin2YrYr9ipJyxcbiAgICAgICAgICBkZXNjcmlwdGlvbjogZXJyb3JNZXNzYWdlLFxuICAgICAgICAgIHZhcmlhbnQ6ICdkZXN0cnVjdGl2ZSdcbiAgICAgICAgfSlcbiAgICAgIH1cblxuICAgICAgLy8gQWx3YXlzIHJlbG9hZCBkYXRhIG9uIGVycm9yIHRvIHN5bmMgd2l0aCBzZXJ2ZXJcbiAgICAgIGF3YWl0IGxvYWRBdWN0aW9ucygpXG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHNldEJpZExvYWRpbmcoZmFsc2UpXG4gICAgfVxuICB9XG5cbiAgY29uc3QgaGFuZGxlU2F2ZUF1Y3Rpb24gPSBhc3luYyAoYXVjdGlvbklkOiBzdHJpbmcpID0+IHtcbiAgICBjb25zb2xlLmxvZygn4p2k77iPIFNhdmUgYnV0dG9uIGNsaWNrZWQgZm9yIGF1Y3Rpb246JywgYXVjdGlvbklkKVxuICAgIFxuICAgIC8vIFNob3cgaW1tZWRpYXRlIGZlZWRiYWNrIHRoYXQgYnV0dG9uIHdhcyBjbGlja2VkXG4gICAgdG9hc3Qoe1xuICAgICAgdGl0bGU6ICfwn5KGINiq2YUg2KfZhNi22LrYtyDYudmE2Ykg2LLYsSDYp9mE2K3Zgdi4JyxcbiAgICAgIGRlc2NyaXB0aW9uOiAn2KzYp9ix2Yog2YXYudin2YTYrNipINin2YTYt9mE2KguLi4nXG4gICAgfSlcbiAgICBcbiAgICB0cnkge1xuICAgICAgY29uc3QgYXVjdGlvbiA9IGF1Y3Rpb25zLmZpbmQoYSA9PiBhLmlkID09PSBhdWN0aW9uSWQpXG4gICAgICBpZiAoIWF1Y3Rpb24pIHtcbiAgICAgICAgdG9hc3Qoe1xuICAgICAgICAgIHRpdGxlOiAn2K7Yt9ijJyxcbiAgICAgICAgICBkZXNjcmlwdGlvbjogJ9mE2YUg2YrYqtmFINin2YTYudir2YjYsSDYudmE2Ykg2KfZhNmF2LLYp9ivJyxcbiAgICAgICAgICB2YXJpYW50OiAnZGVzdHJ1Y3RpdmUnXG4gICAgICAgIH0pXG4gICAgICAgIHJldHVyblxuICAgICAgfVxuICAgICAgXG4gICAgICBjb25zdCBpc0N1cnJlbnRseVNhdmVkID0gc2F2ZWRBdWN0aW9ucy5oYXMoYXVjdGlvbklkKVxuICAgICAgXG4gICAgICAvLyBPcHRpbWlzdGljIFVJIHVwZGF0ZVxuICAgICAgc2V0U2F2ZWRBdWN0aW9ucyhwcmV2ID0+IHtcbiAgICAgICAgY29uc3QgbmV3U2V0ID0gbmV3IFNldChwcmV2KVxuICAgICAgICBpZiAoaXNDdXJyZW50bHlTYXZlZCkge1xuICAgICAgICAgIG5ld1NldC5kZWxldGUoYXVjdGlvbklkKVxuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgIG5ld1NldC5hZGQoYXVjdGlvbklkKVxuICAgICAgICB9XG4gICAgICAgIHJldHVybiBuZXdTZXRcbiAgICAgIH0pXG4gICAgICBcbiAgICAgIGlmIChpc0N1cnJlbnRseVNhdmVkKSB7XG4gICAgICAgIC8vIFJlbW92ZSBmcm9tIGZhdm9yaXRlc1xuICAgICAgICBhd2FpdCBmYXZvcml0ZXNBUEkucmVtb3ZlRmF2b3JpdGUoJ2F1Y3Rpb24nLCBhdWN0aW9uSWQpXG4gICAgICAgIHRvYXN0KHtcbiAgICAgICAgICB0aXRsZTogJ+KdpO+4jyDYqtmFINil2LLYp9mE2Kkg2YXZhiDYp9mE2YXZgdi22YTYqScsXG4gICAgICAgICAgZGVzY3JpcHRpb246IGDYqtmFINil2LLYp9mE2KkgXCIke2F1Y3Rpb24udGl0bGV9XCIg2YXZhiDYp9mE2YXZgdi22YTYqWBcbiAgICAgICAgfSlcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIC8vIEFkZCB0byBmYXZvcml0ZXNcbiAgICAgICAgYXdhaXQgZmF2b3JpdGVzQVBJLmFkZEZhdm9yaXRlKHtcbiAgICAgICAgICBpdGVtVHlwZTogJ2F1Y3Rpb24nLFxuICAgICAgICAgIGl0ZW1JZDogYXVjdGlvbklkLFxuICAgICAgICAgIG5vdGlmaWNhdGlvbnM6IHtcbiAgICAgICAgICAgIGJpZFVwZGF0ZXM6IHRydWUsXG4gICAgICAgICAgICBzdGF0dXNDaGFuZ2VzOiB0cnVlLFxuICAgICAgICAgICAgZW5kaW5nU29vbjogdHJ1ZVxuICAgICAgICAgIH1cbiAgICAgICAgfSlcbiAgICAgICAgdG9hc3Qoe1xuICAgICAgICAgIHRpdGxlOiAn8J+SliDYqtmFINil2LbYp9mB2Kkg2KXZhNmJINin2YTZhdmB2LbZhNipJyxcbiAgICAgICAgICBkZXNjcmlwdGlvbjogYNiq2YUg2KXYttin2YHYqSBcIiR7YXVjdGlvbi50aXRsZX1cIiDYpdmE2Ykg2KfZhNmF2YHYttmE2KlgXG4gICAgICAgIH0pXG4gICAgICB9XG4gICAgfSBjYXRjaCAoZXJyb3I6IGFueSkge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3Igc2F2aW5nIGF1Y3Rpb246JywgZXJyb3IpXG4gICAgICAvLyBSZXZlcnQgb3B0aW1pc3RpYyB1cGRhdGUgb24gZXJyb3JcbiAgICAgIHNldFNhdmVkQXVjdGlvbnMocHJldiA9PiB7XG4gICAgICAgIGNvbnN0IG5ld1NldCA9IG5ldyBTZXQocHJldilcbiAgICAgICAgaWYgKHNhdmVkQXVjdGlvbnMuaGFzKGF1Y3Rpb25JZCkpIHtcbiAgICAgICAgICBuZXdTZXQuYWRkKGF1Y3Rpb25JZClcbiAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICBuZXdTZXQuZGVsZXRlKGF1Y3Rpb25JZClcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gbmV3U2V0XG4gICAgICB9KVxuICAgICAgdG9hc3Qoe1xuICAgICAgICB0aXRsZTogJ9mB2LTZhCDZgdmKINit2YHYuCDYp9mE2YXYstin2K8nLFxuICAgICAgICBkZXNjcmlwdGlvbjogZXJyb3IucmVzcG9uc2U/LmRhdGE/Lm1lc3NhZ2UgfHwgJ9mK2LHYrNmJINin2YTZhdit2KfZiNmE2Kkg2YXYsdipINij2K7YsdmJJyxcbiAgICAgICAgdmFyaWFudDogJ2Rlc3RydWN0aXZlJ1xuICAgICAgfSlcbiAgICB9XG4gIH1cblxuICBjb25zdCBoYW5kbGVWaWV3QXVjdGlvbiA9IGFzeW5jIChhdWN0aW9uSWQ6IHN0cmluZykgPT4ge1xuICAgIGNvbnNvbGUubG9nKCfwn5GB77iPIFZpZXcgYnV0dG9uIGNsaWNrZWQgZm9yIGF1Y3Rpb246JywgYXVjdGlvbklkKVxuXG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IGF1Y3Rpb24gPSBhdWN0aW9ucy5maW5kKGEgPT4gYS5pZCA9PT0gYXVjdGlvbklkKVxuICAgICAgaWYgKCFhdWN0aW9uKSB7XG4gICAgICAgIHRvYXN0KHtcbiAgICAgICAgICB0aXRsZTogJ9iu2LfYoycsXG4gICAgICAgICAgZGVzY3JpcHRpb246ICfZhNmFINmK2KrZhSDYp9mE2LnYq9mI2LEg2LnZhNmJINin2YTZhdiy2KfYrycsXG4gICAgICAgICAgdmFyaWFudDogJ2Rlc3RydWN0aXZlJ1xuICAgICAgICB9KVxuICAgICAgICByZXR1cm5cbiAgICAgIH1cblxuICAgICAgLy8gTmF2aWdhdGUgdG8gYXVjdGlvbiBkZXRhaWwgcGFnZVxuICAgICAgcm91dGVyLnB1c2goYC9hdWN0aW9ucy8ke2F1Y3Rpb25JZH1gKVxuXG4gICAgICB0b2FzdCh7XG4gICAgICAgIHRpdGxlOiAn8J+Rge+4jyDYrNin2LHZiiDYqtit2YXZitmEINin2YTYqtmB2KfYtdmK2YQnLFxuICAgICAgICBkZXNjcmlwdGlvbjogJ9iz2YrYqtmFINi52LHYtiDYqtmB2KfYtdmK2YQg2KfZhNmF2LLYp9ivJ1xuICAgICAgfSlcbiAgICB9IGNhdGNoIChlcnJvcjogYW55KSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciB2aWV3aW5nIGF1Y3Rpb246JywgZXJyb3IpXG4gICAgICB0b2FzdCh7XG4gICAgICAgIHRpdGxlOiAn2YHYtNmEINmB2Yog2LnYsdi2INiq2YHYp9i12YrZhCDYp9mE2YXYstin2K8nLFxuICAgICAgICBkZXNjcmlwdGlvbjogZXJyb3IucmVzcG9uc2U/LmRhdGE/Lm1lc3NhZ2UgfHwgJ9mK2LHYrNmJINin2YTZhdit2KfZiNmE2Kkg2YXYsdipINij2K7YsdmJJyxcbiAgICAgICAgdmFyaWFudDogJ2Rlc3RydWN0aXZlJ1xuICAgICAgfSlcbiAgICB9XG4gIH1cblxuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS02XCI+XG4gICAgICAgIDxoZWFkZXI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW5cIj5cbiAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgIDxoMSBjbGFzc05hbWU9XCJ0ZXh0LTN4bCBmb250LWJvbGRcIj7Yp9mE2YXYstin2K/Yp9iqINin2YTZhdiq2KfYrdipPC9oMT5cbiAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1tdXRlZC1mb3JlZ3JvdW5kXCI+XG4gICAgICAgICAgICAgICAg2KfYs9iq2YPYtNmBINin2YTZhdiy2KfYr9in2Kog2KfZhNit2KfZhNmK2Kkg2YjYtNin2LHZgyDZgdmKINin2YTZhdiy2KfZitiv2KlcbiAgICAgICAgICAgICAgICB7YXV0b1JlZnJlc2ggJiYgKFxuICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LWdyZWVuLTYwMCBtbC0yXCI+XG4gICAgICAgICAgICAgICAgICAgIOKAoiDYqtit2K/ZitirINiq2YTZgtin2KbZiiDZg9mEIDMwINir2KfZhtmK2KlcbiAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTJcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMlwiPlxuICAgICAgICAgICAgICAgIDxMYWJlbCBodG1sRm9yPVwiY3VycmVuY3lcIiBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtXCI+2KfZhNi52YXZhNipOjwvTGFiZWw+XG4gICAgICAgICAgICAgICAgPHNlbGVjdFxuICAgICAgICAgICAgICAgICAgaWQ9XCJjdXJyZW5jeVwiXG4gICAgICAgICAgICAgICAgICB2YWx1ZT17dXNlckN1cnJlbmN5fVxuICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9e2FzeW5jIChlKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IG5ld0N1cnJlbmN5ID0gZS50YXJnZXQudmFsdWVcbiAgICAgICAgICAgICAgICAgICAgc2V0VXNlckN1cnJlbmN5KG5ld0N1cnJlbmN5KVxuICAgICAgICAgICAgICAgICAgICBjdXJyZW5jeVNlcnZpY2Uuc2V0VXNlckN1cnJlbmN5KG5ld0N1cnJlbmN5KVxuICAgICAgICAgICAgICAgICAgICBzZXRMb2FkaW5nKHRydWUpXG4gICAgICAgICAgICAgICAgICAgIGF3YWl0IGxvYWRBdWN0aW9ucygpXG4gICAgICAgICAgICAgICAgICAgIHNldExvYWRpbmcoZmFsc2UpXG4gICAgICAgICAgICAgICAgICAgIHRvYXN0KHtcbiAgICAgICAgICAgICAgICAgICAgICB0aXRsZTogJ/CfkrEg2KrZhSDYqti62YrZitixINin2YTYudmF2YTYqScsXG4gICAgICAgICAgICAgICAgICAgICAgZGVzY3JpcHRpb246IGDYqtmFINin2YTYqtit2YjZitmEINil2YTZiSAke2N1cnJlbmN5U2VydmljZS5nZXRDdXJyZW5jeVN5bWJvbChuZXdDdXJyZW5jeSl9YFxuICAgICAgICAgICAgICAgICAgICB9KVxuICAgICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInB4LTIgcHktMSBib3JkZXIgYm9yZGVyLWdyYXktMzAwIHJvdW5kZWQgdGV4dC1zbSBmb2N1czpvdXRsaW5lLW5vbmUgZm9jdXM6cmluZy0yIGZvY3VzOnJpbmctYmx1ZS01MDBcIlxuICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgIHtjdXJyZW5jeVNlcnZpY2UuZ2V0U3VwcG9ydGVkQ3VycmVuY2llcygpLm1hcChjdXJyZW5jeSA9PiAoXG4gICAgICAgICAgICAgICAgICAgIDxvcHRpb24ga2V5PXtjdXJyZW5jeS5jb2RlfSB2YWx1ZT17Y3VycmVuY3kuY29kZX0+XG4gICAgICAgICAgICAgICAgICAgICAge2N1cnJlbmN5LnN5bWJvbH0ge2N1cnJlbmN5LmNvZGV9XG4gICAgICAgICAgICAgICAgICAgIDwvb3B0aW9uPlxuICAgICAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICAgICAgPC9zZWxlY3Q+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICAgICAgb25DbGljaz17YXN5bmMgKCkgPT4ge1xuICAgICAgICAgICAgICAgICAgYXdhaXQgbG9hZEF1Y3Rpb25zKHRydWUpIC8vIFNob3cgbG9hZGluZyBzdGF0ZVxuICAgICAgICAgICAgICAgICAgc2V0TGFzdFJlZnJlc2gobmV3IERhdGUoKSlcbiAgICAgICAgICAgICAgICAgIHRvYXN0KHtcbiAgICAgICAgICAgICAgICAgICAgdGl0bGU6ICfwn5SEINiq2YUg2KrYrdiv2YrYqyDYp9mE2KjZitin2YbYp9iqJyxcbiAgICAgICAgICAgICAgICAgICAgZGVzY3JpcHRpb246IGDYqtmFINin2YTYudir2YjYsSDYudmE2YkgJHtmaWx0ZXJlZEF1Y3Rpb25zLmxlbmd0aH0g2YXYstin2K8g2YXYqtin2K1gXG4gICAgICAgICAgICAgICAgICB9KVxuICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgICAgdmFyaWFudD1cIm91dGxpbmVcIlxuICAgICAgICAgICAgICAgIGRpc2FibGVkPXtsb2FkaW5nfVxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgPFJlZnJlc2hDdyBjbGFzc05hbWU9e2BoLTQgdy00IG1sLTIgJHtsb2FkaW5nID8gJ2FuaW1hdGUtc3BpbicgOiAnJ31gfSAvPlxuICAgICAgICAgICAgICAgINiq2K3Yr9mK2KtcbiAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiB7XG4gICAgICAgICAgICAgICAgICBzZXRBdXRvUmVmcmVzaCghYXV0b1JlZnJlc2gpXG4gICAgICAgICAgICAgICAgICB0b2FzdCh7XG4gICAgICAgICAgICAgICAgICAgIHRpdGxlOiBhdXRvUmVmcmVzaCA/ICfij7jvuI8g2KrZhSDYpdmK2YLYp9mBINin2YTYqtit2K/ZitirINin2YTYqtmE2YLYp9im2YonIDogJ+KWtu+4jyDYqtmFINiq2LTYutmK2YQg2KfZhNiq2K3Yr9mK2Ksg2KfZhNiq2YTZgtin2KbZiicsXG4gICAgICAgICAgICAgICAgICAgIGRlc2NyaXB0aW9uOiBhdXRvUmVmcmVzaCA/ICfZitmF2YPZhtmDINin2YTYqtit2K/ZitirINmK2K/ZiNmK2KfZiycgOiAn2LPZitiq2YUg2KfZhNiq2K3Yr9mK2Ksg2YPZhCAzMCDYq9in2YbZitipJ1xuICAgICAgICAgICAgICAgICAgfSlcbiAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgIHZhcmlhbnQ9e2F1dG9SZWZyZXNoID8gXCJkZWZhdWx0XCIgOiBcIm91dGxpbmVcIn1cbiAgICAgICAgICAgICAgICBzaXplPVwiaWNvblwiXG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICB7YXV0b1JlZnJlc2ggPyA8UGF1c2UgY2xhc3NOYW1lPVwiaC00IHctNFwiIC8+IDogPFBsYXkgY2xhc3NOYW1lPVwiaC00IHctNFwiIC8+fVxuICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2hlYWRlcj5cblxuICAgICAgICB7LyogU2VhcmNoIGFuZCBGaWx0ZXJzICovfVxuICAgICAgICA8Q2FyZD5cbiAgICAgICAgICA8Q2FyZENvbnRlbnQgY2xhc3NOYW1lPVwicHQtNlwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTRcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJyZWxhdGl2ZVwiPlxuICAgICAgICAgICAgICAgIDxTZWFyY2ggY2xhc3NOYW1lPVwiYWJzb2x1dGUgcmlnaHQtMyB0b3AtMyBoLTQgdy00IHRleHQtbXV0ZWQtZm9yZWdyb3VuZFwiIC8+XG4gICAgICAgICAgICAgICAgPElucHV0XG4gICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cItin2KjYrdirINmB2Yog2KfZhNmF2LLYp9iv2KfYqi4uLlwiXG4gICAgICAgICAgICAgICAgICB2YWx1ZT17c2VhcmNoVGVybX1cbiAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0U2VhcmNoVGVybShlLnRhcmdldC52YWx1ZSl9XG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJwci0xMFwiXG4gICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGZsZXgtY29sIHNtOmZsZXgtcm93IGdhcC00XCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTFcIj5cbiAgICAgICAgICAgICAgICAgIDxMYWJlbCBodG1sRm9yPVwiY2F0ZWdvcnlcIiBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtXCI+2KfZhNmB2KbYqTwvTGFiZWw+XG4gICAgICAgICAgICAgICAgICA8c2VsZWN0XG4gICAgICAgICAgICAgICAgICAgIGlkPVwiY2F0ZWdvcnlcIlxuICAgICAgICAgICAgICAgICAgICB2YWx1ZT17ZmlsdGVyQ2F0ZWdvcnl9XG4gICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0RmlsdGVyQ2F0ZWdvcnkoZS50YXJnZXQudmFsdWUpfVxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgbXQtMSBweC0zIHB5LTIgYm9yZGVyIGJvcmRlci1ncmF5LTMwMCByb3VuZGVkLW1kIGZvY3VzOm91dGxpbmUtbm9uZSBmb2N1czpyaW5nLTIgZm9jdXM6cmluZy1ibHVlLTUwMFwiXG4gICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJcIj7YrNmF2YrYuSDYp9mE2YHYptin2Ko8L29wdGlvbj5cbiAgICAgICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cImVsZWN0cm9uaWNzXCI+2KXZhNmD2KrYsdmI2YbZitin2Ko8L29wdGlvbj5cbiAgICAgICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cInZlaGljbGVzXCI+2LPZitin2LHYp9iqPC9vcHRpb24+XG4gICAgICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJyZWFsX2VzdGF0ZVwiPti52YLYp9ix2KfYqjwvb3B0aW9uPlxuICAgICAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwiYXJ0X2NvbGxlY3RpYmxlc1wiPtmB2YbZiNmGINmI2YXZgtiq2YbZitin2Ko8L29wdGlvbj5cbiAgICAgICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cIm1hY2hpbmVyeVwiPtmF2LnYr9in2Ko8L29wdGlvbj5cbiAgICAgICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cImZ1cm5pdHVyZVwiPtij2KvYp9irPC9vcHRpb24+XG4gICAgICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJqZXdlbHJ5XCI+2YXYrNmI2YfYsdin2Ko8L29wdGlvbj5cbiAgICAgICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cImJvb2tzX21lZGlhXCI+2YPYqtioINmI2YjYs9in2KbYtzwvb3B0aW9uPlxuICAgICAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwiY2xvdGhpbmdcIj7ZhdmE2KfYqNizPC9vcHRpb24+XG4gICAgICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJzcG9ydHNcIj7YsdmK2KfYttipPC9vcHRpb24+XG4gICAgICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJvdGhlclwiPtij2K7YsdmJPC9vcHRpb24+XG4gICAgICAgICAgICAgICAgICA8L3NlbGVjdD5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC0xXCI+XG4gICAgICAgICAgICAgICAgICA8TGFiZWwgaHRtbEZvcj1cInNvcnRcIiBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtXCI+2KrYsdiq2YrYqCDYrdiz2Kg8L0xhYmVsPlxuICAgICAgICAgICAgICAgICAgPHNlbGVjdFxuICAgICAgICAgICAgICAgICAgICBpZD1cInNvcnRcIlxuICAgICAgICAgICAgICAgICAgICB2YWx1ZT17c29ydEJ5fVxuICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldFNvcnRCeShlLnRhcmdldC52YWx1ZSl9XG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBtdC0xIHB4LTMgcHktMiBib3JkZXIgYm9yZGVyLWdyYXktMzAwIHJvdW5kZWQtbWQgZm9jdXM6b3V0bGluZS1ub25lIGZvY3VzOnJpbmctMiBmb2N1czpyaW5nLWJsdWUtNTAwXCJcbiAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cImVuZERhdGVcIj7Yqtin2LHZitiuINin2YTYp9mG2KrZh9in2KE8L29wdGlvbj5cbiAgICAgICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cImN1cnJlbnRCaWRcIj7Yo9i52YTZiSDZhdiy2KfZitiv2Kk8L29wdGlvbj5cbiAgICAgICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cImJpZHNDb3VudFwiPti52K/YryDYp9mE2YXYstin2YrYr9in2Ko8L29wdGlvbj5cbiAgICAgICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cInRpdGxlXCI+2KfZhNin2LPZhTwvb3B0aW9uPlxuICAgICAgICAgICAgICAgICAgPC9zZWxlY3Q+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9DYXJkQ29udGVudD5cbiAgICAgICAgPC9DYXJkPlxuXG4gICAgICAgIHsvKiBTdGF0cyAqL31cbiAgICAgICAge0FycmF5LmlzQXJyYXkoZmlsdGVyZWRBdWN0aW9ucykgJiYgZmlsdGVyZWRBdWN0aW9ucy5sZW5ndGggPiAwICYmIChcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgbWQ6Z3JpZC1jb2xzLTMgZ2FwLTZcIj5cbiAgICAgICAgICAgIDxDYXJkPlxuICAgICAgICAgICAgICA8Q2FyZEhlYWRlcj5cbiAgICAgICAgICAgICAgICA8Q2FyZFRpdGxlIGNsYXNzTmFtZT1cInRleHQtbGdcIj7Ypdis2YXYp9mE2Yog2KfZhNmF2LLYp9iv2KfYqjwvQ2FyZFRpdGxlPlxuICAgICAgICAgICAgICAgIDxDYXJkRGVzY3JpcHRpb24gY2xhc3NOYW1lPVwidGV4dC0yeGwgZm9udC1ib2xkIHRleHQtYmx1ZS02MDBcIj5cbiAgICAgICAgICAgICAgICAgIHtmaWx0ZXJlZEF1Y3Rpb25zLmxlbmd0aH1cbiAgICAgICAgICAgICAgICA8L0NhcmREZXNjcmlwdGlvbj5cbiAgICAgICAgICAgICAgPC9DYXJkSGVhZGVyPlxuICAgICAgICAgICAgPC9DYXJkPlxuICAgICAgICAgICAgPENhcmQ+XG4gICAgICAgICAgICAgIDxDYXJkSGVhZGVyPlxuICAgICAgICAgICAgICAgIDxDYXJkVGl0bGUgY2xhc3NOYW1lPVwidGV4dC1sZ1wiPtin2YTZhdiy2KfZitiv2KfYqiDYp9mE2YbYtNi32Kk8L0NhcmRUaXRsZT5cbiAgICAgICAgICAgICAgICA8Q2FyZERlc2NyaXB0aW9uIGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZCB0ZXh0LWdyZWVuLTYwMFwiPlxuICAgICAgICAgICAgICAgICAge2ZpbHRlcmVkQXVjdGlvbnMucmVkdWNlKChzdW0sIGEpID0+IHN1bSArIChhLmJpZHNDb3VudCB8fCAwKSwgMCl9XG4gICAgICAgICAgICAgICAgPC9DYXJkRGVzY3JpcHRpb24+XG4gICAgICAgICAgICAgIDwvQ2FyZEhlYWRlcj5cbiAgICAgICAgICAgIDwvQ2FyZD5cbiAgICAgICAgICAgIDxDYXJkPlxuICAgICAgICAgICAgICA8Q2FyZEhlYWRlcj5cbiAgICAgICAgICAgICAgICA8Q2FyZFRpdGxlIGNsYXNzTmFtZT1cInRleHQtbGdcIj7Yo9i52YTZiSDZhdiy2KfZitiv2Kk8L0NhcmRUaXRsZT5cbiAgICAgICAgICAgICAgICA8Q2FyZERlc2NyaXB0aW9uIGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZCB0ZXh0LXB1cnBsZS02MDBcIj5cbiAgICAgICAgICAgICAgICAgIHtmaWx0ZXJlZEF1Y3Rpb25zLmxlbmd0aCA+IDBcbiAgICAgICAgICAgICAgICAgICAgPyBjdXJyZW5jeVNlcnZpY2UuZm9ybWF0QW1vdW50KFxuICAgICAgICAgICAgICAgICAgICAgICAgTWF0aC5tYXgoLi4uZmlsdGVyZWRBdWN0aW9ucy5tYXAoYSA9PiBhLmN1cnJlbnRCaWQgfHwgMCkpLFxuICAgICAgICAgICAgICAgICAgICAgICAgdXNlckN1cnJlbmN5XG4gICAgICAgICAgICAgICAgICAgICAgKVxuICAgICAgICAgICAgICAgICAgICA6IGN1cnJlbmN5U2VydmljZS5mb3JtYXRBbW91bnQoMCwgdXNlckN1cnJlbmN5KVxuICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIDwvQ2FyZERlc2NyaXB0aW9uPlxuICAgICAgICAgICAgICA8L0NhcmRIZWFkZXI+XG4gICAgICAgICAgICA8L0NhcmQ+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICl9XG5cbiAgICAgICAgey8qIEF1Y3Rpb25zIEdyaWQgKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBtZDpncmlkLWNvbHMtMiBsZzpncmlkLWNvbHMtMyBnYXAtNlwiPlxuICAgICAgICAgIHtBcnJheS5pc0FycmF5KGZpbHRlcmVkQXVjdGlvbnMpICYmIGZpbHRlcmVkQXVjdGlvbnMubWFwKChhdWN0aW9uKSA9PiAoXG4gICAgICAgICAgICA8Q2FyZCBrZXk9e2F1Y3Rpb24uaWR9IGNsYXNzTmFtZT1cImhvdmVyOnNoYWRvdy1sZyB0cmFuc2l0aW9uLXNoYWRvdyBjdXJzb3ItcG9pbnRlclwiIG9uQ2xpY2s9eygpID0+IGhhbmRsZVZpZXdBdWN0aW9uKGF1Y3Rpb24uaWQpfT5cbiAgICAgICAgICAgICAgPENhcmRIZWFkZXI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhc3BlY3QtdmlkZW8gYmctZ3JhZGllbnQtdG8tYnIgZnJvbS1ncmF5LTEwMCB0by1ncmF5LTIwMCByb3VuZGVkLWxnIG1iLTQgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgcmVsYXRpdmUgb3ZlcmZsb3ctaGlkZGVuXCI+XG4gICAgICAgICAgICAgICAgICB7YXVjdGlvbi5pbWFnZXMgJiYgYXVjdGlvbi5pbWFnZXMubGVuZ3RoID4gMCA/IChcbiAgICAgICAgICAgICAgICAgICAgPGltZ1xuICAgICAgICAgICAgICAgICAgICAgIHNyYz17YXVjdGlvbi5pbWFnZXNbMF0udXJsfVxuICAgICAgICAgICAgICAgICAgICAgIGFsdD17YXVjdGlvbi50aXRsZX1cbiAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgaC1mdWxsIG9iamVjdC1jb3ZlclwiXG4gICAgICAgICAgICAgICAgICAgICAgb25FcnJvcj17KGUpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGUuY3VycmVudFRhcmdldC5zdHlsZS5kaXNwbGF5ID0gJ25vbmUnXG4gICAgICAgICAgICAgICAgICAgICAgICBlLmN1cnJlbnRUYXJnZXQubmV4dEVsZW1lbnRTaWJsaW5nLnN0eWxlLmRpc3BsYXkgPSAnZmxleCdcbiAgICAgICAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgKSA6IG51bGx9XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIHctZnVsbCBoLWZ1bGwgdGV4dC1ncmF5LTUwMFwiIHN0eWxlPXt7ZGlzcGxheTogYXVjdGlvbi5pbWFnZXMgJiYgYXVjdGlvbi5pbWFnZXMubGVuZ3RoID4gMCA/ICdub25lJyA6ICdmbGV4J319PlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPEdhdmVsIGNsYXNzTmFtZT1cImgtOCB3LTggbXgtYXV0byBtYi0yIHRleHQtZ3JheS00MDBcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtc21cIj7YtdmI2LHYqSDYp9mE2YXYstin2K88L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICB7YXVjdGlvbi5zdGF0dXMgPT09ICdhY3RpdmUnICYmIChcbiAgICAgICAgICAgICAgICAgICAgPEJhZGdlIGNsYXNzTmFtZT1cImFic29sdXRlIHRvcC0yIHJpZ2h0LTIgYmctZ3JlZW4tNTAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAg2YbYtNi3XG4gICAgICAgICAgICAgICAgICAgIDwvQmFkZ2U+XG4gICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDxDYXJkVGl0bGUgY2xhc3NOYW1lPVwidGV4dC1sZyBsaW5lLWNsYW1wLTJcIj57YXVjdGlvbi50aXRsZX08L0NhcmRUaXRsZT5cbiAgICAgICAgICAgICAgICA8Q2FyZERlc2NyaXB0aW9uIGNsYXNzTmFtZT1cImxpbmUtY2xhbXAtMlwiPlxuICAgICAgICAgICAgICAgICAge2F1Y3Rpb24uZGVzY3JpcHRpb259XG4gICAgICAgICAgICAgICAgPC9DYXJkRGVzY3JpcHRpb24+XG4gICAgICAgICAgICAgIDwvQ2FyZEhlYWRlcj5cbiAgICAgICAgICAgICAgPENhcmRDb250ZW50IGNsYXNzTmFtZT1cInNwYWNlLXktNFwiPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuXCI+XG4gICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtbXV0ZWQtZm9yZWdyb3VuZFwiPtij2LnZhNmJINmF2LLYp9mK2K/YqTwvcD5cbiAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC14bCBmb250LWJvbGQgdGV4dC1ncmVlbi02MDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICB7Y3VycmVuY3lTZXJ2aWNlLmZvcm1hdEFtb3VudChhdWN0aW9uLmN1cnJlbnRCaWQgfHwgMCwgdXNlckN1cnJlbmN5KX1cbiAgICAgICAgICAgICAgICAgICAgICB7YXVjdGlvbi5vcmlnaW5hbEN1cnJlbmN5ICYmIGF1Y3Rpb24ub3JpZ2luYWxDdXJyZW5jeSAhPT0gdXNlckN1cnJlbmN5ICYmIChcbiAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1ncmF5LTUwMCBtbC0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICjZhdmGIHthdWN0aW9uLm9yaWdpbmFsQ3VycmVuY3l9KVxuICAgICAgICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtbXV0ZWQtZm9yZWdyb3VuZFwiPtin2YTZhdiy2KfZitiv2KfYqjwvcD5cbiAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LXNlbWlib2xkXCI+e2F1Y3Rpb24uYmlkc0NvdW50IHx8IDB9PC9wPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0yXCI+XG4gICAgICAgICAgICAgICAgICA8Q2xvY2sgY2xhc3NOYW1lPVwiaC00IHctNCB0ZXh0LW11dGVkLWZvcmVncm91bmRcIiAvPlxuICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LW11dGVkLWZvcmVncm91bmRcIj5cbiAgICAgICAgICAgICAgICAgICAg2YrZhtiq2YfZiiDYrtmE2KfZhDoge2dldFRpbWVSZW1haW5pbmcoYXVjdGlvbi5lbmREYXRlKX1cbiAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LW11dGVkLWZvcmVncm91bmRcIj5cbiAgICAgICAgICAgICAgICAgINin2YTYqNin2KbYuToge2F1Y3Rpb24uc2VsbGVyfVxuICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGdhcC0yXCI+XG4gICAgICAgICAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eyhlKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgICAgZS5wcmV2ZW50RGVmYXVsdCgpXG4gICAgICAgICAgICAgICAgICAgICAgZS5zdG9wUHJvcGFnYXRpb24oKVxuICAgICAgICAgICAgICAgICAgICAgIGhhbmRsZUJpZChhdWN0aW9uLmlkKVxuICAgICAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJmbGV4LTFcIlxuICAgICAgICAgICAgICAgICAgICBkaXNhYmxlZD17bG9hZGluZyB8fCBiaWRMb2FkaW5nfVxuICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICA8VHJlbmRpbmdVcCBjbGFzc05hbWU9XCJoLTQgdy00IG1sLTJcIiAvPlxuICAgICAgICAgICAgICAgICAgICB7YmlkTG9hZGluZyA/ICfYrNin2LHZiiDYp9mE2YXYstin2YrYr9ipLi4uJyA6ICfZhdiy2KfZitiv2KknfVxuICAgICAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICAgICAgICAgIHZhcmlhbnQ9e3NhdmVkQXVjdGlvbnMuaGFzKGF1Y3Rpb24uaWQpID8gXCJkZWZhdWx0XCIgOiBcIm91dGxpbmVcIn1cbiAgICAgICAgICAgICAgICAgICAgc2l6ZT1cImljb25cIlxuICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoZSkgPT4ge1xuICAgICAgICAgICAgICAgICAgICAgIGUucHJldmVudERlZmF1bHQoKVxuICAgICAgICAgICAgICAgICAgICAgIGUuc3RvcFByb3BhZ2F0aW9uKClcbiAgICAgICAgICAgICAgICAgICAgICBoYW5kbGVTYXZlQXVjdGlvbihhdWN0aW9uLmlkKVxuICAgICAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgICAgICBkaXNhYmxlZD17bG9hZGluZ31cbiAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgPEhlYXJ0IGNsYXNzTmFtZT17YGgtNCB3LTQgJHtzYXZlZEF1Y3Rpb25zLmhhcyhhdWN0aW9uLmlkKSA/ICdmaWxsLWN1cnJlbnQnIDogJyd9YH0gLz5cbiAgICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICAgICAgICB2YXJpYW50PVwib3V0bGluZVwiXG4gICAgICAgICAgICAgICAgICAgIHNpemU9XCJpY29uXCJcbiAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KGUpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgICBlLnByZXZlbnREZWZhdWx0KClcbiAgICAgICAgICAgICAgICAgICAgICBlLnN0b3BQcm9wYWdhdGlvbigpXG4gICAgICAgICAgICAgICAgICAgICAgaGFuZGxlVmlld0F1Y3Rpb24oYXVjdGlvbi5pZClcbiAgICAgICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgICAgICAgZGlzYWJsZWQ9e2xvYWRpbmd9XG4gICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgIDxFeWUgY2xhc3NOYW1lPVwiaC00IHctNFwiIC8+XG4gICAgICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9DYXJkQ29udGVudD5cbiAgICAgICAgICAgIDwvQ2FyZD5cbiAgICAgICAgICApKX1cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgey8qIE5vIGRhdGEgc3RhdGUgKi99XG4gICAgICAgIHshbG9hZGluZyAmJiAoIUFycmF5LmlzQXJyYXkoZmlsdGVyZWRBdWN0aW9ucykgfHwgZmlsdGVyZWRBdWN0aW9ucy5sZW5ndGggPT09IDApICYmIChcbiAgICAgICAgICA8Q2FyZD5cbiAgICAgICAgICAgIDxDYXJkQ29udGVudCBjbGFzc05hbWU9XCJweS0xNiB0ZXh0LWNlbnRlclwiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC1jb2wgaXRlbXMtY2VudGVyIGdhcC00XCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTE2IGgtMTYgcm91bmRlZC1mdWxsIGJnLWdyYXktMTAwIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgICA8VHJlbmRpbmdVcCBjbGFzc05hbWU9XCJoLTggdy04IHRleHQtZ3JheS00MDBcIiAvPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LXNlbWlib2xkIG1iLTJcIj5cbiAgICAgICAgICAgICAgICAgICAge3NlYXJjaFRlcm0gPyAn2YTYpyDYqtmI2KzYryDZhdiy2KfYr9in2Kog2KrYt9in2KjZgiDYp9mE2KjYrdirJyA6ICfZhNinINiq2YjYrNivINmF2LLYp9iv2KfYqiDZhdiq2KfYrdipINit2KfZhNmK2KfZiyd9XG4gICAgICAgICAgICAgICAgICA8L2gzPlxuICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1tdXRlZC1mb3JlZ3JvdW5kXCI+XG4gICAgICAgICAgICAgICAgICAgIHtzZWFyY2hUZXJtIFxuICAgICAgICAgICAgICAgICAgICAgID8gJ9is2LHYqCDYp9mE2KjYrdirINio2YPZhNmF2KfYqiDZhdiu2KrZhNmB2Kkg2KPZiCDYp9mF2LPYrSDZhdix2KjYuSDYp9mE2KjYrdirINmE2LnYsdi2INis2YXZiti5INin2YTZhdiy2KfYr9in2KonXG4gICAgICAgICAgICAgICAgICAgICAgOiAn2LPZitiq2YUg2LnYsdi2INin2YTZhdiy2KfYr9in2Kog2KfZhNmF2KrYp9it2Kkg2YfZhtinINi52YbYryDYpdi22KfZgdiq2YfYpyDZhdmGINmC2KjZhCDYp9mE2LTYsdmD2KfYqidcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvQ2FyZENvbnRlbnQ+XG4gICAgICAgICAgPC9DYXJkPlxuICAgICAgICApfVxuXG4gICAgICAgIHsvKiBMb2FkaW5nIHN0YXRlICovfVxuICAgICAgICB7bG9hZGluZyAmJiAoXG4gICAgICAgICAgPENhcmQ+XG4gICAgICAgICAgICA8Q2FyZENvbnRlbnQgY2xhc3NOYW1lPVwicHktMTYgdGV4dC1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGZsZXgtY29sIGl0ZW1zLWNlbnRlciBnYXAtNFwiPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYW5pbWF0ZS1zcGluIHJvdW5kZWQtZnVsbCBoLTggdy04IGJvcmRlci1iLTIgYm9yZGVyLWJsdWUtNjAwXCI+PC9kaXY+XG4gICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1tdXRlZC1mb3JlZ3JvdW5kXCI+2KzYp9ix2Yog2KrYrdmF2YrZhCDYp9mE2YXYstin2K/Yp9iqLi4uPC9wPlxuICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1ncmF5LTQwMFwiPkxvYWRpbmc6IHtsb2FkaW5nLnRvU3RyaW5nKCl9LCBBdWN0aW9uczoge2F1Y3Rpb25zLmxlbmd0aH0sIEZpbHRlcmVkOiB7ZmlsdGVyZWRBdWN0aW9ucy5sZW5ndGh9PC9wPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvQ2FyZENvbnRlbnQ+XG4gICAgICAgICAgPC9DYXJkPlxuICAgICAgICApfVxuXG4gICAgICAgIHsvKiBCaWRkaW5nIE1vZGFsICovfVxuICAgICAgICA8RGlhbG9nIG9wZW49e3Nob3dCaWRNb2RhbH0gb25PcGVuQ2hhbmdlPXtzZXRTaG93QmlkTW9kYWx9PlxuICAgICAgICAgIDxEaWFsb2dDb250ZW50IGNsYXNzTmFtZT1cInNtOm1heC13LW1kXCI+XG4gICAgICAgICAgICA8RGlhbG9nSGVhZGVyPlxuICAgICAgICAgICAgICA8RGlhbG9nVGl0bGUgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTJcIj5cbiAgICAgICAgICAgICAgICA8R2F2ZWwgY2xhc3NOYW1lPVwiaC01IHctNVwiIC8+XG4gICAgICAgICAgICAgICAg2KrZgtiv2YrZhSDZhdiy2KfZitiv2KlcbiAgICAgICAgICAgICAgPC9EaWFsb2dUaXRsZT5cbiAgICAgICAgICAgIDwvRGlhbG9nSGVhZGVyPlxuXG4gICAgICAgICAgICB7c2VsZWN0ZWRBdWN0aW9uICYmIChcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTRcIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLWdyYXktNTAgcC00IHJvdW5kZWQtbGdcIj5cbiAgICAgICAgICAgICAgICAgIDxoNCBjbGFzc05hbWU9XCJmb250LXNlbWlib2xkIG1iLTJcIj57c2VsZWN0ZWRBdWN0aW9uLnRpdGxlfTwvaDQ+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC00IHRleHQtc20gdGV4dC1ncmF5LTYwMFwiPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0xXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPERvbGxhclNpZ24gY2xhc3NOYW1lPVwiaC00IHctNFwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgPHNwYW4+2KfZhNmF2LLYp9mK2K/YqSDYp9mE2K3Yp9mE2YrYqTogeyhzZWxlY3RlZEF1Y3Rpb24uY3VycmVudEJpZCB8fCAwKS50b0xvY2FsZVN0cmluZygpfSDYsS7Yszwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTFcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8VHJlbmRpbmdVcCBjbGFzc05hbWU9XCJoLTQgdy00XCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICA8c3Bhbj57c2VsZWN0ZWRBdWN0aW9uLmJpZHNDb3VudCB8fCAwfSDZhdiy2KfZitiv2Kk8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktMlwiPlxuICAgICAgICAgICAgICAgICAgPExhYmVsIGh0bWxGb3I9XCJiaWRBbW91bnRcIj7ZhdmC2K/Yp9ixINin2YTZhdiy2KfZitiv2KkgKNixLtizKTwvTGFiZWw+XG4gICAgICAgICAgICAgICAgICA8SW5wdXRcbiAgICAgICAgICAgICAgICAgICAgaWQ9XCJiaWRBbW91bnRcIlxuICAgICAgICAgICAgICAgICAgICB0eXBlPVwibnVtYmVyXCJcbiAgICAgICAgICAgICAgICAgICAgdmFsdWU9e2JpZEFtb3VudH1cbiAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRCaWRBbW91bnQoZS50YXJnZXQudmFsdWUpfVxuICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cItij2K/YrtmEINmF2YLYr9in2LEg2KfZhNmF2LLYp9mK2K/YqVwiXG4gICAgICAgICAgICAgICAgICAgIG1pbj17KHNlbGVjdGVkQXVjdGlvbi5jdXJyZW50QmlkIHx8IDApICsgMX1cbiAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtZ3JheS01MDBcIj5cbiAgICAgICAgICAgICAgICAgICAg2KfZhNit2K8g2KfZhNij2K/ZhtmJOiB7KChzZWxlY3RlZEF1Y3Rpb24uY3VycmVudEJpZCB8fCAwKSArIDEpLnRvTG9jYWxlU3RyaW5nKCl9INixLtizXG4gICAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgKX1cblxuICAgICAgICAgICAgPERpYWxvZ0Zvb3Rlcj5cbiAgICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJvdXRsaW5lXCJcbiAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiB7XG4gICAgICAgICAgICAgICAgICBzZXRTaG93QmlkTW9kYWwoZmFsc2UpXG4gICAgICAgICAgICAgICAgICBzZXRCaWRBbW91bnQoJycpXG4gICAgICAgICAgICAgICAgICBzZXRTZWxlY3RlZEF1Y3Rpb24obnVsbClcbiAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgIGRpc2FibGVkPXtiaWRMb2FkaW5nfVxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAg2KXZhNi62KfYoVxuICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9e3N1Ym1pdEJpZH1cbiAgICAgICAgICAgICAgICBkaXNhYmxlZD17YmlkTG9hZGluZyB8fCAhYmlkQW1vdW50fVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJnLWdyZWVuLTYwMCBob3ZlcjpiZy1ncmVlbi03MDBcIlxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAge2JpZExvYWRpbmcgPyAn2KzYp9ix2Yog2KfZhNiq2YLYr9mK2YUuLi4nIDogJ9iq2YLYr9mK2YUg2KfZhNmF2LLYp9mK2K/YqSd9XG4gICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgPC9EaWFsb2dGb290ZXI+XG4gICAgICAgICAgPC9EaWFsb2dDb250ZW50PlxuICAgICAgICA8L0RpYWxvZz5cbiAgICAgIDwvZGl2PlxuICApXG59XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJ1c2VTdGF0ZSIsInVzZUVmZmVjdCIsIkNhcmQiLCJDYXJkQ29udGVudCIsIkNhcmRIZWFkZXIiLCJDYXJkVGl0bGUiLCJDYXJkRGVzY3JpcHRpb24iLCJCdXR0b24iLCJCYWRnZSIsIklucHV0IiwiRGlhbG9nIiwiRGlhbG9nQ29udGVudCIsIkRpYWxvZ0hlYWRlciIsIkRpYWxvZ1RpdGxlIiwiRGlhbG9nRm9vdGVyIiwiTGFiZWwiLCJTZWFyY2giLCJFeWUiLCJIZWFydCIsIkNsb2NrIiwiVHJlbmRpbmdVcCIsIkdhdmVsIiwiRG9sbGFyU2lnbiIsIlJlZnJlc2hDdyIsIlBhdXNlIiwiUGxheSIsImF1Y3Rpb25BUEkiLCJmYXZvcml0ZXNBUEkiLCJ1c2VSb3V0ZXIiLCJ1c2VUb2FzdCIsImN1cnJlbmN5U2VydmljZSIsIlVzZXJBdWN0aW9uc1BhZ2UiLCJhdWN0aW9ucyIsInNldEF1Y3Rpb25zIiwic2VhcmNoVGVybSIsInNldFNlYXJjaFRlcm0iLCJmaWx0ZXJlZEF1Y3Rpb25zIiwic2V0RmlsdGVyZWRBdWN0aW9ucyIsImxvYWRpbmciLCJzZXRMb2FkaW5nIiwic2F2ZWRBdWN0aW9ucyIsInNldFNhdmVkQXVjdGlvbnMiLCJTZXQiLCJ1c2VyIiwic2V0VXNlciIsInNob3dCaWRNb2RhbCIsInNldFNob3dCaWRNb2RhbCIsInNlbGVjdGVkQXVjdGlvbiIsInNldFNlbGVjdGVkQXVjdGlvbiIsImJpZEFtb3VudCIsInNldEJpZEFtb3VudCIsImJpZExvYWRpbmciLCJzZXRCaWRMb2FkaW5nIiwic29ydEJ5Iiwic2V0U29ydEJ5IiwiZmlsdGVyQ2F0ZWdvcnkiLCJzZXRGaWx0ZXJDYXRlZ29yeSIsImF1dG9SZWZyZXNoIiwic2V0QXV0b1JlZnJlc2giLCJsYXN0UmVmcmVzaCIsInNldExhc3RSZWZyZXNoIiwiRGF0ZSIsInVzZXJDdXJyZW5jeSIsInNldFVzZXJDdXJyZW5jeSIsImV4Y2hhbmdlUmF0ZXMiLCJzZXRFeGNoYW5nZVJhdGVzIiwiYXVjdGlvblZlcnNpb25zIiwic2V0QXVjdGlvblZlcnNpb25zIiwicm91dGVyIiwidG9hc3QiLCJ1c2VyRGF0YSIsImxvY2FsU3RvcmFnZSIsImdldEl0ZW0iLCJwYXJzZWRVc2VyIiwiSlNPTiIsInBhcnNlIiwicm9sZSIsInRpdGxlIiwiZGVzY3JpcHRpb24iLCJ2YXJpYW50IiwicHVzaCIsInNhdmVkQ3VycmVuY3kiLCJnZXRVc2VyQ3VycmVuY3kiLCJjb25zb2xlIiwibG9nIiwic2V0VGltZW91dCIsImxvYWRBdWN0aW9ucyIsImxvYWRTYXZlZEF1Y3Rpb25zIiwibG9hZEV4Y2hhbmdlUmF0ZXMiLCJyYXRlcyIsImdldEV4Y2hhbmdlUmF0ZXMiLCJlcnJvciIsImludGVydmFsIiwic2V0SW50ZXJ2YWwiLCJjbGVhckludGVydmFsIiwic2hvd0xvYWRpbmciLCJyZXNwb25zZSIsImdldEFsbCIsImRhdGEiLCJhdWN0aW9uRGF0YSIsInN1Y2Nlc3MiLCJBcnJheSIsImlzQXJyYXkiLCJ0cmFuc2Zvcm1lZERhdGEiLCJQcm9taXNlIiwiYWxsIiwibWFwIiwiYXVjdGlvbiIsImJhc2VBdWN0aW9uIiwiaWQiLCJfaWQiLCJjdXJyZW50QmlkIiwiY3VycmVudFByaWNlIiwic3RhcnRpbmdQcmljZSIsImVuZERhdGUiLCJiaWRzQ291bnQiLCJiaWRzIiwibGVuZ3RoIiwic2VsbGVyIiwicHJvZmlsZSIsImZ1bGxOYW1lIiwiY29tcGFueU5hbWUiLCJuYW1lIiwiY2F0ZWdvcnkiLCJzdGF0dXMiLCJ2aWV3cyIsImxvY2F0aW9uIiwiY3VycmVuY3kiLCJ2ZXJzaW9uIiwicHJldiIsImNvbnZlcnRlZEJpZCIsImNvbnZlcnRDdXJyZW5jeSIsIk1hdGgiLCJyb3VuZCIsIm9yaWdpbmFsQ3VycmVuY3kiLCJjb252ZXJzaW9uRXJyb3IiLCJ3YXJuIiwiZ2V0RmF2b3JpdGVzIiwidHlwZSIsImZhdm9yaXRlc0RhdGEiLCJmYXZvcml0ZXMiLCJzYXZlZElkcyIsImZhdiIsIml0ZW1JZCIsImZpbHRlcmVkIiwiZmlsdGVyIiwibWF0Y2hlc1NlYXJjaCIsInRvTG93ZXJDYXNlIiwiaW5jbHVkZXMiLCJtYXRjaGVzQ2F0ZWdvcnkiLCJzb3J0IiwiYSIsImIiLCJnZXRUaW1lIiwibG9jYWxlQ29tcGFyZSIsImdldFRpbWVSZW1haW5pbmciLCJub3ciLCJlbmQiLCJkaWZmIiwiaG91cnMiLCJmbG9vciIsImRheXMiLCJjYW5Vc2VyQmlkIiwiaGFuZGxlQmlkIiwiYXVjdGlvbklkIiwiZnJlc2hBdWN0aW9uIiwiZ2V0QnlJZCIsInRyYW5zZm9ybWVkQXVjdGlvbiIsIlN0cmluZyIsInRvTG9jYWxlU3RyaW5nIiwiY3VycmVudEF1Y3Rpb24iLCJmaW5kIiwic3VibWl0QmlkIiwiYmlkQW1vdW50TnVtIiwicGFyc2VGbG9hdCIsImlzTmFOIiwiYXVjdGlvblZlcnNpb24iLCJiaWREYXRhIiwiZmV0Y2giLCJtZXRob2QiLCJoZWFkZXJzIiwiYm9keSIsInN0cmluZ2lmeSIsInJlc3VsdCIsImpzb24iLCJvayIsIkVycm9yIiwibWVzc2FnZSIsImZvcm1hdHRlZEFtb3VudCIsImZvcm1hdEFtb3VudCIsImVycm9yTWVzc2FnZSIsImhhbmRsZVNhdmVBdWN0aW9uIiwiaXNDdXJyZW50bHlTYXZlZCIsImhhcyIsIm5ld1NldCIsImRlbGV0ZSIsImFkZCIsInJlbW92ZUZhdm9yaXRlIiwiYWRkRmF2b3JpdGUiLCJpdGVtVHlwZSIsIm5vdGlmaWNhdGlvbnMiLCJiaWRVcGRhdGVzIiwic3RhdHVzQ2hhbmdlcyIsImVuZGluZ1Nvb24iLCJoYW5kbGVWaWV3QXVjdGlvbiIsImRpdiIsImNsYXNzTmFtZSIsImhlYWRlciIsImgxIiwicCIsInNwYW4iLCJodG1sRm9yIiwic2VsZWN0IiwidmFsdWUiLCJvbkNoYW5nZSIsImUiLCJuZXdDdXJyZW5jeSIsInRhcmdldCIsImdldEN1cnJlbmN5U3ltYm9sIiwiZ2V0U3VwcG9ydGVkQ3VycmVuY2llcyIsIm9wdGlvbiIsImNvZGUiLCJzeW1ib2wiLCJvbkNsaWNrIiwiZGlzYWJsZWQiLCJzaXplIiwicGxhY2Vob2xkZXIiLCJyZWR1Y2UiLCJzdW0iLCJtYXgiLCJpbWFnZXMiLCJpbWciLCJzcmMiLCJ1cmwiLCJhbHQiLCJvbkVycm9yIiwiY3VycmVudFRhcmdldCIsInN0eWxlIiwiZGlzcGxheSIsIm5leHRFbGVtZW50U2libGluZyIsInByZXZlbnREZWZhdWx0Iiwic3RvcFByb3BhZ2F0aW9uIiwiaDMiLCJ0b1N0cmluZyIsIm9wZW4iLCJvbk9wZW5DaGFuZ2UiLCJoNCIsIm1pbiJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/user/auctions/page.tsx\n"));

/***/ })

});