"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/user/auctions/page",{

/***/ "(app-pages-browser)/./app/user/auctions/page.tsx":
/*!************************************!*\
  !*** ./app/user/auctions/page.tsx ***!
  \************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ UserAuctionsPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./components/ui/dialog.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./components/ui/label.tsx\");\n/* harmony import */ var _barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,DollarSign,Eye,Gavel,Heart,Pause,Play,RefreshCw,Search,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,DollarSign,Eye,Gavel,Heart,Pause,Play,RefreshCw,Search,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pause.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,DollarSign,Eye,Gavel,Heart,Pause,Play,RefreshCw,Search,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,DollarSign,Eye,Gavel,Heart,Pause,Play,RefreshCw,Search,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,DollarSign,Eye,Gavel,Heart,Pause,Play,RefreshCw,Search,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/gavel.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,DollarSign,Eye,Gavel,Heart,Pause,Play,RefreshCw,Search,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,DollarSign,Eye,Gavel,Heart,Pause,Play,RefreshCw,Search,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,DollarSign,Eye,Gavel,Heart,Pause,Play,RefreshCw,Search,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,DollarSign,Eye,Gavel,Heart,Pause,Play,RefreshCw,Search,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,DollarSign,Eye,Gavel,Heart,Pause,Play,RefreshCw,Search,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./lib/api.ts\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/use-toast */ \"(app-pages-browser)/./components/ui/use-toast.tsx\");\n/* harmony import */ var _contexts_CurrencyContext__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/contexts/CurrencyContext */ \"(app-pages-browser)/./contexts/CurrencyContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction UserAuctionsPage() {\n    _s();\n    const [auctions, setAuctions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [filteredAuctions, setFilteredAuctions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [savedAuctions, setSavedAuctions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Bidding modal state\n    const [showBidModal, setShowBidModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedAuction, setSelectedAuction] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [bidAmount, setBidAmount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [bidLoading, setBidLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Filter and sort state\n    const [sortBy, setSortBy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"endDate\");\n    const [filterCategory, setFilterCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // Auto-refresh state\n    const [autoRefresh, setAutoRefresh] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [lastRefresh, setLastRefresh] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Date());\n    // Optimistic locking\n    const [auctionVersions, setAuctionVersions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_9__.useRouter)();\n    const { toast } = (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_10__.useToast)();\n    const { userCurrency, formatAmount, convertAmount } = (0,_contexts_CurrencyContext__WEBPACK_IMPORTED_MODULE_11__.useCurrency)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Get user data and check permissions\n        const userData = localStorage.getItem(\"user\");\n        if (userData) {\n            const parsedUser = JSON.parse(userData);\n            setUser(parsedUser);\n            // Redirect admins to their proper dashboard\n            if (parsedUser.role === \"admin\" || parsedUser.role === \"super_admin\") {\n                toast({\n                    title: \"وصول غير مسموح\",\n                    description: \"المديرون يجب أن يستخدموا لوحة إدارة المزادات\",\n                    variant: \"destructive\"\n                });\n                router.push(\"/admin/auctions\");\n                return;\n            }\n            // Redirect government users to their dashboard\n            if (parsedUser.role === \"government\") {\n                toast({\n                    title: \"وصول غير مسموح\",\n                    description: \"الجهات الحكومية تستخدم نظام المناقصات\",\n                    variant: \"destructive\"\n                });\n                router.push(\"/government/dashboard\");\n                return;\n            }\n        }\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Load data\n        loadAuctions();\n        loadSavedAuctions();\n    }, []);\n    // Load auctions when currency changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (userCurrency) {\n            loadAuctions();\n        }\n    }, [\n        userCurrency\n    ]);\n    // Auto-refresh auctions every 30 seconds\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!autoRefresh) return;\n        const interval = setInterval(async ()=>{\n            try {\n                await loadAuctions(false) // Silent refresh without loading state\n                ;\n                setLastRefresh(new Date());\n                console.log(\"\\uD83D\\uDD04 Auto-refreshed auction data\");\n            } catch (error) {\n                console.error(\"Auto-refresh failed:\", error);\n            }\n        }, 30000) // 30 seconds\n        ;\n        return ()=>clearInterval(interval);\n    }, [\n        autoRefresh\n    ]);\n    const loadAuctions = async function() {\n        let showLoading = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : true;\n        try {\n            if (showLoading) {\n                setLoading(true);\n            }\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_8__.auctionAPI.getAll();\n            console.log(\"Auctions API response:\", response.data);\n            // Handle the API response structure properly\n            let auctionData = [];\n            if (response.data && response.data.success) {\n                var _response_data_data;\n                // If the response has a success field, extract the auctions from data.data.auctions\n                auctionData = ((_response_data_data = response.data.data) === null || _response_data_data === void 0 ? void 0 : _response_data_data.auctions) || [];\n            } else if (response.data && Array.isArray(response.data)) {\n                // If response.data is directly an array\n                auctionData = response.data;\n            } else if (response.data && response.data.data && Array.isArray(response.data.data)) {\n                // If the auctions are in response.data.data\n                auctionData = response.data.data;\n            }\n            // Transform and convert currency for each auction\n            const transformedData = await Promise.all(auctionData.map(async (auction)=>{\n                var _auction_bids, _auction_seller_profile, _auction_seller, _auction_seller_profile1, _auction_seller1, _auction_seller2, _auction_seller3;\n                const baseAuction = {\n                    id: auction._id || auction.id,\n                    title: auction.title,\n                    description: auction.description,\n                    currentBid: auction.currentPrice || auction.currentBid || auction.startingPrice || 0,\n                    endDate: auction.endDate,\n                    bidsCount: ((_auction_bids = auction.bids) === null || _auction_bids === void 0 ? void 0 : _auction_bids.length) || auction.bidsCount || 0,\n                    seller: ((_auction_seller = auction.seller) === null || _auction_seller === void 0 ? void 0 : (_auction_seller_profile = _auction_seller.profile) === null || _auction_seller_profile === void 0 ? void 0 : _auction_seller_profile.fullName) || ((_auction_seller1 = auction.seller) === null || _auction_seller1 === void 0 ? void 0 : (_auction_seller_profile1 = _auction_seller1.profile) === null || _auction_seller_profile1 === void 0 ? void 0 : _auction_seller_profile1.companyName) || ((_auction_seller2 = auction.seller) === null || _auction_seller2 === void 0 ? void 0 : _auction_seller2.name) || ((_auction_seller3 = auction.seller) === null || _auction_seller3 === void 0 ? void 0 : _auction_seller3._id) || \"غير محدد\",\n                    category: auction.category,\n                    status: auction.status,\n                    views: auction.views || 0,\n                    location: auction.location,\n                    currency: auction.currency || \"SAR\",\n                    version: auction.version || 0\n                };\n                // Store version for optimistic locking\n                setAuctionVersions((prev)=>({\n                        ...prev,\n                        [baseAuction.id]: baseAuction.version\n                    }));\n                // Convert currency if needed\n                try {\n                    if (baseAuction.currency !== userCurrency) {\n                        const convertedBid = await convertAmount(baseAuction.currentBid, baseAuction.currency, userCurrency);\n                        return {\n                            ...baseAuction,\n                            currentBid: Math.round(convertedBid),\n                            originalCurrency: baseAuction.currency,\n                            currency: userCurrency\n                        };\n                    }\n                } catch (conversionError) {\n                    console.warn(\"Currency conversion failed for auction:\", baseAuction.id, conversionError);\n                }\n                return baseAuction;\n            }));\n            setAuctions(transformedData);\n            setFilteredAuctions(transformedData);\n            console.log(\"✅ Set auctions and filteredAuctions:\", transformedData.length);\n        } catch (error) {\n            console.error(\"Error loading auctions:\", error);\n            setAuctions([]);\n            setFilteredAuctions([]);\n            toast({\n                title: \"فشل في تحميل المزادات\",\n                description: \"حدث خطأ أثناء تحميل المزادات. يرجى المحاولة مرة أخرى.\",\n                variant: \"destructive\"\n            });\n        } finally{\n            if (showLoading) {\n                setLoading(false);\n            }\n            console.log(\"\\uD83C\\uDFC1 Loading finished. Auctions loaded:\", auctions.length);\n        }\n    };\n    const loadSavedAuctions = async ()=>{\n        try {\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_8__.favoritesAPI.getFavorites({\n                type: \"auction\"\n            });\n            // Handle different response structures\n            let favoritesData = [];\n            if (response.data && response.data.success) {\n                favoritesData = response.data.data || [];\n            } else if (response.data && Array.isArray(response.data)) {\n                favoritesData = response.data;\n            } else if (response.data && response.data.favorites && Array.isArray(response.data.favorites)) {\n                favoritesData = response.data.favorites;\n            }\n            // Check if favoritesData is an array before mapping\n            if (Array.isArray(favoritesData)) {\n                const savedIds = new Set(favoritesData.map((fav)=>fav.itemId || fav._id || fav.id));\n                setSavedAuctions(savedIds);\n            } else {\n                console.warn(\"Favorites data is not an array:\", favoritesData);\n                setSavedAuctions(new Set());\n            }\n        } catch (error) {\n            console.error(\"Error loading saved auctions:\", error);\n            // Don't show error toast for favorites as it's not critical\n            setSavedAuctions(new Set());\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Ensure auctions is an array before filtering\n        if (Array.isArray(auctions)) {\n            let filtered = auctions.filter((auction)=>{\n                var _auction_title, _auction_description;\n                const matchesSearch = ((_auction_title = auction.title) === null || _auction_title === void 0 ? void 0 : _auction_title.toLowerCase().includes(searchTerm.toLowerCase())) || ((_auction_description = auction.description) === null || _auction_description === void 0 ? void 0 : _auction_description.toLowerCase().includes(searchTerm.toLowerCase()));\n                const matchesCategory = !filterCategory || auction.category === filterCategory;\n                return matchesSearch && matchesCategory;\n            });\n            // Sort the filtered results\n            filtered = filtered.sort((a, b)=>{\n                switch(sortBy){\n                    case \"endDate\":\n                        return new Date(a.endDate).getTime() - new Date(b.endDate).getTime();\n                    case \"currentBid\":\n                        return (b.currentBid || 0) - (a.currentBid || 0);\n                    case \"bidsCount\":\n                        return (b.bidsCount || 0) - (a.bidsCount || 0);\n                    case \"title\":\n                        return a.title.localeCompare(b.title);\n                    default:\n                        return 0;\n                }\n            });\n            setFilteredAuctions(filtered);\n            console.log(\"\\uD83D\\uDD0D Filtered auctions:\", filtered.length, \"from\", auctions.length);\n        } else {\n            setFilteredAuctions([]);\n        }\n    }, [\n        searchTerm,\n        auctions,\n        filterCategory,\n        sortBy\n    ]);\n    const getTimeRemaining = (endDate)=>{\n        const now = new Date();\n        const end = new Date(endDate);\n        const diff = end.getTime() - now.getTime();\n        if (diff <= 0) return \"انتهى\";\n        const hours = Math.floor(diff / (1000 * 60 * 60));\n        const days = Math.floor(hours / 24);\n        if (days > 0) return \"\".concat(days, \" يوم\");\n        return \"\".concat(hours, \" ساعة\");\n    };\n    // Check if user can bid\n    const canUserBid = (auction)=>{\n        var _user_profile;\n        if (!user) return false;\n        // Only individuals and companies can bid\n        if (user.role !== \"individual\" && user.role !== \"company\") {\n            return false;\n        }\n        // Companies cannot bid on their own auctions\n        if (user.role === \"company\" && auction.seller === ((_user_profile = user.profile) === null || _user_profile === void 0 ? void 0 : _user_profile.companyName)) {\n            return false;\n        }\n        return true;\n    };\n    const handleBid = async (auctionId)=>{\n        console.log(\"\\uD83C\\uDFAF Bid button clicked for auction:\", auctionId);\n        try {\n            var _freshAuction_bids, _freshAuction_seller_profile, _freshAuction_seller, _freshAuction_seller_profile1, _freshAuction_seller1, _freshAuction_seller2;\n            // Fetch fresh auction data before opening modal\n            setLoading(true);\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_8__.auctionAPI.getById(auctionId);\n            let freshAuction = null;\n            if (response.data && response.data.success) {\n                freshAuction = response.data.data.auction;\n            } else if (response.data && response.data.auction) {\n                freshAuction = response.data.auction;\n            }\n            if (!freshAuction) {\n                toast({\n                    title: \"خطأ\",\n                    description: \"لم يتم العثور على المزاد\",\n                    variant: \"destructive\"\n                });\n                return;\n            }\n            // Transform fresh data to match our format\n            const transformedAuction = {\n                id: freshAuction._id || freshAuction.id,\n                title: freshAuction.title,\n                description: freshAuction.description,\n                currentBid: freshAuction.currentPrice || freshAuction.currentBid || freshAuction.startingPrice || 0,\n                endDate: freshAuction.endDate,\n                bidsCount: ((_freshAuction_bids = freshAuction.bids) === null || _freshAuction_bids === void 0 ? void 0 : _freshAuction_bids.length) || freshAuction.bidsCount || 0,\n                seller: ((_freshAuction_seller = freshAuction.seller) === null || _freshAuction_seller === void 0 ? void 0 : (_freshAuction_seller_profile = _freshAuction_seller.profile) === null || _freshAuction_seller_profile === void 0 ? void 0 : _freshAuction_seller_profile.fullName) || ((_freshAuction_seller1 = freshAuction.seller) === null || _freshAuction_seller1 === void 0 ? void 0 : (_freshAuction_seller_profile1 = _freshAuction_seller1.profile) === null || _freshAuction_seller_profile1 === void 0 ? void 0 : _freshAuction_seller_profile1.companyName) || ((_freshAuction_seller2 = freshAuction.seller) === null || _freshAuction_seller2 === void 0 ? void 0 : _freshAuction_seller2.name) || \"غير محدد\",\n                category: freshAuction.category,\n                status: freshAuction.status\n            };\n            // Check if user can bid\n            if (!canUserBid(transformedAuction)) {\n                if ((user === null || user === void 0 ? void 0 : user.role) === \"admin\" || (user === null || user === void 0 ? void 0 : user.role) === \"super_admin\") {\n                    toast({\n                        title: \"وصول غير مسموح\",\n                        description: \"المديرون لا يمكنهم المزايدة على المزادات\",\n                        variant: \"destructive\"\n                    });\n                } else if ((user === null || user === void 0 ? void 0 : user.role) === \"government\") {\n                    toast({\n                        title: \"وصول غير مسموح\",\n                        description: \"الجهات الحكومية لا تزايد على المزادات\",\n                        variant: \"destructive\"\n                    });\n                } else if ((user === null || user === void 0 ? void 0 : user.role) === \"company\") {\n                    toast({\n                        title: \"وصول غير مسموح\",\n                        description: \"لا يمكنك المزايدة على مزادك الخاص\",\n                        variant: \"destructive\"\n                    });\n                } else {\n                    toast({\n                        title: \"وصول غير مسموح\",\n                        description: \"غير مسموح لك بالمزايدة\",\n                        variant: \"destructive\"\n                    });\n                }\n                return;\n            }\n            // Open bidding modal with fresh data\n            setSelectedAuction(transformedAuction);\n            setBidAmount(String((transformedAuction.currentBid || 0) + 1000));\n            setShowBidModal(true);\n            toast({\n                title: \"\\uD83D\\uDCCA تم تحديث بيانات المزاد\",\n                description: \"المزايدة الحالية: \".concat(formatAmount(transformedAuction.currentBid || 0))\n            });\n        } catch (error) {\n            console.error(\"Error fetching fresh auction data:\", error);\n            toast({\n                title: \"خطأ في تحديث البيانات\",\n                description: \"سيتم استخدام البيانات المحفوظة محلياً\",\n                variant: \"destructive\"\n            });\n            // Fallback to cached data\n            const currentAuction = auctions.find((a)=>a.id === auctionId);\n            if (currentAuction && canUserBid(currentAuction)) {\n                setSelectedAuction(currentAuction);\n                setBidAmount(String((currentAuction.currentBid || 0) + 1000));\n                setShowBidModal(true);\n            }\n        } finally{\n            setLoading(false);\n        }\n    };\n    const submitBid = async ()=>{\n        if (!selectedAuction) return;\n        const bidAmountNum = parseFloat(bidAmount);\n        if (isNaN(bidAmountNum) || bidAmountNum <= (selectedAuction.currentBid || 0)) {\n            toast({\n                title: \"مزايدة غير صالحة\",\n                description: \"يجب أن تكون المزايدة أكبر من المزايدة الحالية\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        try {\n            setBidLoading(true);\n            // Get auction version for optimistic locking\n            const auctionVersion = auctionVersions[selectedAuction.id];\n            // Prepare bid data with currency and version\n            const bidData = {\n                bidAmount: bidAmountNum,\n                currency: userCurrency,\n                version: auctionVersion\n            };\n            // Submit bid and wait for server response\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_8__[\"default\"].post(\"/auctions/\".concat(selectedAuction.id, \"/bid\"), bidData);\n            const result = response.data;\n            if (!result.success) {\n                // Handle specific error cases\n                if (response.status === 409) {\n                    // Optimistic locking conflict\n                    toast({\n                        title: \"تم تحديث المزاد ⚠️\",\n                        description: \"قام شخص آخر بتحديث المزاد. سيتم تحديث البيانات.\",\n                        variant: \"destructive\"\n                    });\n                    await loadAuctions();\n                    setShowBidModal(false);\n                    setBidAmount(\"\");\n                    setSelectedAuction(null);\n                    return;\n                }\n                throw new Error(result.message || \"Bid failed\");\n            }\n            // Close modal first\n            setShowBidModal(false);\n            setBidAmount(\"\");\n            setSelectedAuction(null);\n            // Reload fresh data from server\n            await loadAuctions();\n            // Show success message with currency formatting\n            const formattedAmount = formatAmount(bidAmountNum);\n            toast({\n                title: \"تم تقديم المزايدة بنجاح! \\uD83C\\uDF89\",\n                description: \"مقدار المزايدة: \".concat(formattedAmount)\n            });\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error(\"Error placing bid:\", error);\n            // Close modal on error too\n            setShowBidModal(false);\n            setBidAmount(\"\");\n            setSelectedAuction(null);\n            // Show specific error message\n            const errorMessage = ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"يرجى المحاولة مرة أخرى\";\n            if (errorMessage.includes(\"higher than current price\") || errorMessage.includes(\"أكبر من السعر الحالي\")) {\n                toast({\n                    title: \"مزايدة منخفضة ⚠️\",\n                    description: \"قام شخص آخر بمزايدة أعلى. يرجى المحاولة بمبلغ أكبر.\",\n                    variant: \"destructive\"\n                });\n            } else if (errorMessage.includes(\"ended\") || errorMessage.includes(\"انتهى\")) {\n                toast({\n                    title: \"انتهى المزاد ⏰\",\n                    description: \"لقد انتهى هذا المزاد ولا يمكن قبول مزايدات جديدة.\",\n                    variant: \"destructive\"\n                });\n            } else {\n                toast({\n                    title: \"فشل في تقديم المزايدة\",\n                    description: errorMessage,\n                    variant: \"destructive\"\n                });\n            }\n            // Always reload data on error to sync with server\n            await loadAuctions();\n        } finally{\n            setBidLoading(false);\n        }\n    };\n    const handleSaveAuction = async (auctionId)=>{\n        console.log(\"❤️ Save button clicked for auction:\", auctionId);\n        // Show immediate feedback that button was clicked\n        toast({\n            title: \"\\uD83D\\uDC86 تم الضغط على زر الحفظ\",\n            description: \"جاري معالجة الطلب...\"\n        });\n        try {\n            const auction = auctions.find((a)=>a.id === auctionId);\n            if (!auction) {\n                toast({\n                    title: \"خطأ\",\n                    description: \"لم يتم العثور على المزاد\",\n                    variant: \"destructive\"\n                });\n                return;\n            }\n            const isCurrentlySaved = savedAuctions.has(auctionId);\n            // Optimistic UI update\n            setSavedAuctions((prev)=>{\n                const newSet = new Set(prev);\n                if (isCurrentlySaved) {\n                    newSet.delete(auctionId);\n                } else {\n                    newSet.add(auctionId);\n                }\n                return newSet;\n            });\n            if (isCurrentlySaved) {\n                // Remove from favorites\n                await _lib_api__WEBPACK_IMPORTED_MODULE_8__.favoritesAPI.removeFavorite(\"auction\", auctionId);\n                toast({\n                    title: \"❤️ تم إزالة من المفضلة\",\n                    description: 'تم إزالة \"'.concat(auction.title, '\" من المفضلة')\n                });\n            } else {\n                // Add to favorites\n                await _lib_api__WEBPACK_IMPORTED_MODULE_8__.favoritesAPI.addFavorite({\n                    itemType: \"auction\",\n                    itemId: auctionId,\n                    notifications: {\n                        bidUpdates: true,\n                        statusChanges: true,\n                        endingSoon: true\n                    }\n                });\n                toast({\n                    title: \"\\uD83D\\uDC96 تم إضافة إلى المفضلة\",\n                    description: 'تم إضافة \"'.concat(auction.title, '\" إلى المفضلة')\n                });\n            }\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error(\"Error saving auction:\", error);\n            // Revert optimistic update on error\n            setSavedAuctions((prev)=>{\n                const newSet = new Set(prev);\n                if (savedAuctions.has(auctionId)) {\n                    newSet.add(auctionId);\n                } else {\n                    newSet.delete(auctionId);\n                }\n                return newSet;\n            });\n            toast({\n                title: \"فشل في حفظ المزاد\",\n                description: ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"يرجى المحاولة مرة أخرى\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleViewAuction = async (auctionId)=>{\n        console.log(\"\\uD83D\\uDC41️ View button clicked for auction:\", auctionId);\n        try {\n            const auction = auctions.find((a)=>a.id === auctionId);\n            if (!auction) {\n                toast({\n                    title: \"خطأ\",\n                    description: \"لم يتم العثور على المزاد\",\n                    variant: \"destructive\"\n                });\n                return;\n            }\n            // Navigate to auction detail page\n            router.push(\"/auctions/\".concat(auctionId));\n            toast({\n                title: \"\\uD83D\\uDC41️ جاري تحميل التفاصيل\",\n                description: \"سيتم عرض تفاصيل المزاد\"\n            });\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error(\"Error viewing auction:\", error);\n            toast({\n                title: \"فشل في عرض تفاصيل المزاد\",\n                description: ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"يرجى المحاولة مرة أخرى\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-3xl font-bold\",\n                                    children: \"المزادات المتاحة\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 605,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-muted-foreground\",\n                                    children: [\n                                        \"استكشف المزادات الحالية وشارك في المزايدة\",\n                                        autoRefresh && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs text-green-600 ml-2\",\n                                            children: \"• تحديث تلقائي كل 30 ثانية\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                            lineNumber: 609,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 606,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                            lineNumber: 604,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    onClick: async ()=>{\n                                        await loadAuctions(true) // Show loading state\n                                        ;\n                                        setLastRefresh(new Date());\n                                        toast({\n                                            title: \"\\uD83D\\uDD04 تم تحديث البيانات\",\n                                            description: \"تم العثور على \".concat(filteredAuctions.length, \" مزاد متاح\")\n                                        });\n                                    },\n                                    variant: \"outline\",\n                                    disabled: loading,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"h-4 w-4 ml-2 \".concat(loading ? \"animate-spin\" : \"\")\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                            lineNumber: 629,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"تحديث\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 617,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    onClick: ()=>{\n                                        setAutoRefresh(!autoRefresh);\n                                        toast({\n                                            title: autoRefresh ? \"⏸️ تم إيقاف التحديث التلقائي\" : \"▶️ تم تشغيل التحديث التلقائي\",\n                                            description: autoRefresh ? \"يمكنك التحديث يدوياً\" : \"سيتم التحديث كل 30 ثانية\"\n                                        });\n                                    },\n                                    variant: autoRefresh ? \"default\" : \"outline\",\n                                    size: \"icon\",\n                                    children: autoRefresh ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 643,\n                                        columnNumber: 32\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 643,\n                                        columnNumber: 64\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 632,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                            lineNumber: 615,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                    lineNumber: 603,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                lineNumber: 602,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                    className: \"pt-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"absolute right-3 top-3 h-4 w-4 text-muted-foreground\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 654,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                        placeholder: \"ابحث في المزادات...\",\n                                        value: searchTerm,\n                                        onChange: (e)=>setSearchTerm(e.target.value),\n                                        className: \"pr-10\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 655,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                lineNumber: 653,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col sm:flex-row gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                htmlFor: \"category\",\n                                                className: \"text-sm font-medium\",\n                                                children: \"الفئة\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                lineNumber: 665,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                id: \"category\",\n                                                value: filterCategory,\n                                                onChange: (e)=>setFilterCategory(e.target.value),\n                                                className: \"w-full mt-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"\",\n                                                        children: \"جميع الفئات\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 672,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"electronics\",\n                                                        children: \"إلكترونيات\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 673,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"vehicles\",\n                                                        children: \"سيارات\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 674,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"real_estate\",\n                                                        children: \"عقارات\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 675,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"art_collectibles\",\n                                                        children: \"فنون ومقتنيات\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 676,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"machinery\",\n                                                        children: \"معدات\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 677,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"furniture\",\n                                                        children: \"أثاث\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 678,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"jewelry\",\n                                                        children: \"مجوهرات\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 679,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"books_media\",\n                                                        children: \"كتب ووسائط\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 680,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"clothing\",\n                                                        children: \"ملابس\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 681,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"sports\",\n                                                        children: \"رياضة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 682,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"other\",\n                                                        children: \"أخرى\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 683,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                lineNumber: 666,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 664,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                htmlFor: \"sort\",\n                                                className: \"text-sm font-medium\",\n                                                children: \"ترتيب حسب\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                lineNumber: 688,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                id: \"sort\",\n                                                value: sortBy,\n                                                onChange: (e)=>setSortBy(e.target.value),\n                                                className: \"w-full mt-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"endDate\",\n                                                        children: \"تاريخ الانتهاء\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 695,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"currentBid\",\n                                                        children: \"أعلى مزايدة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 696,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"bidsCount\",\n                                                        children: \"عدد المزايدات\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 697,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"title\",\n                                                        children: \"الاسم\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 698,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                lineNumber: 689,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 687,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                lineNumber: 663,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                        lineNumber: 652,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                    lineNumber: 651,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                lineNumber: 650,\n                columnNumber: 9\n            }, this),\n            Array.isArray(filteredAuctions) && filteredAuctions.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                    className: \"text-lg\",\n                                    children: \"إجمالي المزادات\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 711,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                    className: \"text-2xl font-bold text-blue-600\",\n                                    children: filteredAuctions.length\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 712,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                            lineNumber: 710,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                        lineNumber: 709,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                    className: \"text-lg\",\n                                    children: \"المزايدات النشطة\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 719,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                    className: \"text-2xl font-bold text-green-600\",\n                                    children: filteredAuctions.reduce((sum, a)=>sum + (a.bidsCount || 0), 0)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 720,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                            lineNumber: 718,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                        lineNumber: 717,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                    className: \"text-lg\",\n                                    children: \"أعلى مزايدة\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 727,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                    className: \"text-2xl font-bold text-purple-600\",\n                                    children: filteredAuctions.length > 0 ? formatAmount(Math.max(...filteredAuctions.map((a)=>a.currentBid || 0))) : formatAmount(0)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 728,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                            lineNumber: 726,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                        lineNumber: 725,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                lineNumber: 708,\n                columnNumber: 11\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                children: Array.isArray(filteredAuctions) && filteredAuctions.map((auction)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        className: \"hover:shadow-lg transition-shadow cursor-pointer\",\n                        onClick: ()=>handleViewAuction(auction.id),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"aspect-video bg-gradient-to-br from-gray-100 to-gray-200 rounded-lg mb-4 flex items-center justify-center relative overflow-hidden\",\n                                        children: [\n                                            auction.images && auction.images.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: auction.images[0].url,\n                                                alt: auction.title,\n                                                className: \"w-full h-full object-cover\",\n                                                onError: (e)=>{\n                                                    e.currentTarget.style.display = \"none\";\n                                                    e.currentTarget.nextElementSibling.style.display = \"flex\";\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                lineNumber: 746,\n                                                columnNumber: 21\n                                            }, this) : null,\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-center w-full h-full text-gray-500\",\n                                                style: {\n                                                    display: auction.images && auction.images.length > 0 ? \"none\" : \"flex\"\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                            className: \"h-8 w-8 mx-auto mb-2 text-gray-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                            lineNumber: 758,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm\",\n                                                            children: \"صورة المزاد\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                            lineNumber: 759,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                    lineNumber: 757,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                lineNumber: 756,\n                                                columnNumber: 19\n                                            }, this),\n                                            auction.status === \"active\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                className: \"absolute top-2 right-2 bg-green-500\",\n                                                children: \"نشط\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                lineNumber: 763,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 744,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                        className: \"text-lg line-clamp-2\",\n                                        children: auction.title\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 768,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                        className: \"line-clamp-2\",\n                                        children: auction.description\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 769,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                lineNumber: 743,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-muted-foreground\",\n                                                        children: \"أعلى مزايدة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 776,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xl font-bold text-green-600\",\n                                                        children: [\n                                                            formatAmount(auction.currentBid || 0),\n                                                            auction.originalCurrency && auction.originalCurrency !== userCurrency && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs text-gray-500 ml-2\",\n                                                                children: [\n                                                                    \"(من \",\n                                                                    auction.originalCurrency,\n                                                                    \")\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                                lineNumber: 780,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 777,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                lineNumber: 775,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-muted-foreground\",\n                                                        children: \"المزايدات\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 787,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-lg font-semibold\",\n                                                        children: auction.bidsCount || 0\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 788,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                lineNumber: 786,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 774,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                className: \"h-4 w-4 text-muted-foreground\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                lineNumber: 793,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-muted-foreground\",\n                                                children: [\n                                                    \"ينتهي خلال: \",\n                                                    getTimeRemaining(auction.endDate)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                lineNumber: 794,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 792,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-muted-foreground\",\n                                        children: [\n                                            \"البائع: \",\n                                            auction.seller\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 799,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                onClick: (e)=>{\n                                                    e.preventDefault();\n                                                    e.stopPropagation();\n                                                    handleBid(auction.id);\n                                                },\n                                                className: \"flex-1\",\n                                                disabled: loading || bidLoading,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                        className: \"h-4 w-4 ml-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 813,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    bidLoading ? \"جاري المزايدة...\" : \"مزايدة\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                lineNumber: 804,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                variant: savedAuctions.has(auction.id) ? \"default\" : \"outline\",\n                                                size: \"icon\",\n                                                onClick: (e)=>{\n                                                    e.preventDefault();\n                                                    e.stopPropagation();\n                                                    handleSaveAuction(auction.id);\n                                                },\n                                                disabled: loading,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                    className: \"h-4 w-4 \".concat(savedAuctions.has(auction.id) ? \"fill-current\" : \"\")\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                    lineNumber: 826,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                lineNumber: 816,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                variant: \"outline\",\n                                                size: \"icon\",\n                                                onClick: (e)=>{\n                                                    e.preventDefault();\n                                                    e.stopPropagation();\n                                                    handleViewAuction(auction.id);\n                                                },\n                                                disabled: loading,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                    lineNumber: 838,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                lineNumber: 828,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 803,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                lineNumber: 773,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, auction.id, true, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                        lineNumber: 742,\n                        columnNumber: 13\n                    }, this))\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                lineNumber: 740,\n                columnNumber: 9\n            }, this),\n            !loading && (!Array.isArray(filteredAuctions) || filteredAuctions.length === 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                    className: \"py-16 text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col items-center gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-16 h-16 rounded-full bg-gray-100 flex items-center justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                    className: \"h-8 w-8 text-gray-400\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 852,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                lineNumber: 851,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold mb-2\",\n                                        children: searchTerm ? \"لا توجد مزادات تطابق البحث\" : \"لا توجد مزادات متاحة حالياً\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 855,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-muted-foreground\",\n                                        children: searchTerm ? \"جرب البحث بكلمات مختلفة أو امسح مربع البحث لعرض جميع المزادات\" : \"سيتم عرض المزادات المتاحة هنا عند إضافتها من قبل الشركات\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 858,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                lineNumber: 854,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                        lineNumber: 850,\n                        columnNumber: 15\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                    lineNumber: 849,\n                    columnNumber: 13\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                lineNumber: 848,\n                columnNumber: 11\n            }, this),\n            loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                    className: \"py-16 text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col items-center gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                lineNumber: 875,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-muted-foreground\",\n                                children: \"جاري تحميل المزادات...\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                lineNumber: 876,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                        lineNumber: 874,\n                        columnNumber: 15\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                    lineNumber: 873,\n                    columnNumber: 13\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                lineNumber: 872,\n                columnNumber: 11\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__.Dialog, {\n                open: showBidModal,\n                onOpenChange: setShowBidModal,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__.DialogContent, {\n                    className: \"sm:max-w-md\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__.DialogHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__.DialogTitle, {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 887,\n                                        columnNumber: 17\n                                    }, this),\n                                    \"تقديم مزايدة\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                lineNumber: 886,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                            lineNumber: 885,\n                            columnNumber: 13\n                        }, this),\n                        selectedAuction && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gray-50 p-4 rounded-lg\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"font-semibold mb-2\",\n                                            children: selectedAuction.title\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                            lineNumber: 895,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-4 text-sm text-gray-600\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                            lineNumber: 898,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: [\n                                                                \"المزايدة الحالية: \",\n                                                                formatAmount(selectedAuction.currentBid || 0)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                            lineNumber: 899,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                    lineNumber: 897,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                            lineNumber: 902,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: [\n                                                                selectedAuction.bidsCount || 0,\n                                                                \" مزايدة\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                            lineNumber: 903,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                    lineNumber: 901,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                            lineNumber: 896,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 894,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                            htmlFor: \"bidAmount\",\n                                            children: [\n                                                \"مقدار المزايدة (\",\n                                                userCurrency,\n                                                \")\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                            lineNumber: 909,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                            id: \"bidAmount\",\n                                            type: \"number\",\n                                            value: bidAmount,\n                                            onChange: (e)=>setBidAmount(e.target.value),\n                                            placeholder: \"أدخل مقدار المزايدة\",\n                                            min: (selectedAuction.currentBid || 0) + 1\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                            lineNumber: 910,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-gray-500\",\n                                            children: [\n                                                \"الحد الأدنى: \",\n                                                formatAmount((selectedAuction.currentBid || 0) + 1)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                            lineNumber: 918,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 908,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                            lineNumber: 893,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__.DialogFooter, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"outline\",\n                                    onClick: ()=>{\n                                        setShowBidModal(false);\n                                        setBidAmount(\"\");\n                                        setSelectedAuction(null);\n                                    },\n                                    disabled: bidLoading,\n                                    children: \"إلغاء\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 926,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    onClick: submitBid,\n                                    disabled: bidLoading || !bidAmount,\n                                    className: \"bg-green-600 hover:bg-green-700\",\n                                    children: bidLoading ? \"جاري التقديم...\" : \"تقديم المزايدة\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 937,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                            lineNumber: 925,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                    lineNumber: 884,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                lineNumber: 883,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n        lineNumber: 601,\n        columnNumber: 5\n    }, this);\n}\n_s(UserAuctionsPage, \"0C/dfCNanwYtXVZSEIcjKhB8Lbk=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_9__.useRouter,\n        _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_10__.useToast,\n        _contexts_CurrencyContext__WEBPACK_IMPORTED_MODULE_11__.useCurrency\n    ];\n});\n_c = UserAuctionsPage;\nvar _c;\n$RefreshReg$(_c, \"UserAuctionsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/user/auctions/page.tsx\n"));

/***/ })

});