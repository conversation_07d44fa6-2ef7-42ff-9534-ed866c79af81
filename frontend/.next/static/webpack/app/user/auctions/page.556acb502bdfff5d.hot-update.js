"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/user/auctions/page",{

/***/ "(app-pages-browser)/./lib/currencyService.ts":
/*!********************************!*\
  !*** ./lib/currencyService.ts ***!
  \********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   currencyService: function() { return /* binding */ currencyService; }\n/* harmony export */ });\n// Frontend Currency Service\nclass CurrencyService {\n    // Get current exchange rates\n    async getExchangeRates() {\n        try {\n            // Check if cache is still valid\n            if (this.lastUpdated && Date.now() - this.lastUpdated < this.CACHE_DURATION) {\n                return this.exchangeRates;\n            }\n            // Try to fetch fresh rates from backend\n            try {\n                const response = await fetch(\"http://localhost:5000/api/currency/rates\");\n                if (response.ok) {\n                    const data = await response.json();\n                    if (data.success && data.data && data.data.rates) {\n                        this.exchangeRates = data.data.rates;\n                        this.lastUpdated = Date.now();\n                        console.log(\"✅ Currency rates updated from backend\");\n                    }\n                }\n            } catch (apiError) {\n                console.warn(\"⚠️ Failed to fetch fresh currency rates, using cached rates:\", apiError);\n            }\n            return this.exchangeRates;\n        } catch (error) {\n            console.error(\"❌ Currency service error:\", error);\n            return this.exchangeRates; // Return cached rates as fallback\n        }\n    }\n    // Convert amount from one currency to another\n    async convertCurrency(amount, fromCurrency, toCurrency) {\n        try {\n            if (fromCurrency === toCurrency) {\n                return amount;\n            }\n            const rates = await this.getExchangeRates();\n            // Convert to USD first, then to target currency\n            const usdAmount = amount / rates[fromCurrency];\n            const convertedAmount = usdAmount * rates[toCurrency];\n            return Math.round(convertedAmount * 100) / 100; // Round to 2 decimal places\n        } catch (error) {\n            console.error(\"❌ Currency conversion error:\", error);\n            throw new Error(\"Currency conversion failed\");\n        }\n    }\n    // Convert amount to USD (base currency for comparisons)\n    async convertToUSD(amount, fromCurrency) {\n        return this.convertCurrency(amount, fromCurrency, \"USD\");\n    }\n    // Convert amount from USD to target currency\n    async convertFromUSD(amount, toCurrency) {\n        return this.convertCurrency(amount, \"USD\", toCurrency);\n    }\n    // Get currency symbol\n    getCurrencySymbol(currency) {\n        const symbols = {\n            USD: \"$\",\n            SAR: \"ر.س\",\n            EUR: \"€\",\n            GBP: \"\\xa3\",\n            AED: \"د.إ\",\n            KWD: \"د.ك\",\n            QAR: \"ر.ق\",\n            BHD: \"د.ب\",\n            OMR: \"ر.ع\",\n            JOD: \"د.أ\"\n        };\n        return symbols[currency] || currency;\n    }\n    // Format amount with currency\n    formatAmount(amount, currency) {\n        // Handle null/undefined amounts\n        if (amount === null || amount === undefined || isNaN(amount)) {\n            amount = 0;\n        }\n        const symbol = this.getCurrencySymbol(currency);\n        const formattedAmount = amount.toLocaleString();\n        // For Arabic currencies, put symbol after number\n        if ([\n            \"SAR\",\n            \"AED\",\n            \"KWD\",\n            \"QAR\",\n            \"BHD\",\n            \"OMR\"\n        ].includes(currency)) {\n            return \"\".concat(formattedAmount, \" \").concat(symbol);\n        }\n        // For Western currencies, put symbol before number\n        return \"\".concat(symbol).concat(formattedAmount);\n    }\n    // Get supported currencies\n    getSupportedCurrencies() {\n        return [\n            {\n                code: \"USD\",\n                name: \"US Dollar\",\n                symbol: \"$\"\n            },\n            {\n                code: \"SAR\",\n                name: \"Saudi Riyal\",\n                symbol: \"ر.س\"\n            },\n            {\n                code: \"EUR\",\n                name: \"Euro\",\n                symbol: \"€\"\n            },\n            {\n                code: \"GBP\",\n                name: \"British Pound\",\n                symbol: \"\\xa3\"\n            },\n            {\n                code: \"AED\",\n                name: \"UAE Dirham\",\n                symbol: \"د.إ\"\n            },\n            {\n                code: \"KWD\",\n                name: \"Kuwaiti Dinar\",\n                symbol: \"د.ك\"\n            },\n            {\n                code: \"QAR\",\n                name: \"Qatari Riyal\",\n                symbol: \"ر.ق\"\n            },\n            {\n                code: \"BHD\",\n                name: \"Bahraini Dinar\",\n                symbol: \"د.ب\"\n            },\n            {\n                code: \"OMR\",\n                name: \"Omani Rial\",\n                symbol: \"ر.ع\"\n            }\n        ];\n    }\n    // Validate currency code\n    isValidCurrency(currency) {\n        const supportedCurrencies = [\n            \"USD\",\n            \"SAR\",\n            \"EUR\",\n            \"GBP\",\n            \"AED\",\n            \"KWD\",\n            \"QAR\",\n            \"BHD\",\n            \"OMR\",\n            \"JOD\"\n        ];\n        return supportedCurrencies.includes(currency);\n    }\n    // Get user's preferred currency from localStorage or default\n    getUserCurrency() {\n        try {\n            return localStorage.getItem(\"preferredCurrency\") || \"SAR\";\n        } catch (e) {\n            return \"SAR\";\n        }\n    }\n    // Set user's preferred currency\n    setUserCurrency(currency) {\n        try {\n            if (this.isValidCurrency(currency)) {\n                localStorage.setItem(\"preferredCurrency\", currency);\n            }\n        } catch (error) {\n            console.error(\"Failed to save currency preference:\", error);\n        }\n    }\n    // Convert auction data to user's preferred currency\n    async convertAuctionData(auction, userCurrency) {\n        const targetCurrency = userCurrency || this.getUserCurrency();\n        if (!auction.currency || auction.currency === targetCurrency) {\n            return auction;\n        }\n        try {\n            const convertedAuction = {\n                ...auction\n            };\n            // Convert prices\n            if (auction.currentBid) {\n                convertedAuction.currentBid = await this.convertCurrency(auction.currentBid, auction.currency, targetCurrency);\n            }\n            if (auction.startingPrice) {\n                convertedAuction.startingPrice = await this.convertCurrency(auction.startingPrice, auction.currency, targetCurrency);\n            }\n            if (auction.reservePrice) {\n                convertedAuction.reservePrice = await this.convertCurrency(auction.reservePrice, auction.currency, targetCurrency);\n            }\n            // Update currency\n            convertedAuction.currency = targetCurrency;\n            convertedAuction.originalCurrency = auction.currency;\n            return convertedAuction;\n        } catch (error) {\n            console.error(\"Failed to convert auction data:\", error);\n            return auction; // Return original data if conversion fails\n        }\n    }\n    constructor(){\n        this.exchangeRates = {\n            USD: 1.0,\n            SAR: 3.75,\n            EUR: 0.85,\n            GBP: 0.73,\n            AED: 3.67,\n            KWD: 0.30,\n            QAR: 3.64,\n            BHD: 0.38,\n            OMR: 0.38\n        };\n        this.lastUpdated = null;\n        this.CACHE_DURATION = 60 * 60 * 1000 // 1 hour\n        ;\n    }\n}\nconst currencyService = new CurrencyService();\n/* harmony default export */ __webpack_exports__[\"default\"] = (currencyService);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/currencyService.ts\n"));

/***/ })

});