"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/user/auctions/page",{

/***/ "(app-pages-browser)/./app/user/auctions/page.tsx":
/*!************************************!*\
  !*** ./app/user/auctions/page.tsx ***!
  \************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ UserAuctionsPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./components/ui/dialog.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./components/ui/label.tsx\");\n/* harmony import */ var _barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,DollarSign,Eye,Gavel,Heart,Pause,Play,RefreshCw,Search,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,DollarSign,Eye,Gavel,Heart,Pause,Play,RefreshCw,Search,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pause.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,DollarSign,Eye,Gavel,Heart,Pause,Play,RefreshCw,Search,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,DollarSign,Eye,Gavel,Heart,Pause,Play,RefreshCw,Search,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,DollarSign,Eye,Gavel,Heart,Pause,Play,RefreshCw,Search,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/gavel.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,DollarSign,Eye,Gavel,Heart,Pause,Play,RefreshCw,Search,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,DollarSign,Eye,Gavel,Heart,Pause,Play,RefreshCw,Search,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,DollarSign,Eye,Gavel,Heart,Pause,Play,RefreshCw,Search,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,DollarSign,Eye,Gavel,Heart,Pause,Play,RefreshCw,Search,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,DollarSign,Eye,Gavel,Heart,Pause,Play,RefreshCw,Search,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./lib/api.ts\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/use-toast */ \"(app-pages-browser)/./components/ui/use-toast.tsx\");\n/* harmony import */ var _lib_currencyService__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/lib/currencyService */ \"(app-pages-browser)/./lib/currencyService.ts\");\n/* harmony import */ var _contexts_CurrencyContext__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/contexts/CurrencyContext */ \"(app-pages-browser)/./contexts/CurrencyContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction UserAuctionsPage() {\n    _s();\n    const [auctions, setAuctions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [filteredAuctions, setFilteredAuctions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [savedAuctions, setSavedAuctions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Bidding modal state\n    const [showBidModal, setShowBidModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedAuction, setSelectedAuction] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [bidAmount, setBidAmount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [bidLoading, setBidLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Filter and sort state\n    const [sortBy, setSortBy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"endDate\");\n    const [filterCategory, setFilterCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // Auto-refresh state\n    const [autoRefresh, setAutoRefresh] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [lastRefresh, setLastRefresh] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Date());\n    // Optimistic locking\n    const [auctionVersions, setAuctionVersions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_9__.useRouter)();\n    const { toast } = (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_10__.useToast)();\n    const { userCurrency, formatAmount, convertAmount } = (0,_contexts_CurrencyContext__WEBPACK_IMPORTED_MODULE_12__.useCurrency)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Get user data and check permissions\n        const userData = localStorage.getItem(\"user\");\n        if (userData) {\n            const parsedUser = JSON.parse(userData);\n            setUser(parsedUser);\n            // Redirect admins to their proper dashboard\n            if (parsedUser.role === \"admin\" || parsedUser.role === \"super_admin\") {\n                toast({\n                    title: \"وصول غير مسموح\",\n                    description: \"المديرون يجب أن يستخدموا لوحة إدارة المزادات\",\n                    variant: \"destructive\"\n                });\n                router.push(\"/admin/auctions\");\n                return;\n            }\n            // Redirect government users to their dashboard\n            if (parsedUser.role === \"government\") {\n                toast({\n                    title: \"وصول غير مسموح\",\n                    description: \"الجهات الحكومية تستخدم نظام المناقصات\",\n                    variant: \"destructive\"\n                });\n                router.push(\"/government/dashboard\");\n                return;\n            }\n        }\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Load data\n        loadAuctions();\n        loadSavedAuctions();\n    }, []);\n    // Load auctions when currency changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (userCurrency) {\n            loadAuctions();\n        }\n    }, [\n        userCurrency\n    ]);\n    // Auto-refresh auctions every 30 seconds\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!autoRefresh) return;\n        const interval = setInterval(async ()=>{\n            try {\n                await loadAuctions(false) // Silent refresh without loading state\n                ;\n                setLastRefresh(new Date());\n                console.log(\"\\uD83D\\uDD04 Auto-refreshed auction data\");\n            } catch (error) {\n                console.error(\"Auto-refresh failed:\", error);\n            }\n        }, 30000) // 30 seconds\n        ;\n        return ()=>clearInterval(interval);\n    }, [\n        autoRefresh\n    ]);\n    const loadAuctions = async function() {\n        let showLoading = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : true;\n        try {\n            if (showLoading) {\n                setLoading(true);\n            }\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_8__.auctionAPI.getAll();\n            console.log(\"Auctions API response:\", response.data);\n            // Handle the API response structure properly\n            let auctionData = [];\n            if (response.data && response.data.success) {\n                var _response_data_data;\n                // If the response has a success field, extract the auctions from data.data.auctions\n                auctionData = ((_response_data_data = response.data.data) === null || _response_data_data === void 0 ? void 0 : _response_data_data.auctions) || [];\n            } else if (response.data && Array.isArray(response.data)) {\n                // If response.data is directly an array\n                auctionData = response.data;\n            } else if (response.data && response.data.data && Array.isArray(response.data.data)) {\n                // If the auctions are in response.data.data\n                auctionData = response.data.data;\n            }\n            // Transform and convert currency for each auction\n            const transformedData = await Promise.all(auctionData.map(async (auction)=>{\n                var _auction_bids, _auction_seller_profile, _auction_seller, _auction_seller_profile1, _auction_seller1, _auction_seller2, _auction_seller3;\n                const baseAuction = {\n                    id: auction._id || auction.id,\n                    title: auction.title,\n                    description: auction.description,\n                    currentBid: auction.currentPrice || auction.currentBid || auction.startingPrice || 0,\n                    endDate: auction.endDate,\n                    bidsCount: ((_auction_bids = auction.bids) === null || _auction_bids === void 0 ? void 0 : _auction_bids.length) || auction.bidsCount || 0,\n                    seller: ((_auction_seller = auction.seller) === null || _auction_seller === void 0 ? void 0 : (_auction_seller_profile = _auction_seller.profile) === null || _auction_seller_profile === void 0 ? void 0 : _auction_seller_profile.fullName) || ((_auction_seller1 = auction.seller) === null || _auction_seller1 === void 0 ? void 0 : (_auction_seller_profile1 = _auction_seller1.profile) === null || _auction_seller_profile1 === void 0 ? void 0 : _auction_seller_profile1.companyName) || ((_auction_seller2 = auction.seller) === null || _auction_seller2 === void 0 ? void 0 : _auction_seller2.name) || ((_auction_seller3 = auction.seller) === null || _auction_seller3 === void 0 ? void 0 : _auction_seller3._id) || \"غير محدد\",\n                    category: auction.category,\n                    status: auction.status,\n                    views: auction.views || 0,\n                    location: auction.location,\n                    currency: auction.currency || \"SAR\",\n                    version: auction.version || 0\n                };\n                // Store version for optimistic locking\n                setAuctionVersions((prev)=>({\n                        ...prev,\n                        [baseAuction.id]: baseAuction.version\n                    }));\n                // Convert currency if needed\n                try {\n                    if (baseAuction.currency !== userCurrency) {\n                        const convertedBid = await _lib_currencyService__WEBPACK_IMPORTED_MODULE_11__[\"default\"].convertCurrency(baseAuction.currentBid, baseAuction.currency, userCurrency);\n                        return {\n                            ...baseAuction,\n                            currentBid: Math.round(convertedBid),\n                            originalCurrency: baseAuction.currency,\n                            currency: userCurrency\n                        };\n                    }\n                } catch (conversionError) {\n                    console.warn(\"Currency conversion failed for auction:\", baseAuction.id, conversionError);\n                }\n                return baseAuction;\n            }));\n            setAuctions(transformedData);\n            setFilteredAuctions(transformedData);\n            console.log(\"✅ Set auctions and filteredAuctions:\", transformedData.length);\n        } catch (error) {\n            console.error(\"Error loading auctions:\", error);\n            setAuctions([]);\n            setFilteredAuctions([]);\n            toast({\n                title: \"فشل في تحميل المزادات\",\n                description: \"حدث خطأ أثناء تحميل المزادات. يرجى المحاولة مرة أخرى.\",\n                variant: \"destructive\"\n            });\n        } finally{\n            if (showLoading) {\n                setLoading(false);\n            }\n            console.log(\"\\uD83C\\uDFC1 Loading finished. Auctions loaded:\", auctions.length);\n        }\n    };\n    const loadSavedAuctions = async ()=>{\n        try {\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_8__.favoritesAPI.getFavorites({\n                type: \"auction\"\n            });\n            // Handle different response structures\n            let favoritesData = [];\n            if (response.data && response.data.success) {\n                favoritesData = response.data.data || [];\n            } else if (response.data && Array.isArray(response.data)) {\n                favoritesData = response.data;\n            } else if (response.data && response.data.favorites && Array.isArray(response.data.favorites)) {\n                favoritesData = response.data.favorites;\n            }\n            // Check if favoritesData is an array before mapping\n            if (Array.isArray(favoritesData)) {\n                const savedIds = new Set(favoritesData.map((fav)=>fav.itemId || fav._id || fav.id));\n                setSavedAuctions(savedIds);\n            } else {\n                console.warn(\"Favorites data is not an array:\", favoritesData);\n                setSavedAuctions(new Set());\n            }\n        } catch (error) {\n            console.error(\"Error loading saved auctions:\", error);\n            // Don't show error toast for favorites as it's not critical\n            setSavedAuctions(new Set());\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Ensure auctions is an array before filtering\n        if (Array.isArray(auctions)) {\n            let filtered = auctions.filter((auction)=>{\n                var _auction_title, _auction_description;\n                const matchesSearch = ((_auction_title = auction.title) === null || _auction_title === void 0 ? void 0 : _auction_title.toLowerCase().includes(searchTerm.toLowerCase())) || ((_auction_description = auction.description) === null || _auction_description === void 0 ? void 0 : _auction_description.toLowerCase().includes(searchTerm.toLowerCase()));\n                const matchesCategory = !filterCategory || auction.category === filterCategory;\n                return matchesSearch && matchesCategory;\n            });\n            // Sort the filtered results\n            filtered = filtered.sort((a, b)=>{\n                switch(sortBy){\n                    case \"endDate\":\n                        return new Date(a.endDate).getTime() - new Date(b.endDate).getTime();\n                    case \"currentBid\":\n                        return (b.currentBid || 0) - (a.currentBid || 0);\n                    case \"bidsCount\":\n                        return (b.bidsCount || 0) - (a.bidsCount || 0);\n                    case \"title\":\n                        return a.title.localeCompare(b.title);\n                    default:\n                        return 0;\n                }\n            });\n            setFilteredAuctions(filtered);\n            console.log(\"\\uD83D\\uDD0D Filtered auctions:\", filtered.length, \"from\", auctions.length);\n        } else {\n            setFilteredAuctions([]);\n        }\n    }, [\n        searchTerm,\n        auctions,\n        filterCategory,\n        sortBy\n    ]);\n    const getTimeRemaining = (endDate)=>{\n        const now = new Date();\n        const end = new Date(endDate);\n        const diff = end.getTime() - now.getTime();\n        if (diff <= 0) return \"انتهى\";\n        const hours = Math.floor(diff / (1000 * 60 * 60));\n        const days = Math.floor(hours / 24);\n        if (days > 0) return \"\".concat(days, \" يوم\");\n        return \"\".concat(hours, \" ساعة\");\n    };\n    // Check if user can bid\n    const canUserBid = (auction)=>{\n        var _user_profile;\n        if (!user) return false;\n        // Only individuals and companies can bid\n        if (user.role !== \"individual\" && user.role !== \"company\") {\n            return false;\n        }\n        // Companies cannot bid on their own auctions\n        if (user.role === \"company\" && auction.seller === ((_user_profile = user.profile) === null || _user_profile === void 0 ? void 0 : _user_profile.companyName)) {\n            return false;\n        }\n        return true;\n    };\n    const handleBid = async (auctionId)=>{\n        console.log(\"\\uD83C\\uDFAF Bid button clicked for auction:\", auctionId);\n        try {\n            var _freshAuction_bids, _freshAuction_seller_profile, _freshAuction_seller, _freshAuction_seller_profile1, _freshAuction_seller1, _freshAuction_seller2;\n            // Fetch fresh auction data before opening modal\n            setLoading(true);\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_8__.auctionAPI.getById(auctionId);\n            let freshAuction = null;\n            if (response.data && response.data.success) {\n                freshAuction = response.data.data.auction;\n            } else if (response.data && response.data.auction) {\n                freshAuction = response.data.auction;\n            }\n            if (!freshAuction) {\n                toast({\n                    title: \"خطأ\",\n                    description: \"لم يتم العثور على المزاد\",\n                    variant: \"destructive\"\n                });\n                return;\n            }\n            // Transform fresh data to match our format\n            const transformedAuction = {\n                id: freshAuction._id || freshAuction.id,\n                title: freshAuction.title,\n                description: freshAuction.description,\n                currentBid: freshAuction.currentPrice || freshAuction.currentBid || freshAuction.startingPrice || 0,\n                endDate: freshAuction.endDate,\n                bidsCount: ((_freshAuction_bids = freshAuction.bids) === null || _freshAuction_bids === void 0 ? void 0 : _freshAuction_bids.length) || freshAuction.bidsCount || 0,\n                seller: ((_freshAuction_seller = freshAuction.seller) === null || _freshAuction_seller === void 0 ? void 0 : (_freshAuction_seller_profile = _freshAuction_seller.profile) === null || _freshAuction_seller_profile === void 0 ? void 0 : _freshAuction_seller_profile.fullName) || ((_freshAuction_seller1 = freshAuction.seller) === null || _freshAuction_seller1 === void 0 ? void 0 : (_freshAuction_seller_profile1 = _freshAuction_seller1.profile) === null || _freshAuction_seller_profile1 === void 0 ? void 0 : _freshAuction_seller_profile1.companyName) || ((_freshAuction_seller2 = freshAuction.seller) === null || _freshAuction_seller2 === void 0 ? void 0 : _freshAuction_seller2.name) || \"غير محدد\",\n                category: freshAuction.category,\n                status: freshAuction.status\n            };\n            // Check if user can bid\n            if (!canUserBid(transformedAuction)) {\n                if ((user === null || user === void 0 ? void 0 : user.role) === \"admin\" || (user === null || user === void 0 ? void 0 : user.role) === \"super_admin\") {\n                    toast({\n                        title: \"وصول غير مسموح\",\n                        description: \"المديرون لا يمكنهم المزايدة على المزادات\",\n                        variant: \"destructive\"\n                    });\n                } else if ((user === null || user === void 0 ? void 0 : user.role) === \"government\") {\n                    toast({\n                        title: \"وصول غير مسموح\",\n                        description: \"الجهات الحكومية لا تزايد على المزادات\",\n                        variant: \"destructive\"\n                    });\n                } else if ((user === null || user === void 0 ? void 0 : user.role) === \"company\") {\n                    toast({\n                        title: \"وصول غير مسموح\",\n                        description: \"لا يمكنك المزايدة على مزادك الخاص\",\n                        variant: \"destructive\"\n                    });\n                } else {\n                    toast({\n                        title: \"وصول غير مسموح\",\n                        description: \"غير مسموح لك بالمزايدة\",\n                        variant: \"destructive\"\n                    });\n                }\n                return;\n            }\n            // Open bidding modal with fresh data\n            setSelectedAuction(transformedAuction);\n            setBidAmount(String((transformedAuction.currentBid || 0) + 1000));\n            setShowBidModal(true);\n            toast({\n                title: \"\\uD83D\\uDCCA تم تحديث بيانات المزاد\",\n                description: \"المزايدة الحالية: \".concat((transformedAuction.currentBid || 0).toLocaleString(), \" ر.س\")\n            });\n        } catch (error) {\n            console.error(\"Error fetching fresh auction data:\", error);\n            toast({\n                title: \"خطأ في تحديث البيانات\",\n                description: \"سيتم استخدام البيانات المحفوظة محلياً\",\n                variant: \"destructive\"\n            });\n            // Fallback to cached data\n            const currentAuction = auctions.find((a)=>a.id === auctionId);\n            if (currentAuction && canUserBid(currentAuction)) {\n                setSelectedAuction(currentAuction);\n                setBidAmount(String((currentAuction.currentBid || 0) + 1000));\n                setShowBidModal(true);\n            }\n        } finally{\n            setLoading(false);\n        }\n    };\n    const submitBid = async ()=>{\n        if (!selectedAuction) return;\n        const bidAmountNum = parseFloat(bidAmount);\n        if (isNaN(bidAmountNum) || bidAmountNum <= (selectedAuction.currentBid || 0)) {\n            toast({\n                title: \"مزايدة غير صالحة\",\n                description: \"يجب أن تكون المزايدة أكبر من المزايدة الحالية\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        try {\n            setBidLoading(true);\n            // Get auction version for optimistic locking\n            const auctionVersion = auctionVersions[selectedAuction.id];\n            // Prepare bid data with currency and version\n            const bidData = {\n                bidAmount: bidAmountNum,\n                currency: userCurrency,\n                version: auctionVersion\n            };\n            // Submit bid and wait for server response\n            const response = await fetch(\"/api/auctions/\".concat(selectedAuction.id, \"/bid\"), {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    \"Authorization\": \"Bearer \".concat(localStorage.getItem(\"token\"))\n                },\n                body: JSON.stringify(bidData)\n            });\n            const result = await response.json();\n            if (!response.ok) {\n                // Handle specific error cases\n                if (response.status === 409) {\n                    // Optimistic locking conflict\n                    toast({\n                        title: \"تم تحديث المزاد ⚠️\",\n                        description: \"قام شخص آخر بتحديث المزاد. سيتم تحديث البيانات.\",\n                        variant: \"destructive\"\n                    });\n                    await loadAuctions();\n                    setShowBidModal(false);\n                    setBidAmount(\"\");\n                    setSelectedAuction(null);\n                    return;\n                }\n                throw new Error(result.message || \"Bid failed\");\n            }\n            // Close modal first\n            setShowBidModal(false);\n            setBidAmount(\"\");\n            setSelectedAuction(null);\n            // Reload fresh data from server\n            await loadAuctions();\n            // Show success message with currency formatting\n            const formattedAmount = _lib_currencyService__WEBPACK_IMPORTED_MODULE_11__[\"default\"].formatAmount(bidAmountNum, userCurrency);\n            toast({\n                title: \"تم تقديم المزايدة بنجاح! \\uD83C\\uDF89\",\n                description: \"مقدار المزايدة: \".concat(formattedAmount)\n            });\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error(\"Error placing bid:\", error);\n            // Close modal on error too\n            setShowBidModal(false);\n            setBidAmount(\"\");\n            setSelectedAuction(null);\n            // Show specific error message\n            const errorMessage = ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"يرجى المحاولة مرة أخرى\";\n            if (errorMessage.includes(\"higher than current price\") || errorMessage.includes(\"أكبر من السعر الحالي\")) {\n                toast({\n                    title: \"مزايدة منخفضة ⚠️\",\n                    description: \"قام شخص آخر بمزايدة أعلى. يرجى المحاولة بمبلغ أكبر.\",\n                    variant: \"destructive\"\n                });\n            } else if (errorMessage.includes(\"ended\") || errorMessage.includes(\"انتهى\")) {\n                toast({\n                    title: \"انتهى المزاد ⏰\",\n                    description: \"لقد انتهى هذا المزاد ولا يمكن قبول مزايدات جديدة.\",\n                    variant: \"destructive\"\n                });\n            } else {\n                toast({\n                    title: \"فشل في تقديم المزايدة\",\n                    description: errorMessage,\n                    variant: \"destructive\"\n                });\n            }\n            // Always reload data on error to sync with server\n            await loadAuctions();\n        } finally{\n            setBidLoading(false);\n        }\n    };\n    const handleSaveAuction = async (auctionId)=>{\n        console.log(\"❤️ Save button clicked for auction:\", auctionId);\n        // Show immediate feedback that button was clicked\n        toast({\n            title: \"\\uD83D\\uDC86 تم الضغط على زر الحفظ\",\n            description: \"جاري معالجة الطلب...\"\n        });\n        try {\n            const auction = auctions.find((a)=>a.id === auctionId);\n            if (!auction) {\n                toast({\n                    title: \"خطأ\",\n                    description: \"لم يتم العثور على المزاد\",\n                    variant: \"destructive\"\n                });\n                return;\n            }\n            const isCurrentlySaved = savedAuctions.has(auctionId);\n            // Optimistic UI update\n            setSavedAuctions((prev)=>{\n                const newSet = new Set(prev);\n                if (isCurrentlySaved) {\n                    newSet.delete(auctionId);\n                } else {\n                    newSet.add(auctionId);\n                }\n                return newSet;\n            });\n            if (isCurrentlySaved) {\n                // Remove from favorites\n                await _lib_api__WEBPACK_IMPORTED_MODULE_8__.favoritesAPI.removeFavorite(\"auction\", auctionId);\n                toast({\n                    title: \"❤️ تم إزالة من المفضلة\",\n                    description: 'تم إزالة \"'.concat(auction.title, '\" من المفضلة')\n                });\n            } else {\n                // Add to favorites\n                await _lib_api__WEBPACK_IMPORTED_MODULE_8__.favoritesAPI.addFavorite({\n                    itemType: \"auction\",\n                    itemId: auctionId,\n                    notifications: {\n                        bidUpdates: true,\n                        statusChanges: true,\n                        endingSoon: true\n                    }\n                });\n                toast({\n                    title: \"\\uD83D\\uDC96 تم إضافة إلى المفضلة\",\n                    description: 'تم إضافة \"'.concat(auction.title, '\" إلى المفضلة')\n                });\n            }\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error(\"Error saving auction:\", error);\n            // Revert optimistic update on error\n            setSavedAuctions((prev)=>{\n                const newSet = new Set(prev);\n                if (savedAuctions.has(auctionId)) {\n                    newSet.add(auctionId);\n                } else {\n                    newSet.delete(auctionId);\n                }\n                return newSet;\n            });\n            toast({\n                title: \"فشل في حفظ المزاد\",\n                description: ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"يرجى المحاولة مرة أخرى\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleViewAuction = async (auctionId)=>{\n        console.log(\"\\uD83D\\uDC41️ View button clicked for auction:\", auctionId);\n        try {\n            const auction = auctions.find((a)=>a.id === auctionId);\n            if (!auction) {\n                toast({\n                    title: \"خطأ\",\n                    description: \"لم يتم العثور على المزاد\",\n                    variant: \"destructive\"\n                });\n                return;\n            }\n            // Navigate to auction detail page\n            router.push(\"/auctions/\".concat(auctionId));\n            toast({\n                title: \"\\uD83D\\uDC41️ جاري تحميل التفاصيل\",\n                description: \"سيتم عرض تفاصيل المزاد\"\n            });\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error(\"Error viewing auction:\", error);\n            toast({\n                title: \"فشل في عرض تفاصيل المزاد\",\n                description: ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"يرجى المحاولة مرة أخرى\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-3xl font-bold\",\n                                    children: \"المزادات المتاحة\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 612,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-muted-foreground\",\n                                    children: [\n                                        \"استكشف المزادات الحالية وشارك في المزايدة\",\n                                        autoRefresh && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs text-green-600 ml-2\",\n                                            children: \"• تحديث تلقائي كل 30 ثانية\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                            lineNumber: 616,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 613,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                            lineNumber: 611,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                            htmlFor: \"currency\",\n                                            className: \"text-sm font-medium\",\n                                            children: \"العملة:\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                            lineNumber: 624,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            id: \"currency\",\n                                            value: userCurrency,\n                                            onChange: async (e)=>{\n                                                const newCurrency = e.target.value;\n                                                setUserCurrency(newCurrency);\n                                                _lib_currencyService__WEBPACK_IMPORTED_MODULE_11__[\"default\"].setUserCurrency(newCurrency);\n                                                setLoading(true);\n                                                await loadAuctions();\n                                                setLoading(false);\n                                                toast({\n                                                    title: \"\\uD83D\\uDCB1 تم تغيير العملة\",\n                                                    description: \"تم التحويل إلى \".concat(_lib_currencyService__WEBPACK_IMPORTED_MODULE_11__[\"default\"].getCurrencySymbol(newCurrency))\n                                                });\n                                            },\n                                            className: \"px-2 py-1 border border-gray-300 rounded text-sm focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                            children: _lib_currencyService__WEBPACK_IMPORTED_MODULE_11__[\"default\"].getSupportedCurrencies().map((currency)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: currency.code,\n                                                    children: [\n                                                        currency.symbol,\n                                                        \" \",\n                                                        currency.code\n                                                    ]\n                                                }, currency.code, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                    lineNumber: 643,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                            lineNumber: 625,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 623,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    onClick: async ()=>{\n                                        await loadAuctions(true) // Show loading state\n                                        ;\n                                        setLastRefresh(new Date());\n                                        toast({\n                                            title: \"\\uD83D\\uDD04 تم تحديث البيانات\",\n                                            description: \"تم العثور على \".concat(filteredAuctions.length, \" مزاد متاح\")\n                                        });\n                                    },\n                                    variant: \"outline\",\n                                    disabled: loading,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"h-4 w-4 ml-2 \".concat(loading ? \"animate-spin\" : \"\")\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                            lineNumber: 661,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"تحديث\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 649,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    onClick: ()=>{\n                                        setAutoRefresh(!autoRefresh);\n                                        toast({\n                                            title: autoRefresh ? \"⏸️ تم إيقاف التحديث التلقائي\" : \"▶️ تم تشغيل التحديث التلقائي\",\n                                            description: autoRefresh ? \"يمكنك التحديث يدوياً\" : \"سيتم التحديث كل 30 ثانية\"\n                                        });\n                                    },\n                                    variant: autoRefresh ? \"default\" : \"outline\",\n                                    size: \"icon\",\n                                    children: autoRefresh ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 675,\n                                        columnNumber: 32\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 675,\n                                        columnNumber: 64\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 664,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                            lineNumber: 622,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                    lineNumber: 610,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                lineNumber: 609,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                    className: \"pt-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        className: \"absolute right-3 top-3 h-4 w-4 text-muted-foreground\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 686,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                        placeholder: \"ابحث في المزادات...\",\n                                        value: searchTerm,\n                                        onChange: (e)=>setSearchTerm(e.target.value),\n                                        className: \"pr-10\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 687,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                lineNumber: 685,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col sm:flex-row gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                htmlFor: \"category\",\n                                                className: \"text-sm font-medium\",\n                                                children: \"الفئة\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                lineNumber: 697,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                id: \"category\",\n                                                value: filterCategory,\n                                                onChange: (e)=>setFilterCategory(e.target.value),\n                                                className: \"w-full mt-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"\",\n                                                        children: \"جميع الفئات\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 704,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"electronics\",\n                                                        children: \"إلكترونيات\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 705,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"vehicles\",\n                                                        children: \"سيارات\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 706,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"real_estate\",\n                                                        children: \"عقارات\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 707,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"art_collectibles\",\n                                                        children: \"فنون ومقتنيات\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 708,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"machinery\",\n                                                        children: \"معدات\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 709,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"furniture\",\n                                                        children: \"أثاث\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 710,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"jewelry\",\n                                                        children: \"مجوهرات\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 711,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"books_media\",\n                                                        children: \"كتب ووسائط\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 712,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"clothing\",\n                                                        children: \"ملابس\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 713,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"sports\",\n                                                        children: \"رياضة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 714,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"other\",\n                                                        children: \"أخرى\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 715,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                lineNumber: 698,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 696,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                htmlFor: \"sort\",\n                                                className: \"text-sm font-medium\",\n                                                children: \"ترتيب حسب\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                lineNumber: 720,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                id: \"sort\",\n                                                value: sortBy,\n                                                onChange: (e)=>setSortBy(e.target.value),\n                                                className: \"w-full mt-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"endDate\",\n                                                        children: \"تاريخ الانتهاء\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 727,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"currentBid\",\n                                                        children: \"أعلى مزايدة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 728,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"bidsCount\",\n                                                        children: \"عدد المزايدات\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 729,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"title\",\n                                                        children: \"الاسم\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 730,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                lineNumber: 721,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 719,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                lineNumber: 695,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                        lineNumber: 684,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                    lineNumber: 683,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                lineNumber: 682,\n                columnNumber: 9\n            }, this),\n            Array.isArray(filteredAuctions) && filteredAuctions.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                    className: \"text-lg\",\n                                    children: \"إجمالي المزادات\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 743,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                    className: \"text-2xl font-bold text-blue-600\",\n                                    children: filteredAuctions.length\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 744,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                            lineNumber: 742,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                        lineNumber: 741,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                    className: \"text-lg\",\n                                    children: \"المزايدات النشطة\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 751,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                    className: \"text-2xl font-bold text-green-600\",\n                                    children: filteredAuctions.reduce((sum, a)=>sum + (a.bidsCount || 0), 0)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 752,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                            lineNumber: 750,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                        lineNumber: 749,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                    className: \"text-lg\",\n                                    children: \"أعلى مزايدة\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 759,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                    className: \"text-2xl font-bold text-purple-600\",\n                                    children: filteredAuctions.length > 0 ? _lib_currencyService__WEBPACK_IMPORTED_MODULE_11__[\"default\"].formatAmount(Math.max(...filteredAuctions.map((a)=>a.currentBid || 0)), userCurrency) : _lib_currencyService__WEBPACK_IMPORTED_MODULE_11__[\"default\"].formatAmount(0, userCurrency)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 760,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                            lineNumber: 758,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                        lineNumber: 757,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                lineNumber: 740,\n                columnNumber: 11\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                children: Array.isArray(filteredAuctions) && filteredAuctions.map((auction)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        className: \"hover:shadow-lg transition-shadow cursor-pointer\",\n                        onClick: ()=>handleViewAuction(auction.id),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"aspect-video bg-gradient-to-br from-gray-100 to-gray-200 rounded-lg mb-4 flex items-center justify-center relative overflow-hidden\",\n                                        children: [\n                                            auction.images && auction.images.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: auction.images[0].url,\n                                                alt: auction.title,\n                                                className: \"w-full h-full object-cover\",\n                                                onError: (e)=>{\n                                                    e.currentTarget.style.display = \"none\";\n                                                    e.currentTarget.nextElementSibling.style.display = \"flex\";\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                lineNumber: 781,\n                                                columnNumber: 21\n                                            }, this) : null,\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-center w-full h-full text-gray-500\",\n                                                style: {\n                                                    display: auction.images && auction.images.length > 0 ? \"none\" : \"flex\"\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                            className: \"h-8 w-8 mx-auto mb-2 text-gray-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                            lineNumber: 793,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm\",\n                                                            children: \"صورة المزاد\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                            lineNumber: 794,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                    lineNumber: 792,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                lineNumber: 791,\n                                                columnNumber: 19\n                                            }, this),\n                                            auction.status === \"active\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                className: \"absolute top-2 right-2 bg-green-500\",\n                                                children: \"نشط\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                lineNumber: 798,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 779,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                        className: \"text-lg line-clamp-2\",\n                                        children: auction.title\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 803,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                        className: \"line-clamp-2\",\n                                        children: auction.description\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 804,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                lineNumber: 778,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-muted-foreground\",\n                                                        children: \"أعلى مزايدة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 811,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xl font-bold text-green-600\",\n                                                        children: [\n                                                            _lib_currencyService__WEBPACK_IMPORTED_MODULE_11__[\"default\"].formatAmount(auction.currentBid || 0, userCurrency),\n                                                            auction.originalCurrency && auction.originalCurrency !== userCurrency && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs text-gray-500 ml-2\",\n                                                                children: [\n                                                                    \"(من \",\n                                                                    auction.originalCurrency,\n                                                                    \")\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                                lineNumber: 815,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 812,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                lineNumber: 810,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-muted-foreground\",\n                                                        children: \"المزايدات\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 822,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-lg font-semibold\",\n                                                        children: auction.bidsCount || 0\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 823,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                lineNumber: 821,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 809,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                className: \"h-4 w-4 text-muted-foreground\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                lineNumber: 828,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-muted-foreground\",\n                                                children: [\n                                                    \"ينتهي خلال: \",\n                                                    getTimeRemaining(auction.endDate)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                lineNumber: 829,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 827,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-muted-foreground\",\n                                        children: [\n                                            \"البائع: \",\n                                            auction.seller\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 834,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                onClick: (e)=>{\n                                                    e.preventDefault();\n                                                    e.stopPropagation();\n                                                    handleBid(auction.id);\n                                                },\n                                                className: \"flex-1\",\n                                                disabled: loading || bidLoading,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        className: \"h-4 w-4 ml-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 848,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    bidLoading ? \"جاري المزايدة...\" : \"مزايدة\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                lineNumber: 839,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                variant: savedAuctions.has(auction.id) ? \"default\" : \"outline\",\n                                                size: \"icon\",\n                                                onClick: (e)=>{\n                                                    e.preventDefault();\n                                                    e.stopPropagation();\n                                                    handleSaveAuction(auction.id);\n                                                },\n                                                disabled: loading,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                    className: \"h-4 w-4 \".concat(savedAuctions.has(auction.id) ? \"fill-current\" : \"\")\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                    lineNumber: 861,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                lineNumber: 851,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                variant: \"outline\",\n                                                size: \"icon\",\n                                                onClick: (e)=>{\n                                                    e.preventDefault();\n                                                    e.stopPropagation();\n                                                    handleViewAuction(auction.id);\n                                                },\n                                                disabled: loading,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                    lineNumber: 873,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                lineNumber: 863,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 838,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                lineNumber: 808,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, auction.id, true, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                        lineNumber: 777,\n                        columnNumber: 13\n                    }, this))\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                lineNumber: 775,\n                columnNumber: 9\n            }, this),\n            !loading && (!Array.isArray(filteredAuctions) || filteredAuctions.length === 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                    className: \"py-16 text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col items-center gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-16 h-16 rounded-full bg-gray-100 flex items-center justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                    className: \"h-8 w-8 text-gray-400\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 887,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                lineNumber: 886,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold mb-2\",\n                                        children: searchTerm ? \"لا توجد مزادات تطابق البحث\" : \"لا توجد مزادات متاحة حالياً\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 890,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-muted-foreground\",\n                                        children: searchTerm ? \"جرب البحث بكلمات مختلفة أو امسح مربع البحث لعرض جميع المزادات\" : \"سيتم عرض المزادات المتاحة هنا عند إضافتها من قبل الشركات\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 893,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                lineNumber: 889,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                        lineNumber: 885,\n                        columnNumber: 15\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                    lineNumber: 884,\n                    columnNumber: 13\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                lineNumber: 883,\n                columnNumber: 11\n            }, this),\n            loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                    className: \"py-16 text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col items-center gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                lineNumber: 910,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-muted-foreground\",\n                                children: \"جاري تحميل المزادات...\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                lineNumber: 911,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                        lineNumber: 909,\n                        columnNumber: 15\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                    lineNumber: 908,\n                    columnNumber: 13\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                lineNumber: 907,\n                columnNumber: 11\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__.Dialog, {\n                open: showBidModal,\n                onOpenChange: setShowBidModal,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__.DialogContent, {\n                    className: \"sm:max-w-md\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__.DialogHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__.DialogTitle, {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 922,\n                                        columnNumber: 17\n                                    }, this),\n                                    \"تقديم مزايدة\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                lineNumber: 921,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                            lineNumber: 920,\n                            columnNumber: 13\n                        }, this),\n                        selectedAuction && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gray-50 p-4 rounded-lg\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"font-semibold mb-2\",\n                                            children: selectedAuction.title\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                            lineNumber: 930,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-4 text-sm text-gray-600\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                            lineNumber: 933,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: [\n                                                                \"المزايدة الحالية: \",\n                                                                (selectedAuction.currentBid || 0).toLocaleString(),\n                                                                \" ر.س\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                            lineNumber: 934,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                    lineNumber: 932,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                            lineNumber: 937,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: [\n                                                                selectedAuction.bidsCount || 0,\n                                                                \" مزايدة\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                            lineNumber: 938,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                    lineNumber: 936,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                            lineNumber: 931,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 929,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                            htmlFor: \"bidAmount\",\n                                            children: \"مقدار المزايدة (ر.س)\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                            lineNumber: 944,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                            id: \"bidAmount\",\n                                            type: \"number\",\n                                            value: bidAmount,\n                                            onChange: (e)=>setBidAmount(e.target.value),\n                                            placeholder: \"أدخل مقدار المزايدة\",\n                                            min: (selectedAuction.currentBid || 0) + 1\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                            lineNumber: 945,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-gray-500\",\n                                            children: [\n                                                \"الحد الأدنى: \",\n                                                ((selectedAuction.currentBid || 0) + 1).toLocaleString(),\n                                                \" ر.س\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                            lineNumber: 953,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 943,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                            lineNumber: 928,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__.DialogFooter, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"outline\",\n                                    onClick: ()=>{\n                                        setShowBidModal(false);\n                                        setBidAmount(\"\");\n                                        setSelectedAuction(null);\n                                    },\n                                    disabled: bidLoading,\n                                    children: \"إلغاء\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 961,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    onClick: submitBid,\n                                    disabled: bidLoading || !bidAmount,\n                                    className: \"bg-green-600 hover:bg-green-700\",\n                                    children: bidLoading ? \"جاري التقديم...\" : \"تقديم المزايدة\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 972,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                            lineNumber: 960,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                    lineNumber: 919,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                lineNumber: 918,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n        lineNumber: 608,\n        columnNumber: 5\n    }, this);\n}\n_s(UserAuctionsPage, \"0C/dfCNanwYtXVZSEIcjKhB8Lbk=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_9__.useRouter,\n        _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_10__.useToast,\n        _contexts_CurrencyContext__WEBPACK_IMPORTED_MODULE_12__.useCurrency\n    ];\n});\n_c = UserAuctionsPage;\nvar _c;\n$RefreshReg$(_c, \"UserAuctionsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/user/auctions/page.tsx\n"));

/***/ })

});