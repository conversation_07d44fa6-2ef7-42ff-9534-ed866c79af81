"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/user/auctions/page",{

/***/ "(app-pages-browser)/./app/user/auctions/page.tsx":
/*!************************************!*\
  !*** ./app/user/auctions/page.tsx ***!
  \************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ UserAuctionsPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./components/ui/dialog.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./components/ui/label.tsx\");\n/* harmony import */ var _barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,DollarSign,Eye,Gavel,Heart,Pause,Play,RefreshCw,Search,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,DollarSign,Eye,Gavel,Heart,Pause,Play,RefreshCw,Search,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pause.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,DollarSign,Eye,Gavel,Heart,Pause,Play,RefreshCw,Search,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,DollarSign,Eye,Gavel,Heart,Pause,Play,RefreshCw,Search,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,DollarSign,Eye,Gavel,Heart,Pause,Play,RefreshCw,Search,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/gavel.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,DollarSign,Eye,Gavel,Heart,Pause,Play,RefreshCw,Search,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,DollarSign,Eye,Gavel,Heart,Pause,Play,RefreshCw,Search,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,DollarSign,Eye,Gavel,Heart,Pause,Play,RefreshCw,Search,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,DollarSign,Eye,Gavel,Heart,Pause,Play,RefreshCw,Search,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,DollarSign,Eye,Gavel,Heart,Pause,Play,RefreshCw,Search,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./lib/api.ts\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/use-toast */ \"(app-pages-browser)/./components/ui/use-toast.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nfunction UserAuctionsPage() {\n    _s();\n    const [auctions, setAuctions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [filteredAuctions, setFilteredAuctions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [savedAuctions, setSavedAuctions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Bidding modal state\n    const [showBidModal, setShowBidModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedAuction, setSelectedAuction] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [bidAmount, setBidAmount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [bidLoading, setBidLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Filter and sort state\n    const [sortBy, setSortBy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"endDate\");\n    const [filterCategory, setFilterCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // Auto-refresh state\n    const [autoRefresh, setAutoRefresh] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [lastRefresh, setLastRefresh] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Date());\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_9__.useRouter)();\n    const { toast } = (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_10__.useToast)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Get user data and check permissions\n        const userData = localStorage.getItem(\"user\");\n        if (userData) {\n            const parsedUser = JSON.parse(userData);\n            setUser(parsedUser);\n            // Redirect admins to their proper dashboard\n            if (parsedUser.role === \"admin\" || parsedUser.role === \"super_admin\") {\n                toast({\n                    title: \"وصول غير مسموح\",\n                    description: \"المديرون يجب أن يستخدموا لوحة إدارة المزادات\",\n                    variant: \"destructive\"\n                });\n                router.push(\"/admin/auctions\");\n                return;\n            }\n            // Redirect government users to their dashboard\n            if (parsedUser.role === \"government\") {\n                toast({\n                    title: \"وصول غير مسموح\",\n                    description: \"الجهات الحكومية تستخدم نظام المناقصات\",\n                    variant: \"destructive\"\n                });\n                router.push(\"/government/dashboard\");\n                return;\n            }\n        }\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadAuctions();\n        loadSavedAuctions();\n    }, []);\n    // Auto-refresh auctions every 30 seconds\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!autoRefresh) return;\n        const interval = setInterval(async ()=>{\n            try {\n                await loadAuctions();\n                setLastRefresh(new Date());\n                console.log(\"\\uD83D\\uDD04 Auto-refreshed auction data\");\n            } catch (error) {\n                console.error(\"Auto-refresh failed:\", error);\n            }\n        }, 30000) // 30 seconds\n        ;\n        return ()=>clearInterval(interval);\n    }, [\n        autoRefresh\n    ]);\n    const loadAuctions = async ()=>{\n        try {\n            setLoading(true);\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_8__.auctionAPI.getAll();\n            console.log(\"Auctions API response:\", response.data);\n            // Handle the API response structure properly\n            let auctionData = [];\n            if (response.data && response.data.success) {\n                var _response_data_data;\n                // If the response has a success field, extract the auctions from data.data.auctions\n                auctionData = ((_response_data_data = response.data.data) === null || _response_data_data === void 0 ? void 0 : _response_data_data.auctions) || [];\n            } else if (response.data && Array.isArray(response.data)) {\n                // If response.data is directly an array\n                auctionData = response.data;\n            } else if (response.data && response.data.data && Array.isArray(response.data.data)) {\n                // If the auctions are in response.data.data\n                auctionData = response.data.data;\n            }\n            // Transform the data to match the expected format\n            const transformedData = auctionData.map((auction)=>{\n                var _auction_bids, _auction_seller_profile, _auction_seller, _auction_seller_profile1, _auction_seller1, _auction_seller2, _auction_seller3;\n                return {\n                    id: auction._id || auction.id,\n                    title: auction.title,\n                    description: auction.description,\n                    currentBid: auction.currentPrice || auction.currentBid || auction.startingPrice || 0,\n                    endDate: auction.endDate,\n                    bidsCount: ((_auction_bids = auction.bids) === null || _auction_bids === void 0 ? void 0 : _auction_bids.length) || auction.bidsCount || 0,\n                    seller: ((_auction_seller = auction.seller) === null || _auction_seller === void 0 ? void 0 : (_auction_seller_profile = _auction_seller.profile) === null || _auction_seller_profile === void 0 ? void 0 : _auction_seller_profile.fullName) || ((_auction_seller1 = auction.seller) === null || _auction_seller1 === void 0 ? void 0 : (_auction_seller_profile1 = _auction_seller1.profile) === null || _auction_seller_profile1 === void 0 ? void 0 : _auction_seller_profile1.companyName) || ((_auction_seller2 = auction.seller) === null || _auction_seller2 === void 0 ? void 0 : _auction_seller2.name) || ((_auction_seller3 = auction.seller) === null || _auction_seller3 === void 0 ? void 0 : _auction_seller3._id) || \"غير محدد\",\n                    category: auction.category,\n                    status: auction.status,\n                    views: auction.views || 0,\n                    location: auction.location\n                };\n            });\n            setAuctions(transformedData);\n            setFilteredAuctions(transformedData);\n        } catch (error) {\n            console.error(\"Error loading auctions:\", error);\n            setAuctions([]);\n            setFilteredAuctions([]);\n            toast({\n                title: \"فشل في تحميل المزادات\",\n                description: \"حدث خطأ أثناء تحميل المزادات. يرجى المحاولة مرة أخرى.\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    const loadSavedAuctions = async ()=>{\n        try {\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_8__.favoritesAPI.getFavorites({\n                type: \"auction\"\n            });\n            // Handle different response structures\n            let favoritesData = [];\n            if (response.data && response.data.success) {\n                favoritesData = response.data.data || [];\n            } else if (response.data && Array.isArray(response.data)) {\n                favoritesData = response.data;\n            } else if (response.data && response.data.favorites && Array.isArray(response.data.favorites)) {\n                favoritesData = response.data.favorites;\n            }\n            const savedIds = new Set(favoritesData.map((fav)=>fav.itemId || fav._id || fav.id));\n            setSavedAuctions(savedIds);\n        } catch (error) {\n            console.error(\"Error loading saved auctions:\", error);\n            // Don't show error toast for favorites as it's not critical\n            setSavedAuctions(new Set());\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Ensure auctions is an array before filtering\n        if (Array.isArray(auctions)) {\n            let filtered = auctions.filter((auction)=>{\n                var _auction_title, _auction_description;\n                const matchesSearch = ((_auction_title = auction.title) === null || _auction_title === void 0 ? void 0 : _auction_title.toLowerCase().includes(searchTerm.toLowerCase())) || ((_auction_description = auction.description) === null || _auction_description === void 0 ? void 0 : _auction_description.toLowerCase().includes(searchTerm.toLowerCase()));\n                const matchesCategory = !filterCategory || auction.category === filterCategory;\n                return matchesSearch && matchesCategory;\n            });\n            // Sort the filtered results\n            filtered = filtered.sort((a, b)=>{\n                switch(sortBy){\n                    case \"endDate\":\n                        return new Date(a.endDate).getTime() - new Date(b.endDate).getTime();\n                    case \"currentBid\":\n                        return (b.currentBid || 0) - (a.currentBid || 0);\n                    case \"bidsCount\":\n                        return (b.bidsCount || 0) - (a.bidsCount || 0);\n                    case \"title\":\n                        return a.title.localeCompare(b.title);\n                    default:\n                        return 0;\n                }\n            });\n            setFilteredAuctions(filtered);\n        } else {\n            setFilteredAuctions([]);\n        }\n    }, [\n        searchTerm,\n        auctions,\n        filterCategory,\n        sortBy\n    ]);\n    const getTimeRemaining = (endDate)=>{\n        const now = new Date();\n        const end = new Date(endDate);\n        const diff = end.getTime() - now.getTime();\n        if (diff <= 0) return \"انتهى\";\n        const hours = Math.floor(diff / (1000 * 60 * 60));\n        const days = Math.floor(hours / 24);\n        if (days > 0) return \"\".concat(days, \" يوم\");\n        return \"\".concat(hours, \" ساعة\");\n    };\n    // Check if user can bid\n    const canUserBid = (auction)=>{\n        var _user_profile;\n        if (!user) return false;\n        // Only individuals and companies can bid\n        if (user.role !== \"individual\" && user.role !== \"company\") {\n            return false;\n        }\n        // Companies cannot bid on their own auctions\n        if (user.role === \"company\" && auction.seller === ((_user_profile = user.profile) === null || _user_profile === void 0 ? void 0 : _user_profile.companyName)) {\n            return false;\n        }\n        return true;\n    };\n    const handleBid = async (auctionId)=>{\n        console.log(\"\\uD83C\\uDFAF Bid button clicked for auction:\", auctionId);\n        try {\n            var _freshAuction_bids, _freshAuction_seller_profile, _freshAuction_seller, _freshAuction_seller_profile1, _freshAuction_seller1, _freshAuction_seller2;\n            // Fetch fresh auction data before opening modal\n            setLoading(true);\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_8__.auctionAPI.getById(auctionId);\n            let freshAuction = null;\n            if (response.data && response.data.success) {\n                freshAuction = response.data.data.auction;\n            } else if (response.data && response.data.auction) {\n                freshAuction = response.data.auction;\n            }\n            if (!freshAuction) {\n                toast({\n                    title: \"خطأ\",\n                    description: \"لم يتم العثور على المزاد\",\n                    variant: \"destructive\"\n                });\n                return;\n            }\n            // Transform fresh data to match our format\n            const transformedAuction = {\n                id: freshAuction._id || freshAuction.id,\n                title: freshAuction.title,\n                description: freshAuction.description,\n                currentBid: freshAuction.currentPrice || freshAuction.currentBid || freshAuction.startingPrice || 0,\n                endDate: freshAuction.endDate,\n                bidsCount: ((_freshAuction_bids = freshAuction.bids) === null || _freshAuction_bids === void 0 ? void 0 : _freshAuction_bids.length) || freshAuction.bidsCount || 0,\n                seller: ((_freshAuction_seller = freshAuction.seller) === null || _freshAuction_seller === void 0 ? void 0 : (_freshAuction_seller_profile = _freshAuction_seller.profile) === null || _freshAuction_seller_profile === void 0 ? void 0 : _freshAuction_seller_profile.fullName) || ((_freshAuction_seller1 = freshAuction.seller) === null || _freshAuction_seller1 === void 0 ? void 0 : (_freshAuction_seller_profile1 = _freshAuction_seller1.profile) === null || _freshAuction_seller_profile1 === void 0 ? void 0 : _freshAuction_seller_profile1.companyName) || ((_freshAuction_seller2 = freshAuction.seller) === null || _freshAuction_seller2 === void 0 ? void 0 : _freshAuction_seller2.name) || \"غير محدد\",\n                category: freshAuction.category,\n                status: freshAuction.status\n            };\n            // Check if user can bid\n            if (!canUserBid(transformedAuction)) {\n                if ((user === null || user === void 0 ? void 0 : user.role) === \"admin\" || (user === null || user === void 0 ? void 0 : user.role) === \"super_admin\") {\n                    toast({\n                        title: \"وصول غير مسموح\",\n                        description: \"المديرون لا يمكنهم المزايدة على المزادات\",\n                        variant: \"destructive\"\n                    });\n                } else if ((user === null || user === void 0 ? void 0 : user.role) === \"government\") {\n                    toast({\n                        title: \"وصول غير مسموح\",\n                        description: \"الجهات الحكومية لا تزايد على المزادات\",\n                        variant: \"destructive\"\n                    });\n                } else if ((user === null || user === void 0 ? void 0 : user.role) === \"company\") {\n                    toast({\n                        title: \"وصول غير مسموح\",\n                        description: \"لا يمكنك المزايدة على مزادك الخاص\",\n                        variant: \"destructive\"\n                    });\n                } else {\n                    toast({\n                        title: \"وصول غير مسموح\",\n                        description: \"غير مسموح لك بالمزايدة\",\n                        variant: \"destructive\"\n                    });\n                }\n                return;\n            }\n            // Open bidding modal with fresh data\n            setSelectedAuction(transformedAuction);\n            setBidAmount(String((transformedAuction.currentBid || 0) + 1000));\n            setShowBidModal(true);\n            toast({\n                title: \"\\uD83D\\uDCCA تم تحديث بيانات المزاد\",\n                description: \"المزايدة الحالية: \".concat((transformedAuction.currentBid || 0).toLocaleString(), \" ر.س\")\n            });\n        } catch (error) {\n            console.error(\"Error fetching fresh auction data:\", error);\n            toast({\n                title: \"خطأ في تحديث البيانات\",\n                description: \"سيتم استخدام البيانات المحفوظة محلياً\",\n                variant: \"destructive\"\n            });\n            // Fallback to cached data\n            const currentAuction = auctions.find((a)=>a.id === auctionId);\n            if (currentAuction && canUserBid(currentAuction)) {\n                setSelectedAuction(currentAuction);\n                setBidAmount(String((currentAuction.currentBid || 0) + 1000));\n                setShowBidModal(true);\n            }\n        } finally{\n            setLoading(false);\n        }\n    };\n    const submitBid = async ()=>{\n        if (!selectedAuction) return;\n        const bidAmountNum = parseFloat(bidAmount);\n        if (isNaN(bidAmountNum) || bidAmountNum <= (selectedAuction.currentBid || 0)) {\n            toast({\n                title: \"مزايدة غير صالحة\",\n                description: \"يجب أن تكون المزايدة أكبر من المزايدة الحالية\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        try {\n            setBidLoading(true);\n            // Submit bid and wait for server response\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_8__.auctionAPI.placeBid(selectedAuction.id, bidAmountNum);\n            // Close modal first\n            setShowBidModal(false);\n            setBidAmount(\"\");\n            setSelectedAuction(null);\n            // Reload fresh data from server instead of optimistic updates\n            await loadAuctions();\n            // Show success message with actual server data\n            if (response.data && response.data.success) {\n                var _response_data_data_auction, _response_data_data;\n                const actualBidAmount = ((_response_data_data = response.data.data) === null || _response_data_data === void 0 ? void 0 : (_response_data_data_auction = _response_data_data.auction) === null || _response_data_data_auction === void 0 ? void 0 : _response_data_data_auction.currentPrice) || bidAmountNum;\n                toast({\n                    title: \"تم تقديم المزايدة بنجاح! \\uD83C\\uDF89\",\n                    description: \"مقدار المزايدة: \".concat(actualBidAmount.toLocaleString(), \" ر.س\")\n                });\n            } else {\n                toast({\n                    title: \"تم تقديم المزايدة بنجاح! \\uD83C\\uDF89\",\n                    description: \"مقدار المزايدة: \".concat(bidAmountNum.toLocaleString(), \" ر.س\")\n                });\n            }\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error(\"Error placing bid:\", error);\n            // Close modal on error too\n            setShowBidModal(false);\n            setBidAmount(\"\");\n            setSelectedAuction(null);\n            // Show specific error message\n            const errorMessage = ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"يرجى المحاولة مرة أخرى\";\n            if (errorMessage.includes(\"higher than current price\") || errorMessage.includes(\"أكبر من السعر الحالي\")) {\n                toast({\n                    title: \"مزايدة منخفضة ⚠️\",\n                    description: \"قام شخص آخر بمزايدة أعلى. يرجى المحاولة بمبلغ أكبر.\",\n                    variant: \"destructive\"\n                });\n            } else if (errorMessage.includes(\"ended\") || errorMessage.includes(\"انتهى\")) {\n                toast({\n                    title: \"انتهى المزاد ⏰\",\n                    description: \"لقد انتهى هذا المزاد ولا يمكن قبول مزايدات جديدة.\",\n                    variant: \"destructive\"\n                });\n            } else {\n                toast({\n                    title: \"فشل في تقديم المزايدة\",\n                    description: errorMessage,\n                    variant: \"destructive\"\n                });\n            }\n            // Always reload data on error to sync with server\n            await loadAuctions();\n        } finally{\n            setBidLoading(false);\n        }\n    };\n    const handleSaveAuction = async (auctionId)=>{\n        console.log(\"❤️ Save button clicked for auction:\", auctionId);\n        // Show immediate feedback that button was clicked\n        toast({\n            title: \"\\uD83D\\uDC86 تم الضغط على زر الحفظ\",\n            description: \"جاري معالجة الطلب...\"\n        });\n        try {\n            const auction = auctions.find((a)=>a.id === auctionId);\n            if (!auction) {\n                toast({\n                    title: \"خطأ\",\n                    description: \"لم يتم العثور على المزاد\",\n                    variant: \"destructive\"\n                });\n                return;\n            }\n            const isCurrentlySaved = savedAuctions.has(auctionId);\n            // Optimistic UI update\n            setSavedAuctions((prev)=>{\n                const newSet = new Set(prev);\n                if (isCurrentlySaved) {\n                    newSet.delete(auctionId);\n                } else {\n                    newSet.add(auctionId);\n                }\n                return newSet;\n            });\n            if (isCurrentlySaved) {\n                // Remove from favorites\n                await _lib_api__WEBPACK_IMPORTED_MODULE_8__.favoritesAPI.removeFavorite(\"auction\", auctionId);\n                toast({\n                    title: \"❤️ تم إزالة من المفضلة\",\n                    description: 'تم إزالة \"'.concat(auction.title, '\" من المفضلة')\n                });\n            } else {\n                // Add to favorites\n                await _lib_api__WEBPACK_IMPORTED_MODULE_8__.favoritesAPI.addFavorite({\n                    itemType: \"auction\",\n                    itemId: auctionId,\n                    notifications: {\n                        bidUpdates: true,\n                        statusChanges: true,\n                        endingSoon: true\n                    }\n                });\n                toast({\n                    title: \"\\uD83D\\uDC96 تم إضافة إلى المفضلة\",\n                    description: 'تم إضافة \"'.concat(auction.title, '\" إلى المفضلة')\n                });\n            }\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error(\"Error saving auction:\", error);\n            // Revert optimistic update on error\n            setSavedAuctions((prev)=>{\n                const newSet = new Set(prev);\n                if (savedAuctions.has(auctionId)) {\n                    newSet.add(auctionId);\n                } else {\n                    newSet.delete(auctionId);\n                }\n                return newSet;\n            });\n            toast({\n                title: \"فشل في حفظ المزاد\",\n                description: ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"يرجى المحاولة مرة أخرى\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleViewAuction = async (auctionId)=>{\n        console.log(\"\\uD83D\\uDC41️ View button clicked for auction:\", auctionId);\n        try {\n            const auction = auctions.find((a)=>a.id === auctionId);\n            if (!auction) {\n                toast({\n                    title: \"خطأ\",\n                    description: \"لم يتم العثور على المزاد\",\n                    variant: \"destructive\"\n                });\n                return;\n            }\n            // Navigate to auction detail page\n            router.push(\"/auctions/\".concat(auctionId));\n            toast({\n                title: \"\\uD83D\\uDC41️ جاري تحميل التفاصيل\",\n                description: \"سيتم عرض تفاصيل المزاد\"\n            });\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error(\"Error viewing auction:\", error);\n            toast({\n                title: \"فشل في عرض تفاصيل المزاد\",\n                description: ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"يرجى المحاولة مرة أخرى\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-3xl font-bold\",\n                                    children: \"المزادات المتاحة\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 522,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-muted-foreground\",\n                                    children: [\n                                        \"استكشف المزادات الحالية وشارك في المزايدة\",\n                                        autoRefresh && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs text-green-600 ml-2\",\n                                            children: \"• تحديث تلقائي كل 30 ثانية\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                            lineNumber: 526,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 523,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                            lineNumber: 521,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    onClick: async ()=>{\n                                        setLoading(true);\n                                        await loadAuctions();\n                                        setLastRefresh(new Date());\n                                        setLoading(false);\n                                        toast({\n                                            title: \"\\uD83D\\uDD04 تم تحديث البيانات\",\n                                            description: \"تم العثور على \".concat(filteredAuctions.length, \" مزاد متاح\")\n                                        });\n                                    },\n                                    variant: \"outline\",\n                                    disabled: loading,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"h-4 w-4 ml-2 \".concat(loading ? \"animate-spin\" : \"\")\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                            lineNumber: 547,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"تحديث\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 533,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    onClick: ()=>{\n                                        setAutoRefresh(!autoRefresh);\n                                        toast({\n                                            title: autoRefresh ? \"⏸️ تم إيقاف التحديث التلقائي\" : \"▶️ تم تشغيل التحديث التلقائي\",\n                                            description: autoRefresh ? \"يمكنك التحديث يدوياً\" : \"سيتم التحديث كل 30 ثانية\"\n                                        });\n                                    },\n                                    variant: autoRefresh ? \"default\" : \"outline\",\n                                    size: \"icon\",\n                                    children: autoRefresh ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 561,\n                                        columnNumber: 32\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 561,\n                                        columnNumber: 64\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 550,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                            lineNumber: 532,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                    lineNumber: 520,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                lineNumber: 519,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                    className: \"pt-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"absolute right-3 top-3 h-4 w-4 text-muted-foreground\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 572,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                        placeholder: \"ابحث في المزادات...\",\n                                        value: searchTerm,\n                                        onChange: (e)=>setSearchTerm(e.target.value),\n                                        className: \"pr-10\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 573,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                lineNumber: 571,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col sm:flex-row gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                htmlFor: \"category\",\n                                                className: \"text-sm font-medium\",\n                                                children: \"الفئة\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                lineNumber: 583,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                id: \"category\",\n                                                value: filterCategory,\n                                                onChange: (e)=>setFilterCategory(e.target.value),\n                                                className: \"w-full mt-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"\",\n                                                        children: \"جميع الفئات\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 590,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"electronics\",\n                                                        children: \"إلكترونيات\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 591,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"vehicles\",\n                                                        children: \"سيارات\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 592,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"real_estate\",\n                                                        children: \"عقارات\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 593,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"art_collectibles\",\n                                                        children: \"فنون ومقتنيات\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 594,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"machinery\",\n                                                        children: \"معدات\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 595,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"furniture\",\n                                                        children: \"أثاث\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 596,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"jewelry\",\n                                                        children: \"مجوهرات\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 597,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"books_media\",\n                                                        children: \"كتب ووسائط\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 598,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"clothing\",\n                                                        children: \"ملابس\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 599,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"sports\",\n                                                        children: \"رياضة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 600,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"other\",\n                                                        children: \"أخرى\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 601,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                lineNumber: 584,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 582,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                htmlFor: \"sort\",\n                                                className: \"text-sm font-medium\",\n                                                children: \"ترتيب حسب\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                lineNumber: 606,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                id: \"sort\",\n                                                value: sortBy,\n                                                onChange: (e)=>setSortBy(e.target.value),\n                                                className: \"w-full mt-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"endDate\",\n                                                        children: \"تاريخ الانتهاء\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 613,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"currentBid\",\n                                                        children: \"أعلى مزايدة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 614,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"bidsCount\",\n                                                        children: \"عدد المزايدات\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 615,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"title\",\n                                                        children: \"الاسم\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 616,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                lineNumber: 607,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 605,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                lineNumber: 581,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                        lineNumber: 570,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                    lineNumber: 569,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                lineNumber: 568,\n                columnNumber: 9\n            }, this),\n            Array.isArray(filteredAuctions) && filteredAuctions.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                    className: \"text-lg\",\n                                    children: \"إجمالي المزادات\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 629,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                    className: \"text-2xl font-bold text-blue-600\",\n                                    children: filteredAuctions.length\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 630,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                            lineNumber: 628,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                        lineNumber: 627,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                    className: \"text-lg\",\n                                    children: \"المزايدات النشطة\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 637,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                    className: \"text-2xl font-bold text-green-600\",\n                                    children: filteredAuctions.reduce((sum, a)=>sum + (a.bidsCount || 0), 0)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 638,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                            lineNumber: 636,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                        lineNumber: 635,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                    className: \"text-lg\",\n                                    children: \"أعلى مزايدة\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 645,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                    className: \"text-2xl font-bold text-purple-600\",\n                                    children: filteredAuctions.length > 0 ? Math.max(...filteredAuctions.map((a)=>a.currentBid || 0)).toLocaleString() + \" ر.س\" : \"0 ر.س\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 646,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                            lineNumber: 644,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                        lineNumber: 643,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                lineNumber: 626,\n                columnNumber: 11\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                children: Array.isArray(filteredAuctions) && filteredAuctions.map((auction)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        className: \"hover:shadow-lg transition-shadow cursor-pointer\",\n                        onClick: ()=>handleViewAuction(auction.id),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"aspect-video bg-gradient-to-br from-gray-100 to-gray-200 rounded-lg mb-4 flex items-center justify-center relative overflow-hidden\",\n                                        children: [\n                                            auction.images && auction.images.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: auction.images[0].url,\n                                                alt: auction.title,\n                                                className: \"w-full h-full object-cover\",\n                                                onError: (e)=>{\n                                                    e.currentTarget.style.display = \"none\";\n                                                    e.currentTarget.nextElementSibling.style.display = \"flex\";\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                lineNumber: 664,\n                                                columnNumber: 21\n                                            }, this) : null,\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-center w-full h-full text-gray-500\",\n                                                style: {\n                                                    display: auction.images && auction.images.length > 0 ? \"none\" : \"flex\"\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                            className: \"h-8 w-8 mx-auto mb-2 text-gray-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                            lineNumber: 676,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm\",\n                                                            children: \"صورة المزاد\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                            lineNumber: 677,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                    lineNumber: 675,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                lineNumber: 674,\n                                                columnNumber: 19\n                                            }, this),\n                                            auction.status === \"active\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                className: \"absolute top-2 right-2 bg-green-500\",\n                                                children: \"نشط\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                lineNumber: 681,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 662,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                        className: \"text-lg line-clamp-2\",\n                                        children: auction.title\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 686,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                        className: \"line-clamp-2\",\n                                        children: auction.description\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 687,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                lineNumber: 661,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-muted-foreground\",\n                                                        children: \"أعلى مزايدة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 694,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xl font-bold text-green-600\",\n                                                        children: [\n                                                            (auction.currentBid || 0).toLocaleString(),\n                                                            \" ر.س\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 695,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                lineNumber: 693,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-muted-foreground\",\n                                                        children: \"المزايدات\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 700,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-lg font-semibold\",\n                                                        children: auction.bidsCount || 0\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 701,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                lineNumber: 699,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 692,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                className: \"h-4 w-4 text-muted-foreground\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                lineNumber: 706,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-muted-foreground\",\n                                                children: [\n                                                    \"ينتهي خلال: \",\n                                                    getTimeRemaining(auction.endDate)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                lineNumber: 707,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 705,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-muted-foreground\",\n                                        children: [\n                                            \"البائع: \",\n                                            auction.seller\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 712,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                onClick: (e)=>{\n                                                    e.preventDefault();\n                                                    e.stopPropagation();\n                                                    handleBid(auction.id);\n                                                },\n                                                className: \"flex-1\",\n                                                disabled: loading || bidLoading,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                        className: \"h-4 w-4 ml-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 726,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    bidLoading ? \"جاري المزايدة...\" : \"مزايدة\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                lineNumber: 717,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                variant: savedAuctions.has(auction.id) ? \"default\" : \"outline\",\n                                                size: \"icon\",\n                                                onClick: (e)=>{\n                                                    e.preventDefault();\n                                                    e.stopPropagation();\n                                                    handleSaveAuction(auction.id);\n                                                },\n                                                disabled: loading,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    className: \"h-4 w-4 \".concat(savedAuctions.has(auction.id) ? \"fill-current\" : \"\")\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                    lineNumber: 739,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                lineNumber: 729,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                variant: \"outline\",\n                                                size: \"icon\",\n                                                onClick: (e)=>{\n                                                    e.preventDefault();\n                                                    e.stopPropagation();\n                                                    handleViewAuction(auction.id);\n                                                },\n                                                disabled: loading,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                    lineNumber: 751,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                lineNumber: 741,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 716,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                lineNumber: 691,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, auction.id, true, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                        lineNumber: 660,\n                        columnNumber: 13\n                    }, this))\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                lineNumber: 658,\n                columnNumber: 9\n            }, this),\n            !loading && (!Array.isArray(filteredAuctions) || filteredAuctions.length === 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                    className: \"py-16 text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col items-center gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-16 h-16 rounded-full bg-gray-100 flex items-center justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                    className: \"h-8 w-8 text-gray-400\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 765,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                lineNumber: 764,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold mb-2\",\n                                        children: searchTerm ? \"لا توجد مزادات تطابق البحث\" : \"لا توجد مزادات متاحة حالياً\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 768,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-muted-foreground\",\n                                        children: searchTerm ? \"جرب البحث بكلمات مختلفة أو امسح مربع البحث لعرض جميع المزادات\" : \"سيتم عرض المزادات المتاحة هنا عند إضافتها من قبل الشركات\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 771,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                lineNumber: 767,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                        lineNumber: 763,\n                        columnNumber: 15\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                    lineNumber: 762,\n                    columnNumber: 13\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                lineNumber: 761,\n                columnNumber: 11\n            }, this),\n            loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                    className: \"py-16 text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col items-center gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                lineNumber: 788,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-muted-foreground\",\n                                children: \"جاري تحميل المزادات...\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                lineNumber: 789,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                        lineNumber: 787,\n                        columnNumber: 15\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                    lineNumber: 786,\n                    columnNumber: 13\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                lineNumber: 785,\n                columnNumber: 11\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__.Dialog, {\n                open: showBidModal,\n                onOpenChange: setShowBidModal,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__.DialogContent, {\n                    className: \"sm:max-w-md\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__.DialogHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__.DialogTitle, {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 800,\n                                        columnNumber: 17\n                                    }, this),\n                                    \"تقديم مزايدة\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                lineNumber: 799,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                            lineNumber: 798,\n                            columnNumber: 13\n                        }, this),\n                        selectedAuction && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gray-50 p-4 rounded-lg\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"font-semibold mb-2\",\n                                            children: selectedAuction.title\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                            lineNumber: 808,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-4 text-sm text-gray-600\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                            lineNumber: 811,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: [\n                                                                \"المزايدة الحالية: \",\n                                                                (selectedAuction.currentBid || 0).toLocaleString(),\n                                                                \" ر.س\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                            lineNumber: 812,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                    lineNumber: 810,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                            lineNumber: 815,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: [\n                                                                selectedAuction.bidsCount || 0,\n                                                                \" مزايدة\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                            lineNumber: 816,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                    lineNumber: 814,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                            lineNumber: 809,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 807,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                            htmlFor: \"bidAmount\",\n                                            children: \"مقدار المزايدة (ر.س)\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                            lineNumber: 822,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                            id: \"bidAmount\",\n                                            type: \"number\",\n                                            value: bidAmount,\n                                            onChange: (e)=>setBidAmount(e.target.value),\n                                            placeholder: \"أدخل مقدار المزايدة\",\n                                            min: (selectedAuction.currentBid || 0) + 1\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                            lineNumber: 823,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-gray-500\",\n                                            children: [\n                                                \"الحد الأدنى: \",\n                                                ((selectedAuction.currentBid || 0) + 1).toLocaleString(),\n                                                \" ر.س\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                            lineNumber: 831,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 821,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                            lineNumber: 806,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__.DialogFooter, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"outline\",\n                                    onClick: ()=>{\n                                        setShowBidModal(false);\n                                        setBidAmount(\"\");\n                                        setSelectedAuction(null);\n                                    },\n                                    disabled: bidLoading,\n                                    children: \"إلغاء\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 839,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    onClick: submitBid,\n                                    disabled: bidLoading || !bidAmount,\n                                    className: \"bg-green-600 hover:bg-green-700\",\n                                    children: bidLoading ? \"جاري التقديم...\" : \"تقديم المزايدة\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 850,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                            lineNumber: 838,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                    lineNumber: 797,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                lineNumber: 796,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n        lineNumber: 518,\n        columnNumber: 5\n    }, this);\n}\n_s(UserAuctionsPage, \"/KNak5Fe2X9JN8XbbthPvkUxI9k=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_9__.useRouter,\n        _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_10__.useToast\n    ];\n});\n_c = UserAuctionsPage;\nvar _c;\n$RefreshReg$(_c, \"UserAuctionsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/user/auctions/page.tsx\n"));

/***/ })

});