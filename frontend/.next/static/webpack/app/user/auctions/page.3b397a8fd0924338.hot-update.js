"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/user/auctions/page",{

/***/ "(app-pages-browser)/./app/user/auctions/page.tsx":
/*!************************************!*\
  !*** ./app/user/auctions/page.tsx ***!
  \************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ UserAuctionsPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./components/ui/dialog.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./components/ui/label.tsx\");\n/* harmony import */ var _barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,DollarSign,Eye,Gavel,Heart,Pause,Play,RefreshCw,Search,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,DollarSign,Eye,Gavel,Heart,Pause,Play,RefreshCw,Search,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pause.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,DollarSign,Eye,Gavel,Heart,Pause,Play,RefreshCw,Search,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,DollarSign,Eye,Gavel,Heart,Pause,Play,RefreshCw,Search,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,DollarSign,Eye,Gavel,Heart,Pause,Play,RefreshCw,Search,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/gavel.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,DollarSign,Eye,Gavel,Heart,Pause,Play,RefreshCw,Search,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,DollarSign,Eye,Gavel,Heart,Pause,Play,RefreshCw,Search,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,DollarSign,Eye,Gavel,Heart,Pause,Play,RefreshCw,Search,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,DollarSign,Eye,Gavel,Heart,Pause,Play,RefreshCw,Search,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,DollarSign,Eye,Gavel,Heart,Pause,Play,RefreshCw,Search,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./lib/api.ts\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/use-toast */ \"(app-pages-browser)/./components/ui/use-toast.tsx\");\n/* harmony import */ var _lib_currencyService__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/lib/currencyService */ \"(app-pages-browser)/./lib/currencyService.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nfunction UserAuctionsPage() {\n    _s();\n    const [auctions, setAuctions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [filteredAuctions, setFilteredAuctions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [savedAuctions, setSavedAuctions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Bidding modal state\n    const [showBidModal, setShowBidModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedAuction, setSelectedAuction] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [bidAmount, setBidAmount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [bidLoading, setBidLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Filter and sort state\n    const [sortBy, setSortBy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"endDate\");\n    const [filterCategory, setFilterCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // Auto-refresh state\n    const [autoRefresh, setAutoRefresh] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [lastRefresh, setLastRefresh] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Date());\n    // Currency state\n    const [userCurrency, setUserCurrency] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"SAR\");\n    const [exchangeRates, setExchangeRates] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    // Optimistic locking\n    const [auctionVersions, setAuctionVersions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_9__.useRouter)();\n    const { toast } = (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_10__.useToast)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Get user data and check permissions\n        const userData = localStorage.getItem(\"user\");\n        if (userData) {\n            const parsedUser = JSON.parse(userData);\n            setUser(parsedUser);\n            // Redirect admins to their proper dashboard\n            if (parsedUser.role === \"admin\" || parsedUser.role === \"super_admin\") {\n                toast({\n                    title: \"وصول غير مسموح\",\n                    description: \"المديرون يجب أن يستخدموا لوحة إدارة المزادات\",\n                    variant: \"destructive\"\n                });\n                router.push(\"/admin/auctions\");\n                return;\n            }\n            // Redirect government users to their dashboard\n            if (parsedUser.role === \"government\") {\n                toast({\n                    title: \"وصول غير مسموح\",\n                    description: \"الجهات الحكومية تستخدم نظام المناقصات\",\n                    variant: \"destructive\"\n                });\n                router.push(\"/government/dashboard\");\n                return;\n            }\n        }\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Initialize currency\n        const savedCurrency = _lib_currencyService__WEBPACK_IMPORTED_MODULE_11__[\"default\"].getUserCurrency();\n        setUserCurrency(savedCurrency);\n        loadAuctions();\n        loadSavedAuctions();\n        loadExchangeRates();\n    }, []);\n    const loadExchangeRates = async ()=>{\n        try {\n            const rates = await _lib_currencyService__WEBPACK_IMPORTED_MODULE_11__[\"default\"].getExchangeRates();\n            setExchangeRates(rates);\n        } catch (error) {\n            console.error(\"Failed to load exchange rates:\", error);\n        }\n    };\n    // Auto-refresh auctions every 30 seconds\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!autoRefresh) return;\n        const interval = setInterval(async ()=>{\n            try {\n                await loadAuctions(false) // Silent refresh without loading state\n                ;\n                setLastRefresh(new Date());\n                console.log(\"\\uD83D\\uDD04 Auto-refreshed auction data\");\n            } catch (error) {\n                console.error(\"Auto-refresh failed:\", error);\n            }\n        }, 30000) // 30 seconds\n        ;\n        return ()=>clearInterval(interval);\n    }, [\n        autoRefresh\n    ]);\n    const loadAuctions = async function() {\n        let showLoading = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : true;\n        try {\n            console.log(\"\\uD83D\\uDD04 Starting loadAuctions...\", showLoading ? \"with loading\" : \"silent\");\n            if (showLoading) {\n                setLoading(true);\n            }\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_8__.auctionAPI.getAll();\n            console.log(\"Auctions API response:\", response.data);\n            // Handle the API response structure properly\n            let auctionData = [];\n            if (response.data && response.data.success) {\n                var _response_data_data;\n                // If the response has a success field, extract the auctions from data.data.auctions\n                auctionData = ((_response_data_data = response.data.data) === null || _response_data_data === void 0 ? void 0 : _response_data_data.auctions) || [];\n            } else if (response.data && Array.isArray(response.data)) {\n                // If response.data is directly an array\n                auctionData = response.data;\n            } else if (response.data && response.data.data && Array.isArray(response.data.data)) {\n                // If the auctions are in response.data.data\n                auctionData = response.data.data;\n            }\n            // Transform and convert currency for each auction\n            const transformedData1 = await Promise.all(auctionData.map(async (auction)=>{\n                var _auction_bids, _auction_seller_profile, _auction_seller, _auction_seller_profile1, _auction_seller1, _auction_seller2, _auction_seller3;\n                const baseAuction = {\n                    id: auction._id || auction.id,\n                    title: auction.title,\n                    description: auction.description,\n                    currentBid: auction.currentPrice || auction.currentBid || auction.startingPrice || 0,\n                    endDate: auction.endDate,\n                    bidsCount: ((_auction_bids = auction.bids) === null || _auction_bids === void 0 ? void 0 : _auction_bids.length) || auction.bidsCount || 0,\n                    seller: ((_auction_seller = auction.seller) === null || _auction_seller === void 0 ? void 0 : (_auction_seller_profile = _auction_seller.profile) === null || _auction_seller_profile === void 0 ? void 0 : _auction_seller_profile.fullName) || ((_auction_seller1 = auction.seller) === null || _auction_seller1 === void 0 ? void 0 : (_auction_seller_profile1 = _auction_seller1.profile) === null || _auction_seller_profile1 === void 0 ? void 0 : _auction_seller_profile1.companyName) || ((_auction_seller2 = auction.seller) === null || _auction_seller2 === void 0 ? void 0 : _auction_seller2.name) || ((_auction_seller3 = auction.seller) === null || _auction_seller3 === void 0 ? void 0 : _auction_seller3._id) || \"غير محدد\",\n                    category: auction.category,\n                    status: auction.status,\n                    views: auction.views || 0,\n                    location: auction.location,\n                    currency: auction.currency || \"SAR\",\n                    version: auction.version || 0\n                };\n                // Store version for optimistic locking\n                setAuctionVersions((prev)=>({\n                        ...prev,\n                        [baseAuction.id]: baseAuction.version\n                    }));\n                // Convert currency if needed\n                try {\n                    if (baseAuction.currency !== userCurrency) {\n                        const convertedBid = await _lib_currencyService__WEBPACK_IMPORTED_MODULE_11__[\"default\"].convertCurrency(baseAuction.currentBid, baseAuction.currency, userCurrency);\n                        console.log(\"\\uD83D\\uDCB1 Converting \".concat(baseAuction.currentBid, \" \").concat(baseAuction.currency, \" → \").concat(Math.round(convertedBid), \" \").concat(userCurrency));\n                        return {\n                            ...baseAuction,\n                            currentBid: Math.round(convertedBid),\n                            originalCurrency: baseAuction.currency,\n                            currency: userCurrency\n                        };\n                    }\n                } catch (conversionError) {\n                    console.warn(\"Currency conversion failed for auction:\", baseAuction.id, conversionError);\n                }\n                return baseAuction;\n            }));\n            setAuctions(transformedData1);\n            setFilteredAuctions(transformedData1);\n            console.log(\"✅ Set auctions and filteredAuctions:\", transformedData1.length);\n        } catch (error) {\n            console.error(\"Error loading auctions:\", error);\n            setAuctions([]);\n            setFilteredAuctions([]);\n            toast({\n                title: \"فشل في تحميل المزادات\",\n                description: \"حدث خطأ أثناء تحميل المزادات. يرجى المحاولة مرة أخرى.\",\n                variant: \"destructive\"\n            });\n        } finally{\n            var _transformedData;\n            if (showLoading) {\n                setLoading(false);\n            }\n            console.log(\"\\uD83C\\uDFC1 Loading finished. Auctions loaded:\", ((_transformedData = transformedData) === null || _transformedData === void 0 ? void 0 : _transformedData.length) || 0);\n        }\n    };\n    const loadSavedAuctions = async ()=>{\n        try {\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_8__.favoritesAPI.getFavorites({\n                type: \"auction\"\n            });\n            // Handle different response structures\n            let favoritesData = [];\n            if (response.data && response.data.success) {\n                favoritesData = response.data.data || [];\n            } else if (response.data && Array.isArray(response.data)) {\n                favoritesData = response.data;\n            } else if (response.data && response.data.favorites && Array.isArray(response.data.favorites)) {\n                favoritesData = response.data.favorites;\n            }\n            // Check if favoritesData is an array before mapping\n            if (Array.isArray(favoritesData)) {\n                const savedIds = new Set(favoritesData.map((fav)=>fav.itemId || fav._id || fav.id));\n                setSavedAuctions(savedIds);\n            } else {\n                console.warn(\"Favorites data is not an array:\", favoritesData);\n                setSavedAuctions(new Set());\n            }\n        } catch (error) {\n            console.error(\"Error loading saved auctions:\", error);\n            // Don't show error toast for favorites as it's not critical\n            setSavedAuctions(new Set());\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Ensure auctions is an array before filtering\n        if (Array.isArray(auctions)) {\n            let filtered = auctions.filter((auction)=>{\n                var _auction_title, _auction_description;\n                const matchesSearch = ((_auction_title = auction.title) === null || _auction_title === void 0 ? void 0 : _auction_title.toLowerCase().includes(searchTerm.toLowerCase())) || ((_auction_description = auction.description) === null || _auction_description === void 0 ? void 0 : _auction_description.toLowerCase().includes(searchTerm.toLowerCase()));\n                const matchesCategory = !filterCategory || auction.category === filterCategory;\n                return matchesSearch && matchesCategory;\n            });\n            // Sort the filtered results\n            filtered = filtered.sort((a, b)=>{\n                switch(sortBy){\n                    case \"endDate\":\n                        return new Date(a.endDate).getTime() - new Date(b.endDate).getTime();\n                    case \"currentBid\":\n                        return (b.currentBid || 0) - (a.currentBid || 0);\n                    case \"bidsCount\":\n                        return (b.bidsCount || 0) - (a.bidsCount || 0);\n                    case \"title\":\n                        return a.title.localeCompare(b.title);\n                    default:\n                        return 0;\n                }\n            });\n            setFilteredAuctions(filtered);\n            console.log(\"\\uD83D\\uDD0D Filtered auctions:\", filtered.length, \"from\", auctions.length);\n        } else {\n            setFilteredAuctions([]);\n        }\n    }, [\n        searchTerm,\n        auctions,\n        filterCategory,\n        sortBy\n    ]);\n    const getTimeRemaining = (endDate)=>{\n        const now = new Date();\n        const end = new Date(endDate);\n        const diff = end.getTime() - now.getTime();\n        if (diff <= 0) return \"انتهى\";\n        const hours = Math.floor(diff / (1000 * 60 * 60));\n        const days = Math.floor(hours / 24);\n        if (days > 0) return \"\".concat(days, \" يوم\");\n        return \"\".concat(hours, \" ساعة\");\n    };\n    // Check if user can bid\n    const canUserBid = (auction)=>{\n        var _user_profile;\n        if (!user) return false;\n        // Only individuals and companies can bid\n        if (user.role !== \"individual\" && user.role !== \"company\") {\n            return false;\n        }\n        // Companies cannot bid on their own auctions\n        if (user.role === \"company\" && auction.seller === ((_user_profile = user.profile) === null || _user_profile === void 0 ? void 0 : _user_profile.companyName)) {\n            return false;\n        }\n        return true;\n    };\n    const handleBid = async (auctionId)=>{\n        console.log(\"\\uD83C\\uDFAF Bid button clicked for auction:\", auctionId);\n        try {\n            var _freshAuction_bids, _freshAuction_seller_profile, _freshAuction_seller, _freshAuction_seller_profile1, _freshAuction_seller1, _freshAuction_seller2;\n            // Fetch fresh auction data before opening modal\n            setLoading(true);\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_8__.auctionAPI.getById(auctionId);\n            let freshAuction = null;\n            if (response.data && response.data.success) {\n                freshAuction = response.data.data.auction;\n            } else if (response.data && response.data.auction) {\n                freshAuction = response.data.auction;\n            }\n            if (!freshAuction) {\n                toast({\n                    title: \"خطأ\",\n                    description: \"لم يتم العثور على المزاد\",\n                    variant: \"destructive\"\n                });\n                return;\n            }\n            // Transform fresh data to match our format\n            const transformedAuction = {\n                id: freshAuction._id || freshAuction.id,\n                title: freshAuction.title,\n                description: freshAuction.description,\n                currentBid: freshAuction.currentPrice || freshAuction.currentBid || freshAuction.startingPrice || 0,\n                endDate: freshAuction.endDate,\n                bidsCount: ((_freshAuction_bids = freshAuction.bids) === null || _freshAuction_bids === void 0 ? void 0 : _freshAuction_bids.length) || freshAuction.bidsCount || 0,\n                seller: ((_freshAuction_seller = freshAuction.seller) === null || _freshAuction_seller === void 0 ? void 0 : (_freshAuction_seller_profile = _freshAuction_seller.profile) === null || _freshAuction_seller_profile === void 0 ? void 0 : _freshAuction_seller_profile.fullName) || ((_freshAuction_seller1 = freshAuction.seller) === null || _freshAuction_seller1 === void 0 ? void 0 : (_freshAuction_seller_profile1 = _freshAuction_seller1.profile) === null || _freshAuction_seller_profile1 === void 0 ? void 0 : _freshAuction_seller_profile1.companyName) || ((_freshAuction_seller2 = freshAuction.seller) === null || _freshAuction_seller2 === void 0 ? void 0 : _freshAuction_seller2.name) || \"غير محدد\",\n                category: freshAuction.category,\n                status: freshAuction.status\n            };\n            // Check if user can bid\n            if (!canUserBid(transformedAuction)) {\n                if ((user === null || user === void 0 ? void 0 : user.role) === \"admin\" || (user === null || user === void 0 ? void 0 : user.role) === \"super_admin\") {\n                    toast({\n                        title: \"وصول غير مسموح\",\n                        description: \"المديرون لا يمكنهم المزايدة على المزادات\",\n                        variant: \"destructive\"\n                    });\n                } else if ((user === null || user === void 0 ? void 0 : user.role) === \"government\") {\n                    toast({\n                        title: \"وصول غير مسموح\",\n                        description: \"الجهات الحكومية لا تزايد على المزادات\",\n                        variant: \"destructive\"\n                    });\n                } else if ((user === null || user === void 0 ? void 0 : user.role) === \"company\") {\n                    toast({\n                        title: \"وصول غير مسموح\",\n                        description: \"لا يمكنك المزايدة على مزادك الخاص\",\n                        variant: \"destructive\"\n                    });\n                } else {\n                    toast({\n                        title: \"وصول غير مسموح\",\n                        description: \"غير مسموح لك بالمزايدة\",\n                        variant: \"destructive\"\n                    });\n                }\n                return;\n            }\n            // Open bidding modal with fresh data\n            setSelectedAuction(transformedAuction);\n            setBidAmount(String((transformedAuction.currentBid || 0) + 1000));\n            setShowBidModal(true);\n            toast({\n                title: \"\\uD83D\\uDCCA تم تحديث بيانات المزاد\",\n                description: \"المزايدة الحالية: \".concat((transformedAuction.currentBid || 0).toLocaleString(), \" ر.س\")\n            });\n        } catch (error) {\n            console.error(\"Error fetching fresh auction data:\", error);\n            toast({\n                title: \"خطأ في تحديث البيانات\",\n                description: \"سيتم استخدام البيانات المحفوظة محلياً\",\n                variant: \"destructive\"\n            });\n            // Fallback to cached data\n            const currentAuction = auctions.find((a)=>a.id === auctionId);\n            if (currentAuction && canUserBid(currentAuction)) {\n                setSelectedAuction(currentAuction);\n                setBidAmount(String((currentAuction.currentBid || 0) + 1000));\n                setShowBidModal(true);\n            }\n        } finally{\n            setLoading(false);\n        }\n    };\n    const submitBid = async ()=>{\n        if (!selectedAuction) return;\n        const bidAmountNum = parseFloat(bidAmount);\n        if (isNaN(bidAmountNum) || bidAmountNum <= (selectedAuction.currentBid || 0)) {\n            toast({\n                title: \"مزايدة غير صالحة\",\n                description: \"يجب أن تكون المزايدة أكبر من المزايدة الحالية\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        try {\n            setBidLoading(true);\n            // Get auction version for optimistic locking\n            const auctionVersion = auctionVersions[selectedAuction.id];\n            // Prepare bid data with currency and version\n            const bidData = {\n                bidAmount: bidAmountNum,\n                currency: userCurrency,\n                version: auctionVersion\n            };\n            // Submit bid and wait for server response\n            const response = await fetch(\"/api/auctions/\".concat(selectedAuction.id, \"/bid\"), {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    \"Authorization\": \"Bearer \".concat(localStorage.getItem(\"token\"))\n                },\n                body: JSON.stringify(bidData)\n            });\n            const result = await response.json();\n            if (!response.ok) {\n                // Handle specific error cases\n                if (response.status === 409) {\n                    // Optimistic locking conflict\n                    toast({\n                        title: \"تم تحديث المزاد ⚠️\",\n                        description: \"قام شخص آخر بتحديث المزاد. سيتم تحديث البيانات.\",\n                        variant: \"destructive\"\n                    });\n                    await loadAuctions();\n                    setShowBidModal(false);\n                    setBidAmount(\"\");\n                    setSelectedAuction(null);\n                    return;\n                }\n                throw new Error(result.message || \"Bid failed\");\n            }\n            // Close modal first\n            setShowBidModal(false);\n            setBidAmount(\"\");\n            setSelectedAuction(null);\n            // Reload fresh data from server\n            await loadAuctions();\n            // Show success message with currency formatting\n            const formattedAmount = _lib_currencyService__WEBPACK_IMPORTED_MODULE_11__[\"default\"].formatAmount(bidAmountNum, userCurrency);\n            toast({\n                title: \"تم تقديم المزايدة بنجاح! \\uD83C\\uDF89\",\n                description: \"مقدار المزايدة: \".concat(formattedAmount)\n            });\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error(\"Error placing bid:\", error);\n            // Close modal on error too\n            setShowBidModal(false);\n            setBidAmount(\"\");\n            setSelectedAuction(null);\n            // Show specific error message\n            const errorMessage = ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"يرجى المحاولة مرة أخرى\";\n            if (errorMessage.includes(\"higher than current price\") || errorMessage.includes(\"أكبر من السعر الحالي\")) {\n                toast({\n                    title: \"مزايدة منخفضة ⚠️\",\n                    description: \"قام شخص آخر بمزايدة أعلى. يرجى المحاولة بمبلغ أكبر.\",\n                    variant: \"destructive\"\n                });\n            } else if (errorMessage.includes(\"ended\") || errorMessage.includes(\"انتهى\")) {\n                toast({\n                    title: \"انتهى المزاد ⏰\",\n                    description: \"لقد انتهى هذا المزاد ولا يمكن قبول مزايدات جديدة.\",\n                    variant: \"destructive\"\n                });\n            } else {\n                toast({\n                    title: \"فشل في تقديم المزايدة\",\n                    description: errorMessage,\n                    variant: \"destructive\"\n                });\n            }\n            // Always reload data on error to sync with server\n            await loadAuctions();\n        } finally{\n            setBidLoading(false);\n        }\n    };\n    const handleSaveAuction = async (auctionId)=>{\n        console.log(\"❤️ Save button clicked for auction:\", auctionId);\n        // Show immediate feedback that button was clicked\n        toast({\n            title: \"\\uD83D\\uDC86 تم الضغط على زر الحفظ\",\n            description: \"جاري معالجة الطلب...\"\n        });\n        try {\n            const auction = auctions.find((a)=>a.id === auctionId);\n            if (!auction) {\n                toast({\n                    title: \"خطأ\",\n                    description: \"لم يتم العثور على المزاد\",\n                    variant: \"destructive\"\n                });\n                return;\n            }\n            const isCurrentlySaved = savedAuctions.has(auctionId);\n            // Optimistic UI update\n            setSavedAuctions((prev)=>{\n                const newSet = new Set(prev);\n                if (isCurrentlySaved) {\n                    newSet.delete(auctionId);\n                } else {\n                    newSet.add(auctionId);\n                }\n                return newSet;\n            });\n            if (isCurrentlySaved) {\n                // Remove from favorites\n                await _lib_api__WEBPACK_IMPORTED_MODULE_8__.favoritesAPI.removeFavorite(\"auction\", auctionId);\n                toast({\n                    title: \"❤️ تم إزالة من المفضلة\",\n                    description: 'تم إزالة \"'.concat(auction.title, '\" من المفضلة')\n                });\n            } else {\n                // Add to favorites\n                await _lib_api__WEBPACK_IMPORTED_MODULE_8__.favoritesAPI.addFavorite({\n                    itemType: \"auction\",\n                    itemId: auctionId,\n                    notifications: {\n                        bidUpdates: true,\n                        statusChanges: true,\n                        endingSoon: true\n                    }\n                });\n                toast({\n                    title: \"\\uD83D\\uDC96 تم إضافة إلى المفضلة\",\n                    description: 'تم إضافة \"'.concat(auction.title, '\" إلى المفضلة')\n                });\n            }\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error(\"Error saving auction:\", error);\n            // Revert optimistic update on error\n            setSavedAuctions((prev)=>{\n                const newSet = new Set(prev);\n                if (savedAuctions.has(auctionId)) {\n                    newSet.add(auctionId);\n                } else {\n                    newSet.delete(auctionId);\n                }\n                return newSet;\n            });\n            toast({\n                title: \"فشل في حفظ المزاد\",\n                description: ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"يرجى المحاولة مرة أخرى\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleViewAuction = async (auctionId)=>{\n        console.log(\"\\uD83D\\uDC41️ View button clicked for auction:\", auctionId);\n        try {\n            const auction = auctions.find((a)=>a.id === auctionId);\n            if (!auction) {\n                toast({\n                    title: \"خطأ\",\n                    description: \"لم يتم العثور على المزاد\",\n                    variant: \"destructive\"\n                });\n                return;\n            }\n            // Navigate to auction detail page\n            router.push(\"/auctions/\".concat(auctionId));\n            toast({\n                title: \"\\uD83D\\uDC41️ جاري تحميل التفاصيل\",\n                description: \"سيتم عرض تفاصيل المزاد\"\n            });\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error(\"Error viewing auction:\", error);\n            toast({\n                title: \"فشل في عرض تفاصيل المزاد\",\n                description: ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"يرجى المحاولة مرة أخرى\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-3xl font-bold\",\n                                    children: \"المزادات المتاحة\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 619,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-muted-foreground\",\n                                    children: [\n                                        \"استكشف المزادات الحالية وشارك في المزايدة\",\n                                        autoRefresh && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs text-green-600 ml-2\",\n                                            children: \"• تحديث تلقائي كل 30 ثانية\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                            lineNumber: 623,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 620,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                            lineNumber: 618,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                            htmlFor: \"currency\",\n                                            className: \"text-sm font-medium\",\n                                            children: \"العملة:\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                            lineNumber: 631,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            id: \"currency\",\n                                            value: userCurrency,\n                                            onChange: async (e)=>{\n                                                const newCurrency = e.target.value;\n                                                setUserCurrency(newCurrency);\n                                                _lib_currencyService__WEBPACK_IMPORTED_MODULE_11__[\"default\"].setUserCurrency(newCurrency);\n                                                setLoading(true);\n                                                await loadAuctions();\n                                                setLoading(false);\n                                                toast({\n                                                    title: \"\\uD83D\\uDCB1 تم تغيير العملة\",\n                                                    description: \"تم التحويل إلى \".concat(_lib_currencyService__WEBPACK_IMPORTED_MODULE_11__[\"default\"].getCurrencySymbol(newCurrency))\n                                                });\n                                            },\n                                            className: \"px-2 py-1 border border-gray-300 rounded text-sm focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                            children: _lib_currencyService__WEBPACK_IMPORTED_MODULE_11__[\"default\"].getSupportedCurrencies().map((currency)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: currency.code,\n                                                    children: [\n                                                        currency.symbol,\n                                                        \" \",\n                                                        currency.code\n                                                    ]\n                                                }, currency.code, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                    lineNumber: 650,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                            lineNumber: 632,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 630,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    onClick: async ()=>{\n                                        await loadAuctions(true) // Show loading state\n                                        ;\n                                        setLastRefresh(new Date());\n                                        toast({\n                                            title: \"\\uD83D\\uDD04 تم تحديث البيانات\",\n                                            description: \"تم العثور على \".concat(filteredAuctions.length, \" مزاد متاح\")\n                                        });\n                                    },\n                                    variant: \"outline\",\n                                    disabled: loading,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"h-4 w-4 ml-2 \".concat(loading ? \"animate-spin\" : \"\")\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                            lineNumber: 668,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"تحديث\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 656,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    onClick: ()=>{\n                                        setAutoRefresh(!autoRefresh);\n                                        toast({\n                                            title: autoRefresh ? \"⏸️ تم إيقاف التحديث التلقائي\" : \"▶️ تم تشغيل التحديث التلقائي\",\n                                            description: autoRefresh ? \"يمكنك التحديث يدوياً\" : \"سيتم التحديث كل 30 ثانية\"\n                                        });\n                                    },\n                                    variant: autoRefresh ? \"default\" : \"outline\",\n                                    size: \"icon\",\n                                    children: autoRefresh ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 682,\n                                        columnNumber: 32\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 682,\n                                        columnNumber: 64\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 671,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                            lineNumber: 629,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                    lineNumber: 617,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                lineNumber: 616,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                    className: \"pt-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"absolute right-3 top-3 h-4 w-4 text-muted-foreground\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 693,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                        placeholder: \"ابحث في المزادات...\",\n                                        value: searchTerm,\n                                        onChange: (e)=>setSearchTerm(e.target.value),\n                                        className: \"pr-10\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 694,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                lineNumber: 692,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col sm:flex-row gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                htmlFor: \"category\",\n                                                className: \"text-sm font-medium\",\n                                                children: \"الفئة\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                lineNumber: 704,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                id: \"category\",\n                                                value: filterCategory,\n                                                onChange: (e)=>setFilterCategory(e.target.value),\n                                                className: \"w-full mt-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"\",\n                                                        children: \"جميع الفئات\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 711,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"electronics\",\n                                                        children: \"إلكترونيات\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 712,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"vehicles\",\n                                                        children: \"سيارات\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 713,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"real_estate\",\n                                                        children: \"عقارات\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 714,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"art_collectibles\",\n                                                        children: \"فنون ومقتنيات\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 715,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"machinery\",\n                                                        children: \"معدات\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 716,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"furniture\",\n                                                        children: \"أثاث\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 717,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"jewelry\",\n                                                        children: \"مجوهرات\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 718,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"books_media\",\n                                                        children: \"كتب ووسائط\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 719,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"clothing\",\n                                                        children: \"ملابس\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 720,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"sports\",\n                                                        children: \"رياضة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 721,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"other\",\n                                                        children: \"أخرى\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 722,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                lineNumber: 705,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 703,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                htmlFor: \"sort\",\n                                                className: \"text-sm font-medium\",\n                                                children: \"ترتيب حسب\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                lineNumber: 727,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                id: \"sort\",\n                                                value: sortBy,\n                                                onChange: (e)=>setSortBy(e.target.value),\n                                                className: \"w-full mt-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"endDate\",\n                                                        children: \"تاريخ الانتهاء\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 734,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"currentBid\",\n                                                        children: \"أعلى مزايدة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 735,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"bidsCount\",\n                                                        children: \"عدد المزايدات\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 736,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"title\",\n                                                        children: \"الاسم\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 737,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                lineNumber: 728,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 726,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                lineNumber: 702,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                        lineNumber: 691,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                    lineNumber: 690,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                lineNumber: 689,\n                columnNumber: 9\n            }, this),\n            Array.isArray(filteredAuctions) && filteredAuctions.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                    className: \"text-lg\",\n                                    children: \"إجمالي المزادات\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 750,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                    className: \"text-2xl font-bold text-blue-600\",\n                                    children: filteredAuctions.length\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 751,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                            lineNumber: 749,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                        lineNumber: 748,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                    className: \"text-lg\",\n                                    children: \"المزايدات النشطة\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 758,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                    className: \"text-2xl font-bold text-green-600\",\n                                    children: filteredAuctions.reduce((sum, a)=>sum + (a.bidsCount || 0), 0)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 759,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                            lineNumber: 757,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                        lineNumber: 756,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                    className: \"text-lg\",\n                                    children: \"أعلى مزايدة\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 766,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                    className: \"text-2xl font-bold text-purple-600\",\n                                    children: filteredAuctions.length > 0 ? _lib_currencyService__WEBPACK_IMPORTED_MODULE_11__[\"default\"].formatAmount(Math.max(...filteredAuctions.map((a)=>a.currentBid || 0)), userCurrency) : _lib_currencyService__WEBPACK_IMPORTED_MODULE_11__[\"default\"].formatAmount(0, userCurrency)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 767,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                            lineNumber: 765,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                        lineNumber: 764,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                lineNumber: 747,\n                columnNumber: 11\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                children: Array.isArray(filteredAuctions) && filteredAuctions.map((auction)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        className: \"hover:shadow-lg transition-shadow cursor-pointer\",\n                        onClick: ()=>handleViewAuction(auction.id),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"aspect-video bg-gradient-to-br from-gray-100 to-gray-200 rounded-lg mb-4 flex items-center justify-center relative overflow-hidden\",\n                                        children: [\n                                            auction.images && auction.images.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: auction.images[0].url,\n                                                alt: auction.title,\n                                                className: \"w-full h-full object-cover\",\n                                                onError: (e)=>{\n                                                    e.currentTarget.style.display = \"none\";\n                                                    e.currentTarget.nextElementSibling.style.display = \"flex\";\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                lineNumber: 788,\n                                                columnNumber: 21\n                                            }, this) : null,\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-center w-full h-full text-gray-500\",\n                                                style: {\n                                                    display: auction.images && auction.images.length > 0 ? \"none\" : \"flex\"\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                            className: \"h-8 w-8 mx-auto mb-2 text-gray-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                            lineNumber: 800,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm\",\n                                                            children: \"صورة المزاد\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                            lineNumber: 801,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                    lineNumber: 799,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                lineNumber: 798,\n                                                columnNumber: 19\n                                            }, this),\n                                            auction.status === \"active\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                className: \"absolute top-2 right-2 bg-green-500\",\n                                                children: \"نشط\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                lineNumber: 805,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 786,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                        className: \"text-lg line-clamp-2\",\n                                        children: auction.title\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 810,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                        className: \"line-clamp-2\",\n                                        children: auction.description\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 811,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                lineNumber: 785,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-muted-foreground\",\n                                                        children: \"أعلى مزايدة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 818,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xl font-bold text-green-600\",\n                                                        children: _lib_currencyService__WEBPACK_IMPORTED_MODULE_11__[\"default\"].formatAmount(auction.currentBid || 0, auction.currency || userCurrency)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 819,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                lineNumber: 817,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-muted-foreground\",\n                                                        children: \"المزايدات\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 824,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-lg font-semibold\",\n                                                        children: auction.bidsCount || 0\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 825,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                lineNumber: 823,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 816,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                className: \"h-4 w-4 text-muted-foreground\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                lineNumber: 830,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-muted-foreground\",\n                                                children: [\n                                                    \"ينتهي خلال: \",\n                                                    getTimeRemaining(auction.endDate)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                lineNumber: 831,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 829,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-muted-foreground\",\n                                        children: [\n                                            \"البائع: \",\n                                            auction.seller\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 836,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                onClick: (e)=>{\n                                                    e.preventDefault();\n                                                    e.stopPropagation();\n                                                    handleBid(auction.id);\n                                                },\n                                                className: \"flex-1\",\n                                                disabled: loading || bidLoading,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                        className: \"h-4 w-4 ml-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 850,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    bidLoading ? \"جاري المزايدة...\" : \"مزايدة\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                lineNumber: 841,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                variant: savedAuctions.has(auction.id) ? \"default\" : \"outline\",\n                                                size: \"icon\",\n                                                onClick: (e)=>{\n                                                    e.preventDefault();\n                                                    e.stopPropagation();\n                                                    handleSaveAuction(auction.id);\n                                                },\n                                                disabled: loading,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                    className: \"h-4 w-4 \".concat(savedAuctions.has(auction.id) ? \"fill-current\" : \"\")\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                    lineNumber: 863,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                lineNumber: 853,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                variant: \"outline\",\n                                                size: \"icon\",\n                                                onClick: (e)=>{\n                                                    e.preventDefault();\n                                                    e.stopPropagation();\n                                                    handleViewAuction(auction.id);\n                                                },\n                                                disabled: loading,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                    lineNumber: 875,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                lineNumber: 865,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 840,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                lineNumber: 815,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, auction.id, true, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                        lineNumber: 784,\n                        columnNumber: 13\n                    }, this))\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                lineNumber: 782,\n                columnNumber: 9\n            }, this),\n            !loading && (!Array.isArray(filteredAuctions) || filteredAuctions.length === 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                    className: \"py-16 text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col items-center gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-16 h-16 rounded-full bg-gray-100 flex items-center justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                    className: \"h-8 w-8 text-gray-400\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 889,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                lineNumber: 888,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold mb-2\",\n                                        children: searchTerm ? \"لا توجد مزادات تطابق البحث\" : \"لا توجد مزادات متاحة حالياً\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 892,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-muted-foreground\",\n                                        children: searchTerm ? \"جرب البحث بكلمات مختلفة أو امسح مربع البحث لعرض جميع المزادات\" : \"سيتم عرض المزادات المتاحة هنا عند إضافتها من قبل الشركات\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 895,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                lineNumber: 891,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                        lineNumber: 887,\n                        columnNumber: 15\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                    lineNumber: 886,\n                    columnNumber: 13\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                lineNumber: 885,\n                columnNumber: 11\n            }, this),\n            loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                    className: \"py-16 text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col items-center gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                lineNumber: 912,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-muted-foreground\",\n                                children: \"جاري تحميل المزادات...\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                lineNumber: 913,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xs text-gray-400\",\n                                children: [\n                                    \"Loading: \",\n                                    loading.toString(),\n                                    \", Auctions: \",\n                                    auctions.length,\n                                    \", Filtered: \",\n                                    filteredAuctions.length\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                lineNumber: 914,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                        lineNumber: 911,\n                        columnNumber: 15\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                    lineNumber: 910,\n                    columnNumber: 13\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                lineNumber: 909,\n                columnNumber: 11\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__.Dialog, {\n                open: showBidModal,\n                onOpenChange: setShowBidModal,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__.DialogContent, {\n                    className: \"sm:max-w-md\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__.DialogHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__.DialogTitle, {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 925,\n                                        columnNumber: 17\n                                    }, this),\n                                    \"تقديم مزايدة\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                lineNumber: 924,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                            lineNumber: 923,\n                            columnNumber: 13\n                        }, this),\n                        selectedAuction && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gray-50 p-4 rounded-lg\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"font-semibold mb-2\",\n                                            children: selectedAuction.title\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                            lineNumber: 933,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-4 text-sm text-gray-600\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                            lineNumber: 936,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: [\n                                                                \"المزايدة الحالية: \",\n                                                                (selectedAuction.currentBid || 0).toLocaleString(),\n                                                                \" ر.س\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                            lineNumber: 937,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                    lineNumber: 935,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                            lineNumber: 940,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: [\n                                                                selectedAuction.bidsCount || 0,\n                                                                \" مزايدة\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                            lineNumber: 941,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                    lineNumber: 939,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                            lineNumber: 934,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 932,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                            htmlFor: \"bidAmount\",\n                                            children: \"مقدار المزايدة (ر.س)\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                            lineNumber: 947,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                            id: \"bidAmount\",\n                                            type: \"number\",\n                                            value: bidAmount,\n                                            onChange: (e)=>setBidAmount(e.target.value),\n                                            placeholder: \"أدخل مقدار المزايدة\",\n                                            min: (selectedAuction.currentBid || 0) + 1\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                            lineNumber: 948,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-gray-500\",\n                                            children: [\n                                                \"الحد الأدنى: \",\n                                                ((selectedAuction.currentBid || 0) + 1).toLocaleString(),\n                                                \" ر.س\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                            lineNumber: 956,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 946,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                            lineNumber: 931,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__.DialogFooter, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"outline\",\n                                    onClick: ()=>{\n                                        setShowBidModal(false);\n                                        setBidAmount(\"\");\n                                        setSelectedAuction(null);\n                                    },\n                                    disabled: bidLoading,\n                                    children: \"إلغاء\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 964,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    onClick: submitBid,\n                                    disabled: bidLoading || !bidAmount,\n                                    className: \"bg-green-600 hover:bg-green-700\",\n                                    children: bidLoading ? \"جاري التقديم...\" : \"تقديم المزايدة\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 975,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                            lineNumber: 963,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                    lineNumber: 922,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                lineNumber: 921,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n        lineNumber: 615,\n        columnNumber: 5\n    }, this);\n}\n_s(UserAuctionsPage, \"z+c0gDlNDBuJNNkaYMi/S/91rKQ=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_9__.useRouter,\n        _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_10__.useToast\n    ];\n});\n_c = UserAuctionsPage;\nvar _c;\n$RefreshReg$(_c, \"UserAuctionsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/user/auctions/page.tsx\n"));

/***/ })

});