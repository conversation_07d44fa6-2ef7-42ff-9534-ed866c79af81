"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/user/auctions/page",{

/***/ "(app-pages-browser)/./app/user/auctions/page.tsx":
/*!************************************!*\
  !*** ./app/user/auctions/page.tsx ***!
  \************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ UserAuctionsPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./components/ui/dialog.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./components/ui/label.tsx\");\n/* harmony import */ var _barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,DollarSign,Eye,Gavel,Heart,Pause,Play,RefreshCw,Search,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,DollarSign,Eye,Gavel,Heart,Pause,Play,RefreshCw,Search,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pause.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,DollarSign,Eye,Gavel,Heart,Pause,Play,RefreshCw,Search,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,DollarSign,Eye,Gavel,Heart,Pause,Play,RefreshCw,Search,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,DollarSign,Eye,Gavel,Heart,Pause,Play,RefreshCw,Search,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/gavel.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,DollarSign,Eye,Gavel,Heart,Pause,Play,RefreshCw,Search,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,DollarSign,Eye,Gavel,Heart,Pause,Play,RefreshCw,Search,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,DollarSign,Eye,Gavel,Heart,Pause,Play,RefreshCw,Search,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,DollarSign,Eye,Gavel,Heart,Pause,Play,RefreshCw,Search,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,DollarSign,Eye,Gavel,Heart,Pause,Play,RefreshCw,Search,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./lib/api.ts\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/use-toast */ \"(app-pages-browser)/./components/ui/use-toast.tsx\");\n/* harmony import */ var _contexts_CurrencyContext__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/contexts/CurrencyContext */ \"(app-pages-browser)/./contexts/CurrencyContext.tsx\");\n/* harmony import */ var _components_CurrencyDisplay__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/CurrencyDisplay */ \"(app-pages-browser)/./components/CurrencyDisplay.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction UserAuctionsPage() {\n    _s();\n    const [auctions, setAuctions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [filteredAuctions, setFilteredAuctions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [savedAuctions, setSavedAuctions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Bidding modal state\n    const [showBidModal, setShowBidModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedAuction, setSelectedAuction] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [bidAmount, setBidAmount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [bidLoading, setBidLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Filter and sort state\n    const [sortBy, setSortBy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"endDate\");\n    const [filterCategory, setFilterCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // Auto-refresh state\n    const [autoRefresh, setAutoRefresh] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [lastRefresh, setLastRefresh] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Date());\n    // Optimistic locking\n    const [auctionVersions, setAuctionVersions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_9__.useRouter)();\n    const { toast } = (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_10__.useToast)();\n    const { userCurrency, formatAmount, convertAmount } = (0,_contexts_CurrencyContext__WEBPACK_IMPORTED_MODULE_11__.useCurrency)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Get user data and check permissions\n        const userData = localStorage.getItem(\"user\");\n        if (userData) {\n            const parsedUser = JSON.parse(userData);\n            setUser(parsedUser);\n            // Redirect admins to their proper dashboard\n            if (parsedUser.role === \"admin\" || parsedUser.role === \"super_admin\") {\n                toast({\n                    title: \"وصول غير مسموح\",\n                    description: \"المديرون يجب أن يستخدموا لوحة إدارة المزادات\",\n                    variant: \"destructive\"\n                });\n                router.push(\"/admin/auctions\");\n                return;\n            }\n            // Redirect government users to their dashboard\n            if (parsedUser.role === \"government\") {\n                toast({\n                    title: \"وصول غير مسموح\",\n                    description: \"الجهات الحكومية تستخدم نظام المناقصات\",\n                    variant: \"destructive\"\n                });\n                router.push(\"/government/dashboard\");\n                return;\n            }\n        }\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Load data\n        loadAuctions();\n        loadSavedAuctions();\n    }, []);\n    // Load auctions when currency changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (userCurrency) {\n            loadAuctions();\n        }\n    }, [\n        userCurrency\n    ]);\n    // Auto-refresh auctions every 30 seconds\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!autoRefresh) return;\n        const interval = setInterval(async ()=>{\n            try {\n                await loadAuctions(false) // Silent refresh without loading state\n                ;\n                setLastRefresh(new Date());\n                console.log(\"\\uD83D\\uDD04 Auto-refreshed auction data\");\n            } catch (error) {\n                console.error(\"Auto-refresh failed:\", error);\n            }\n        }, 30000) // 30 seconds\n        ;\n        return ()=>clearInterval(interval);\n    }, [\n        autoRefresh\n    ]);\n    const loadAuctions = async function() {\n        let showLoading = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : true;\n        try {\n            if (showLoading) {\n                setLoading(true);\n            }\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_8__.auctionAPI.getAll();\n            console.log(\"Auctions API response:\", response.data);\n            // Handle the API response structure properly\n            let auctionData = [];\n            if (response.data && response.data.success) {\n                var _response_data_data;\n                // If the response has a success field, extract the auctions from data.data.auctions\n                auctionData = ((_response_data_data = response.data.data) === null || _response_data_data === void 0 ? void 0 : _response_data_data.auctions) || [];\n            } else if (response.data && Array.isArray(response.data)) {\n                // If response.data is directly an array\n                auctionData = response.data;\n            } else if (response.data && response.data.data && Array.isArray(response.data.data)) {\n                // If the auctions are in response.data.data\n                auctionData = response.data.data;\n            }\n            // Transform and convert currency for each auction\n            const transformedData = await Promise.all(auctionData.map(async (auction)=>{\n                var _auction_bids, _auction_seller_profile, _auction_seller, _auction_seller_profile1, _auction_seller1, _auction_seller2, _auction_seller3;\n                const baseAuction = {\n                    id: auction._id || auction.id,\n                    title: auction.title,\n                    description: auction.description,\n                    currentBid: auction.currentPrice || auction.currentBid || auction.startingPrice || 0,\n                    endDate: auction.endDate,\n                    bidsCount: ((_auction_bids = auction.bids) === null || _auction_bids === void 0 ? void 0 : _auction_bids.length) || auction.bidsCount || 0,\n                    seller: ((_auction_seller = auction.seller) === null || _auction_seller === void 0 ? void 0 : (_auction_seller_profile = _auction_seller.profile) === null || _auction_seller_profile === void 0 ? void 0 : _auction_seller_profile.fullName) || ((_auction_seller1 = auction.seller) === null || _auction_seller1 === void 0 ? void 0 : (_auction_seller_profile1 = _auction_seller1.profile) === null || _auction_seller_profile1 === void 0 ? void 0 : _auction_seller_profile1.companyName) || ((_auction_seller2 = auction.seller) === null || _auction_seller2 === void 0 ? void 0 : _auction_seller2.name) || ((_auction_seller3 = auction.seller) === null || _auction_seller3 === void 0 ? void 0 : _auction_seller3._id) || \"غير محدد\",\n                    category: auction.category,\n                    status: auction.status,\n                    views: auction.views || 0,\n                    location: auction.location,\n                    currency: auction.currency || \"SAR\",\n                    version: auction.version || 0\n                };\n                // Store version for optimistic locking\n                setAuctionVersions((prev)=>({\n                        ...prev,\n                        [baseAuction.id]: baseAuction.version\n                    }));\n                // Convert currency if needed\n                try {\n                    if (baseAuction.currency !== userCurrency) {\n                        const convertedBid = await convertAmount(baseAuction.currentBid, baseAuction.currency, userCurrency);\n                        return {\n                            ...baseAuction,\n                            currentBid: Math.round(convertedBid),\n                            originalCurrency: baseAuction.currency,\n                            currency: userCurrency\n                        };\n                    }\n                } catch (conversionError) {\n                    console.warn(\"Currency conversion failed for auction:\", baseAuction.id, conversionError);\n                }\n                return baseAuction;\n            }));\n            setAuctions(transformedData);\n            setFilteredAuctions(transformedData);\n            console.log(\"✅ Set auctions and filteredAuctions:\", transformedData.length);\n        } catch (error) {\n            console.error(\"Error loading auctions:\", error);\n            setAuctions([]);\n            setFilteredAuctions([]);\n            toast({\n                title: \"فشل في تحميل المزادات\",\n                description: \"حدث خطأ أثناء تحميل المزادات. يرجى المحاولة مرة أخرى.\",\n                variant: \"destructive\"\n            });\n        } finally{\n            if (showLoading) {\n                setLoading(false);\n            }\n            console.log(\"\\uD83C\\uDFC1 Loading finished. Auctions loaded:\", auctions.length);\n        }\n    };\n    const loadSavedAuctions = async ()=>{\n        try {\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_8__.favoritesAPI.getFavorites({\n                type: \"auction\"\n            });\n            // Handle different response structures\n            let favoritesData = [];\n            if (response.data && response.data.success) {\n                favoritesData = response.data.data || [];\n            } else if (response.data && Array.isArray(response.data)) {\n                favoritesData = response.data;\n            } else if (response.data && response.data.favorites && Array.isArray(response.data.favorites)) {\n                favoritesData = response.data.favorites;\n            }\n            // Check if favoritesData is an array before mapping\n            if (Array.isArray(favoritesData)) {\n                const savedIds = new Set(favoritesData.map((fav)=>fav.itemId || fav._id || fav.id));\n                setSavedAuctions(savedIds);\n            } else {\n                console.warn(\"Favorites data is not an array:\", favoritesData);\n                setSavedAuctions(new Set());\n            }\n        } catch (error) {\n            console.error(\"Error loading saved auctions:\", error);\n            // Don't show error toast for favorites as it's not critical\n            setSavedAuctions(new Set());\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Ensure auctions is an array before filtering\n        if (Array.isArray(auctions)) {\n            let filtered = auctions.filter((auction)=>{\n                var _auction_title, _auction_description;\n                const matchesSearch = ((_auction_title = auction.title) === null || _auction_title === void 0 ? void 0 : _auction_title.toLowerCase().includes(searchTerm.toLowerCase())) || ((_auction_description = auction.description) === null || _auction_description === void 0 ? void 0 : _auction_description.toLowerCase().includes(searchTerm.toLowerCase()));\n                const matchesCategory = !filterCategory || auction.category === filterCategory;\n                return matchesSearch && matchesCategory;\n            });\n            // Sort the filtered results\n            filtered = filtered.sort((a, b)=>{\n                switch(sortBy){\n                    case \"endDate\":\n                        return new Date(a.endDate).getTime() - new Date(b.endDate).getTime();\n                    case \"currentBid\":\n                        return (b.currentBid || 0) - (a.currentBid || 0);\n                    case \"bidsCount\":\n                        return (b.bidsCount || 0) - (a.bidsCount || 0);\n                    case \"title\":\n                        return a.title.localeCompare(b.title);\n                    default:\n                        return 0;\n                }\n            });\n            setFilteredAuctions(filtered);\n            console.log(\"\\uD83D\\uDD0D Filtered auctions:\", filtered.length, \"from\", auctions.length);\n        } else {\n            setFilteredAuctions([]);\n        }\n    }, [\n        searchTerm,\n        auctions,\n        filterCategory,\n        sortBy\n    ]);\n    const getTimeRemaining = (endDate)=>{\n        const now = new Date();\n        const end = new Date(endDate);\n        const diff = end.getTime() - now.getTime();\n        if (diff <= 0) return \"انتهى\";\n        const hours = Math.floor(diff / (1000 * 60 * 60));\n        const days = Math.floor(hours / 24);\n        if (days > 0) return \"\".concat(days, \" يوم\");\n        return \"\".concat(hours, \" ساعة\");\n    };\n    // Check if user can bid\n    const canUserBid = (auction)=>{\n        var _user_profile;\n        if (!user) return false;\n        // Only individuals and companies can bid\n        if (user.role !== \"individual\" && user.role !== \"company\") {\n            return false;\n        }\n        // Companies cannot bid on their own auctions\n        if (user.role === \"company\" && auction.seller === ((_user_profile = user.profile) === null || _user_profile === void 0 ? void 0 : _user_profile.companyName)) {\n            return false;\n        }\n        return true;\n    };\n    const handleBid = async (auctionId)=>{\n        console.log(\"\\uD83C\\uDFAF Bid button clicked for auction:\", auctionId);\n        try {\n            var _freshAuction_bids, _freshAuction_seller_profile, _freshAuction_seller, _freshAuction_seller_profile1, _freshAuction_seller1, _freshAuction_seller2;\n            // Fetch fresh auction data before opening modal\n            setLoading(true);\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_8__.auctionAPI.getById(auctionId);\n            let freshAuction = null;\n            if (response.data && response.data.success) {\n                freshAuction = response.data.data.auction;\n            } else if (response.data && response.data.auction) {\n                freshAuction = response.data.auction;\n            }\n            if (!freshAuction) {\n                toast({\n                    title: \"خطأ\",\n                    description: \"لم يتم العثور على المزاد\",\n                    variant: \"destructive\"\n                });\n                return;\n            }\n            // Transform fresh data to match our format\n            const transformedAuction = {\n                id: freshAuction._id || freshAuction.id,\n                title: freshAuction.title,\n                description: freshAuction.description,\n                currentBid: freshAuction.currentPrice || freshAuction.currentBid || freshAuction.startingPrice || 0,\n                endDate: freshAuction.endDate,\n                bidsCount: ((_freshAuction_bids = freshAuction.bids) === null || _freshAuction_bids === void 0 ? void 0 : _freshAuction_bids.length) || freshAuction.bidsCount || 0,\n                seller: ((_freshAuction_seller = freshAuction.seller) === null || _freshAuction_seller === void 0 ? void 0 : (_freshAuction_seller_profile = _freshAuction_seller.profile) === null || _freshAuction_seller_profile === void 0 ? void 0 : _freshAuction_seller_profile.fullName) || ((_freshAuction_seller1 = freshAuction.seller) === null || _freshAuction_seller1 === void 0 ? void 0 : (_freshAuction_seller_profile1 = _freshAuction_seller1.profile) === null || _freshAuction_seller_profile1 === void 0 ? void 0 : _freshAuction_seller_profile1.companyName) || ((_freshAuction_seller2 = freshAuction.seller) === null || _freshAuction_seller2 === void 0 ? void 0 : _freshAuction_seller2.name) || \"غير محدد\",\n                category: freshAuction.category,\n                status: freshAuction.status\n            };\n            // Check if user can bid\n            if (!canUserBid(transformedAuction)) {\n                if ((user === null || user === void 0 ? void 0 : user.role) === \"admin\" || (user === null || user === void 0 ? void 0 : user.role) === \"super_admin\") {\n                    toast({\n                        title: \"وصول غير مسموح\",\n                        description: \"المديرون لا يمكنهم المزايدة على المزادات\",\n                        variant: \"destructive\"\n                    });\n                } else if ((user === null || user === void 0 ? void 0 : user.role) === \"government\") {\n                    toast({\n                        title: \"وصول غير مسموح\",\n                        description: \"الجهات الحكومية لا تزايد على المزادات\",\n                        variant: \"destructive\"\n                    });\n                } else if ((user === null || user === void 0 ? void 0 : user.role) === \"company\") {\n                    toast({\n                        title: \"وصول غير مسموح\",\n                        description: \"لا يمكنك المزايدة على مزادك الخاص\",\n                        variant: \"destructive\"\n                    });\n                } else {\n                    toast({\n                        title: \"وصول غير مسموح\",\n                        description: \"غير مسموح لك بالمزايدة\",\n                        variant: \"destructive\"\n                    });\n                }\n                return;\n            }\n            // Open bidding modal with fresh data\n            setSelectedAuction(transformedAuction);\n            setBidAmount(String((transformedAuction.currentBid || 0) + 1000));\n            setShowBidModal(true);\n            toast({\n                title: \"\\uD83D\\uDCCA تم تحديث بيانات المزاد\",\n                description: \"المزايدة الحالية: \".concat(formatAmount(transformedAuction.currentBid || 0))\n            });\n        } catch (error) {\n            console.error(\"Error fetching fresh auction data:\", error);\n            toast({\n                title: \"خطأ في تحديث البيانات\",\n                description: \"سيتم استخدام البيانات المحفوظة محلياً\",\n                variant: \"destructive\"\n            });\n            // Fallback to cached data\n            const currentAuction = auctions.find((a)=>a.id === auctionId);\n            if (currentAuction && canUserBid(currentAuction)) {\n                setSelectedAuction(currentAuction);\n                setBidAmount(String((currentAuction.currentBid || 0) + 1000));\n                setShowBidModal(true);\n            }\n        } finally{\n            setLoading(false);\n        }\n    };\n    const submitBid = async ()=>{\n        if (!selectedAuction) return;\n        const bidAmountNum = parseFloat(bidAmount);\n        if (isNaN(bidAmountNum) || bidAmountNum <= (selectedAuction.currentBid || 0)) {\n            toast({\n                title: \"مزايدة غير صالحة\",\n                description: \"يجب أن تكون المزايدة أكبر من المزايدة الحالية\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        try {\n            setBidLoading(true);\n            // Get auction version for optimistic locking\n            const auctionVersion = auctionVersions[selectedAuction.id];\n            // Prepare bid data with currency and version\n            const bidData = {\n                bidAmount: bidAmountNum,\n                currency: userCurrency,\n                version: auctionVersion\n            };\n            // Submit bid and wait for server response\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_8__[\"default\"].post(\"/auctions/\".concat(selectedAuction.id, \"/bid\"), bidData);\n            const result = response.data;\n            if (!result.success) {\n                throw new Error(result.message || \"Bid failed\");\n            }\n            // Close modal first\n            setShowBidModal(false);\n            setBidAmount(\"\");\n            setSelectedAuction(null);\n            // Reload fresh data from server\n            await loadAuctions();\n            // Show success message with currency formatting\n            const formattedAmount = formatAmount(bidAmountNum);\n            toast({\n                title: \"تم تقديم المزايدة بنجاح! \\uD83C\\uDF89\",\n                description: \"مقدار المزايدة: \".concat(formattedAmount)\n            });\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error(\"Error placing bid:\", error);\n            // Close modal on error too\n            setShowBidModal(false);\n            setBidAmount(\"\");\n            setSelectedAuction(null);\n            // Show specific error message\n            const errorMessage = ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"يرجى المحاولة مرة أخرى\";\n            if (errorMessage.includes(\"higher than current price\") || errorMessage.includes(\"أكبر من السعر الحالي\")) {\n                toast({\n                    title: \"مزايدة منخفضة ⚠️\",\n                    description: \"قام شخص آخر بمزايدة أعلى. يرجى المحاولة بمبلغ أكبر.\",\n                    variant: \"destructive\"\n                });\n            } else if (errorMessage.includes(\"ended\") || errorMessage.includes(\"انتهى\")) {\n                toast({\n                    title: \"انتهى المزاد ⏰\",\n                    description: \"لقد انتهى هذا المزاد ولا يمكن قبول مزايدات جديدة.\",\n                    variant: \"destructive\"\n                });\n            } else {\n                toast({\n                    title: \"فشل في تقديم المزايدة\",\n                    description: errorMessage,\n                    variant: \"destructive\"\n                });\n            }\n            // Always reload data on error to sync with server\n            await loadAuctions();\n        } finally{\n            setBidLoading(false);\n        }\n    };\n    const handleSaveAuction = async (auctionId)=>{\n        console.log(\"❤️ Save button clicked for auction:\", auctionId);\n        // Show immediate feedback that button was clicked\n        toast({\n            title: \"\\uD83D\\uDC86 تم الضغط على زر الحفظ\",\n            description: \"جاري معالجة الطلب...\"\n        });\n        try {\n            const auction = auctions.find((a)=>a.id === auctionId);\n            if (!auction) {\n                toast({\n                    title: \"خطأ\",\n                    description: \"لم يتم العثور على المزاد\",\n                    variant: \"destructive\"\n                });\n                return;\n            }\n            const isCurrentlySaved = savedAuctions.has(auctionId);\n            // Optimistic UI update\n            setSavedAuctions((prev)=>{\n                const newSet = new Set(prev);\n                if (isCurrentlySaved) {\n                    newSet.delete(auctionId);\n                } else {\n                    newSet.add(auctionId);\n                }\n                return newSet;\n            });\n            if (isCurrentlySaved) {\n                // Remove from favorites\n                await _lib_api__WEBPACK_IMPORTED_MODULE_8__.favoritesAPI.removeFavorite(\"auction\", auctionId);\n                toast({\n                    title: \"❤️ تم إزالة من المفضلة\",\n                    description: 'تم إزالة \"'.concat(auction.title, '\" من المفضلة')\n                });\n            } else {\n                // Add to favorites\n                await _lib_api__WEBPACK_IMPORTED_MODULE_8__.favoritesAPI.addFavorite({\n                    itemType: \"auction\",\n                    itemId: auctionId,\n                    notifications: {\n                        bidUpdates: true,\n                        statusChanges: true,\n                        endingSoon: true\n                    }\n                });\n                toast({\n                    title: \"\\uD83D\\uDC96 تم إضافة إلى المفضلة\",\n                    description: 'تم إضافة \"'.concat(auction.title, '\" إلى المفضلة')\n                });\n            }\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error(\"Error saving auction:\", error);\n            // Revert optimistic update on error\n            setSavedAuctions((prev)=>{\n                const newSet = new Set(prev);\n                if (savedAuctions.has(auctionId)) {\n                    newSet.add(auctionId);\n                } else {\n                    newSet.delete(auctionId);\n                }\n                return newSet;\n            });\n            toast({\n                title: \"فشل في حفظ المزاد\",\n                description: ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"يرجى المحاولة مرة أخرى\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleViewAuction = async (auctionId)=>{\n        console.log(\"\\uD83D\\uDC41️ View button clicked for auction:\", auctionId);\n        try {\n            const auction = auctions.find((a)=>a.id === auctionId);\n            if (!auction) {\n                toast({\n                    title: \"خطأ\",\n                    description: \"لم يتم العثور على المزاد\",\n                    variant: \"destructive\"\n                });\n                return;\n            }\n            // Navigate to auction detail page\n            router.push(\"/auctions/\".concat(auctionId));\n            toast({\n                title: \"\\uD83D\\uDC41️ جاري تحميل التفاصيل\",\n                description: \"سيتم عرض تفاصيل المزاد\"\n            });\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error(\"Error viewing auction:\", error);\n            toast({\n                title: \"فشل في عرض تفاصيل المزاد\",\n                description: ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"يرجى المحاولة مرة أخرى\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-3xl font-bold\",\n                                    children: \"المزادات المتاحة\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 592,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-muted-foreground\",\n                                    children: [\n                                        \"استكشف المزادات الحالية وشارك في المزايدة\",\n                                        autoRefresh && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs text-green-600 ml-2\",\n                                            children: \"• تحديث تلقائي كل 30 ثانية\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                            lineNumber: 596,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 593,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                            lineNumber: 591,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    onClick: async ()=>{\n                                        await loadAuctions(true) // Show loading state\n                                        ;\n                                        setLastRefresh(new Date());\n                                        toast({\n                                            title: \"\\uD83D\\uDD04 تم تحديث البيانات\",\n                                            description: \"تم العثور على \".concat(filteredAuctions.length, \" مزاد متاح\")\n                                        });\n                                    },\n                                    variant: \"outline\",\n                                    disabled: loading,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"h-4 w-4 ml-2 \".concat(loading ? \"animate-spin\" : \"\")\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                            lineNumber: 616,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"تحديث\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 604,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    onClick: ()=>{\n                                        setAutoRefresh(!autoRefresh);\n                                        toast({\n                                            title: autoRefresh ? \"⏸️ تم إيقاف التحديث التلقائي\" : \"▶️ تم تشغيل التحديث التلقائي\",\n                                            description: autoRefresh ? \"يمكنك التحديث يدوياً\" : \"سيتم التحديث كل 30 ثانية\"\n                                        });\n                                    },\n                                    variant: autoRefresh ? \"default\" : \"outline\",\n                                    size: \"icon\",\n                                    children: autoRefresh ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 630,\n                                        columnNumber: 32\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 630,\n                                        columnNumber: 64\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 619,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                            lineNumber: 602,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                    lineNumber: 590,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                lineNumber: 589,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                    className: \"pt-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        className: \"absolute right-3 top-3 h-4 w-4 text-muted-foreground\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 641,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                        placeholder: \"ابحث في المزادات...\",\n                                        value: searchTerm,\n                                        onChange: (e)=>setSearchTerm(e.target.value),\n                                        className: \"pr-10\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 642,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                lineNumber: 640,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col sm:flex-row gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                htmlFor: \"category\",\n                                                className: \"text-sm font-medium\",\n                                                children: \"الفئة\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                lineNumber: 652,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                id: \"category\",\n                                                value: filterCategory,\n                                                onChange: (e)=>setFilterCategory(e.target.value),\n                                                className: \"w-full mt-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"\",\n                                                        children: \"جميع الفئات\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 659,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"electronics\",\n                                                        children: \"إلكترونيات\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 660,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"vehicles\",\n                                                        children: \"سيارات\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 661,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"real_estate\",\n                                                        children: \"عقارات\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 662,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"art_collectibles\",\n                                                        children: \"فنون ومقتنيات\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 663,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"machinery\",\n                                                        children: \"معدات\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 664,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"furniture\",\n                                                        children: \"أثاث\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 665,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"jewelry\",\n                                                        children: \"مجوهرات\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 666,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"books_media\",\n                                                        children: \"كتب ووسائط\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 667,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"clothing\",\n                                                        children: \"ملابس\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 668,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"sports\",\n                                                        children: \"رياضة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 669,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"other\",\n                                                        children: \"أخرى\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 670,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                lineNumber: 653,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 651,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                htmlFor: \"sort\",\n                                                className: \"text-sm font-medium\",\n                                                children: \"ترتيب حسب\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                lineNumber: 675,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                id: \"sort\",\n                                                value: sortBy,\n                                                onChange: (e)=>setSortBy(e.target.value),\n                                                className: \"w-full mt-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"endDate\",\n                                                        children: \"تاريخ الانتهاء\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 682,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"currentBid\",\n                                                        children: \"أعلى مزايدة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 683,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"bidsCount\",\n                                                        children: \"عدد المزايدات\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 684,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"title\",\n                                                        children: \"الاسم\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 685,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                lineNumber: 676,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 674,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                lineNumber: 650,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                        lineNumber: 639,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                    lineNumber: 638,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                lineNumber: 637,\n                columnNumber: 9\n            }, this),\n            Array.isArray(filteredAuctions) && filteredAuctions.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                    className: \"text-lg\",\n                                    children: \"إجمالي المزادات\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 698,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                    className: \"text-2xl font-bold text-blue-600\",\n                                    children: filteredAuctions.length\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 699,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                            lineNumber: 697,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                        lineNumber: 696,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                    className: \"text-lg\",\n                                    children: \"المزايدات النشطة\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 706,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                    className: \"text-2xl font-bold text-green-600\",\n                                    children: filteredAuctions.reduce((sum, a)=>sum + (a.bidsCount || 0), 0)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 707,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                            lineNumber: 705,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                        lineNumber: 704,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                    className: \"text-lg\",\n                                    children: \"أعلى مزايدة\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 714,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                    className: \"text-2xl font-bold text-purple-600\",\n                                    children: filteredAuctions.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_CurrencyDisplay__WEBPACK_IMPORTED_MODULE_12__.CurrencyDisplay, {\n                                        amount: Math.max(...filteredAuctions.map((a)=>a.currentBid || 0)),\n                                        fromCurrency: \"SAR\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 717,\n                                        columnNumber: 21\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_CurrencyDisplay__WEBPACK_IMPORTED_MODULE_12__.CurrencyDisplay, {\n                                        amount: 0,\n                                        fromCurrency: \"SAR\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 722,\n                                        columnNumber: 21\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 715,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                            lineNumber: 713,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                        lineNumber: 712,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                lineNumber: 695,\n                columnNumber: 11\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                children: Array.isArray(filteredAuctions) && filteredAuctions.map((auction)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        className: \"hover:shadow-lg transition-shadow cursor-pointer\",\n                        onClick: ()=>handleViewAuction(auction.id),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"aspect-video bg-gradient-to-br from-gray-100 to-gray-200 rounded-lg mb-4 flex items-center justify-center relative overflow-hidden\",\n                                        children: [\n                                            auction.images && auction.images.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: auction.images[0].url,\n                                                alt: auction.title,\n                                                className: \"w-full h-full object-cover\",\n                                                onError: (e)=>{\n                                                    e.currentTarget.style.display = \"none\";\n                                                    e.currentTarget.nextElementSibling.style.display = \"flex\";\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                lineNumber: 737,\n                                                columnNumber: 21\n                                            }, this) : null,\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-center w-full h-full text-gray-500\",\n                                                style: {\n                                                    display: auction.images && auction.images.length > 0 ? \"none\" : \"flex\"\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                            className: \"h-8 w-8 mx-auto mb-2 text-gray-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                            lineNumber: 749,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm\",\n                                                            children: \"صورة المزاد\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                            lineNumber: 750,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                    lineNumber: 748,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                lineNumber: 747,\n                                                columnNumber: 19\n                                            }, this),\n                                            auction.status === \"active\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                className: \"absolute top-2 right-2 bg-green-500\",\n                                                children: \"نشط\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                lineNumber: 754,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 735,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                        className: \"text-lg line-clamp-2\",\n                                        children: auction.title\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 759,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                        className: \"line-clamp-2\",\n                                        children: auction.description\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 760,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                lineNumber: 734,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-muted-foreground\",\n                                                        children: \"أعلى مزايدة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 767,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xl font-bold text-green-600\",\n                                                        children: [\n                                                            formatAmount(auction.currentBid || 0),\n                                                            auction.originalCurrency && auction.originalCurrency !== userCurrency && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs text-gray-500 ml-2\",\n                                                                children: [\n                                                                    \"(من \",\n                                                                    auction.originalCurrency,\n                                                                    \")\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                                lineNumber: 771,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 768,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                lineNumber: 766,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-muted-foreground\",\n                                                        children: \"المزايدات\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 778,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-lg font-semibold\",\n                                                        children: auction.bidsCount || 0\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 779,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                lineNumber: 777,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 765,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                className: \"h-4 w-4 text-muted-foreground\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                lineNumber: 784,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-muted-foreground\",\n                                                children: [\n                                                    \"ينتهي خلال: \",\n                                                    getTimeRemaining(auction.endDate)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                lineNumber: 785,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 783,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-muted-foreground\",\n                                        children: [\n                                            \"البائع: \",\n                                            auction.seller\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 790,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                onClick: (e)=>{\n                                                    e.preventDefault();\n                                                    e.stopPropagation();\n                                                    handleBid(auction.id);\n                                                },\n                                                className: \"flex-1\",\n                                                disabled: loading || bidLoading,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        className: \"h-4 w-4 ml-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 804,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    bidLoading ? \"جاري المزايدة...\" : \"مزايدة\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                lineNumber: 795,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                variant: savedAuctions.has(auction.id) ? \"default\" : \"outline\",\n                                                size: \"icon\",\n                                                onClick: (e)=>{\n                                                    e.preventDefault();\n                                                    e.stopPropagation();\n                                                    handleSaveAuction(auction.id);\n                                                },\n                                                disabled: loading,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                    className: \"h-4 w-4 \".concat(savedAuctions.has(auction.id) ? \"fill-current\" : \"\")\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                    lineNumber: 817,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                lineNumber: 807,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                variant: \"outline\",\n                                                size: \"icon\",\n                                                onClick: (e)=>{\n                                                    e.preventDefault();\n                                                    e.stopPropagation();\n                                                    handleViewAuction(auction.id);\n                                                },\n                                                disabled: loading,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                    lineNumber: 829,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                lineNumber: 819,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 794,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                lineNumber: 764,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, auction.id, true, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                        lineNumber: 733,\n                        columnNumber: 13\n                    }, this))\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                lineNumber: 731,\n                columnNumber: 9\n            }, this),\n            !loading && (!Array.isArray(filteredAuctions) || filteredAuctions.length === 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                    className: \"py-16 text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col items-center gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-16 h-16 rounded-full bg-gray-100 flex items-center justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                    className: \"h-8 w-8 text-gray-400\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 843,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                lineNumber: 842,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold mb-2\",\n                                        children: searchTerm ? \"لا توجد مزادات تطابق البحث\" : \"لا توجد مزادات متاحة حالياً\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 846,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-muted-foreground\",\n                                        children: searchTerm ? \"جرب البحث بكلمات مختلفة أو امسح مربع البحث لعرض جميع المزادات\" : \"سيتم عرض المزادات المتاحة هنا عند إضافتها من قبل الشركات\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 849,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                lineNumber: 845,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                        lineNumber: 841,\n                        columnNumber: 15\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                    lineNumber: 840,\n                    columnNumber: 13\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                lineNumber: 839,\n                columnNumber: 11\n            }, this),\n            loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                    className: \"py-16 text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col items-center gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                lineNumber: 866,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-muted-foreground\",\n                                children: \"جاري تحميل المزادات...\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                lineNumber: 867,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                        lineNumber: 865,\n                        columnNumber: 15\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                    lineNumber: 864,\n                    columnNumber: 13\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                lineNumber: 863,\n                columnNumber: 11\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__.Dialog, {\n                open: showBidModal,\n                onOpenChange: setShowBidModal,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__.DialogContent, {\n                    className: \"sm:max-w-md\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__.DialogHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__.DialogTitle, {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 878,\n                                        columnNumber: 17\n                                    }, this),\n                                    \"تقديم مزايدة\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                lineNumber: 877,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                            lineNumber: 876,\n                            columnNumber: 13\n                        }, this),\n                        selectedAuction && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gray-50 p-4 rounded-lg\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"font-semibold mb-2\",\n                                            children: selectedAuction.title\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                            lineNumber: 886,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-4 text-sm text-gray-600\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                            lineNumber: 889,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: [\n                                                                \"المزايدة الحالية: \",\n                                                                formatAmount(selectedAuction.currentBid || 0)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                            lineNumber: 890,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                    lineNumber: 888,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                            lineNumber: 893,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: [\n                                                                selectedAuction.bidsCount || 0,\n                                                                \" مزايدة\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                            lineNumber: 894,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                    lineNumber: 892,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                            lineNumber: 887,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 885,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                            htmlFor: \"bidAmount\",\n                                            children: [\n                                                \"مقدار المزايدة (\",\n                                                userCurrency,\n                                                \")\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                            lineNumber: 900,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                            id: \"bidAmount\",\n                                            type: \"number\",\n                                            value: bidAmount,\n                                            onChange: (e)=>setBidAmount(e.target.value),\n                                            placeholder: \"أدخل مقدار المزايدة\",\n                                            min: (selectedAuction.currentBid || 0) + 1\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                            lineNumber: 901,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-gray-500\",\n                                            children: [\n                                                \"الحد الأدنى: \",\n                                                formatAmount((selectedAuction.currentBid || 0) + 1)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                            lineNumber: 909,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 899,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                            lineNumber: 884,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__.DialogFooter, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"outline\",\n                                    onClick: ()=>{\n                                        setShowBidModal(false);\n                                        setBidAmount(\"\");\n                                        setSelectedAuction(null);\n                                    },\n                                    disabled: bidLoading,\n                                    children: \"إلغاء\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 917,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    onClick: submitBid,\n                                    disabled: bidLoading || !bidAmount,\n                                    className: \"bg-green-600 hover:bg-green-700\",\n                                    children: bidLoading ? \"جاري التقديم...\" : \"تقديم المزايدة\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 928,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                            lineNumber: 916,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                    lineNumber: 875,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                lineNumber: 874,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n        lineNumber: 588,\n        columnNumber: 5\n    }, this);\n}\n_s(UserAuctionsPage, \"0C/dfCNanwYtXVZSEIcjKhB8Lbk=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_9__.useRouter,\n        _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_10__.useToast,\n        _contexts_CurrencyContext__WEBPACK_IMPORTED_MODULE_11__.useCurrency\n    ];\n});\n_c = UserAuctionsPage;\nvar _c;\n$RefreshReg$(_c, \"UserAuctionsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/user/auctions/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./components/CurrencyDisplay.tsx":
/*!****************************************!*\
  !*** ./components/CurrencyDisplay.tsx ***!
  \****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CurrencyDisplay: function() { return /* binding */ CurrencyDisplay; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_CurrencyContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/CurrencyContext */ \"(app-pages-browser)/./contexts/CurrencyContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ CurrencyDisplay auto */ \nvar _s = $RefreshSig$();\n\n\nfunction CurrencyDisplay(param) {\n    let { amount, fromCurrency, className = \"\", showOriginal = false, showDualCurrency = true } = param;\n    _s();\n    const { formatAmountWithConversion, userCurrency } = (0,_contexts_CurrencyContext__WEBPACK_IMPORTED_MODULE_2__.useCurrency)();\n    const [displayText, setDisplayText] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [usdDisplayText, setUsdDisplayText] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const convertAndFormat = async ()=>{\n            try {\n                setIsLoading(true);\n                // Convert to user's selected currency\n                const formattedAmount = await formatAmountWithConversion(amount, fromCurrency, userCurrency);\n                setDisplayText(formattedAmount);\n                // Convert to USD if user currency is not USD and showDualCurrency is true\n                if (showDualCurrency && userCurrency !== \"USD\") {\n                    const usdFormattedAmount = await formatAmountWithConversion(amount, fromCurrency, \"USD\");\n                    setUsdDisplayText(usdFormattedAmount);\n                } else {\n                    setUsdDisplayText(\"\");\n                }\n            } catch (error) {\n                console.error(\"Currency conversion failed:\", error);\n                // Fallback to original amount with target currency symbol\n                setDisplayText(\"\".concat(amount.toLocaleString(), \" \").concat(userCurrency));\n                setUsdDisplayText(\"\");\n            } finally{\n                setIsLoading(false);\n            }\n        };\n        convertAndFormat();\n    }, [\n        amount,\n        fromCurrency,\n        userCurrency,\n        formatAmountWithConversion,\n        showDualCurrency\n    ]);\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"animate-pulse \".concat(className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"h-4 bg-gray-200 rounded w-20\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/CurrencyDisplay.tsx\",\n                lineNumber: 58,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/CurrencyDisplay.tsx\",\n            lineNumber: 57,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: className,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"font-semibold\",\n                children: displayText\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/CurrencyDisplay.tsx\",\n                lineNumber: 65,\n                columnNumber: 7\n            }, this),\n            showDualCurrency && userCurrency !== \"USD\" && usdDisplayText && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-sm text-gray-500 mt-1\",\n                children: usdDisplayText\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/CurrencyDisplay.tsx\",\n                lineNumber: 69,\n                columnNumber: 9\n            }, this),\n            showOriginal && fromCurrency !== userCurrency && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-xs text-gray-400 mt-1\",\n                children: [\n                    \"(من \",\n                    amount.toLocaleString(),\n                    \" \",\n                    fromCurrency,\n                    \")\"\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/CurrencyDisplay.tsx\",\n                lineNumber: 74,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/CurrencyDisplay.tsx\",\n        lineNumber: 64,\n        columnNumber: 5\n    }, this);\n}\n_s(CurrencyDisplay, \"4Ln3/C9xc+Ue4sBDNnkTE/bzAGI=\", false, function() {\n    return [\n        _contexts_CurrencyContext__WEBPACK_IMPORTED_MODULE_2__.useCurrency\n    ];\n});\n_c = CurrencyDisplay;\nvar _c;\n$RefreshReg$(_c, \"CurrencyDisplay\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/CurrencyDisplay.tsx\n"));

/***/ })

});