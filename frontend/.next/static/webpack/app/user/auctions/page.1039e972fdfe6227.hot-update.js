"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/user/auctions/page",{

/***/ "(app-pages-browser)/./app/user/auctions/page.tsx":
/*!************************************!*\
  !*** ./app/user/auctions/page.tsx ***!
  \************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ UserAuctionsPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./components/ui/dialog.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./components/ui/label.tsx\");\n/* harmony import */ var _barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,DollarSign,Eye,Gavel,Heart,Pause,Play,RefreshCw,Search,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,DollarSign,Eye,Gavel,Heart,Pause,Play,RefreshCw,Search,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pause.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,DollarSign,Eye,Gavel,Heart,Pause,Play,RefreshCw,Search,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,DollarSign,Eye,Gavel,Heart,Pause,Play,RefreshCw,Search,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,DollarSign,Eye,Gavel,Heart,Pause,Play,RefreshCw,Search,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/gavel.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,DollarSign,Eye,Gavel,Heart,Pause,Play,RefreshCw,Search,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,DollarSign,Eye,Gavel,Heart,Pause,Play,RefreshCw,Search,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,DollarSign,Eye,Gavel,Heart,Pause,Play,RefreshCw,Search,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,DollarSign,Eye,Gavel,Heart,Pause,Play,RefreshCw,Search,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,DollarSign,Eye,Gavel,Heart,Pause,Play,RefreshCw,Search,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./lib/api.ts\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/use-toast */ \"(app-pages-browser)/./components/ui/use-toast.tsx\");\n/* harmony import */ var _contexts_CurrencyContext__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/contexts/CurrencyContext */ \"(app-pages-browser)/./contexts/CurrencyContext.tsx\");\n/* harmony import */ var _components_CurrencyDisplay__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/CurrencyDisplay */ \"(app-pages-browser)/./components/CurrencyDisplay.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction UserAuctionsPage() {\n    _s();\n    const [auctions, setAuctions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [filteredAuctions, setFilteredAuctions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [savedAuctions, setSavedAuctions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Bidding modal state\n    const [showBidModal, setShowBidModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedAuction, setSelectedAuction] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [bidAmount, setBidAmount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [bidLoading, setBidLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Filter and sort state\n    const [sortBy, setSortBy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"endDate\");\n    const [filterCategory, setFilterCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // Auto-refresh state\n    const [autoRefresh, setAutoRefresh] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [lastRefresh, setLastRefresh] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Date());\n    // Optimistic locking\n    const [auctionVersions, setAuctionVersions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_9__.useRouter)();\n    const { toast } = (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_10__.useToast)();\n    const { userCurrency, formatAmount, convertAmount } = (0,_contexts_CurrencyContext__WEBPACK_IMPORTED_MODULE_11__.useCurrency)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Get user data and check permissions\n        const userData = localStorage.getItem(\"user\");\n        if (userData) {\n            const parsedUser = JSON.parse(userData);\n            setUser(parsedUser);\n            // Redirect admins to their proper dashboard\n            if (parsedUser.role === \"admin\" || parsedUser.role === \"super_admin\") {\n                toast({\n                    title: \"وصول غير مسموح\",\n                    description: \"المديرون يجب أن يستخدموا لوحة إدارة المزادات\",\n                    variant: \"destructive\"\n                });\n                router.push(\"/admin/auctions\");\n                return;\n            }\n            // Redirect government users to their dashboard\n            if (parsedUser.role === \"government\") {\n                toast({\n                    title: \"وصول غير مسموح\",\n                    description: \"الجهات الحكومية تستخدم نظام المناقصات\",\n                    variant: \"destructive\"\n                });\n                router.push(\"/government/dashboard\");\n                return;\n            }\n        }\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Load data\n        loadAuctions();\n        loadSavedAuctions();\n    }, []);\n    // Load auctions when currency changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (userCurrency) {\n            loadAuctions();\n        }\n    }, [\n        userCurrency\n    ]);\n    // Auto-refresh auctions every 30 seconds\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!autoRefresh) return;\n        const interval = setInterval(async ()=>{\n            try {\n                await loadAuctions(false) // Silent refresh without loading state\n                ;\n                setLastRefresh(new Date());\n                console.log(\"\\uD83D\\uDD04 Auto-refreshed auction data\");\n            } catch (error) {\n                console.error(\"Auto-refresh failed:\", error);\n            }\n        }, 30000) // 30 seconds\n        ;\n        return ()=>clearInterval(interval);\n    }, [\n        autoRefresh\n    ]);\n    const loadAuctions = async function() {\n        let showLoading = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : true;\n        try {\n            if (showLoading) {\n                setLoading(true);\n            }\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_8__.auctionAPI.getAll();\n            console.log(\"Auctions API response:\", response.data);\n            // Handle the API response structure properly\n            let auctionData = [];\n            if (response.data && response.data.success) {\n                var _response_data_data;\n                // If the response has a success field, extract the auctions from data.data.auctions\n                auctionData = ((_response_data_data = response.data.data) === null || _response_data_data === void 0 ? void 0 : _response_data_data.auctions) || [];\n            } else if (response.data && Array.isArray(response.data)) {\n                // If response.data is directly an array\n                auctionData = response.data;\n            } else if (response.data && response.data.data && Array.isArray(response.data.data)) {\n                // If the auctions are in response.data.data\n                auctionData = response.data.data;\n            }\n            // Transform and convert currency for each auction\n            const transformedData = await Promise.all(auctionData.map(async (auction)=>{\n                var _auction_bids, _auction_seller_profile, _auction_seller, _auction_seller_profile1, _auction_seller1, _auction_seller2, _auction_seller3;\n                const baseAuction = {\n                    id: auction._id || auction.id,\n                    title: auction.title,\n                    description: auction.description,\n                    currentBid: auction.currentPrice || auction.currentBid || auction.startingPrice || 0,\n                    endDate: auction.endDate,\n                    bidsCount: ((_auction_bids = auction.bids) === null || _auction_bids === void 0 ? void 0 : _auction_bids.length) || auction.bidsCount || 0,\n                    seller: ((_auction_seller = auction.seller) === null || _auction_seller === void 0 ? void 0 : (_auction_seller_profile = _auction_seller.profile) === null || _auction_seller_profile === void 0 ? void 0 : _auction_seller_profile.fullName) || ((_auction_seller1 = auction.seller) === null || _auction_seller1 === void 0 ? void 0 : (_auction_seller_profile1 = _auction_seller1.profile) === null || _auction_seller_profile1 === void 0 ? void 0 : _auction_seller_profile1.companyName) || ((_auction_seller2 = auction.seller) === null || _auction_seller2 === void 0 ? void 0 : _auction_seller2.name) || ((_auction_seller3 = auction.seller) === null || _auction_seller3 === void 0 ? void 0 : _auction_seller3._id) || \"غير محدد\",\n                    category: auction.category,\n                    status: auction.status,\n                    views: auction.views || 0,\n                    location: auction.location,\n                    currency: auction.currency || \"SAR\",\n                    version: auction.version || 0\n                };\n                // Store version for optimistic locking\n                setAuctionVersions((prev)=>({\n                        ...prev,\n                        [baseAuction.id]: baseAuction.version\n                    }));\n                // Convert currency if needed\n                try {\n                    if (baseAuction.currency !== userCurrency) {\n                        const convertedBid = await convertAmount(baseAuction.currentBid, baseAuction.currency, userCurrency);\n                        return {\n                            ...baseAuction,\n                            currentBid: Math.round(convertedBid),\n                            originalCurrency: baseAuction.currency,\n                            currency: userCurrency\n                        };\n                    }\n                } catch (conversionError) {\n                    console.warn(\"Currency conversion failed for auction:\", baseAuction.id, conversionError);\n                }\n                return baseAuction;\n            }));\n            setAuctions(transformedData);\n            setFilteredAuctions(transformedData);\n            console.log(\"✅ Set auctions and filteredAuctions:\", transformedData.length);\n        } catch (error) {\n            console.error(\"Error loading auctions:\", error);\n            setAuctions([]);\n            setFilteredAuctions([]);\n            toast({\n                title: \"فشل في تحميل المزادات\",\n                description: \"حدث خطأ أثناء تحميل المزادات. يرجى المحاولة مرة أخرى.\",\n                variant: \"destructive\"\n            });\n        } finally{\n            if (showLoading) {\n                setLoading(false);\n            }\n            console.log(\"\\uD83C\\uDFC1 Loading finished. Auctions loaded:\", auctions.length);\n        }\n    };\n    const loadSavedAuctions = async ()=>{\n        try {\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_8__.favoritesAPI.getFavorites({\n                type: \"auction\"\n            });\n            // Handle different response structures\n            let favoritesData = [];\n            if (response.data && response.data.success) {\n                favoritesData = response.data.data || [];\n            } else if (response.data && Array.isArray(response.data)) {\n                favoritesData = response.data;\n            } else if (response.data && response.data.favorites && Array.isArray(response.data.favorites)) {\n                favoritesData = response.data.favorites;\n            }\n            // Check if favoritesData is an array before mapping\n            if (Array.isArray(favoritesData)) {\n                const savedIds = new Set(favoritesData.map((fav)=>fav.itemId || fav._id || fav.id));\n                setSavedAuctions(savedIds);\n            } else {\n                console.warn(\"Favorites data is not an array:\", favoritesData);\n                setSavedAuctions(new Set());\n            }\n        } catch (error) {\n            console.error(\"Error loading saved auctions:\", error);\n            // Don't show error toast for favorites as it's not critical\n            setSavedAuctions(new Set());\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Ensure auctions is an array before filtering\n        if (Array.isArray(auctions)) {\n            let filtered = auctions.filter((auction)=>{\n                var _auction_title, _auction_description;\n                const matchesSearch = ((_auction_title = auction.title) === null || _auction_title === void 0 ? void 0 : _auction_title.toLowerCase().includes(searchTerm.toLowerCase())) || ((_auction_description = auction.description) === null || _auction_description === void 0 ? void 0 : _auction_description.toLowerCase().includes(searchTerm.toLowerCase()));\n                const matchesCategory = !filterCategory || auction.category === filterCategory;\n                return matchesSearch && matchesCategory;\n            });\n            // Sort the filtered results\n            filtered = filtered.sort((a, b)=>{\n                switch(sortBy){\n                    case \"endDate\":\n                        return new Date(a.endDate).getTime() - new Date(b.endDate).getTime();\n                    case \"currentBid\":\n                        return (b.currentBid || 0) - (a.currentBid || 0);\n                    case \"bidsCount\":\n                        return (b.bidsCount || 0) - (a.bidsCount || 0);\n                    case \"title\":\n                        return a.title.localeCompare(b.title);\n                    default:\n                        return 0;\n                }\n            });\n            setFilteredAuctions(filtered);\n            console.log(\"\\uD83D\\uDD0D Filtered auctions:\", filtered.length, \"from\", auctions.length);\n        } else {\n            setFilteredAuctions([]);\n        }\n    }, [\n        searchTerm,\n        auctions,\n        filterCategory,\n        sortBy\n    ]);\n    const getTimeRemaining = (endDate)=>{\n        const now = new Date();\n        const end = new Date(endDate);\n        const diff = end.getTime() - now.getTime();\n        if (diff <= 0) return \"انتهى\";\n        const hours = Math.floor(diff / (1000 * 60 * 60));\n        const days = Math.floor(hours / 24);\n        if (days > 0) return \"\".concat(days, \" يوم\");\n        return \"\".concat(hours, \" ساعة\");\n    };\n    // Check if user can bid\n    const canUserBid = (auction)=>{\n        var _user_profile;\n        if (!user) return false;\n        // Only individuals and companies can bid\n        if (user.role !== \"individual\" && user.role !== \"company\") {\n            return false;\n        }\n        // Companies cannot bid on their own auctions\n        if (user.role === \"company\" && auction.seller === ((_user_profile = user.profile) === null || _user_profile === void 0 ? void 0 : _user_profile.companyName)) {\n            return false;\n        }\n        return true;\n    };\n    const handleBid = async (auctionId)=>{\n        console.log(\"\\uD83C\\uDFAF Bid button clicked for auction:\", auctionId);\n        try {\n            var _freshAuction_bids, _freshAuction_seller_profile, _freshAuction_seller, _freshAuction_seller_profile1, _freshAuction_seller1, _freshAuction_seller2;\n            // Fetch fresh auction data before opening modal\n            setLoading(true);\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_8__.auctionAPI.getById(auctionId);\n            let freshAuction = null;\n            if (response.data && response.data.success) {\n                freshAuction = response.data.data.auction;\n            } else if (response.data && response.data.auction) {\n                freshAuction = response.data.auction;\n            }\n            if (!freshAuction) {\n                toast({\n                    title: \"خطأ\",\n                    description: \"لم يتم العثور على المزاد\",\n                    variant: \"destructive\"\n                });\n                return;\n            }\n            // Transform fresh data to match our format\n            const transformedAuction = {\n                id: freshAuction._id || freshAuction.id,\n                title: freshAuction.title,\n                description: freshAuction.description,\n                currentBid: freshAuction.currentPrice || freshAuction.currentBid || freshAuction.startingPrice || 0,\n                endDate: freshAuction.endDate,\n                bidsCount: ((_freshAuction_bids = freshAuction.bids) === null || _freshAuction_bids === void 0 ? void 0 : _freshAuction_bids.length) || freshAuction.bidsCount || 0,\n                seller: ((_freshAuction_seller = freshAuction.seller) === null || _freshAuction_seller === void 0 ? void 0 : (_freshAuction_seller_profile = _freshAuction_seller.profile) === null || _freshAuction_seller_profile === void 0 ? void 0 : _freshAuction_seller_profile.fullName) || ((_freshAuction_seller1 = freshAuction.seller) === null || _freshAuction_seller1 === void 0 ? void 0 : (_freshAuction_seller_profile1 = _freshAuction_seller1.profile) === null || _freshAuction_seller_profile1 === void 0 ? void 0 : _freshAuction_seller_profile1.companyName) || ((_freshAuction_seller2 = freshAuction.seller) === null || _freshAuction_seller2 === void 0 ? void 0 : _freshAuction_seller2.name) || \"غير محدد\",\n                category: freshAuction.category,\n                status: freshAuction.status\n            };\n            // Check if user can bid\n            if (!canUserBid(transformedAuction)) {\n                if ((user === null || user === void 0 ? void 0 : user.role) === \"admin\" || (user === null || user === void 0 ? void 0 : user.role) === \"super_admin\") {\n                    toast({\n                        title: \"وصول غير مسموح\",\n                        description: \"المديرون لا يمكنهم المزايدة على المزادات\",\n                        variant: \"destructive\"\n                    });\n                } else if ((user === null || user === void 0 ? void 0 : user.role) === \"government\") {\n                    toast({\n                        title: \"وصول غير مسموح\",\n                        description: \"الجهات الحكومية لا تزايد على المزادات\",\n                        variant: \"destructive\"\n                    });\n                } else if ((user === null || user === void 0 ? void 0 : user.role) === \"company\") {\n                    toast({\n                        title: \"وصول غير مسموح\",\n                        description: \"لا يمكنك المزايدة على مزادك الخاص\",\n                        variant: \"destructive\"\n                    });\n                } else {\n                    toast({\n                        title: \"وصول غير مسموح\",\n                        description: \"غير مسموح لك بالمزايدة\",\n                        variant: \"destructive\"\n                    });\n                }\n                return;\n            }\n            // Open bidding modal with fresh data\n            setSelectedAuction(transformedAuction);\n            setBidAmount(String((transformedAuction.currentBid || 0) + 1000));\n            setShowBidModal(true);\n            toast({\n                title: \"\\uD83D\\uDCCA تم تحديث بيانات المزاد\",\n                description: \"المزايدة الحالية: \".concat(formatAmount(transformedAuction.currentBid || 0))\n            });\n        } catch (error) {\n            console.error(\"Error fetching fresh auction data:\", error);\n            toast({\n                title: \"خطأ في تحديث البيانات\",\n                description: \"سيتم استخدام البيانات المحفوظة محلياً\",\n                variant: \"destructive\"\n            });\n            // Fallback to cached data\n            const currentAuction = auctions.find((a)=>a.id === auctionId);\n            if (currentAuction && canUserBid(currentAuction)) {\n                setSelectedAuction(currentAuction);\n                setBidAmount(String((currentAuction.currentBid || 0) + 1000));\n                setShowBidModal(true);\n            }\n        } finally{\n            setLoading(false);\n        }\n    };\n    const submitBid = async ()=>{\n        if (!selectedAuction) return;\n        const bidAmountNum = parseFloat(bidAmount);\n        if (isNaN(bidAmountNum) || bidAmountNum <= (selectedAuction.currentBid || 0)) {\n            toast({\n                title: \"مزايدة غير صالحة\",\n                description: \"يجب أن تكون المزايدة أكبر من المزايدة الحالية\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        try {\n            setBidLoading(true);\n            // Get auction version for optimistic locking\n            const auctionVersion = auctionVersions[selectedAuction.id];\n            // Prepare bid data with currency and version\n            const bidData = {\n                bidAmount: bidAmountNum,\n                currency: userCurrency,\n                version: auctionVersion\n            };\n            // Submit bid and wait for server response\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_8__[\"default\"].post(\"/auctions/\".concat(selectedAuction.id, \"/bid\"), bidData);\n            const result = response.data;\n            if (!result.success) {\n                throw new Error(result.message || \"Bid failed\");\n            }\n            // Close modal first\n            setShowBidModal(false);\n            setBidAmount(\"\");\n            setSelectedAuction(null);\n            // Reload fresh data from server\n            await loadAuctions();\n            // Show success message with currency formatting\n            const formattedAmount = formatAmount(bidAmountNum);\n            toast({\n                title: \"تم تقديم المزايدة بنجاح! \\uD83C\\uDF89\",\n                description: \"مقدار المزايدة: \".concat(formattedAmount)\n            });\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error(\"Error placing bid:\", error);\n            // Close modal on error too\n            setShowBidModal(false);\n            setBidAmount(\"\");\n            setSelectedAuction(null);\n            // Show specific error message\n            const errorMessage = ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"يرجى المحاولة مرة أخرى\";\n            if (errorMessage.includes(\"higher than current price\") || errorMessage.includes(\"أكبر من السعر الحالي\")) {\n                toast({\n                    title: \"مزايدة منخفضة ⚠️\",\n                    description: \"قام شخص آخر بمزايدة أعلى. يرجى المحاولة بمبلغ أكبر.\",\n                    variant: \"destructive\"\n                });\n            } else if (errorMessage.includes(\"ended\") || errorMessage.includes(\"انتهى\")) {\n                toast({\n                    title: \"انتهى المزاد ⏰\",\n                    description: \"لقد انتهى هذا المزاد ولا يمكن قبول مزايدات جديدة.\",\n                    variant: \"destructive\"\n                });\n            } else {\n                toast({\n                    title: \"فشل في تقديم المزايدة\",\n                    description: errorMessage,\n                    variant: \"destructive\"\n                });\n            }\n            // Always reload data on error to sync with server\n            await loadAuctions();\n        } finally{\n            setBidLoading(false);\n        }\n    };\n    const handleSaveAuction = async (auctionId)=>{\n        console.log(\"❤️ Save button clicked for auction:\", auctionId);\n        // Show immediate feedback that button was clicked\n        toast({\n            title: \"\\uD83D\\uDC86 تم الضغط على زر الحفظ\",\n            description: \"جاري معالجة الطلب...\"\n        });\n        try {\n            const auction = auctions.find((a)=>a.id === auctionId);\n            if (!auction) {\n                toast({\n                    title: \"خطأ\",\n                    description: \"لم يتم العثور على المزاد\",\n                    variant: \"destructive\"\n                });\n                return;\n            }\n            const isCurrentlySaved = savedAuctions.has(auctionId);\n            // Optimistic UI update\n            setSavedAuctions((prev)=>{\n                const newSet = new Set(prev);\n                if (isCurrentlySaved) {\n                    newSet.delete(auctionId);\n                } else {\n                    newSet.add(auctionId);\n                }\n                return newSet;\n            });\n            if (isCurrentlySaved) {\n                // Remove from favorites\n                await _lib_api__WEBPACK_IMPORTED_MODULE_8__.favoritesAPI.removeFavorite(\"auction\", auctionId);\n                toast({\n                    title: \"❤️ تم إزالة من المفضلة\",\n                    description: 'تم إزالة \"'.concat(auction.title, '\" من المفضلة')\n                });\n            } else {\n                // Add to favorites\n                await _lib_api__WEBPACK_IMPORTED_MODULE_8__.favoritesAPI.addFavorite({\n                    itemType: \"auction\",\n                    itemId: auctionId,\n                    notifications: {\n                        bidUpdates: true,\n                        statusChanges: true,\n                        endingSoon: true\n                    }\n                });\n                toast({\n                    title: \"\\uD83D\\uDC96 تم إضافة إلى المفضلة\",\n                    description: 'تم إضافة \"'.concat(auction.title, '\" إلى المفضلة')\n                });\n            }\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error(\"Error saving auction:\", error);\n            // Revert optimistic update on error\n            setSavedAuctions((prev)=>{\n                const newSet = new Set(prev);\n                if (savedAuctions.has(auctionId)) {\n                    newSet.add(auctionId);\n                } else {\n                    newSet.delete(auctionId);\n                }\n                return newSet;\n            });\n            toast({\n                title: \"فشل في حفظ المزاد\",\n                description: ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"يرجى المحاولة مرة أخرى\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleViewAuction = async (auctionId)=>{\n        console.log(\"\\uD83D\\uDC41️ View button clicked for auction:\", auctionId);\n        try {\n            const auction = auctions.find((a)=>a.id === auctionId);\n            if (!auction) {\n                toast({\n                    title: \"خطأ\",\n                    description: \"لم يتم العثور على المزاد\",\n                    variant: \"destructive\"\n                });\n                return;\n            }\n            // Navigate to auction detail page\n            router.push(\"/auctions/\".concat(auctionId));\n            toast({\n                title: \"\\uD83D\\uDC41️ جاري تحميل التفاصيل\",\n                description: \"سيتم عرض تفاصيل المزاد\"\n            });\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error(\"Error viewing auction:\", error);\n            toast({\n                title: \"فشل في عرض تفاصيل المزاد\",\n                description: ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"يرجى المحاولة مرة أخرى\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-3xl font-bold\",\n                                    children: \"المزادات المتاحة\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 592,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-muted-foreground\",\n                                    children: [\n                                        \"استكشف المزادات الحالية وشارك في المزايدة\",\n                                        autoRefresh && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs text-green-600 ml-2\",\n                                            children: \"• تحديث تلقائي كل 30 ثانية\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                            lineNumber: 596,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 593,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                            lineNumber: 591,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    onClick: async ()=>{\n                                        await loadAuctions(true) // Show loading state\n                                        ;\n                                        setLastRefresh(new Date());\n                                        toast({\n                                            title: \"\\uD83D\\uDD04 تم تحديث البيانات\",\n                                            description: \"تم العثور على \".concat(filteredAuctions.length, \" مزاد متاح\")\n                                        });\n                                    },\n                                    variant: \"outline\",\n                                    disabled: loading,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"h-4 w-4 ml-2 \".concat(loading ? \"animate-spin\" : \"\")\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                            lineNumber: 616,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"تحديث\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 604,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    onClick: ()=>{\n                                        setAutoRefresh(!autoRefresh);\n                                        toast({\n                                            title: autoRefresh ? \"⏸️ تم إيقاف التحديث التلقائي\" : \"▶️ تم تشغيل التحديث التلقائي\",\n                                            description: autoRefresh ? \"يمكنك التحديث يدوياً\" : \"سيتم التحديث كل 30 ثانية\"\n                                        });\n                                    },\n                                    variant: autoRefresh ? \"default\" : \"outline\",\n                                    size: \"icon\",\n                                    children: autoRefresh ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 630,\n                                        columnNumber: 32\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 630,\n                                        columnNumber: 64\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 619,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                            lineNumber: 602,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                    lineNumber: 590,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                lineNumber: 589,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                    className: \"pt-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        className: \"absolute right-3 top-3 h-4 w-4 text-muted-foreground\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 641,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                        placeholder: \"ابحث في المزادات...\",\n                                        value: searchTerm,\n                                        onChange: (e)=>setSearchTerm(e.target.value),\n                                        className: \"pr-10\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 642,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                lineNumber: 640,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col sm:flex-row gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                htmlFor: \"category\",\n                                                className: \"text-sm font-medium\",\n                                                children: \"الفئة\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                lineNumber: 652,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                id: \"category\",\n                                                value: filterCategory,\n                                                onChange: (e)=>setFilterCategory(e.target.value),\n                                                className: \"w-full mt-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"\",\n                                                        children: \"جميع الفئات\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 659,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"electronics\",\n                                                        children: \"إلكترونيات\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 660,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"vehicles\",\n                                                        children: \"سيارات\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 661,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"real_estate\",\n                                                        children: \"عقارات\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 662,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"art_collectibles\",\n                                                        children: \"فنون ومقتنيات\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 663,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"machinery\",\n                                                        children: \"معدات\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 664,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"furniture\",\n                                                        children: \"أثاث\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 665,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"jewelry\",\n                                                        children: \"مجوهرات\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 666,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"books_media\",\n                                                        children: \"كتب ووسائط\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 667,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"clothing\",\n                                                        children: \"ملابس\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 668,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"sports\",\n                                                        children: \"رياضة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 669,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"other\",\n                                                        children: \"أخرى\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 670,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                lineNumber: 653,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 651,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                htmlFor: \"sort\",\n                                                className: \"text-sm font-medium\",\n                                                children: \"ترتيب حسب\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                lineNumber: 675,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                id: \"sort\",\n                                                value: sortBy,\n                                                onChange: (e)=>setSortBy(e.target.value),\n                                                className: \"w-full mt-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"endDate\",\n                                                        children: \"تاريخ الانتهاء\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 682,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"currentBid\",\n                                                        children: \"أعلى مزايدة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 683,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"bidsCount\",\n                                                        children: \"عدد المزايدات\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 684,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"title\",\n                                                        children: \"الاسم\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 685,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                lineNumber: 676,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 674,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                lineNumber: 650,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                        lineNumber: 639,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                    lineNumber: 638,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                lineNumber: 637,\n                columnNumber: 9\n            }, this),\n            Array.isArray(filteredAuctions) && filteredAuctions.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                    className: \"text-lg\",\n                                    children: \"إجمالي المزادات\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 698,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                    className: \"text-2xl font-bold text-blue-600\",\n                                    children: filteredAuctions.length\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 699,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                            lineNumber: 697,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                        lineNumber: 696,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                    className: \"text-lg\",\n                                    children: \"المزايدات النشطة\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 706,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                    className: \"text-2xl font-bold text-green-600\",\n                                    children: filteredAuctions.reduce((sum, a)=>sum + (a.bidsCount || 0), 0)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 707,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                            lineNumber: 705,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                        lineNumber: 704,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                    className: \"text-lg\",\n                                    children: \"أعلى مزايدة\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 714,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                    className: \"text-2xl font-bold text-purple-600\",\n                                    children: filteredAuctions.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_CurrencyDisplay__WEBPACK_IMPORTED_MODULE_12__.CurrencyDisplay, {\n                                        amount: Math.max(...filteredAuctions.map((a)=>a.currentBid || 0)),\n                                        fromCurrency: \"SAR\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 717,\n                                        columnNumber: 21\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_CurrencyDisplay__WEBPACK_IMPORTED_MODULE_12__.CurrencyDisplay, {\n                                        amount: 0,\n                                        fromCurrency: \"SAR\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 722,\n                                        columnNumber: 21\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 715,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                            lineNumber: 713,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                        lineNumber: 712,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                lineNumber: 695,\n                columnNumber: 11\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                children: Array.isArray(filteredAuctions) && filteredAuctions.map((auction)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        className: \"hover:shadow-lg transition-shadow cursor-pointer\",\n                        onClick: ()=>handleViewAuction(auction.id),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"aspect-video bg-gradient-to-br from-gray-100 to-gray-200 rounded-lg mb-4 flex items-center justify-center relative overflow-hidden\",\n                                        children: [\n                                            auction.images && auction.images.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: auction.images[0].url,\n                                                alt: auction.title,\n                                                className: \"w-full h-full object-cover\",\n                                                onError: (e)=>{\n                                                    e.currentTarget.style.display = \"none\";\n                                                    e.currentTarget.nextElementSibling.style.display = \"flex\";\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                lineNumber: 737,\n                                                columnNumber: 21\n                                            }, this) : null,\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-center w-full h-full text-gray-500\",\n                                                style: {\n                                                    display: auction.images && auction.images.length > 0 ? \"none\" : \"flex\"\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                            className: \"h-8 w-8 mx-auto mb-2 text-gray-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                            lineNumber: 749,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm\",\n                                                            children: \"صورة المزاد\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                            lineNumber: 750,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                    lineNumber: 748,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                lineNumber: 747,\n                                                columnNumber: 19\n                                            }, this),\n                                            auction.status === \"active\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                className: \"absolute top-2 right-2 bg-green-500\",\n                                                children: \"نشط\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                lineNumber: 754,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 735,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                        className: \"text-lg line-clamp-2\",\n                                        children: auction.title\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 759,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                        className: \"line-clamp-2\",\n                                        children: auction.description\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 760,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                lineNumber: 734,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-muted-foreground\",\n                                                        children: \"أعلى مزايدة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 767,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xl font-bold text-green-600\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_CurrencyDisplay__WEBPACK_IMPORTED_MODULE_12__.CurrencyDisplay, {\n                                                            amount: auction.currentBid || 0,\n                                                            fromCurrency: \"SAR\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                            lineNumber: 769,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 768,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                lineNumber: 766,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-muted-foreground\",\n                                                        children: \"المزايدات\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 776,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-lg font-semibold\",\n                                                        children: auction.bidsCount || 0\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 777,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                lineNumber: 775,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 765,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                className: \"h-4 w-4 text-muted-foreground\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                lineNumber: 782,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-muted-foreground\",\n                                                children: [\n                                                    \"ينتهي خلال: \",\n                                                    getTimeRemaining(auction.endDate)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                lineNumber: 783,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 781,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-muted-foreground\",\n                                        children: [\n                                            \"البائع: \",\n                                            auction.seller\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 788,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                onClick: (e)=>{\n                                                    e.preventDefault();\n                                                    e.stopPropagation();\n                                                    handleBid(auction.id);\n                                                },\n                                                className: \"flex-1\",\n                                                disabled: loading || bidLoading,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        className: \"h-4 w-4 ml-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 802,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    bidLoading ? \"جاري المزايدة...\" : \"مزايدة\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                lineNumber: 793,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                variant: savedAuctions.has(auction.id) ? \"default\" : \"outline\",\n                                                size: \"icon\",\n                                                onClick: (e)=>{\n                                                    e.preventDefault();\n                                                    e.stopPropagation();\n                                                    handleSaveAuction(auction.id);\n                                                },\n                                                disabled: loading,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                    className: \"h-4 w-4 \".concat(savedAuctions.has(auction.id) ? \"fill-current\" : \"\")\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                    lineNumber: 815,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                lineNumber: 805,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                variant: \"outline\",\n                                                size: \"icon\",\n                                                onClick: (e)=>{\n                                                    e.preventDefault();\n                                                    e.stopPropagation();\n                                                    handleViewAuction(auction.id);\n                                                },\n                                                disabled: loading,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                    lineNumber: 827,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                lineNumber: 817,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 792,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                lineNumber: 764,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, auction.id, true, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                        lineNumber: 733,\n                        columnNumber: 13\n                    }, this))\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                lineNumber: 731,\n                columnNumber: 9\n            }, this),\n            !loading && (!Array.isArray(filteredAuctions) || filteredAuctions.length === 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                    className: \"py-16 text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col items-center gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-16 h-16 rounded-full bg-gray-100 flex items-center justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                    className: \"h-8 w-8 text-gray-400\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 841,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                lineNumber: 840,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold mb-2\",\n                                        children: searchTerm ? \"لا توجد مزادات تطابق البحث\" : \"لا توجد مزادات متاحة حالياً\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 844,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-muted-foreground\",\n                                        children: searchTerm ? \"جرب البحث بكلمات مختلفة أو امسح مربع البحث لعرض جميع المزادات\" : \"سيتم عرض المزادات المتاحة هنا عند إضافتها من قبل الشركات\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 847,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                lineNumber: 843,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                        lineNumber: 839,\n                        columnNumber: 15\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                    lineNumber: 838,\n                    columnNumber: 13\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                lineNumber: 837,\n                columnNumber: 11\n            }, this),\n            loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                    className: \"py-16 text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col items-center gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                lineNumber: 864,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-muted-foreground\",\n                                children: \"جاري تحميل المزادات...\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                lineNumber: 865,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                        lineNumber: 863,\n                        columnNumber: 15\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                    lineNumber: 862,\n                    columnNumber: 13\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                lineNumber: 861,\n                columnNumber: 11\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__.Dialog, {\n                open: showBidModal,\n                onOpenChange: setShowBidModal,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__.DialogContent, {\n                    className: \"sm:max-w-md\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__.DialogHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__.DialogTitle, {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 876,\n                                        columnNumber: 17\n                                    }, this),\n                                    \"تقديم مزايدة\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                lineNumber: 875,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                            lineNumber: 874,\n                            columnNumber: 13\n                        }, this),\n                        selectedAuction && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gray-50 p-4 rounded-lg\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"font-semibold mb-2\",\n                                            children: selectedAuction.title\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                            lineNumber: 884,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-4 text-sm text-gray-600\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                            lineNumber: 887,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"المزايدة الحالية: \"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                            lineNumber: 888,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_CurrencyDisplay__WEBPACK_IMPORTED_MODULE_12__.CurrencyDisplay, {\n                                                            amount: selectedAuction.currentBid || 0,\n                                                            fromCurrency: \"SAR\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                            lineNumber: 889,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                    lineNumber: 886,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                            lineNumber: 895,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: [\n                                                                selectedAuction.bidsCount || 0,\n                                                                \" مزايدة\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                            lineNumber: 896,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                    lineNumber: 894,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                            lineNumber: 885,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 883,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                            htmlFor: \"bidAmount\",\n                                            children: [\n                                                \"مقدار المزايدة (\",\n                                                userCurrency,\n                                                \")\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                            lineNumber: 902,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                            id: \"bidAmount\",\n                                            type: \"number\",\n                                            value: bidAmount,\n                                            onChange: (e)=>setBidAmount(e.target.value),\n                                            placeholder: \"أدخل مقدار المزايدة\",\n                                            min: (selectedAuction.currentBid || 0) + 1\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                            lineNumber: 903,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-gray-500\",\n                                            children: [\n                                                \"الحد الأدنى: \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_CurrencyDisplay__WEBPACK_IMPORTED_MODULE_12__.CurrencyDisplay, {\n                                                    amount: (selectedAuction.currentBid || 0) + 1,\n                                                    fromCurrency: \"SAR\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                    lineNumber: 912,\n                                                    columnNumber: 34\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                            lineNumber: 911,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 901,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                            lineNumber: 882,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__.DialogFooter, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"outline\",\n                                    onClick: ()=>{\n                                        setShowBidModal(false);\n                                        setBidAmount(\"\");\n                                        setSelectedAuction(null);\n                                    },\n                                    disabled: bidLoading,\n                                    children: \"إلغاء\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 922,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    onClick: submitBid,\n                                    disabled: bidLoading || !bidAmount,\n                                    className: \"bg-green-600 hover:bg-green-700\",\n                                    children: bidLoading ? \"جاري التقديم...\" : \"تقديم المزايدة\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 933,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                            lineNumber: 921,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                    lineNumber: 873,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                lineNumber: 872,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n        lineNumber: 588,\n        columnNumber: 5\n    }, this);\n}\n_s(UserAuctionsPage, \"0C/dfCNanwYtXVZSEIcjKhB8Lbk=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_9__.useRouter,\n        _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_10__.useToast,\n        _contexts_CurrencyContext__WEBPACK_IMPORTED_MODULE_11__.useCurrency\n    ];\n});\n_c = UserAuctionsPage;\nvar _c;\n$RefreshReg$(_c, \"UserAuctionsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/user/auctions/page.tsx\n"));

/***/ })

});