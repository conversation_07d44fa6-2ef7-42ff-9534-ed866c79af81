"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/user/auctions/page",{

/***/ "(app-pages-browser)/./app/user/auctions/page.tsx":
/*!************************************!*\
  !*** ./app/user/auctions/page.tsx ***!
  \************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ UserAuctionsPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./components/ui/dialog.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./components/ui/label.tsx\");\n/* harmony import */ var _barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,DollarSign,Eye,Gavel,Heart,Pause,Play,RefreshCw,Search,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,DollarSign,Eye,Gavel,Heart,Pause,Play,RefreshCw,Search,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pause.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,DollarSign,Eye,Gavel,Heart,Pause,Play,RefreshCw,Search,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,DollarSign,Eye,Gavel,Heart,Pause,Play,RefreshCw,Search,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,DollarSign,Eye,Gavel,Heart,Pause,Play,RefreshCw,Search,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/gavel.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,DollarSign,Eye,Gavel,Heart,Pause,Play,RefreshCw,Search,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,DollarSign,Eye,Gavel,Heart,Pause,Play,RefreshCw,Search,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,DollarSign,Eye,Gavel,Heart,Pause,Play,RefreshCw,Search,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,DollarSign,Eye,Gavel,Heart,Pause,Play,RefreshCw,Search,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,DollarSign,Eye,Gavel,Heart,Pause,Play,RefreshCw,Search,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./lib/api.ts\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/use-toast */ \"(app-pages-browser)/./components/ui/use-toast.tsx\");\n/* harmony import */ var _lib_currencyService__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/lib/currencyService */ \"(app-pages-browser)/./lib/currencyService.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nfunction UserAuctionsPage() {\n    _s();\n    const [auctions, setAuctions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [filteredAuctions, setFilteredAuctions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [savedAuctions, setSavedAuctions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Bidding modal state\n    const [showBidModal, setShowBidModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedAuction, setSelectedAuction] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [bidAmount, setBidAmount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [bidLoading, setBidLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Filter and sort state\n    const [sortBy, setSortBy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"endDate\");\n    const [filterCategory, setFilterCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // Auto-refresh state\n    const [autoRefresh, setAutoRefresh] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [lastRefresh, setLastRefresh] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Date());\n    // Currency state\n    const [userCurrency, setUserCurrency] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"SAR\");\n    const [exchangeRates, setExchangeRates] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    // Optimistic locking\n    const [auctionVersions, setAuctionVersions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_9__.useRouter)();\n    const { toast } = (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_10__.useToast)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Get user data and check permissions\n        const userData = localStorage.getItem(\"user\");\n        if (userData) {\n            const parsedUser = JSON.parse(userData);\n            setUser(parsedUser);\n            // Redirect admins to their proper dashboard\n            if (parsedUser.role === \"admin\" || parsedUser.role === \"super_admin\") {\n                toast({\n                    title: \"وصول غير مسموح\",\n                    description: \"المديرون يجب أن يستخدموا لوحة إدارة المزادات\",\n                    variant: \"destructive\"\n                });\n                router.push(\"/admin/auctions\");\n                return;\n            }\n            // Redirect government users to their dashboard\n            if (parsedUser.role === \"government\") {\n                toast({\n                    title: \"وصول غير مسموح\",\n                    description: \"الجهات الحكومية تستخدم نظام المناقصات\",\n                    variant: \"destructive\"\n                });\n                router.push(\"/government/dashboard\");\n                return;\n            }\n        }\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Initialize currency\n        const savedCurrency = _lib_currencyService__WEBPACK_IMPORTED_MODULE_11__[\"default\"].getUserCurrency();\n        setUserCurrency(savedCurrency);\n        loadAuctions();\n        loadSavedAuctions();\n        loadExchangeRates();\n    }, []);\n    const loadExchangeRates = async ()=>{\n        try {\n            const rates = await _lib_currencyService__WEBPACK_IMPORTED_MODULE_11__[\"default\"].getExchangeRates();\n            setExchangeRates(rates);\n        } catch (error) {\n            console.error(\"Failed to load exchange rates:\", error);\n        }\n    };\n    // Auto-refresh auctions every 30 seconds\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!autoRefresh) return;\n        const interval = setInterval(async ()=>{\n            try {\n                await loadAuctions(false) // Silent refresh without loading state\n                ;\n                setLastRefresh(new Date());\n                console.log(\"\\uD83D\\uDD04 Auto-refreshed auction data\");\n            } catch (error) {\n                console.error(\"Auto-refresh failed:\", error);\n            }\n        }, 30000) // 30 seconds\n        ;\n        return ()=>clearInterval(interval);\n    }, [\n        autoRefresh\n    ]);\n    const loadAuctions = async function() {\n        let showLoading = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : true;\n        try {\n            console.log(\"\\uD83D\\uDD04 Starting loadAuctions...\", showLoading ? \"with loading\" : \"silent\");\n            if (showLoading) {\n                setLoading(true);\n            }\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_8__.auctionAPI.getAll();\n            console.log(\"Auctions API response:\", response.data);\n            // Handle the API response structure properly\n            let auctionData = [];\n            if (response.data && response.data.success) {\n                var _response_data_data;\n                // If the response has a success field, extract the auctions from data.data.auctions\n                auctionData = ((_response_data_data = response.data.data) === null || _response_data_data === void 0 ? void 0 : _response_data_data.auctions) || [];\n            } else if (response.data && Array.isArray(response.data)) {\n                // If response.data is directly an array\n                auctionData = response.data;\n            } else if (response.data && response.data.data && Array.isArray(response.data.data)) {\n                // If the auctions are in response.data.data\n                auctionData = response.data.data;\n            }\n            // Transform and convert currency for each auction\n            const transformedData = await Promise.all(auctionData.map(async (auction)=>{\n                var _auction_bids, _auction_seller_profile, _auction_seller, _auction_seller_profile1, _auction_seller1, _auction_seller2, _auction_seller3;\n                const baseAuction = {\n                    id: auction._id || auction.id,\n                    title: auction.title,\n                    description: auction.description,\n                    currentBid: auction.currentPrice || auction.currentBid || auction.startingPrice || 0,\n                    endDate: auction.endDate,\n                    bidsCount: ((_auction_bids = auction.bids) === null || _auction_bids === void 0 ? void 0 : _auction_bids.length) || auction.bidsCount || 0,\n                    seller: ((_auction_seller = auction.seller) === null || _auction_seller === void 0 ? void 0 : (_auction_seller_profile = _auction_seller.profile) === null || _auction_seller_profile === void 0 ? void 0 : _auction_seller_profile.fullName) || ((_auction_seller1 = auction.seller) === null || _auction_seller1 === void 0 ? void 0 : (_auction_seller_profile1 = _auction_seller1.profile) === null || _auction_seller_profile1 === void 0 ? void 0 : _auction_seller_profile1.companyName) || ((_auction_seller2 = auction.seller) === null || _auction_seller2 === void 0 ? void 0 : _auction_seller2.name) || ((_auction_seller3 = auction.seller) === null || _auction_seller3 === void 0 ? void 0 : _auction_seller3._id) || \"غير محدد\",\n                    category: auction.category,\n                    status: auction.status,\n                    views: auction.views || 0,\n                    location: auction.location,\n                    currency: auction.currency || \"SAR\",\n                    version: auction.version || 0\n                };\n                // Store version for optimistic locking\n                setAuctionVersions((prev)=>({\n                        ...prev,\n                        [baseAuction.id]: baseAuction.version\n                    }));\n                // Convert currency if needed\n                console.log(\"\\uD83D\\uDD0D Checking conversion: \".concat(baseAuction.currency, \" vs \").concat(userCurrency, \" for auction \").concat(baseAuction.id));\n                try {\n                    if (baseAuction.currency !== userCurrency) {\n                        console.log(\"\\uD83D\\uDCB1 Converting \".concat(baseAuction.currentBid, \" \").concat(baseAuction.currency, \" → \").concat(userCurrency));\n                        const convertedBid = await _lib_currencyService__WEBPACK_IMPORTED_MODULE_11__[\"default\"].convertCurrency(baseAuction.currentBid, baseAuction.currency, userCurrency);\n                        console.log(\"\\uD83D\\uDCB1 Converted result: \".concat(Math.round(convertedBid), \" \").concat(userCurrency));\n                        return {\n                            ...baseAuction,\n                            currentBid: Math.round(convertedBid),\n                            originalCurrency: baseAuction.currency,\n                            currency: userCurrency\n                        };\n                    } else {\n                        console.log(\"⏭️ No conversion needed: currencies match (\".concat(baseAuction.currency, \")\"));\n                    }\n                } catch (conversionError) {\n                    console.warn(\"Currency conversion failed for auction:\", baseAuction.id, conversionError);\n                }\n                return baseAuction;\n            }));\n            setAuctions(transformedData);\n            setFilteredAuctions(transformedData);\n            console.log(\"✅ Set auctions and filteredAuctions:\", transformedData.length);\n        } catch (error) {\n            console.error(\"Error loading auctions:\", error);\n            setAuctions([]);\n            setFilteredAuctions([]);\n            toast({\n                title: \"فشل في تحميل المزادات\",\n                description: \"حدث خطأ أثناء تحميل المزادات. يرجى المحاولة مرة أخرى.\",\n                variant: \"destructive\"\n            });\n        } finally{\n            if (showLoading) {\n                setLoading(false);\n            }\n            console.log(\"\\uD83C\\uDFC1 Loading finished. Auctions loaded:\", auctions.length);\n        }\n    };\n    const loadSavedAuctions = async ()=>{\n        try {\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_8__.favoritesAPI.getFavorites({\n                type: \"auction\"\n            });\n            // Handle different response structures\n            let favoritesData = [];\n            if (response.data && response.data.success) {\n                favoritesData = response.data.data || [];\n            } else if (response.data && Array.isArray(response.data)) {\n                favoritesData = response.data;\n            } else if (response.data && response.data.favorites && Array.isArray(response.data.favorites)) {\n                favoritesData = response.data.favorites;\n            }\n            // Check if favoritesData is an array before mapping\n            if (Array.isArray(favoritesData)) {\n                const savedIds = new Set(favoritesData.map((fav)=>fav.itemId || fav._id || fav.id));\n                setSavedAuctions(savedIds);\n            } else {\n                console.warn(\"Favorites data is not an array:\", favoritesData);\n                setSavedAuctions(new Set());\n            }\n        } catch (error) {\n            console.error(\"Error loading saved auctions:\", error);\n            // Don't show error toast for favorites as it's not critical\n            setSavedAuctions(new Set());\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Ensure auctions is an array before filtering\n        if (Array.isArray(auctions)) {\n            let filtered = auctions.filter((auction)=>{\n                var _auction_title, _auction_description;\n                const matchesSearch = ((_auction_title = auction.title) === null || _auction_title === void 0 ? void 0 : _auction_title.toLowerCase().includes(searchTerm.toLowerCase())) || ((_auction_description = auction.description) === null || _auction_description === void 0 ? void 0 : _auction_description.toLowerCase().includes(searchTerm.toLowerCase()));\n                const matchesCategory = !filterCategory || auction.category === filterCategory;\n                return matchesSearch && matchesCategory;\n            });\n            // Sort the filtered results\n            filtered = filtered.sort((a, b)=>{\n                switch(sortBy){\n                    case \"endDate\":\n                        return new Date(a.endDate).getTime() - new Date(b.endDate).getTime();\n                    case \"currentBid\":\n                        return (b.currentBid || 0) - (a.currentBid || 0);\n                    case \"bidsCount\":\n                        return (b.bidsCount || 0) - (a.bidsCount || 0);\n                    case \"title\":\n                        return a.title.localeCompare(b.title);\n                    default:\n                        return 0;\n                }\n            });\n            setFilteredAuctions(filtered);\n            console.log(\"\\uD83D\\uDD0D Filtered auctions:\", filtered.length, \"from\", auctions.length);\n        } else {\n            setFilteredAuctions([]);\n        }\n    }, [\n        searchTerm,\n        auctions,\n        filterCategory,\n        sortBy\n    ]);\n    const getTimeRemaining = (endDate)=>{\n        const now = new Date();\n        const end = new Date(endDate);\n        const diff = end.getTime() - now.getTime();\n        if (diff <= 0) return \"انتهى\";\n        const hours = Math.floor(diff / (1000 * 60 * 60));\n        const days = Math.floor(hours / 24);\n        if (days > 0) return \"\".concat(days, \" يوم\");\n        return \"\".concat(hours, \" ساعة\");\n    };\n    // Check if user can bid\n    const canUserBid = (auction)=>{\n        var _user_profile;\n        if (!user) return false;\n        // Only individuals and companies can bid\n        if (user.role !== \"individual\" && user.role !== \"company\") {\n            return false;\n        }\n        // Companies cannot bid on their own auctions\n        if (user.role === \"company\" && auction.seller === ((_user_profile = user.profile) === null || _user_profile === void 0 ? void 0 : _user_profile.companyName)) {\n            return false;\n        }\n        return true;\n    };\n    const handleBid = async (auctionId)=>{\n        console.log(\"\\uD83C\\uDFAF Bid button clicked for auction:\", auctionId);\n        try {\n            var _freshAuction_bids, _freshAuction_seller_profile, _freshAuction_seller, _freshAuction_seller_profile1, _freshAuction_seller1, _freshAuction_seller2;\n            // Fetch fresh auction data before opening modal\n            setLoading(true);\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_8__.auctionAPI.getById(auctionId);\n            let freshAuction = null;\n            if (response.data && response.data.success) {\n                freshAuction = response.data.data.auction;\n            } else if (response.data && response.data.auction) {\n                freshAuction = response.data.auction;\n            }\n            if (!freshAuction) {\n                toast({\n                    title: \"خطأ\",\n                    description: \"لم يتم العثور على المزاد\",\n                    variant: \"destructive\"\n                });\n                return;\n            }\n            // Transform fresh data to match our format\n            const transformedAuction = {\n                id: freshAuction._id || freshAuction.id,\n                title: freshAuction.title,\n                description: freshAuction.description,\n                currentBid: freshAuction.currentPrice || freshAuction.currentBid || freshAuction.startingPrice || 0,\n                endDate: freshAuction.endDate,\n                bidsCount: ((_freshAuction_bids = freshAuction.bids) === null || _freshAuction_bids === void 0 ? void 0 : _freshAuction_bids.length) || freshAuction.bidsCount || 0,\n                seller: ((_freshAuction_seller = freshAuction.seller) === null || _freshAuction_seller === void 0 ? void 0 : (_freshAuction_seller_profile = _freshAuction_seller.profile) === null || _freshAuction_seller_profile === void 0 ? void 0 : _freshAuction_seller_profile.fullName) || ((_freshAuction_seller1 = freshAuction.seller) === null || _freshAuction_seller1 === void 0 ? void 0 : (_freshAuction_seller_profile1 = _freshAuction_seller1.profile) === null || _freshAuction_seller_profile1 === void 0 ? void 0 : _freshAuction_seller_profile1.companyName) || ((_freshAuction_seller2 = freshAuction.seller) === null || _freshAuction_seller2 === void 0 ? void 0 : _freshAuction_seller2.name) || \"غير محدد\",\n                category: freshAuction.category,\n                status: freshAuction.status\n            };\n            // Check if user can bid\n            if (!canUserBid(transformedAuction)) {\n                if ((user === null || user === void 0 ? void 0 : user.role) === \"admin\" || (user === null || user === void 0 ? void 0 : user.role) === \"super_admin\") {\n                    toast({\n                        title: \"وصول غير مسموح\",\n                        description: \"المديرون لا يمكنهم المزايدة على المزادات\",\n                        variant: \"destructive\"\n                    });\n                } else if ((user === null || user === void 0 ? void 0 : user.role) === \"government\") {\n                    toast({\n                        title: \"وصول غير مسموح\",\n                        description: \"الجهات الحكومية لا تزايد على المزادات\",\n                        variant: \"destructive\"\n                    });\n                } else if ((user === null || user === void 0 ? void 0 : user.role) === \"company\") {\n                    toast({\n                        title: \"وصول غير مسموح\",\n                        description: \"لا يمكنك المزايدة على مزادك الخاص\",\n                        variant: \"destructive\"\n                    });\n                } else {\n                    toast({\n                        title: \"وصول غير مسموح\",\n                        description: \"غير مسموح لك بالمزايدة\",\n                        variant: \"destructive\"\n                    });\n                }\n                return;\n            }\n            // Open bidding modal with fresh data\n            setSelectedAuction(transformedAuction);\n            setBidAmount(String((transformedAuction.currentBid || 0) + 1000));\n            setShowBidModal(true);\n            toast({\n                title: \"\\uD83D\\uDCCA تم تحديث بيانات المزاد\",\n                description: \"المزايدة الحالية: \".concat((transformedAuction.currentBid || 0).toLocaleString(), \" ر.س\")\n            });\n        } catch (error) {\n            console.error(\"Error fetching fresh auction data:\", error);\n            toast({\n                title: \"خطأ في تحديث البيانات\",\n                description: \"سيتم استخدام البيانات المحفوظة محلياً\",\n                variant: \"destructive\"\n            });\n            // Fallback to cached data\n            const currentAuction = auctions.find((a)=>a.id === auctionId);\n            if (currentAuction && canUserBid(currentAuction)) {\n                setSelectedAuction(currentAuction);\n                setBidAmount(String((currentAuction.currentBid || 0) + 1000));\n                setShowBidModal(true);\n            }\n        } finally{\n            setLoading(false);\n        }\n    };\n    const submitBid = async ()=>{\n        if (!selectedAuction) return;\n        const bidAmountNum = parseFloat(bidAmount);\n        if (isNaN(bidAmountNum) || bidAmountNum <= (selectedAuction.currentBid || 0)) {\n            toast({\n                title: \"مزايدة غير صالحة\",\n                description: \"يجب أن تكون المزايدة أكبر من المزايدة الحالية\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        try {\n            setBidLoading(true);\n            // Get auction version for optimistic locking\n            const auctionVersion = auctionVersions[selectedAuction.id];\n            // Prepare bid data with currency and version\n            const bidData = {\n                bidAmount: bidAmountNum,\n                currency: userCurrency,\n                version: auctionVersion\n            };\n            // Submit bid and wait for server response\n            const response = await fetch(\"/api/auctions/\".concat(selectedAuction.id, \"/bid\"), {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    \"Authorization\": \"Bearer \".concat(localStorage.getItem(\"token\"))\n                },\n                body: JSON.stringify(bidData)\n            });\n            const result = await response.json();\n            if (!response.ok) {\n                // Handle specific error cases\n                if (response.status === 409) {\n                    // Optimistic locking conflict\n                    toast({\n                        title: \"تم تحديث المزاد ⚠️\",\n                        description: \"قام شخص آخر بتحديث المزاد. سيتم تحديث البيانات.\",\n                        variant: \"destructive\"\n                    });\n                    await loadAuctions();\n                    setShowBidModal(false);\n                    setBidAmount(\"\");\n                    setSelectedAuction(null);\n                    return;\n                }\n                throw new Error(result.message || \"Bid failed\");\n            }\n            // Close modal first\n            setShowBidModal(false);\n            setBidAmount(\"\");\n            setSelectedAuction(null);\n            // Reload fresh data from server\n            await loadAuctions();\n            // Show success message with currency formatting\n            const formattedAmount = _lib_currencyService__WEBPACK_IMPORTED_MODULE_11__[\"default\"].formatAmount(bidAmountNum, userCurrency);\n            toast({\n                title: \"تم تقديم المزايدة بنجاح! \\uD83C\\uDF89\",\n                description: \"مقدار المزايدة: \".concat(formattedAmount)\n            });\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error(\"Error placing bid:\", error);\n            // Close modal on error too\n            setShowBidModal(false);\n            setBidAmount(\"\");\n            setSelectedAuction(null);\n            // Show specific error message\n            const errorMessage = ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"يرجى المحاولة مرة أخرى\";\n            if (errorMessage.includes(\"higher than current price\") || errorMessage.includes(\"أكبر من السعر الحالي\")) {\n                toast({\n                    title: \"مزايدة منخفضة ⚠️\",\n                    description: \"قام شخص آخر بمزايدة أعلى. يرجى المحاولة بمبلغ أكبر.\",\n                    variant: \"destructive\"\n                });\n            } else if (errorMessage.includes(\"ended\") || errorMessage.includes(\"انتهى\")) {\n                toast({\n                    title: \"انتهى المزاد ⏰\",\n                    description: \"لقد انتهى هذا المزاد ولا يمكن قبول مزايدات جديدة.\",\n                    variant: \"destructive\"\n                });\n            } else {\n                toast({\n                    title: \"فشل في تقديم المزايدة\",\n                    description: errorMessage,\n                    variant: \"destructive\"\n                });\n            }\n            // Always reload data on error to sync with server\n            await loadAuctions();\n        } finally{\n            setBidLoading(false);\n        }\n    };\n    const handleSaveAuction = async (auctionId)=>{\n        console.log(\"❤️ Save button clicked for auction:\", auctionId);\n        // Show immediate feedback that button was clicked\n        toast({\n            title: \"\\uD83D\\uDC86 تم الضغط على زر الحفظ\",\n            description: \"جاري معالجة الطلب...\"\n        });\n        try {\n            const auction = auctions.find((a)=>a.id === auctionId);\n            if (!auction) {\n                toast({\n                    title: \"خطأ\",\n                    description: \"لم يتم العثور على المزاد\",\n                    variant: \"destructive\"\n                });\n                return;\n            }\n            const isCurrentlySaved = savedAuctions.has(auctionId);\n            // Optimistic UI update\n            setSavedAuctions((prev)=>{\n                const newSet = new Set(prev);\n                if (isCurrentlySaved) {\n                    newSet.delete(auctionId);\n                } else {\n                    newSet.add(auctionId);\n                }\n                return newSet;\n            });\n            if (isCurrentlySaved) {\n                // Remove from favorites\n                await _lib_api__WEBPACK_IMPORTED_MODULE_8__.favoritesAPI.removeFavorite(\"auction\", auctionId);\n                toast({\n                    title: \"❤️ تم إزالة من المفضلة\",\n                    description: 'تم إزالة \"'.concat(auction.title, '\" من المفضلة')\n                });\n            } else {\n                // Add to favorites\n                await _lib_api__WEBPACK_IMPORTED_MODULE_8__.favoritesAPI.addFavorite({\n                    itemType: \"auction\",\n                    itemId: auctionId,\n                    notifications: {\n                        bidUpdates: true,\n                        statusChanges: true,\n                        endingSoon: true\n                    }\n                });\n                toast({\n                    title: \"\\uD83D\\uDC96 تم إضافة إلى المفضلة\",\n                    description: 'تم إضافة \"'.concat(auction.title, '\" إلى المفضلة')\n                });\n            }\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error(\"Error saving auction:\", error);\n            // Revert optimistic update on error\n            setSavedAuctions((prev)=>{\n                const newSet = new Set(prev);\n                if (savedAuctions.has(auctionId)) {\n                    newSet.add(auctionId);\n                } else {\n                    newSet.delete(auctionId);\n                }\n                return newSet;\n            });\n            toast({\n                title: \"فشل في حفظ المزاد\",\n                description: ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"يرجى المحاولة مرة أخرى\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleViewAuction = async (auctionId)=>{\n        console.log(\"\\uD83D\\uDC41️ View button clicked for auction:\", auctionId);\n        try {\n            const auction = auctions.find((a)=>a.id === auctionId);\n            if (!auction) {\n                toast({\n                    title: \"خطأ\",\n                    description: \"لم يتم العثور على المزاد\",\n                    variant: \"destructive\"\n                });\n                return;\n            }\n            // Navigate to auction detail page\n            router.push(\"/auctions/\".concat(auctionId));\n            toast({\n                title: \"\\uD83D\\uDC41️ جاري تحميل التفاصيل\",\n                description: \"سيتم عرض تفاصيل المزاد\"\n            });\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error(\"Error viewing auction:\", error);\n            toast({\n                title: \"فشل في عرض تفاصيل المزاد\",\n                description: ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"يرجى المحاولة مرة أخرى\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-3xl font-bold\",\n                                    children: \"المزادات المتاحة\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 623,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-muted-foreground\",\n                                    children: [\n                                        \"استكشف المزادات الحالية وشارك في المزايدة\",\n                                        autoRefresh && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs text-green-600 ml-2\",\n                                            children: \"• تحديث تلقائي كل 30 ثانية\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                            lineNumber: 627,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 624,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                            lineNumber: 622,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                            htmlFor: \"currency\",\n                                            className: \"text-sm font-medium\",\n                                            children: \"العملة:\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                            lineNumber: 635,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            id: \"currency\",\n                                            value: userCurrency,\n                                            onChange: async (e)=>{\n                                                const newCurrency = e.target.value;\n                                                setUserCurrency(newCurrency);\n                                                _lib_currencyService__WEBPACK_IMPORTED_MODULE_11__[\"default\"].setUserCurrency(newCurrency);\n                                                setLoading(true);\n                                                await loadAuctions();\n                                                setLoading(false);\n                                                toast({\n                                                    title: \"\\uD83D\\uDCB1 تم تغيير العملة\",\n                                                    description: \"تم التحويل إلى \".concat(_lib_currencyService__WEBPACK_IMPORTED_MODULE_11__[\"default\"].getCurrencySymbol(newCurrency))\n                                                });\n                                            },\n                                            className: \"px-2 py-1 border border-gray-300 rounded text-sm focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                            children: _lib_currencyService__WEBPACK_IMPORTED_MODULE_11__[\"default\"].getSupportedCurrencies().map((currency)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: currency.code,\n                                                    children: [\n                                                        currency.symbol,\n                                                        \" \",\n                                                        currency.code\n                                                    ]\n                                                }, currency.code, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                    lineNumber: 654,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                            lineNumber: 636,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 634,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    onClick: async ()=>{\n                                        await loadAuctions(true) // Show loading state\n                                        ;\n                                        setLastRefresh(new Date());\n                                        toast({\n                                            title: \"\\uD83D\\uDD04 تم تحديث البيانات\",\n                                            description: \"تم العثور على \".concat(filteredAuctions.length, \" مزاد متاح\")\n                                        });\n                                    },\n                                    variant: \"outline\",\n                                    disabled: loading,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"h-4 w-4 ml-2 \".concat(loading ? \"animate-spin\" : \"\")\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                            lineNumber: 672,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"تحديث\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 660,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    onClick: ()=>{\n                                        setAutoRefresh(!autoRefresh);\n                                        toast({\n                                            title: autoRefresh ? \"⏸️ تم إيقاف التحديث التلقائي\" : \"▶️ تم تشغيل التحديث التلقائي\",\n                                            description: autoRefresh ? \"يمكنك التحديث يدوياً\" : \"سيتم التحديث كل 30 ثانية\"\n                                        });\n                                    },\n                                    variant: autoRefresh ? \"default\" : \"outline\",\n                                    size: \"icon\",\n                                    children: autoRefresh ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 686,\n                                        columnNumber: 32\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 686,\n                                        columnNumber: 64\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 675,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                            lineNumber: 633,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                    lineNumber: 621,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                lineNumber: 620,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                    className: \"pt-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"absolute right-3 top-3 h-4 w-4 text-muted-foreground\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 697,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                        placeholder: \"ابحث في المزادات...\",\n                                        value: searchTerm,\n                                        onChange: (e)=>setSearchTerm(e.target.value),\n                                        className: \"pr-10\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 698,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                lineNumber: 696,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col sm:flex-row gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                htmlFor: \"category\",\n                                                className: \"text-sm font-medium\",\n                                                children: \"الفئة\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                lineNumber: 708,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                id: \"category\",\n                                                value: filterCategory,\n                                                onChange: (e)=>setFilterCategory(e.target.value),\n                                                className: \"w-full mt-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"\",\n                                                        children: \"جميع الفئات\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 715,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"electronics\",\n                                                        children: \"إلكترونيات\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 716,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"vehicles\",\n                                                        children: \"سيارات\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 717,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"real_estate\",\n                                                        children: \"عقارات\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 718,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"art_collectibles\",\n                                                        children: \"فنون ومقتنيات\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 719,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"machinery\",\n                                                        children: \"معدات\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 720,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"furniture\",\n                                                        children: \"أثاث\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 721,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"jewelry\",\n                                                        children: \"مجوهرات\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 722,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"books_media\",\n                                                        children: \"كتب ووسائط\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 723,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"clothing\",\n                                                        children: \"ملابس\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 724,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"sports\",\n                                                        children: \"رياضة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 725,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"other\",\n                                                        children: \"أخرى\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 726,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                lineNumber: 709,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 707,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                htmlFor: \"sort\",\n                                                className: \"text-sm font-medium\",\n                                                children: \"ترتيب حسب\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                lineNumber: 731,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                id: \"sort\",\n                                                value: sortBy,\n                                                onChange: (e)=>setSortBy(e.target.value),\n                                                className: \"w-full mt-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"endDate\",\n                                                        children: \"تاريخ الانتهاء\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 738,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"currentBid\",\n                                                        children: \"أعلى مزايدة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 739,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"bidsCount\",\n                                                        children: \"عدد المزايدات\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 740,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"title\",\n                                                        children: \"الاسم\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 741,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                lineNumber: 732,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 730,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                lineNumber: 706,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                        lineNumber: 695,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                    lineNumber: 694,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                lineNumber: 693,\n                columnNumber: 9\n            }, this),\n            Array.isArray(filteredAuctions) && filteredAuctions.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                    className: \"text-lg\",\n                                    children: \"إجمالي المزادات\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 754,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                    className: \"text-2xl font-bold text-blue-600\",\n                                    children: filteredAuctions.length\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 755,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                            lineNumber: 753,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                        lineNumber: 752,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                    className: \"text-lg\",\n                                    children: \"المزايدات النشطة\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 762,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                    className: \"text-2xl font-bold text-green-600\",\n                                    children: filteredAuctions.reduce((sum, a)=>sum + (a.bidsCount || 0), 0)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 763,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                            lineNumber: 761,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                        lineNumber: 760,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                    className: \"text-lg\",\n                                    children: \"أعلى مزايدة\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 770,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                    className: \"text-2xl font-bold text-purple-600\",\n                                    children: filteredAuctions.length > 0 ? _lib_currencyService__WEBPACK_IMPORTED_MODULE_11__[\"default\"].formatAmount(Math.max(...filteredAuctions.map((a)=>a.currentBid || 0)), userCurrency) : _lib_currencyService__WEBPACK_IMPORTED_MODULE_11__[\"default\"].formatAmount(0, userCurrency)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 771,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                            lineNumber: 769,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                        lineNumber: 768,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                lineNumber: 751,\n                columnNumber: 11\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                children: Array.isArray(filteredAuctions) && filteredAuctions.map((auction)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        className: \"hover:shadow-lg transition-shadow cursor-pointer\",\n                        onClick: ()=>handleViewAuction(auction.id),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"aspect-video bg-gradient-to-br from-gray-100 to-gray-200 rounded-lg mb-4 flex items-center justify-center relative overflow-hidden\",\n                                        children: [\n                                            auction.images && auction.images.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: auction.images[0].url,\n                                                alt: auction.title,\n                                                className: \"w-full h-full object-cover\",\n                                                onError: (e)=>{\n                                                    e.currentTarget.style.display = \"none\";\n                                                    e.currentTarget.nextElementSibling.style.display = \"flex\";\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                lineNumber: 792,\n                                                columnNumber: 21\n                                            }, this) : null,\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-center w-full h-full text-gray-500\",\n                                                style: {\n                                                    display: auction.images && auction.images.length > 0 ? \"none\" : \"flex\"\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                            className: \"h-8 w-8 mx-auto mb-2 text-gray-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                            lineNumber: 804,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm\",\n                                                            children: \"صورة المزاد\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                            lineNumber: 805,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                    lineNumber: 803,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                lineNumber: 802,\n                                                columnNumber: 19\n                                            }, this),\n                                            auction.status === \"active\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                className: \"absolute top-2 right-2 bg-green-500\",\n                                                children: \"نشط\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                lineNumber: 809,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 790,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                        className: \"text-lg line-clamp-2\",\n                                        children: auction.title\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 814,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                        className: \"line-clamp-2\",\n                                        children: auction.description\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 815,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                lineNumber: 789,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-muted-foreground\",\n                                                        children: \"أعلى مزايدة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 822,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xl font-bold text-green-600\",\n                                                        children: _lib_currencyService__WEBPACK_IMPORTED_MODULE_11__[\"default\"].formatAmount(auction.currentBid || 0, auction.currency || userCurrency)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 823,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                lineNumber: 821,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-muted-foreground\",\n                                                        children: \"المزايدات\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 828,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-lg font-semibold\",\n                                                        children: auction.bidsCount || 0\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 829,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                lineNumber: 827,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 820,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                className: \"h-4 w-4 text-muted-foreground\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                lineNumber: 834,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-muted-foreground\",\n                                                children: [\n                                                    \"ينتهي خلال: \",\n                                                    getTimeRemaining(auction.endDate)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                lineNumber: 835,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 833,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-muted-foreground\",\n                                        children: [\n                                            \"البائع: \",\n                                            auction.seller\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 840,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                onClick: (e)=>{\n                                                    e.preventDefault();\n                                                    e.stopPropagation();\n                                                    handleBid(auction.id);\n                                                },\n                                                className: \"flex-1\",\n                                                disabled: loading || bidLoading,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                        className: \"h-4 w-4 ml-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                        lineNumber: 854,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    bidLoading ? \"جاري المزايدة...\" : \"مزايدة\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                lineNumber: 845,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                variant: savedAuctions.has(auction.id) ? \"default\" : \"outline\",\n                                                size: \"icon\",\n                                                onClick: (e)=>{\n                                                    e.preventDefault();\n                                                    e.stopPropagation();\n                                                    handleSaveAuction(auction.id);\n                                                },\n                                                disabled: loading,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                    className: \"h-4 w-4 \".concat(savedAuctions.has(auction.id) ? \"fill-current\" : \"\")\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                    lineNumber: 867,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                lineNumber: 857,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                variant: \"outline\",\n                                                size: \"icon\",\n                                                onClick: (e)=>{\n                                                    e.preventDefault();\n                                                    e.stopPropagation();\n                                                    handleViewAuction(auction.id);\n                                                },\n                                                disabled: loading,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                    lineNumber: 879,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                lineNumber: 869,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 844,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                lineNumber: 819,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, auction.id, true, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                        lineNumber: 788,\n                        columnNumber: 13\n                    }, this))\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                lineNumber: 786,\n                columnNumber: 9\n            }, this),\n            !loading && (!Array.isArray(filteredAuctions) || filteredAuctions.length === 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                    className: \"py-16 text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col items-center gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-16 h-16 rounded-full bg-gray-100 flex items-center justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                    className: \"h-8 w-8 text-gray-400\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 893,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                lineNumber: 892,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold mb-2\",\n                                        children: searchTerm ? \"لا توجد مزادات تطابق البحث\" : \"لا توجد مزادات متاحة حالياً\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 896,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-muted-foreground\",\n                                        children: searchTerm ? \"جرب البحث بكلمات مختلفة أو امسح مربع البحث لعرض جميع المزادات\" : \"سيتم عرض المزادات المتاحة هنا عند إضافتها من قبل الشركات\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 899,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                lineNumber: 895,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                        lineNumber: 891,\n                        columnNumber: 15\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                    lineNumber: 890,\n                    columnNumber: 13\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                lineNumber: 889,\n                columnNumber: 11\n            }, this),\n            loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                    className: \"py-16 text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col items-center gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                lineNumber: 916,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-muted-foreground\",\n                                children: \"جاري تحميل المزادات...\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                lineNumber: 917,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xs text-gray-400\",\n                                children: [\n                                    \"Loading: \",\n                                    loading.toString(),\n                                    \", Auctions: \",\n                                    auctions.length,\n                                    \", Filtered: \",\n                                    filteredAuctions.length\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                lineNumber: 918,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                        lineNumber: 915,\n                        columnNumber: 15\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                    lineNumber: 914,\n                    columnNumber: 13\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                lineNumber: 913,\n                columnNumber: 11\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__.Dialog, {\n                open: showBidModal,\n                onOpenChange: setShowBidModal,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__.DialogContent, {\n                    className: \"sm:max-w-md\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__.DialogHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__.DialogTitle, {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                        lineNumber: 929,\n                                        columnNumber: 17\n                                    }, this),\n                                    \"تقديم مزايدة\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                lineNumber: 928,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                            lineNumber: 927,\n                            columnNumber: 13\n                        }, this),\n                        selectedAuction && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gray-50 p-4 rounded-lg\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"font-semibold mb-2\",\n                                            children: selectedAuction.title\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                            lineNumber: 937,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-4 text-sm text-gray-600\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                            lineNumber: 940,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: [\n                                                                \"المزايدة الحالية: \",\n                                                                (selectedAuction.currentBid || 0).toLocaleString(),\n                                                                \" ر.س\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                            lineNumber: 941,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                    lineNumber: 939,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_DollarSign_Eye_Gavel_Heart_Pause_Play_RefreshCw_Search_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                            lineNumber: 944,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: [\n                                                                selectedAuction.bidsCount || 0,\n                                                                \" مزايدة\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                            lineNumber: 945,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                                    lineNumber: 943,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                            lineNumber: 938,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 936,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                            htmlFor: \"bidAmount\",\n                                            children: \"مقدار المزايدة (ر.س)\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                            lineNumber: 951,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                            id: \"bidAmount\",\n                                            type: \"number\",\n                                            value: bidAmount,\n                                            onChange: (e)=>setBidAmount(e.target.value),\n                                            placeholder: \"أدخل مقدار المزايدة\",\n                                            min: (selectedAuction.currentBid || 0) + 1\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                            lineNumber: 952,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-gray-500\",\n                                            children: [\n                                                \"الحد الأدنى: \",\n                                                ((selectedAuction.currentBid || 0) + 1).toLocaleString(),\n                                                \" ر.س\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                            lineNumber: 960,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 950,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                            lineNumber: 935,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__.DialogFooter, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"outline\",\n                                    onClick: ()=>{\n                                        setShowBidModal(false);\n                                        setBidAmount(\"\");\n                                        setSelectedAuction(null);\n                                    },\n                                    disabled: bidLoading,\n                                    children: \"إلغاء\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 968,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    onClick: submitBid,\n                                    disabled: bidLoading || !bidAmount,\n                                    className: \"bg-green-600 hover:bg-green-700\",\n                                    children: bidLoading ? \"جاري التقديم...\" : \"تقديم المزايدة\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                                    lineNumber: 979,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                            lineNumber: 967,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                    lineNumber: 926,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n                lineNumber: 925,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/auctions/page.tsx\",\n        lineNumber: 619,\n        columnNumber: 5\n    }, this);\n}\n_s(UserAuctionsPage, \"z+c0gDlNDBuJNNkaYMi/S/91rKQ=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_9__.useRouter,\n        _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_10__.useToast\n    ];\n});\n_c = UserAuctionsPage;\nvar _c;\n$RefreshReg$(_c, \"UserAuctionsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FwcC91c2VyL2F1Y3Rpb25zL3BhZ2UudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBRWtEO0FBQzhDO0FBQ2pEO0FBQ0Y7QUFDQTtBQUMwRDtBQUMxRDtBQUVtRjtBQUM1RTtBQUNUO0FBQ1M7QUFDRDtBQUdwQyxTQUFTZ0M7O0lBQ3RCLE1BQU0sQ0FBQ0MsVUFBVUMsWUFBWSxHQUFHakMsK0NBQVFBLENBQVEsRUFBRTtJQUNsRCxNQUFNLENBQUNrQyxZQUFZQyxjQUFjLEdBQUduQywrQ0FBUUEsQ0FBQztJQUM3QyxNQUFNLENBQUNvQyxrQkFBa0JDLG9CQUFvQixHQUFHckMsK0NBQVFBLENBQVEsRUFBRTtJQUNsRSxNQUFNLENBQUNzQyxTQUFTQyxXQUFXLEdBQUd2QywrQ0FBUUEsQ0FBQztJQUN2QyxNQUFNLENBQUN3QyxlQUFlQyxpQkFBaUIsR0FBR3pDLCtDQUFRQSxDQUFjLElBQUkwQztJQUNwRSxNQUFNLENBQUNDLE1BQU1DLFFBQVEsR0FBRzVDLCtDQUFRQSxDQUFNO0lBRXRDLHNCQUFzQjtJQUN0QixNQUFNLENBQUM2QyxjQUFjQyxnQkFBZ0IsR0FBRzlDLCtDQUFRQSxDQUFDO0lBQ2pELE1BQU0sQ0FBQytDLGlCQUFpQkMsbUJBQW1CLEdBQUdoRCwrQ0FBUUEsQ0FBTTtJQUM1RCxNQUFNLENBQUNpRCxXQUFXQyxhQUFhLEdBQUdsRCwrQ0FBUUEsQ0FBQztJQUMzQyxNQUFNLENBQUNtRCxZQUFZQyxjQUFjLEdBQUdwRCwrQ0FBUUEsQ0FBQztJQUU3Qyx3QkFBd0I7SUFDeEIsTUFBTSxDQUFDcUQsUUFBUUMsVUFBVSxHQUFHdEQsK0NBQVFBLENBQUM7SUFDckMsTUFBTSxDQUFDdUQsZ0JBQWdCQyxrQkFBa0IsR0FBR3hELCtDQUFRQSxDQUFDO0lBRXJELHFCQUFxQjtJQUNyQixNQUFNLENBQUN5RCxhQUFhQyxlQUFlLEdBQUcxRCwrQ0FBUUEsQ0FBQztJQUMvQyxNQUFNLENBQUMyRCxhQUFhQyxlQUFlLEdBQUc1RCwrQ0FBUUEsQ0FBTyxJQUFJNkQ7SUFFekQsaUJBQWlCO0lBQ2pCLE1BQU0sQ0FBQ0MsY0FBY0MsZ0JBQWdCLEdBQUcvRCwrQ0FBUUEsQ0FBQztJQUNqRCxNQUFNLENBQUNnRSxlQUFlQyxpQkFBaUIsR0FBR2pFLCtDQUFRQSxDQUFNLENBQUM7SUFFekQscUJBQXFCO0lBQ3JCLE1BQU0sQ0FBQ2tFLGlCQUFpQkMsbUJBQW1CLEdBQUduRSwrQ0FBUUEsQ0FBMEIsQ0FBQztJQUVqRixNQUFNb0UsU0FBU3hDLDBEQUFTQTtJQUN4QixNQUFNLEVBQUV5QyxLQUFLLEVBQUUsR0FBR3hDLG1FQUFRQTtJQUUxQjVCLGdEQUFTQSxDQUFDO1FBQ1Isc0NBQXNDO1FBQ3RDLE1BQU1xRSxXQUFXQyxhQUFhQyxPQUFPLENBQUM7UUFDdEMsSUFBSUYsVUFBVTtZQUNaLE1BQU1HLGFBQWFDLEtBQUtDLEtBQUssQ0FBQ0w7WUFDOUIxQixRQUFRNkI7WUFFUiw0Q0FBNEM7WUFDNUMsSUFBSUEsV0FBV0csSUFBSSxLQUFLLFdBQVdILFdBQVdHLElBQUksS0FBSyxlQUFlO2dCQUNwRVAsTUFBTTtvQkFDSlEsT0FBTztvQkFDUEMsYUFBYTtvQkFDYkMsU0FBUztnQkFDWDtnQkFDQVgsT0FBT1ksSUFBSSxDQUFDO2dCQUNaO1lBQ0Y7WUFFQSwrQ0FBK0M7WUFDL0MsSUFBSVAsV0FBV0csSUFBSSxLQUFLLGNBQWM7Z0JBQ3BDUCxNQUFNO29CQUNKUSxPQUFPO29CQUNQQyxhQUFhO29CQUNiQyxTQUFTO2dCQUNYO2dCQUNBWCxPQUFPWSxJQUFJLENBQUM7Z0JBQ1o7WUFDRjtRQUNGO0lBQ0YsR0FBRyxFQUFFO0lBRUwvRSxnREFBU0EsQ0FBQztRQUNSLHNCQUFzQjtRQUN0QixNQUFNZ0YsZ0JBQWdCbkQsNkRBQWVBLENBQUNvRCxlQUFlO1FBQ3JEbkIsZ0JBQWdCa0I7UUFFaEJFO1FBQ0FDO1FBQ0FDO0lBQ0YsR0FBRyxFQUFFO0lBRUwsTUFBTUEsb0JBQW9CO1FBQ3hCLElBQUk7WUFDRixNQUFNQyxRQUFRLE1BQU14RCw2REFBZUEsQ0FBQ3lELGdCQUFnQjtZQUNwRHRCLGlCQUFpQnFCO1FBQ25CLEVBQUUsT0FBT0UsT0FBTztZQUNkQyxRQUFRRCxLQUFLLENBQUMsa0NBQWtDQTtRQUNsRDtJQUNGO0lBRUEseUNBQXlDO0lBQ3pDdkYsZ0RBQVNBLENBQUM7UUFDUixJQUFJLENBQUN3RCxhQUFhO1FBRWxCLE1BQU1pQyxXQUFXQyxZQUFZO1lBQzNCLElBQUk7Z0JBQ0YsTUFBTVIsYUFBYSxPQUFPLHVDQUF1Qzs7Z0JBQ2pFdkIsZUFBZSxJQUFJQztnQkFDbkI0QixRQUFRRyxHQUFHLENBQUM7WUFDZCxFQUFFLE9BQU9KLE9BQU87Z0JBQ2RDLFFBQVFELEtBQUssQ0FBQyx3QkFBd0JBO1lBQ3hDO1FBQ0YsR0FBRyxPQUFPLGFBQWE7O1FBRXZCLE9BQU8sSUFBTUssY0FBY0g7SUFDN0IsR0FBRztRQUFDakM7S0FBWTtJQUVoQixNQUFNMEIsZUFBZTtZQUFPVywrRUFBYztRQUN4QyxJQUFJO1lBQ0ZMLFFBQVFHLEdBQUcsQ0FBQyx5Q0FBK0JFLGNBQWMsaUJBQWlCO1lBQzFFLElBQUlBLGFBQWE7Z0JBQ2Z2RCxXQUFXO1lBQ2I7WUFDQSxNQUFNd0QsV0FBVyxNQUFNckUsZ0RBQVVBLENBQUNzRSxNQUFNO1lBRXhDUCxRQUFRRyxHQUFHLENBQUMsMEJBQTBCRyxTQUFTRSxJQUFJO1lBRW5ELDZDQUE2QztZQUM3QyxJQUFJQyxjQUFjLEVBQUU7WUFDcEIsSUFBSUgsU0FBU0UsSUFBSSxJQUFJRixTQUFTRSxJQUFJLENBQUNFLE9BQU8sRUFBRTtvQkFFNUJKO2dCQURkLG9GQUFvRjtnQkFDcEZHLGNBQWNILEVBQUFBLHNCQUFBQSxTQUFTRSxJQUFJLENBQUNBLElBQUksY0FBbEJGLDBDQUFBQSxvQkFBb0IvRCxRQUFRLEtBQUksRUFBRTtZQUNsRCxPQUFPLElBQUkrRCxTQUFTRSxJQUFJLElBQUlHLE1BQU1DLE9BQU8sQ0FBQ04sU0FBU0UsSUFBSSxHQUFHO2dCQUN4RCx3Q0FBd0M7Z0JBQ3hDQyxjQUFjSCxTQUFTRSxJQUFJO1lBQzdCLE9BQU8sSUFBSUYsU0FBU0UsSUFBSSxJQUFJRixTQUFTRSxJQUFJLENBQUNBLElBQUksSUFBSUcsTUFBTUMsT0FBTyxDQUFDTixTQUFTRSxJQUFJLENBQUNBLElBQUksR0FBRztnQkFDbkYsNENBQTRDO2dCQUM1Q0MsY0FBY0gsU0FBU0UsSUFBSSxDQUFDQSxJQUFJO1lBQ2xDO1lBRUEsa0RBQWtEO1lBQ2xELE1BQU1LLGtCQUFrQixNQUFNQyxRQUFRQyxHQUFHLENBQUNOLFlBQVlPLEdBQUcsQ0FBQyxPQUFPQztvQkFPbERBLGVBQ0hBLHlCQUFBQSxpQkFBcUNBLDBCQUFBQSxrQkFBd0NBLGtCQUF3QkE7Z0JBUC9HLE1BQU1DLGNBQWM7b0JBQ2xCQyxJQUFJRixRQUFRRyxHQUFHLElBQUlILFFBQVFFLEVBQUU7b0JBQzdCL0IsT0FBTzZCLFFBQVE3QixLQUFLO29CQUNwQkMsYUFBYTRCLFFBQVE1QixXQUFXO29CQUNoQ2dDLFlBQVlKLFFBQVFLLFlBQVksSUFBSUwsUUFBUUksVUFBVSxJQUFJSixRQUFRTSxhQUFhLElBQUk7b0JBQ25GQyxTQUFTUCxRQUFRTyxPQUFPO29CQUN4QkMsV0FBV1IsRUFBQUEsZ0JBQUFBLFFBQVFTLElBQUksY0FBWlQsb0NBQUFBLGNBQWNVLE1BQU0sS0FBSVYsUUFBUVEsU0FBUyxJQUFJO29CQUN4REcsUUFBUVgsRUFBQUEsa0JBQUFBLFFBQVFXLE1BQU0sY0FBZFgsdUNBQUFBLDBCQUFBQSxnQkFBZ0JZLE9BQU8sY0FBdkJaLDhDQUFBQSx3QkFBeUJhLFFBQVEsT0FBSWIsbUJBQUFBLFFBQVFXLE1BQU0sY0FBZFgsd0NBQUFBLDJCQUFBQSxpQkFBZ0JZLE9BQU8sY0FBdkJaLCtDQUFBQSx5QkFBeUJjLFdBQVcsT0FBSWQsbUJBQUFBLFFBQVFXLE1BQU0sY0FBZFgsdUNBQUFBLGlCQUFnQmUsSUFBSSxPQUFJZixtQkFBQUEsUUFBUVcsTUFBTSxjQUFkWCx1Q0FBQUEsaUJBQWdCRyxHQUFHLEtBQUk7b0JBQ3BJYSxVQUFVaEIsUUFBUWdCLFFBQVE7b0JBQzFCQyxRQUFRakIsUUFBUWlCLE1BQU07b0JBQ3RCQyxPQUFPbEIsUUFBUWtCLEtBQUssSUFBSTtvQkFDeEJDLFVBQVVuQixRQUFRbUIsUUFBUTtvQkFDMUJDLFVBQVVwQixRQUFRb0IsUUFBUSxJQUFJO29CQUM5QkMsU0FBU3JCLFFBQVFxQixPQUFPLElBQUk7Z0JBQzlCO2dCQUVBLHVDQUF1QztnQkFDdkM1RCxtQkFBbUI2RCxDQUFBQSxPQUFTO3dCQUMxQixHQUFHQSxJQUFJO3dCQUNQLENBQUNyQixZQUFZQyxFQUFFLENBQUMsRUFBRUQsWUFBWW9CLE9BQU87b0JBQ3ZDO2dCQUVBLDZCQUE2QjtnQkFDN0J0QyxRQUFRRyxHQUFHLENBQUMscUNBQXNEOUIsT0FBM0I2QyxZQUFZbUIsUUFBUSxFQUFDLFFBQWtDbkIsT0FBNUI3QyxjQUFhLGlCQUE4QixPQUFmNkMsWUFBWUMsRUFBRTtnQkFDNUcsSUFBSTtvQkFDRixJQUFJRCxZQUFZbUIsUUFBUSxLQUFLaEUsY0FBYzt3QkFDekMyQixRQUFRRyxHQUFHLENBQUMsMkJBQTJDZSxPQUExQkEsWUFBWUcsVUFBVSxFQUFDLEtBQTZCaEQsT0FBMUI2QyxZQUFZbUIsUUFBUSxFQUFDLE9BQWtCLE9BQWJoRTt3QkFDakYsTUFBTW1FLGVBQWUsTUFBTW5HLDZEQUFlQSxDQUFDb0csZUFBZSxDQUN4RHZCLFlBQVlHLFVBQVUsRUFDdEJILFlBQVltQixRQUFRLEVBQ3BCaEU7d0JBRUYyQixRQUFRRyxHQUFHLENBQUMsa0NBQW9EOUIsT0FBNUJxRSxLQUFLQyxLQUFLLENBQUNILGVBQWMsS0FBZ0IsT0FBYm5FO3dCQUNoRSxPQUFPOzRCQUNMLEdBQUc2QyxXQUFXOzRCQUNkRyxZQUFZcUIsS0FBS0MsS0FBSyxDQUFDSDs0QkFDdkJJLGtCQUFrQjFCLFlBQVltQixRQUFROzRCQUN0Q0EsVUFBVWhFO3dCQUNaO29CQUNGLE9BQU87d0JBQ0wyQixRQUFRRyxHQUFHLENBQUMsOENBQW1FLE9BQXJCZSxZQUFZbUIsUUFBUSxFQUFDO29CQUNqRjtnQkFDRixFQUFFLE9BQU9RLGlCQUFpQjtvQkFDeEI3QyxRQUFROEMsSUFBSSxDQUFDLDJDQUEyQzVCLFlBQVlDLEVBQUUsRUFBRTBCO2dCQUMxRTtnQkFFQSxPQUFPM0I7WUFDVDtZQUVBMUUsWUFBWXFFO1lBQ1pqRSxvQkFBb0JpRTtZQUNwQmIsUUFBUUcsR0FBRyxDQUFDLHdDQUF3Q1UsZ0JBQWdCYyxNQUFNO1FBQzVFLEVBQUUsT0FBTzVCLE9BQU87WUFDZEMsUUFBUUQsS0FBSyxDQUFDLDJCQUEyQkE7WUFDekN2RCxZQUFZLEVBQUU7WUFDZEksb0JBQW9CLEVBQUU7WUFFdEJnQyxNQUFNO2dCQUNKUSxPQUFPO2dCQUNQQyxhQUFhO2dCQUNiQyxTQUFTO1lBQ1g7UUFDRixTQUFVO1lBQ1IsSUFBSWUsYUFBYTtnQkFDZnZELFdBQVc7WUFDYjtZQUNBa0QsUUFBUUcsR0FBRyxDQUFDLG1EQUF5QzVELFNBQVNvRixNQUFNO1FBQ3RFO0lBQ0Y7SUFFQSxNQUFNaEMsb0JBQW9CO1FBQ3hCLElBQUk7WUFDRixNQUFNVyxXQUFXLE1BQU1wRSxrREFBWUEsQ0FBQzZHLFlBQVksQ0FBQztnQkFBRUMsTUFBTTtZQUFVO1lBRW5FLHVDQUF1QztZQUN2QyxJQUFJQyxnQkFBZ0IsRUFBRTtZQUN0QixJQUFJM0MsU0FBU0UsSUFBSSxJQUFJRixTQUFTRSxJQUFJLENBQUNFLE9BQU8sRUFBRTtnQkFDMUN1QyxnQkFBZ0IzQyxTQUFTRSxJQUFJLENBQUNBLElBQUksSUFBSSxFQUFFO1lBQzFDLE9BQU8sSUFBSUYsU0FBU0UsSUFBSSxJQUFJRyxNQUFNQyxPQUFPLENBQUNOLFNBQVNFLElBQUksR0FBRztnQkFDeER5QyxnQkFBZ0IzQyxTQUFTRSxJQUFJO1lBQy9CLE9BQU8sSUFBSUYsU0FBU0UsSUFBSSxJQUFJRixTQUFTRSxJQUFJLENBQUMwQyxTQUFTLElBQUl2QyxNQUFNQyxPQUFPLENBQUNOLFNBQVNFLElBQUksQ0FBQzBDLFNBQVMsR0FBRztnQkFDN0ZELGdCQUFnQjNDLFNBQVNFLElBQUksQ0FBQzBDLFNBQVM7WUFDekM7WUFFQSxvREFBb0Q7WUFDcEQsSUFBSXZDLE1BQU1DLE9BQU8sQ0FBQ3FDLGdCQUFnQjtnQkFDaEMsTUFBTUUsV0FBVyxJQUFJbEcsSUFBWWdHLGNBQWNqQyxHQUFHLENBQUMsQ0FBQ29DLE1BQWFBLElBQUlDLE1BQU0sSUFBSUQsSUFBSWhDLEdBQUcsSUFBSWdDLElBQUlqQyxFQUFFO2dCQUNoR25FLGlCQUFpQm1HO1lBQ25CLE9BQU87Z0JBQ0xuRCxRQUFROEMsSUFBSSxDQUFDLG1DQUFtQ0c7Z0JBQ2hEakcsaUJBQWlCLElBQUlDO1lBQ3ZCO1FBQ0YsRUFBRSxPQUFPOEMsT0FBTztZQUNkQyxRQUFRRCxLQUFLLENBQUMsaUNBQWlDQTtZQUMvQyw0REFBNEQ7WUFDNUQvQyxpQkFBaUIsSUFBSUM7UUFDdkI7SUFDRjtJQUVBekMsZ0RBQVNBLENBQUM7UUFDUiwrQ0FBK0M7UUFDL0MsSUFBSW1HLE1BQU1DLE9BQU8sQ0FBQ3JFLFdBQVc7WUFDM0IsSUFBSStHLFdBQVcvRyxTQUFTZ0gsTUFBTSxDQUFDdEMsQ0FBQUE7b0JBQ1BBLGdCQUNEQTtnQkFEckIsTUFBTXVDLGdCQUFnQnZDLEVBQUFBLGlCQUFBQSxRQUFRN0IsS0FBSyxjQUFiNkIscUNBQUFBLGVBQWV3QyxXQUFXLEdBQUdDLFFBQVEsQ0FBQ2pILFdBQVdnSCxXQUFXLFVBQzdEeEMsdUJBQUFBLFFBQVE1QixXQUFXLGNBQW5CNEIsMkNBQUFBLHFCQUFxQndDLFdBQVcsR0FBR0MsUUFBUSxDQUFDakgsV0FBV2dILFdBQVc7Z0JBQ3ZGLE1BQU1FLGtCQUFrQixDQUFDN0Ysa0JBQWtCbUQsUUFBUWdCLFFBQVEsS0FBS25FO2dCQUNoRSxPQUFPMEYsaUJBQWlCRztZQUMxQjtZQUVBLDRCQUE0QjtZQUM1QkwsV0FBV0EsU0FBU00sSUFBSSxDQUFDLENBQUNDLEdBQUdDO2dCQUMzQixPQUFRbEc7b0JBQ04sS0FBSzt3QkFDSCxPQUFPLElBQUlRLEtBQUt5RixFQUFFckMsT0FBTyxFQUFFdUMsT0FBTyxLQUFLLElBQUkzRixLQUFLMEYsRUFBRXRDLE9BQU8sRUFBRXVDLE9BQU87b0JBQ3BFLEtBQUs7d0JBQ0gsT0FBTyxDQUFDRCxFQUFFekMsVUFBVSxJQUFJLEtBQU13QyxDQUFBQSxFQUFFeEMsVUFBVSxJQUFJO29CQUNoRCxLQUFLO3dCQUNILE9BQU8sQ0FBQ3lDLEVBQUVyQyxTQUFTLElBQUksS0FBTW9DLENBQUFBLEVBQUVwQyxTQUFTLElBQUk7b0JBQzlDLEtBQUs7d0JBQ0gsT0FBT29DLEVBQUV6RSxLQUFLLENBQUM0RSxhQUFhLENBQUNGLEVBQUUxRSxLQUFLO29CQUN0Qzt3QkFDRSxPQUFPO2dCQUNYO1lBQ0Y7WUFFQXhDLG9CQUFvQjBHO1lBQ3BCdEQsUUFBUUcsR0FBRyxDQUFDLG1DQUF5Qm1ELFNBQVMzQixNQUFNLEVBQUUsUUFBUXBGLFNBQVNvRixNQUFNO1FBQy9FLE9BQU87WUFDTC9FLG9CQUFvQixFQUFFO1FBQ3hCO0lBQ0YsR0FBRztRQUFDSDtRQUFZRjtRQUFVdUI7UUFBZ0JGO0tBQU87SUFFakQsTUFBTXFHLG1CQUFtQixDQUFDekM7UUFDeEIsTUFBTTBDLE1BQU0sSUFBSTlGO1FBQ2hCLE1BQU0rRixNQUFNLElBQUkvRixLQUFLb0Q7UUFDckIsTUFBTTRDLE9BQU9ELElBQUlKLE9BQU8sS0FBS0csSUFBSUgsT0FBTztRQUV4QyxJQUFJSyxRQUFRLEdBQUcsT0FBTztRQUV0QixNQUFNQyxRQUFRM0IsS0FBSzRCLEtBQUssQ0FBQ0YsT0FBUSxRQUFPLEtBQUssRUFBQztRQUM5QyxNQUFNRyxPQUFPN0IsS0FBSzRCLEtBQUssQ0FBQ0QsUUFBUTtRQUVoQyxJQUFJRSxPQUFPLEdBQUcsT0FBTyxHQUFRLE9BQUxBLE1BQUs7UUFDN0IsT0FBTyxHQUFTLE9BQU5GLE9BQU07SUFDbEI7SUFFQSx3QkFBd0I7SUFDeEIsTUFBTUcsYUFBYSxDQUFDdkQ7WUFTZ0MvRDtRQVJsRCxJQUFJLENBQUNBLE1BQU0sT0FBTztRQUVsQix5Q0FBeUM7UUFDekMsSUFBSUEsS0FBS2lDLElBQUksS0FBSyxnQkFBZ0JqQyxLQUFLaUMsSUFBSSxLQUFLLFdBQVc7WUFDekQsT0FBTztRQUNUO1FBRUEsNkNBQTZDO1FBQzdDLElBQUlqQyxLQUFLaUMsSUFBSSxLQUFLLGFBQWE4QixRQUFRVyxNQUFNLE9BQUsxRSxnQkFBQUEsS0FBSzJFLE9BQU8sY0FBWjNFLG9DQUFBQSxjQUFjNkUsV0FBVyxHQUFFO1lBQzNFLE9BQU87UUFDVDtRQUVBLE9BQU87SUFDVDtJQUVBLE1BQU0wQyxZQUFZLE9BQU9DO1FBQ3ZCMUUsUUFBUUcsR0FBRyxDQUFDLGdEQUFzQ3VFO1FBRWxELElBQUk7Z0JBNEJXQyxvQkFDSEEsOEJBQUFBLHNCQUEwQ0EsK0JBQUFBLHVCQUE2Q0E7WUE1QmpHLGdEQUFnRDtZQUNoRDdILFdBQVc7WUFDWCxNQUFNd0QsV0FBVyxNQUFNckUsZ0RBQVVBLENBQUMySSxPQUFPLENBQUNGO1lBRTFDLElBQUlDLGVBQWU7WUFDbkIsSUFBSXJFLFNBQVNFLElBQUksSUFBSUYsU0FBU0UsSUFBSSxDQUFDRSxPQUFPLEVBQUU7Z0JBQzFDaUUsZUFBZXJFLFNBQVNFLElBQUksQ0FBQ0EsSUFBSSxDQUFDUyxPQUFPO1lBQzNDLE9BQU8sSUFBSVgsU0FBU0UsSUFBSSxJQUFJRixTQUFTRSxJQUFJLENBQUNTLE9BQU8sRUFBRTtnQkFDakQwRCxlQUFlckUsU0FBU0UsSUFBSSxDQUFDUyxPQUFPO1lBQ3RDO1lBRUEsSUFBSSxDQUFDMEQsY0FBYztnQkFDakIvRixNQUFNO29CQUNKUSxPQUFPO29CQUNQQyxhQUFhO29CQUNiQyxTQUFTO2dCQUNYO2dCQUNBO1lBQ0Y7WUFFQSwyQ0FBMkM7WUFDM0MsTUFBTXVGLHFCQUFxQjtnQkFDekIxRCxJQUFJd0QsYUFBYXZELEdBQUcsSUFBSXVELGFBQWF4RCxFQUFFO2dCQUN2Qy9CLE9BQU91RixhQUFhdkYsS0FBSztnQkFDekJDLGFBQWFzRixhQUFhdEYsV0FBVztnQkFDckNnQyxZQUFZc0QsYUFBYXJELFlBQVksSUFBSXFELGFBQWF0RCxVQUFVLElBQUlzRCxhQUFhcEQsYUFBYSxJQUFJO2dCQUNsR0MsU0FBU21ELGFBQWFuRCxPQUFPO2dCQUM3QkMsV0FBV2tELEVBQUFBLHFCQUFBQSxhQUFhakQsSUFBSSxjQUFqQmlELHlDQUFBQSxtQkFBbUJoRCxNQUFNLEtBQUlnRCxhQUFhbEQsU0FBUyxJQUFJO2dCQUNsRUcsUUFBUStDLEVBQUFBLHVCQUFBQSxhQUFhL0MsTUFBTSxjQUFuQitDLDRDQUFBQSwrQkFBQUEscUJBQXFCOUMsT0FBTyxjQUE1QjhDLG1EQUFBQSw2QkFBOEI3QyxRQUFRLE9BQUk2Qyx3QkFBQUEsYUFBYS9DLE1BQU0sY0FBbkIrQyw2Q0FBQUEsZ0NBQUFBLHNCQUFxQjlDLE9BQU8sY0FBNUI4QyxvREFBQUEsOEJBQThCNUMsV0FBVyxPQUFJNEMsd0JBQUFBLGFBQWEvQyxNQUFNLGNBQW5CK0MsNENBQUFBLHNCQUFxQjNDLElBQUksS0FBSTtnQkFDNUhDLFVBQVUwQyxhQUFhMUMsUUFBUTtnQkFDL0JDLFFBQVF5QyxhQUFhekMsTUFBTTtZQUM3QjtZQUVBLHdCQUF3QjtZQUN4QixJQUFJLENBQUNzQyxXQUFXSyxxQkFBcUI7Z0JBQ25DLElBQUkzSCxDQUFBQSxpQkFBQUEsMkJBQUFBLEtBQU1pQyxJQUFJLE1BQUssV0FBV2pDLENBQUFBLGlCQUFBQSwyQkFBQUEsS0FBTWlDLElBQUksTUFBSyxlQUFlO29CQUMxRFAsTUFBTTt3QkFDSlEsT0FBTzt3QkFDUEMsYUFBYTt3QkFDYkMsU0FBUztvQkFDWDtnQkFDRixPQUFPLElBQUlwQyxDQUFBQSxpQkFBQUEsMkJBQUFBLEtBQU1pQyxJQUFJLE1BQUssY0FBYztvQkFDdENQLE1BQU07d0JBQ0pRLE9BQU87d0JBQ1BDLGFBQWE7d0JBQ2JDLFNBQVM7b0JBQ1g7Z0JBQ0YsT0FBTyxJQUFJcEMsQ0FBQUEsaUJBQUFBLDJCQUFBQSxLQUFNaUMsSUFBSSxNQUFLLFdBQVc7b0JBQ25DUCxNQUFNO3dCQUNKUSxPQUFPO3dCQUNQQyxhQUFhO3dCQUNiQyxTQUFTO29CQUNYO2dCQUNGLE9BQU87b0JBQ0xWLE1BQU07d0JBQ0pRLE9BQU87d0JBQ1BDLGFBQWE7d0JBQ2JDLFNBQVM7b0JBQ1g7Z0JBQ0Y7Z0JBQ0E7WUFDRjtZQUVBLHFDQUFxQztZQUNyQy9CLG1CQUFtQnNIO1lBQ25CcEgsYUFBYXFILE9BQU8sQ0FBQ0QsbUJBQW1CeEQsVUFBVSxJQUFJLEtBQUs7WUFDM0RoRSxnQkFBZ0I7WUFFaEJ1QixNQUFNO2dCQUNKUSxPQUFPO2dCQUNQQyxhQUFhLHFCQUEyRSxPQUF0RCxDQUFDd0YsbUJBQW1CeEQsVUFBVSxJQUFJLEdBQUcwRCxjQUFjLElBQUc7WUFDMUY7UUFFRixFQUFFLE9BQU9oRixPQUFZO1lBQ25CQyxRQUFRRCxLQUFLLENBQUMsc0NBQXNDQTtZQUNwRG5CLE1BQU07Z0JBQ0pRLE9BQU87Z0JBQ1BDLGFBQWE7Z0JBQ2JDLFNBQVM7WUFDWDtZQUVBLDBCQUEwQjtZQUMxQixNQUFNMEYsaUJBQWlCekksU0FBUzBJLElBQUksQ0FBQ3BCLENBQUFBLElBQUtBLEVBQUUxQyxFQUFFLEtBQUt1RDtZQUNuRCxJQUFJTSxrQkFBa0JSLFdBQVdRLGlCQUFpQjtnQkFDaER6SCxtQkFBbUJ5SDtnQkFDbkJ2SCxhQUFhcUgsT0FBTyxDQUFDRSxlQUFlM0QsVUFBVSxJQUFJLEtBQUs7Z0JBQ3ZEaEUsZ0JBQWdCO1lBQ2xCO1FBQ0YsU0FBVTtZQUNSUCxXQUFXO1FBQ2I7SUFDRjtJQUVBLE1BQU1vSSxZQUFZO1FBQ2hCLElBQUksQ0FBQzVILGlCQUFpQjtRQUV0QixNQUFNNkgsZUFBZUMsV0FBVzVIO1FBQ2hDLElBQUk2SCxNQUFNRixpQkFBaUJBLGdCQUFpQjdILENBQUFBLGdCQUFnQitELFVBQVUsSUFBSSxJQUFJO1lBQzVFekMsTUFBTTtnQkFDSlEsT0FBTztnQkFDUEMsYUFBYTtnQkFDYkMsU0FBUztZQUNYO1lBQ0E7UUFDRjtRQUVBLElBQUk7WUFDRjNCLGNBQWM7WUFFZCw2Q0FBNkM7WUFDN0MsTUFBTTJILGlCQUFpQjdHLGVBQWUsQ0FBQ25CLGdCQUFnQjZELEVBQUUsQ0FBQztZQUUxRCw2Q0FBNkM7WUFDN0MsTUFBTW9FLFVBQVU7Z0JBQ2QvSCxXQUFXMkg7Z0JBQ1g5QyxVQUFVaEU7Z0JBQ1ZpRSxTQUFTZ0Q7WUFDWDtZQUVBLDBDQUEwQztZQUMxQyxNQUFNaEYsV0FBVyxNQUFNa0YsTUFBTSxpQkFBb0MsT0FBbkJsSSxnQkFBZ0I2RCxFQUFFLEVBQUMsU0FBTztnQkFDdEVzRSxRQUFRO2dCQUNSQyxTQUFTO29CQUNQLGdCQUFnQjtvQkFDaEIsaUJBQWlCLFVBQXdDLE9BQTlCNUcsYUFBYUMsT0FBTyxDQUFDO2dCQUNsRDtnQkFDQTRHLE1BQU0xRyxLQUFLMkcsU0FBUyxDQUFDTDtZQUN2QjtZQUVBLE1BQU1NLFNBQVMsTUFBTXZGLFNBQVN3RixJQUFJO1lBRWxDLElBQUksQ0FBQ3hGLFNBQVN5RixFQUFFLEVBQUU7Z0JBQ2hCLDhCQUE4QjtnQkFDOUIsSUFBSXpGLFNBQVM0QixNQUFNLEtBQUssS0FBSztvQkFDM0IsOEJBQThCO29CQUM5QnRELE1BQU07d0JBQ0pRLE9BQU87d0JBQ1BDLGFBQWE7d0JBQ2JDLFNBQVM7b0JBQ1g7b0JBQ0EsTUFBTUk7b0JBQ05yQyxnQkFBZ0I7b0JBQ2hCSSxhQUFhO29CQUNiRixtQkFBbUI7b0JBQ25CO2dCQUNGO2dCQUNBLE1BQU0sSUFBSXlJLE1BQU1ILE9BQU9JLE9BQU8sSUFBSTtZQUNwQztZQUVBLG9CQUFvQjtZQUNwQjVJLGdCQUFnQjtZQUNoQkksYUFBYTtZQUNiRixtQkFBbUI7WUFFbkIsZ0NBQWdDO1lBQ2hDLE1BQU1tQztZQUVOLGdEQUFnRDtZQUNoRCxNQUFNd0csa0JBQWtCN0osNkRBQWVBLENBQUM4SixZQUFZLENBQUNoQixjQUFjOUc7WUFDbkVPLE1BQU07Z0JBQ0pRLE9BQU87Z0JBQ1BDLGFBQWEsbUJBQW1DLE9BQWhCNkc7WUFDbEM7UUFFRixFQUFFLE9BQU9uRyxPQUFZO2dCQVNFQSxzQkFBQUE7WUFSckJDLFFBQVFELEtBQUssQ0FBQyxzQkFBc0JBO1lBRXBDLDJCQUEyQjtZQUMzQjFDLGdCQUFnQjtZQUNoQkksYUFBYTtZQUNiRixtQkFBbUI7WUFFbkIsOEJBQThCO1lBQzlCLE1BQU02SSxlQUFlckcsRUFBQUEsa0JBQUFBLE1BQU1PLFFBQVEsY0FBZFAsdUNBQUFBLHVCQUFBQSxnQkFBZ0JTLElBQUksY0FBcEJULDJDQUFBQSxxQkFBc0JrRyxPQUFPLEtBQUk7WUFFdEQsSUFBSUcsYUFBYTFDLFFBQVEsQ0FBQyxnQ0FBZ0MwQyxhQUFhMUMsUUFBUSxDQUFDLHlCQUF5QjtnQkFDdkc5RSxNQUFNO29CQUNKUSxPQUFPO29CQUNQQyxhQUFhO29CQUNiQyxTQUFTO2dCQUNYO1lBQ0YsT0FBTyxJQUFJOEcsYUFBYTFDLFFBQVEsQ0FBQyxZQUFZMEMsYUFBYTFDLFFBQVEsQ0FBQyxVQUFVO2dCQUMzRTlFLE1BQU07b0JBQ0pRLE9BQU87b0JBQ1BDLGFBQWE7b0JBQ2JDLFNBQVM7Z0JBQ1g7WUFDRixPQUFPO2dCQUNMVixNQUFNO29CQUNKUSxPQUFPO29CQUNQQyxhQUFhK0c7b0JBQ2I5RyxTQUFTO2dCQUNYO1lBQ0Y7WUFFQSxrREFBa0Q7WUFDbEQsTUFBTUk7UUFDUixTQUFVO1lBQ1IvQixjQUFjO1FBQ2hCO0lBQ0Y7SUFFQSxNQUFNMEksb0JBQW9CLE9BQU8zQjtRQUMvQjFFLFFBQVFHLEdBQUcsQ0FBQyx1Q0FBdUN1RTtRQUVuRCxrREFBa0Q7UUFDbEQ5RixNQUFNO1lBQ0pRLE9BQU87WUFDUEMsYUFBYTtRQUNmO1FBRUEsSUFBSTtZQUNGLE1BQU00QixVQUFVMUUsU0FBUzBJLElBQUksQ0FBQ3BCLENBQUFBLElBQUtBLEVBQUUxQyxFQUFFLEtBQUt1RDtZQUM1QyxJQUFJLENBQUN6RCxTQUFTO2dCQUNackMsTUFBTTtvQkFDSlEsT0FBTztvQkFDUEMsYUFBYTtvQkFDYkMsU0FBUztnQkFDWDtnQkFDQTtZQUNGO1lBRUEsTUFBTWdILG1CQUFtQnZKLGNBQWN3SixHQUFHLENBQUM3QjtZQUUzQyx1QkFBdUI7WUFDdkIxSCxpQkFBaUJ1RixDQUFBQTtnQkFDZixNQUFNaUUsU0FBUyxJQUFJdkosSUFBSXNGO2dCQUN2QixJQUFJK0Qsa0JBQWtCO29CQUNwQkUsT0FBT0MsTUFBTSxDQUFDL0I7Z0JBQ2hCLE9BQU87b0JBQ0w4QixPQUFPRSxHQUFHLENBQUNoQztnQkFDYjtnQkFDQSxPQUFPOEI7WUFDVDtZQUVBLElBQUlGLGtCQUFrQjtnQkFDcEIsd0JBQXdCO2dCQUN4QixNQUFNcEssa0RBQVlBLENBQUN5SyxjQUFjLENBQUMsV0FBV2pDO2dCQUM3QzlGLE1BQU07b0JBQ0pRLE9BQU87b0JBQ1BDLGFBQWEsYUFBMkIsT0FBZDRCLFFBQVE3QixLQUFLLEVBQUM7Z0JBQzFDO1lBQ0YsT0FBTztnQkFDTCxtQkFBbUI7Z0JBQ25CLE1BQU1sRCxrREFBWUEsQ0FBQzBLLFdBQVcsQ0FBQztvQkFDN0JDLFVBQVU7b0JBQ1Z4RCxRQUFRcUI7b0JBQ1JvQyxlQUFlO3dCQUNiQyxZQUFZO3dCQUNaQyxlQUFlO3dCQUNmQyxZQUFZO29CQUNkO2dCQUNGO2dCQUNBckksTUFBTTtvQkFDSlEsT0FBTztvQkFDUEMsYUFBYSxhQUEyQixPQUFkNEIsUUFBUTdCLEtBQUssRUFBQztnQkFDMUM7WUFDRjtRQUNGLEVBQUUsT0FBT1csT0FBWTtnQkFjSkEsc0JBQUFBO1lBYmZDLFFBQVFELEtBQUssQ0FBQyx5QkFBeUJBO1lBQ3ZDLG9DQUFvQztZQUNwQy9DLGlCQUFpQnVGLENBQUFBO2dCQUNmLE1BQU1pRSxTQUFTLElBQUl2SixJQUFJc0Y7Z0JBQ3ZCLElBQUl4RixjQUFjd0osR0FBRyxDQUFDN0IsWUFBWTtvQkFDaEM4QixPQUFPRSxHQUFHLENBQUNoQztnQkFDYixPQUFPO29CQUNMOEIsT0FBT0MsTUFBTSxDQUFDL0I7Z0JBQ2hCO2dCQUNBLE9BQU84QjtZQUNUO1lBQ0E1SCxNQUFNO2dCQUNKUSxPQUFPO2dCQUNQQyxhQUFhVSxFQUFBQSxrQkFBQUEsTUFBTU8sUUFBUSxjQUFkUCx1Q0FBQUEsdUJBQUFBLGdCQUFnQlMsSUFBSSxjQUFwQlQsMkNBQUFBLHFCQUFzQmtHLE9BQU8sS0FBSTtnQkFDOUMzRyxTQUFTO1lBQ1g7UUFDRjtJQUNGO0lBRUEsTUFBTTRILG9CQUFvQixPQUFPeEM7UUFDL0IxRSxRQUFRRyxHQUFHLENBQUMsa0RBQXdDdUU7UUFFcEQsSUFBSTtZQUNGLE1BQU16RCxVQUFVMUUsU0FBUzBJLElBQUksQ0FBQ3BCLENBQUFBLElBQUtBLEVBQUUxQyxFQUFFLEtBQUt1RDtZQUM1QyxJQUFJLENBQUN6RCxTQUFTO2dCQUNackMsTUFBTTtvQkFDSlEsT0FBTztvQkFDUEMsYUFBYTtvQkFDYkMsU0FBUztnQkFDWDtnQkFDQTtZQUNGO1lBRUEsa0NBQWtDO1lBQ2xDWCxPQUFPWSxJQUFJLENBQUMsYUFBdUIsT0FBVm1GO1lBRXpCOUYsTUFBTTtnQkFDSlEsT0FBTztnQkFDUEMsYUFBYTtZQUNmO1FBQ0YsRUFBRSxPQUFPVSxPQUFZO2dCQUlKQSxzQkFBQUE7WUFIZkMsUUFBUUQsS0FBSyxDQUFDLDBCQUEwQkE7WUFDeENuQixNQUFNO2dCQUNKUSxPQUFPO2dCQUNQQyxhQUFhVSxFQUFBQSxrQkFBQUEsTUFBTU8sUUFBUSxjQUFkUCx1Q0FBQUEsdUJBQUFBLGdCQUFnQlMsSUFBSSxjQUFwQlQsMkNBQUFBLHFCQUFzQmtHLE9BQU8sS0FBSTtnQkFDOUMzRyxTQUFTO1lBQ1g7UUFDRjtJQUNGO0lBRUEscUJBQ0UsOERBQUM2SDtRQUFJQyxXQUFVOzswQkFDWCw4REFBQ0M7MEJBQ0MsNEVBQUNGO29CQUFJQyxXQUFVOztzQ0FDYiw4REFBQ0Q7OzhDQUNDLDhEQUFDRztvQ0FBR0YsV0FBVTs4Q0FBcUI7Ozs7Ozs4Q0FDbkMsOERBQUNHO29DQUFFSCxXQUFVOzt3Q0FBd0I7d0NBRWxDcEosNkJBQ0MsOERBQUN3Sjs0Q0FBS0osV0FBVTtzREFBOEI7Ozs7Ozs7Ozs7Ozs7Ozs7OztzQ0FNcEQsOERBQUNEOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQ0Q7b0NBQUlDLFdBQVU7O3NEQUNiLDhEQUFDOUwsdURBQUtBOzRDQUFDbU0sU0FBUTs0Q0FBV0wsV0FBVTtzREFBc0I7Ozs7OztzREFDMUQsOERBQUNNOzRDQUNDdkcsSUFBRzs0Q0FDSHdHLE9BQU90Sjs0Q0FDUHVKLFVBQVUsT0FBT0M7Z0RBQ2YsTUFBTUMsY0FBY0QsRUFBRUUsTUFBTSxDQUFDSixLQUFLO2dEQUNsQ3JKLGdCQUFnQndKO2dEQUNoQnpMLDZEQUFlQSxDQUFDaUMsZUFBZSxDQUFDd0o7Z0RBQ2hDaEwsV0FBVztnREFDWCxNQUFNNEM7Z0RBQ041QyxXQUFXO2dEQUNYOEIsTUFBTTtvREFDSlEsT0FBTztvREFDUEMsYUFBYSxrQkFBaUUsT0FBL0NoRCw2REFBZUEsQ0FBQzJMLGlCQUFpQixDQUFDRjtnREFDbkU7NENBQ0Y7NENBQ0FWLFdBQVU7c0RBRVQvSyw2REFBZUEsQ0FBQzRMLHNCQUFzQixHQUFHakgsR0FBRyxDQUFDcUIsQ0FBQUEseUJBQzVDLDhEQUFDNkY7b0RBQTJCUCxPQUFPdEYsU0FBUzhGLElBQUk7O3dEQUM3QzlGLFNBQVMrRixNQUFNO3dEQUFDO3dEQUFFL0YsU0FBUzhGLElBQUk7O21EQURyQjlGLFNBQVM4RixJQUFJOzs7Ozs7Ozs7Ozs7Ozs7OzhDQU1oQyw4REFBQ3JOLHlEQUFNQTtvQ0FDTHVOLFNBQVM7d0NBQ1AsTUFBTTNJLGFBQWEsTUFBTSxxQkFBcUI7O3dDQUM5Q3ZCLGVBQWUsSUFBSUM7d0NBQ25CUSxNQUFNOzRDQUNKUSxPQUFPOzRDQUNQQyxhQUFhLGlCQUF5QyxPQUF4QjFDLGlCQUFpQmdGLE1BQU0sRUFBQzt3Q0FDeEQ7b0NBQ0Y7b0NBQ0FyQyxTQUFRO29DQUNSZ0osVUFBVXpMOztzREFFViw4REFBQ2Ysb0pBQVNBOzRDQUFDc0wsV0FBVyxnQkFBOEMsT0FBOUJ2SyxVQUFVLGlCQUFpQjs7Ozs7O3dDQUFROzs7Ozs7OzhDQUczRSw4REFBQy9CLHlEQUFNQTtvQ0FDTHVOLFNBQVM7d0NBQ1BwSyxlQUFlLENBQUNEO3dDQUNoQlksTUFBTTs0Q0FDSlEsT0FBT3BCLGNBQWMsaUNBQWlDOzRDQUN0RHFCLGFBQWFyQixjQUFjLHlCQUF5Qjt3Q0FDdEQ7b0NBQ0Y7b0NBQ0FzQixTQUFTdEIsY0FBYyxZQUFZO29DQUNuQ3VLLE1BQUs7OENBRUp2Syw0QkFBYyw4REFBQ2pDLG9KQUFLQTt3Q0FBQ3FMLFdBQVU7Ozs7OzZEQUFlLDhEQUFDcEwsb0pBQUlBO3dDQUFDb0wsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFPdkUsOERBQUMzTSxxREFBSUE7MEJBQ0gsNEVBQUNDLDREQUFXQTtvQkFBQzBNLFdBQVU7OEJBQ3JCLDRFQUFDRDt3QkFBSUMsV0FBVTs7MENBQ2IsOERBQUNEO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQzdMLG9KQUFNQTt3Q0FBQzZMLFdBQVU7Ozs7OztrREFDbEIsOERBQUNwTSx1REFBS0E7d0NBQ0p3TixhQUFZO3dDQUNaYixPQUFPbEw7d0NBQ1BtTCxVQUFVLENBQUNDLElBQU1uTCxjQUFjbUwsRUFBRUUsTUFBTSxDQUFDSixLQUFLO3dDQUM3Q1AsV0FBVTs7Ozs7Ozs7Ozs7OzBDQUlkLDhEQUFDRDtnQ0FBSUMsV0FBVTs7a0RBQ2IsOERBQUNEO3dDQUFJQyxXQUFVOzswREFDYiw4REFBQzlMLHVEQUFLQTtnREFBQ21NLFNBQVE7Z0RBQVdMLFdBQVU7MERBQXNCOzs7Ozs7MERBQzFELDhEQUFDTTtnREFDQ3ZHLElBQUc7Z0RBQ0h3RyxPQUFPN0o7Z0RBQ1A4SixVQUFVLENBQUNDLElBQU05SixrQkFBa0I4SixFQUFFRSxNQUFNLENBQUNKLEtBQUs7Z0RBQ2pEUCxXQUFVOztrRUFFViw4REFBQ2M7d0RBQU9QLE9BQU07a0VBQUc7Ozs7OztrRUFDakIsOERBQUNPO3dEQUFPUCxPQUFNO2tFQUFjOzs7Ozs7a0VBQzVCLDhEQUFDTzt3REFBT1AsT0FBTTtrRUFBVzs7Ozs7O2tFQUN6Qiw4REFBQ087d0RBQU9QLE9BQU07a0VBQWM7Ozs7OztrRUFDNUIsOERBQUNPO3dEQUFPUCxPQUFNO2tFQUFtQjs7Ozs7O2tFQUNqQyw4REFBQ087d0RBQU9QLE9BQU07a0VBQVk7Ozs7OztrRUFDMUIsOERBQUNPO3dEQUFPUCxPQUFNO2tFQUFZOzs7Ozs7a0VBQzFCLDhEQUFDTzt3REFBT1AsT0FBTTtrRUFBVTs7Ozs7O2tFQUN4Qiw4REFBQ087d0RBQU9QLE9BQU07a0VBQWM7Ozs7OztrRUFDNUIsOERBQUNPO3dEQUFPUCxPQUFNO2tFQUFXOzs7Ozs7a0VBQ3pCLDhEQUFDTzt3REFBT1AsT0FBTTtrRUFBUzs7Ozs7O2tFQUN2Qiw4REFBQ087d0RBQU9QLE9BQU07a0VBQVE7Ozs7Ozs7Ozs7Ozs7Ozs7OztrREFJMUIsOERBQUNSO3dDQUFJQyxXQUFVOzswREFDYiw4REFBQzlMLHVEQUFLQTtnREFBQ21NLFNBQVE7Z0RBQU9MLFdBQVU7MERBQXNCOzs7Ozs7MERBQ3RELDhEQUFDTTtnREFDQ3ZHLElBQUc7Z0RBQ0h3RyxPQUFPL0o7Z0RBQ1BnSyxVQUFVLENBQUNDLElBQU1oSyxVQUFVZ0ssRUFBRUUsTUFBTSxDQUFDSixLQUFLO2dEQUN6Q1AsV0FBVTs7a0VBRVYsOERBQUNjO3dEQUFPUCxPQUFNO2tFQUFVOzs7Ozs7a0VBQ3hCLDhEQUFDTzt3REFBT1AsT0FBTTtrRUFBYTs7Ozs7O2tFQUMzQiw4REFBQ087d0RBQU9QLE9BQU07a0VBQVk7Ozs7OztrRUFDMUIsOERBQUNPO3dEQUFPUCxPQUFNO2tFQUFROzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O1lBU2pDaEgsTUFBTUMsT0FBTyxDQUFDakUscUJBQXFCQSxpQkFBaUJnRixNQUFNLEdBQUcsbUJBQzVELDhEQUFDd0Y7Z0JBQUlDLFdBQVU7O2tDQUNiLDhEQUFDM00scURBQUlBO2tDQUNILDRFQUFDRSwyREFBVUE7OzhDQUNULDhEQUFDQywwREFBU0E7b0NBQUN3TSxXQUFVOzhDQUFVOzs7Ozs7OENBQy9CLDhEQUFDdk0sZ0VBQWVBO29DQUFDdU0sV0FBVTs4Q0FDeEJ6SyxpQkFBaUJnRixNQUFNOzs7Ozs7Ozs7Ozs7Ozs7OztrQ0FJOUIsOERBQUNsSCxxREFBSUE7a0NBQ0gsNEVBQUNFLDJEQUFVQTs7OENBQ1QsOERBQUNDLDBEQUFTQTtvQ0FBQ3dNLFdBQVU7OENBQVU7Ozs7Ozs4Q0FDL0IsOERBQUN2TSxnRUFBZUE7b0NBQUN1TSxXQUFVOzhDQUN4QnpLLGlCQUFpQjhMLE1BQU0sQ0FBQyxDQUFDQyxLQUFLN0UsSUFBTTZFLE1BQU83RSxDQUFBQSxFQUFFcEMsU0FBUyxJQUFJLElBQUk7Ozs7Ozs7Ozs7Ozs7Ozs7O2tDQUlyRSw4REFBQ2hILHFEQUFJQTtrQ0FDSCw0RUFBQ0UsMkRBQVVBOzs4Q0FDVCw4REFBQ0MsMERBQVNBO29DQUFDd00sV0FBVTs4Q0FBVTs7Ozs7OzhDQUMvQiw4REFBQ3ZNLGdFQUFlQTtvQ0FBQ3VNLFdBQVU7OENBQ3hCekssaUJBQWlCZ0YsTUFBTSxHQUFHLElBQ3ZCdEYsNkRBQWVBLENBQUM4SixZQUFZLENBQzFCekQsS0FBS2lHLEdBQUcsSUFBSWhNLGlCQUFpQnFFLEdBQUcsQ0FBQzZDLENBQUFBLElBQUtBLEVBQUV4QyxVQUFVLElBQUksS0FDdERoRCxnQkFFRmhDLDZEQUFlQSxDQUFDOEosWUFBWSxDQUFDLEdBQUc5SDs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBUzlDLDhEQUFDOEk7Z0JBQUlDLFdBQVU7MEJBQ1p6RyxNQUFNQyxPQUFPLENBQUNqRSxxQkFBcUJBLGlCQUFpQnFFLEdBQUcsQ0FBQyxDQUFDQyx3QkFDeEQsOERBQUN4RyxxREFBSUE7d0JBQWtCMk0sV0FBVTt3QkFBbURpQixTQUFTLElBQU1uQixrQkFBa0JqRyxRQUFRRSxFQUFFOzswQ0FDN0gsOERBQUN4RywyREFBVUE7O2tEQUNULDhEQUFDd007d0NBQUlDLFdBQVU7OzRDQUNabkcsUUFBUTJILE1BQU0sSUFBSTNILFFBQVEySCxNQUFNLENBQUNqSCxNQUFNLEdBQUcsa0JBQ3pDLDhEQUFDa0g7Z0RBQ0NDLEtBQUs3SCxRQUFRMkgsTUFBTSxDQUFDLEVBQUUsQ0FBQ0csR0FBRztnREFDMUJDLEtBQUsvSCxRQUFRN0IsS0FBSztnREFDbEJnSSxXQUFVO2dEQUNWNkIsU0FBUyxDQUFDcEI7b0RBQ1JBLEVBQUVxQixhQUFhLENBQUNDLEtBQUssQ0FBQ0MsT0FBTyxHQUFHO29EQUNoQ3ZCLEVBQUVxQixhQUFhLENBQUNHLGtCQUFrQixDQUFDRixLQUFLLENBQUNDLE9BQU8sR0FBRztnREFDckQ7Ozs7O3VEQUVBOzBEQUNKLDhEQUFDakM7Z0RBQUlDLFdBQVU7Z0RBQStEK0IsT0FBTztvREFBQ0MsU0FBU25JLFFBQVEySCxNQUFNLElBQUkzSCxRQUFRMkgsTUFBTSxDQUFDakgsTUFBTSxHQUFHLElBQUksU0FBUztnREFBTTswREFDMUosNEVBQUN3RjtvREFBSUMsV0FBVTs7c0VBQ2IsOERBQUN4TCxvSkFBS0E7NERBQUN3TCxXQUFVOzs7Ozs7c0VBQ2pCLDhEQUFDSTs0REFBS0osV0FBVTtzRUFBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7NENBRzdCbkcsUUFBUWlCLE1BQU0sS0FBSywwQkFDbEIsOERBQUNuSCx1REFBS0E7Z0RBQUNxTSxXQUFVOzBEQUFzQzs7Ozs7Ozs7Ozs7O2tEQUszRCw4REFBQ3hNLDBEQUFTQTt3Q0FBQ3dNLFdBQVU7a0RBQXdCbkcsUUFBUTdCLEtBQUs7Ozs7OztrREFDMUQsOERBQUN2RSxnRUFBZUE7d0NBQUN1TSxXQUFVO2tEQUN4Qm5HLFFBQVE1QixXQUFXOzs7Ozs7Ozs7Ozs7MENBR3hCLDhEQUFDM0UsNERBQVdBO2dDQUFDME0sV0FBVTs7a0RBQ3JCLDhEQUFDRDt3Q0FBSUMsV0FBVTs7MERBQ2IsOERBQUNEOztrRUFDQyw4REFBQ0k7d0RBQUVILFdBQVU7a0VBQWdDOzs7Ozs7a0VBQzdDLDhEQUFDRzt3REFBRUgsV0FBVTtrRUFDVi9LLDZEQUFlQSxDQUFDOEosWUFBWSxDQUFDbEYsUUFBUUksVUFBVSxJQUFJLEdBQUdKLFFBQVFvQixRQUFRLElBQUloRTs7Ozs7Ozs7Ozs7OzBEQUcvRSw4REFBQzhJO2dEQUFJQyxXQUFVOztrRUFDYiw4REFBQ0c7d0RBQUVILFdBQVU7a0VBQWdDOzs7Ozs7a0VBQzdDLDhEQUFDRzt3REFBRUgsV0FBVTtrRUFBeUJuRyxRQUFRUSxTQUFTLElBQUk7Ozs7Ozs7Ozs7Ozs7Ozs7OztrREFJL0QsOERBQUMwRjt3Q0FBSUMsV0FBVTs7MERBQ2IsOERBQUMxTCxvSkFBS0E7Z0RBQUMwTCxXQUFVOzs7Ozs7MERBQ2pCLDhEQUFDSTtnREFBS0osV0FBVTs7b0RBQWdDO29EQUNqQ25ELGlCQUFpQmhELFFBQVFPLE9BQU87Ozs7Ozs7Ozs7Ozs7a0RBSWpELDhEQUFDMkY7d0NBQUlDLFdBQVU7OzRDQUFnQzs0Q0FDcENuRyxRQUFRVyxNQUFNOzs7Ozs7O2tEQUd6Qiw4REFBQ3VGO3dDQUFJQyxXQUFVOzswREFDYiw4REFBQ3RNLHlEQUFNQTtnREFDTHVOLFNBQVMsQ0FBQ1I7b0RBQ1JBLEVBQUV5QixjQUFjO29EQUNoQnpCLEVBQUUwQixlQUFlO29EQUNqQjlFLFVBQVV4RCxRQUFRRSxFQUFFO2dEQUN0QjtnREFDQWlHLFdBQVU7Z0RBQ1ZrQixVQUFVekwsV0FBV2E7O2tFQUVyQiw4REFBQy9CLG9KQUFVQTt3REFBQ3lMLFdBQVU7Ozs7OztvREFDckIxSixhQUFhLHFCQUFxQjs7Ozs7OzswREFFckMsOERBQUM1Qyx5REFBTUE7Z0RBQ0x3RSxTQUFTdkMsY0FBY3dKLEdBQUcsQ0FBQ3RGLFFBQVFFLEVBQUUsSUFBSSxZQUFZO2dEQUNyRG9ILE1BQUs7Z0RBQ0xGLFNBQVMsQ0FBQ1I7b0RBQ1JBLEVBQUV5QixjQUFjO29EQUNoQnpCLEVBQUUwQixlQUFlO29EQUNqQmxELGtCQUFrQnBGLFFBQVFFLEVBQUU7Z0RBQzlCO2dEQUNBbUgsVUFBVXpMOzBEQUVWLDRFQUFDcEIsb0pBQUtBO29EQUFDMkwsV0FBVyxXQUErRCxPQUFwRHJLLGNBQWN3SixHQUFHLENBQUN0RixRQUFRRSxFQUFFLElBQUksaUJBQWlCOzs7Ozs7Ozs7OzswREFFaEYsOERBQUNyRyx5REFBTUE7Z0RBQ0x3RSxTQUFRO2dEQUNSaUosTUFBSztnREFDTEYsU0FBUyxDQUFDUjtvREFDUkEsRUFBRXlCLGNBQWM7b0RBQ2hCekIsRUFBRTBCLGVBQWU7b0RBQ2pCckMsa0JBQWtCakcsUUFBUUUsRUFBRTtnREFDOUI7Z0RBQ0FtSCxVQUFVekw7MERBRVYsNEVBQUNyQixvSkFBR0E7b0RBQUM0TCxXQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7dUJBM0ZabkcsUUFBUUUsRUFBRTs7Ozs7Ozs7OztZQW9HeEIsQ0FBQ3RFLFdBQVksRUFBQzhELE1BQU1DLE9BQU8sQ0FBQ2pFLHFCQUFxQkEsaUJBQWlCZ0YsTUFBTSxLQUFLLG9CQUM1RSw4REFBQ2xILHFEQUFJQTswQkFDSCw0RUFBQ0MsNERBQVdBO29CQUFDME0sV0FBVTs4QkFDckIsNEVBQUNEO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQ0Q7Z0NBQUlDLFdBQVU7MENBQ2IsNEVBQUN6TCxvSkFBVUE7b0NBQUN5TCxXQUFVOzs7Ozs7Ozs7OzswQ0FFeEIsOERBQUNEOztrREFDQyw4REFBQ3FDO3dDQUFHcEMsV0FBVTtrREFDWDNLLGFBQWEsK0JBQStCOzs7Ozs7a0RBRS9DLDhEQUFDOEs7d0NBQUVILFdBQVU7a0RBQ1YzSyxhQUNHLGtFQUNBOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O1lBVWZJLHlCQUNDLDhEQUFDcEMscURBQUlBOzBCQUNILDRFQUFDQyw0REFBV0E7b0JBQUMwTSxXQUFVOzhCQUNyQiw0RUFBQ0Q7d0JBQUlDLFdBQVU7OzBDQUNiLDhEQUFDRDtnQ0FBSUMsV0FBVTs7Ozs7OzBDQUNmLDhEQUFDRztnQ0FBRUgsV0FBVTswQ0FBd0I7Ozs7OzswQ0FDckMsOERBQUNHO2dDQUFFSCxXQUFVOztvQ0FBd0I7b0NBQVV2SyxRQUFRNE0sUUFBUTtvQ0FBRztvQ0FBYWxOLFNBQVNvRixNQUFNO29DQUFDO29DQUFhaEYsaUJBQWlCZ0YsTUFBTTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBTzNJLDhEQUFDMUcseURBQU1BO2dCQUFDeU8sTUFBTXRNO2dCQUFjdU0sY0FBY3RNOzBCQUN4Qyw0RUFBQ25DLGdFQUFhQTtvQkFBQ2tNLFdBQVU7O3NDQUN2Qiw4REFBQ2pNLCtEQUFZQTtzQ0FDWCw0RUFBQ0MsOERBQVdBO2dDQUFDZ00sV0FBVTs7a0RBQ3JCLDhEQUFDeEwsb0pBQUtBO3dDQUFDd0wsV0FBVTs7Ozs7O29DQUFZOzs7Ozs7Ozs7Ozs7d0JBS2hDOUosaUNBQ0MsOERBQUM2Sjs0QkFBSUMsV0FBVTs7OENBQ2IsOERBQUNEO29DQUFJQyxXQUFVOztzREFDYiw4REFBQ3dDOzRDQUFHeEMsV0FBVTtzREFBc0I5SixnQkFBZ0I4QixLQUFLOzs7Ozs7c0RBQ3pELDhEQUFDK0g7NENBQUlDLFdBQVU7OzhEQUNiLDhEQUFDRDtvREFBSUMsV0FBVTs7c0VBQ2IsOERBQUN2TCxvSkFBVUE7NERBQUN1TCxXQUFVOzs7Ozs7c0VBQ3RCLDhEQUFDSTs7Z0VBQUs7Z0VBQW9CbEssQ0FBQUEsZ0JBQWdCK0QsVUFBVSxJQUFJLEdBQUcwRCxjQUFjO2dFQUFHOzs7Ozs7Ozs7Ozs7OzhEQUU5RSw4REFBQ29DO29EQUFJQyxXQUFVOztzRUFDYiw4REFBQ3pMLG9KQUFVQTs0REFBQ3lMLFdBQVU7Ozs7OztzRUFDdEIsOERBQUNJOztnRUFBTWxLLGdCQUFnQm1FLFNBQVMsSUFBSTtnRUFBRTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4Q0FLNUMsOERBQUMwRjtvQ0FBSUMsV0FBVTs7c0RBQ2IsOERBQUM5TCx1REFBS0E7NENBQUNtTSxTQUFRO3NEQUFZOzs7Ozs7c0RBQzNCLDhEQUFDek0sdURBQUtBOzRDQUNKbUcsSUFBRzs0Q0FDSDZCLE1BQUs7NENBQ0wyRSxPQUFPbks7NENBQ1BvSyxVQUFVLENBQUNDLElBQU1wSyxhQUFhb0ssRUFBRUUsTUFBTSxDQUFDSixLQUFLOzRDQUM1Q2EsYUFBWTs0Q0FDWnFCLEtBQUssQ0FBQ3ZNLGdCQUFnQitELFVBQVUsSUFBSSxLQUFLOzs7Ozs7c0RBRTNDLDhEQUFDa0c7NENBQUVILFdBQVU7O2dEQUF3QjtnREFDcEIsRUFBQzlKLGdCQUFnQitELFVBQVUsSUFBSSxLQUFLLEdBQUcwRCxjQUFjO2dEQUFHOzs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NDQU0vRSw4REFBQzFKLCtEQUFZQTs7OENBQ1gsOERBQUNQLHlEQUFNQTtvQ0FDTHdFLFNBQVE7b0NBQ1IrSSxTQUFTO3dDQUNQaEwsZ0JBQWdCO3dDQUNoQkksYUFBYTt3Q0FDYkYsbUJBQW1CO29DQUNyQjtvQ0FDQStLLFVBQVU1Szs4Q0FDWDs7Ozs7OzhDQUdELDhEQUFDNUMseURBQU1BO29DQUNMdU4sU0FBU25EO29DQUNUb0QsVUFBVTVLLGNBQWMsQ0FBQ0Y7b0NBQ3pCNEosV0FBVTs4Q0FFVDFKLGFBQWEsb0JBQW9COzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQU9sRDtHQTc4QndCcEI7O1FBNkJQSCxzREFBU0E7UUFDTkMsK0RBQVFBOzs7S0E5QkpFIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL2FwcC91c2VyL2F1Y3Rpb25zL3BhZ2UudHN4PzRlZmQiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnXG5cbmltcG9ydCBSZWFjdCwgeyB1c2VTdGF0ZSwgdXNlRWZmZWN0IH0gZnJvbSAncmVhY3QnXG5pbXBvcnQgeyBDYXJkLCBDYXJkQ29udGVudCwgQ2FyZEhlYWRlciwgQ2FyZFRpdGxlLCBDYXJkRGVzY3JpcHRpb24gfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvY2FyZCdcbmltcG9ydCB7IEJ1dHRvbiB9IGZyb20gJ0AvY29tcG9uZW50cy91aS9idXR0b24nXG5pbXBvcnQgeyBCYWRnZSB9IGZyb20gJ0AvY29tcG9uZW50cy91aS9iYWRnZSdcbmltcG9ydCB7IElucHV0IH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL2lucHV0J1xuaW1wb3J0IHsgRGlhbG9nLCBEaWFsb2dDb250ZW50LCBEaWFsb2dIZWFkZXIsIERpYWxvZ1RpdGxlLCBEaWFsb2dGb290ZXIgfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvZGlhbG9nJ1xuaW1wb3J0IHsgTGFiZWwgfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvbGFiZWwnXG5cbmltcG9ydCB7IFNlYXJjaCwgRXllLCBIZWFydCwgQ2xvY2ssIFRyZW5kaW5nVXAsIEdhdmVsLCBEb2xsYXJTaWduLCBGaWx0ZXIsIFNvcnRBc2MsIFJlZnJlc2hDdywgUGF1c2UsIFBsYXkgfSBmcm9tICdsdWNpZGUtcmVhY3QnXG5pbXBvcnQgeyBhdWN0aW9uQVBJLCBmYXZvcml0ZXNBUEkgfSBmcm9tICdAL2xpYi9hcGknXG5pbXBvcnQgeyB1c2VSb3V0ZXIgfSBmcm9tICduZXh0L25hdmlnYXRpb24nXG5pbXBvcnQgeyB1c2VUb2FzdCB9IGZyb20gJ0AvY29tcG9uZW50cy91aS91c2UtdG9hc3QnXG5pbXBvcnQgY3VycmVuY3lTZXJ2aWNlIGZyb20gJ0AvbGliL2N1cnJlbmN5U2VydmljZSdcblxuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBVc2VyQXVjdGlvbnNQYWdlKCkge1xuICBjb25zdCBbYXVjdGlvbnMsIHNldEF1Y3Rpb25zXSA9IHVzZVN0YXRlPGFueVtdPihbXSlcbiAgY29uc3QgW3NlYXJjaFRlcm0sIHNldFNlYXJjaFRlcm1dID0gdXNlU3RhdGUoJycpXG4gIGNvbnN0IFtmaWx0ZXJlZEF1Y3Rpb25zLCBzZXRGaWx0ZXJlZEF1Y3Rpb25zXSA9IHVzZVN0YXRlPGFueVtdPihbXSlcbiAgY29uc3QgW2xvYWRpbmcsIHNldExvYWRpbmddID0gdXNlU3RhdGUoZmFsc2UpXG4gIGNvbnN0IFtzYXZlZEF1Y3Rpb25zLCBzZXRTYXZlZEF1Y3Rpb25zXSA9IHVzZVN0YXRlPFNldDxzdHJpbmc+PihuZXcgU2V0KCkpXG4gIGNvbnN0IFt1c2VyLCBzZXRVc2VyXSA9IHVzZVN0YXRlPGFueT4obnVsbClcblxuICAvLyBCaWRkaW5nIG1vZGFsIHN0YXRlXG4gIGNvbnN0IFtzaG93QmlkTW9kYWwsIHNldFNob3dCaWRNb2RhbF0gPSB1c2VTdGF0ZShmYWxzZSlcbiAgY29uc3QgW3NlbGVjdGVkQXVjdGlvbiwgc2V0U2VsZWN0ZWRBdWN0aW9uXSA9IHVzZVN0YXRlPGFueT4obnVsbClcbiAgY29uc3QgW2JpZEFtb3VudCwgc2V0QmlkQW1vdW50XSA9IHVzZVN0YXRlKCcnKVxuICBjb25zdCBbYmlkTG9hZGluZywgc2V0QmlkTG9hZGluZ10gPSB1c2VTdGF0ZShmYWxzZSlcblxuICAvLyBGaWx0ZXIgYW5kIHNvcnQgc3RhdGVcbiAgY29uc3QgW3NvcnRCeSwgc2V0U29ydEJ5XSA9IHVzZVN0YXRlKCdlbmREYXRlJylcbiAgY29uc3QgW2ZpbHRlckNhdGVnb3J5LCBzZXRGaWx0ZXJDYXRlZ29yeV0gPSB1c2VTdGF0ZSgnJylcblxuICAvLyBBdXRvLXJlZnJlc2ggc3RhdGVcbiAgY29uc3QgW2F1dG9SZWZyZXNoLCBzZXRBdXRvUmVmcmVzaF0gPSB1c2VTdGF0ZSh0cnVlKVxuICBjb25zdCBbbGFzdFJlZnJlc2gsIHNldExhc3RSZWZyZXNoXSA9IHVzZVN0YXRlPERhdGU+KG5ldyBEYXRlKCkpXG5cbiAgLy8gQ3VycmVuY3kgc3RhdGVcbiAgY29uc3QgW3VzZXJDdXJyZW5jeSwgc2V0VXNlckN1cnJlbmN5XSA9IHVzZVN0YXRlKCdTQVInKVxuICBjb25zdCBbZXhjaGFuZ2VSYXRlcywgc2V0RXhjaGFuZ2VSYXRlc10gPSB1c2VTdGF0ZTxhbnk+KHt9KVxuXG4gIC8vIE9wdGltaXN0aWMgbG9ja2luZ1xuICBjb25zdCBbYXVjdGlvblZlcnNpb25zLCBzZXRBdWN0aW9uVmVyc2lvbnNdID0gdXNlU3RhdGU8e1trZXk6IHN0cmluZ106IG51bWJlcn0+KHt9KVxuXG4gIGNvbnN0IHJvdXRlciA9IHVzZVJvdXRlcigpXG4gIGNvbnN0IHsgdG9hc3QgfSA9IHVzZVRvYXN0KClcblxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIC8vIEdldCB1c2VyIGRhdGEgYW5kIGNoZWNrIHBlcm1pc3Npb25zXG4gICAgY29uc3QgdXNlckRhdGEgPSBsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgndXNlcicpXG4gICAgaWYgKHVzZXJEYXRhKSB7XG4gICAgICBjb25zdCBwYXJzZWRVc2VyID0gSlNPTi5wYXJzZSh1c2VyRGF0YSlcbiAgICAgIHNldFVzZXIocGFyc2VkVXNlcilcblxuICAgICAgLy8gUmVkaXJlY3QgYWRtaW5zIHRvIHRoZWlyIHByb3BlciBkYXNoYm9hcmRcbiAgICAgIGlmIChwYXJzZWRVc2VyLnJvbGUgPT09ICdhZG1pbicgfHwgcGFyc2VkVXNlci5yb2xlID09PSAnc3VwZXJfYWRtaW4nKSB7XG4gICAgICAgIHRvYXN0KHtcbiAgICAgICAgICB0aXRsZTogJ9mI2LXZiNmEINi62YrYsSDZhdiz2YXZiNitJyxcbiAgICAgICAgICBkZXNjcmlwdGlvbjogJ9in2YTZhdiv2YrYsdmI2YYg2YrYrNioINij2YYg2YrYs9iq2K7Yr9mF2YjYpyDZhNmI2K3YqSDYpdiv2KfYsdipINin2YTZhdiy2KfYr9in2KonLFxuICAgICAgICAgIHZhcmlhbnQ6ICdkZXN0cnVjdGl2ZSdcbiAgICAgICAgfSlcbiAgICAgICAgcm91dGVyLnB1c2goJy9hZG1pbi9hdWN0aW9ucycpXG4gICAgICAgIHJldHVyblxuICAgICAgfVxuXG4gICAgICAvLyBSZWRpcmVjdCBnb3Zlcm5tZW50IHVzZXJzIHRvIHRoZWlyIGRhc2hib2FyZFxuICAgICAgaWYgKHBhcnNlZFVzZXIucm9sZSA9PT0gJ2dvdmVybm1lbnQnKSB7XG4gICAgICAgIHRvYXN0KHtcbiAgICAgICAgICB0aXRsZTogJ9mI2LXZiNmEINi62YrYsSDZhdiz2YXZiNitJyxcbiAgICAgICAgICBkZXNjcmlwdGlvbjogJ9in2YTYrNmH2KfYqiDYp9mE2K3Zg9mI2YXZitipINiq2LPYqtiu2K/ZhSDZhti42KfZhSDYp9mE2YXZhtin2YLYtdin2KonLFxuICAgICAgICAgIHZhcmlhbnQ6ICdkZXN0cnVjdGl2ZSdcbiAgICAgICAgfSlcbiAgICAgICAgcm91dGVyLnB1c2goJy9nb3Zlcm5tZW50L2Rhc2hib2FyZCcpXG4gICAgICAgIHJldHVyblxuICAgICAgfVxuICAgIH1cbiAgfSwgW10pXG5cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICAvLyBJbml0aWFsaXplIGN1cnJlbmN5XG4gICAgY29uc3Qgc2F2ZWRDdXJyZW5jeSA9IGN1cnJlbmN5U2VydmljZS5nZXRVc2VyQ3VycmVuY3koKVxuICAgIHNldFVzZXJDdXJyZW5jeShzYXZlZEN1cnJlbmN5KVxuXG4gICAgbG9hZEF1Y3Rpb25zKClcbiAgICBsb2FkU2F2ZWRBdWN0aW9ucygpXG4gICAgbG9hZEV4Y2hhbmdlUmF0ZXMoKVxuICB9LCBbXSlcblxuICBjb25zdCBsb2FkRXhjaGFuZ2VSYXRlcyA9IGFzeW5jICgpID0+IHtcbiAgICB0cnkge1xuICAgICAgY29uc3QgcmF0ZXMgPSBhd2FpdCBjdXJyZW5jeVNlcnZpY2UuZ2V0RXhjaGFuZ2VSYXRlcygpXG4gICAgICBzZXRFeGNoYW5nZVJhdGVzKHJhdGVzKVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdGYWlsZWQgdG8gbG9hZCBleGNoYW5nZSByYXRlczonLCBlcnJvcilcbiAgICB9XG4gIH1cblxuICAvLyBBdXRvLXJlZnJlc2ggYXVjdGlvbnMgZXZlcnkgMzAgc2Vjb25kc1xuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGlmICghYXV0b1JlZnJlc2gpIHJldHVyblxuXG4gICAgY29uc3QgaW50ZXJ2YWwgPSBzZXRJbnRlcnZhbChhc3luYyAoKSA9PiB7XG4gICAgICB0cnkge1xuICAgICAgICBhd2FpdCBsb2FkQXVjdGlvbnMoZmFsc2UpIC8vIFNpbGVudCByZWZyZXNoIHdpdGhvdXQgbG9hZGluZyBzdGF0ZVxuICAgICAgICBzZXRMYXN0UmVmcmVzaChuZXcgRGF0ZSgpKVxuICAgICAgICBjb25zb2xlLmxvZygn8J+UhCBBdXRvLXJlZnJlc2hlZCBhdWN0aW9uIGRhdGEnKVxuICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgY29uc29sZS5lcnJvcignQXV0by1yZWZyZXNoIGZhaWxlZDonLCBlcnJvcilcbiAgICAgIH1cbiAgICB9LCAzMDAwMCkgLy8gMzAgc2Vjb25kc1xuXG4gICAgcmV0dXJuICgpID0+IGNsZWFySW50ZXJ2YWwoaW50ZXJ2YWwpXG4gIH0sIFthdXRvUmVmcmVzaF0pXG5cbiAgY29uc3QgbG9hZEF1Y3Rpb25zID0gYXN5bmMgKHNob3dMb2FkaW5nID0gdHJ1ZSkgPT4ge1xuICAgIHRyeSB7XG4gICAgICBjb25zb2xlLmxvZygn8J+UhCBTdGFydGluZyBsb2FkQXVjdGlvbnMuLi4nLCBzaG93TG9hZGluZyA/ICd3aXRoIGxvYWRpbmcnIDogJ3NpbGVudCcpXG4gICAgICBpZiAoc2hvd0xvYWRpbmcpIHtcbiAgICAgICAgc2V0TG9hZGluZyh0cnVlKVxuICAgICAgfVxuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBhdWN0aW9uQVBJLmdldEFsbCgpXG5cbiAgICAgIGNvbnNvbGUubG9nKCdBdWN0aW9ucyBBUEkgcmVzcG9uc2U6JywgcmVzcG9uc2UuZGF0YSlcblxuICAgICAgLy8gSGFuZGxlIHRoZSBBUEkgcmVzcG9uc2Ugc3RydWN0dXJlIHByb3Blcmx5XG4gICAgICBsZXQgYXVjdGlvbkRhdGEgPSBbXVxuICAgICAgaWYgKHJlc3BvbnNlLmRhdGEgJiYgcmVzcG9uc2UuZGF0YS5zdWNjZXNzKSB7XG4gICAgICAgIC8vIElmIHRoZSByZXNwb25zZSBoYXMgYSBzdWNjZXNzIGZpZWxkLCBleHRyYWN0IHRoZSBhdWN0aW9ucyBmcm9tIGRhdGEuZGF0YS5hdWN0aW9uc1xuICAgICAgICBhdWN0aW9uRGF0YSA9IHJlc3BvbnNlLmRhdGEuZGF0YT8uYXVjdGlvbnMgfHwgW11cbiAgICAgIH0gZWxzZSBpZiAocmVzcG9uc2UuZGF0YSAmJiBBcnJheS5pc0FycmF5KHJlc3BvbnNlLmRhdGEpKSB7XG4gICAgICAgIC8vIElmIHJlc3BvbnNlLmRhdGEgaXMgZGlyZWN0bHkgYW4gYXJyYXlcbiAgICAgICAgYXVjdGlvbkRhdGEgPSByZXNwb25zZS5kYXRhXG4gICAgICB9IGVsc2UgaWYgKHJlc3BvbnNlLmRhdGEgJiYgcmVzcG9uc2UuZGF0YS5kYXRhICYmIEFycmF5LmlzQXJyYXkocmVzcG9uc2UuZGF0YS5kYXRhKSkge1xuICAgICAgICAvLyBJZiB0aGUgYXVjdGlvbnMgYXJlIGluIHJlc3BvbnNlLmRhdGEuZGF0YVxuICAgICAgICBhdWN0aW9uRGF0YSA9IHJlc3BvbnNlLmRhdGEuZGF0YVxuICAgICAgfVxuXG4gICAgICAvLyBUcmFuc2Zvcm0gYW5kIGNvbnZlcnQgY3VycmVuY3kgZm9yIGVhY2ggYXVjdGlvblxuICAgICAgY29uc3QgdHJhbnNmb3JtZWREYXRhID0gYXdhaXQgUHJvbWlzZS5hbGwoYXVjdGlvbkRhdGEubWFwKGFzeW5jIChhdWN0aW9uOiBhbnkpID0+IHtcbiAgICAgICAgY29uc3QgYmFzZUF1Y3Rpb24gPSB7XG4gICAgICAgICAgaWQ6IGF1Y3Rpb24uX2lkIHx8IGF1Y3Rpb24uaWQsXG4gICAgICAgICAgdGl0bGU6IGF1Y3Rpb24udGl0bGUsXG4gICAgICAgICAgZGVzY3JpcHRpb246IGF1Y3Rpb24uZGVzY3JpcHRpb24sXG4gICAgICAgICAgY3VycmVudEJpZDogYXVjdGlvbi5jdXJyZW50UHJpY2UgfHwgYXVjdGlvbi5jdXJyZW50QmlkIHx8IGF1Y3Rpb24uc3RhcnRpbmdQcmljZSB8fCAwLFxuICAgICAgICAgIGVuZERhdGU6IGF1Y3Rpb24uZW5kRGF0ZSxcbiAgICAgICAgICBiaWRzQ291bnQ6IGF1Y3Rpb24uYmlkcz8ubGVuZ3RoIHx8IGF1Y3Rpb24uYmlkc0NvdW50IHx8IDAsXG4gICAgICAgICAgc2VsbGVyOiBhdWN0aW9uLnNlbGxlcj8ucHJvZmlsZT8uZnVsbE5hbWUgfHwgYXVjdGlvbi5zZWxsZXI/LnByb2ZpbGU/LmNvbXBhbnlOYW1lIHx8IGF1Y3Rpb24uc2VsbGVyPy5uYW1lIHx8IGF1Y3Rpb24uc2VsbGVyPy5faWQgfHwgJ9i62YrYsSDZhdit2K/YrycsXG4gICAgICAgICAgY2F0ZWdvcnk6IGF1Y3Rpb24uY2F0ZWdvcnksXG4gICAgICAgICAgc3RhdHVzOiBhdWN0aW9uLnN0YXR1cyxcbiAgICAgICAgICB2aWV3czogYXVjdGlvbi52aWV3cyB8fCAwLFxuICAgICAgICAgIGxvY2F0aW9uOiBhdWN0aW9uLmxvY2F0aW9uLFxuICAgICAgICAgIGN1cnJlbmN5OiBhdWN0aW9uLmN1cnJlbmN5IHx8ICdTQVInLFxuICAgICAgICAgIHZlcnNpb246IGF1Y3Rpb24udmVyc2lvbiB8fCAwXG4gICAgICAgIH1cblxuICAgICAgICAvLyBTdG9yZSB2ZXJzaW9uIGZvciBvcHRpbWlzdGljIGxvY2tpbmdcbiAgICAgICAgc2V0QXVjdGlvblZlcnNpb25zKHByZXYgPT4gKHtcbiAgICAgICAgICAuLi5wcmV2LFxuICAgICAgICAgIFtiYXNlQXVjdGlvbi5pZF06IGJhc2VBdWN0aW9uLnZlcnNpb25cbiAgICAgICAgfSkpXG5cbiAgICAgICAgLy8gQ29udmVydCBjdXJyZW5jeSBpZiBuZWVkZWRcbiAgICAgICAgY29uc29sZS5sb2coYPCflI0gQ2hlY2tpbmcgY29udmVyc2lvbjogJHtiYXNlQXVjdGlvbi5jdXJyZW5jeX0gdnMgJHt1c2VyQ3VycmVuY3l9IGZvciBhdWN0aW9uICR7YmFzZUF1Y3Rpb24uaWR9YClcbiAgICAgICAgdHJ5IHtcbiAgICAgICAgICBpZiAoYmFzZUF1Y3Rpb24uY3VycmVuY3kgIT09IHVzZXJDdXJyZW5jeSkge1xuICAgICAgICAgICAgY29uc29sZS5sb2coYPCfkrEgQ29udmVydGluZyAke2Jhc2VBdWN0aW9uLmN1cnJlbnRCaWR9ICR7YmFzZUF1Y3Rpb24uY3VycmVuY3l9IOKGkiAke3VzZXJDdXJyZW5jeX1gKVxuICAgICAgICAgICAgY29uc3QgY29udmVydGVkQmlkID0gYXdhaXQgY3VycmVuY3lTZXJ2aWNlLmNvbnZlcnRDdXJyZW5jeShcbiAgICAgICAgICAgICAgYmFzZUF1Y3Rpb24uY3VycmVudEJpZCxcbiAgICAgICAgICAgICAgYmFzZUF1Y3Rpb24uY3VycmVuY3ksXG4gICAgICAgICAgICAgIHVzZXJDdXJyZW5jeVxuICAgICAgICAgICAgKVxuICAgICAgICAgICAgY29uc29sZS5sb2coYPCfkrEgQ29udmVydGVkIHJlc3VsdDogJHtNYXRoLnJvdW5kKGNvbnZlcnRlZEJpZCl9ICR7dXNlckN1cnJlbmN5fWApXG4gICAgICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgICAuLi5iYXNlQXVjdGlvbixcbiAgICAgICAgICAgICAgY3VycmVudEJpZDogTWF0aC5yb3VuZChjb252ZXJ0ZWRCaWQpLFxuICAgICAgICAgICAgICBvcmlnaW5hbEN1cnJlbmN5OiBiYXNlQXVjdGlvbi5jdXJyZW5jeSxcbiAgICAgICAgICAgICAgY3VycmVuY3k6IHVzZXJDdXJyZW5jeVxuICAgICAgICAgICAgfVxuICAgICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICBjb25zb2xlLmxvZyhg4o+t77iPIE5vIGNvbnZlcnNpb24gbmVlZGVkOiBjdXJyZW5jaWVzIG1hdGNoICgke2Jhc2VBdWN0aW9uLmN1cnJlbmN5fSlgKVxuICAgICAgICAgIH1cbiAgICAgICAgfSBjYXRjaCAoY29udmVyc2lvbkVycm9yKSB7XG4gICAgICAgICAgY29uc29sZS53YXJuKCdDdXJyZW5jeSBjb252ZXJzaW9uIGZhaWxlZCBmb3IgYXVjdGlvbjonLCBiYXNlQXVjdGlvbi5pZCwgY29udmVyc2lvbkVycm9yKVxuICAgICAgICB9XG5cbiAgICAgICAgcmV0dXJuIGJhc2VBdWN0aW9uXG4gICAgICB9KSlcblxuICAgICAgc2V0QXVjdGlvbnModHJhbnNmb3JtZWREYXRhKVxuICAgICAgc2V0RmlsdGVyZWRBdWN0aW9ucyh0cmFuc2Zvcm1lZERhdGEpXG4gICAgICBjb25zb2xlLmxvZygn4pyFIFNldCBhdWN0aW9ucyBhbmQgZmlsdGVyZWRBdWN0aW9uczonLCB0cmFuc2Zvcm1lZERhdGEubGVuZ3RoKVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBsb2FkaW5nIGF1Y3Rpb25zOicsIGVycm9yKVxuICAgICAgc2V0QXVjdGlvbnMoW10pXG4gICAgICBzZXRGaWx0ZXJlZEF1Y3Rpb25zKFtdKVxuXG4gICAgICB0b2FzdCh7XG4gICAgICAgIHRpdGxlOiAn2YHYtNmEINmB2Yog2KrYrdmF2YrZhCDYp9mE2YXYstin2K/Yp9iqJyxcbiAgICAgICAgZGVzY3JpcHRpb246ICfYrdiv2Ksg2K7Yt9ijINij2KvZhtin2KEg2KrYrdmF2YrZhCDYp9mE2YXYstin2K/Yp9iqLiDZitix2KzZiSDYp9mE2YXYrdin2YjZhNipINmF2LHYqSDYo9iu2LHZiS4nLFxuICAgICAgICB2YXJpYW50OiAnZGVzdHJ1Y3RpdmUnXG4gICAgICB9KVxuICAgIH0gZmluYWxseSB7XG4gICAgICBpZiAoc2hvd0xvYWRpbmcpIHtcbiAgICAgICAgc2V0TG9hZGluZyhmYWxzZSlcbiAgICAgIH1cbiAgICAgIGNvbnNvbGUubG9nKCfwn4+BIExvYWRpbmcgZmluaXNoZWQuIEF1Y3Rpb25zIGxvYWRlZDonLCBhdWN0aW9ucy5sZW5ndGgpXG4gICAgfVxuICB9XG5cbiAgY29uc3QgbG9hZFNhdmVkQXVjdGlvbnMgPSBhc3luYyAoKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmF2b3JpdGVzQVBJLmdldEZhdm9yaXRlcyh7IHR5cGU6ICdhdWN0aW9uJyB9KVxuXG4gICAgICAvLyBIYW5kbGUgZGlmZmVyZW50IHJlc3BvbnNlIHN0cnVjdHVyZXNcbiAgICAgIGxldCBmYXZvcml0ZXNEYXRhID0gW11cbiAgICAgIGlmIChyZXNwb25zZS5kYXRhICYmIHJlc3BvbnNlLmRhdGEuc3VjY2Vzcykge1xuICAgICAgICBmYXZvcml0ZXNEYXRhID0gcmVzcG9uc2UuZGF0YS5kYXRhIHx8IFtdXG4gICAgICB9IGVsc2UgaWYgKHJlc3BvbnNlLmRhdGEgJiYgQXJyYXkuaXNBcnJheShyZXNwb25zZS5kYXRhKSkge1xuICAgICAgICBmYXZvcml0ZXNEYXRhID0gcmVzcG9uc2UuZGF0YVxuICAgICAgfSBlbHNlIGlmIChyZXNwb25zZS5kYXRhICYmIHJlc3BvbnNlLmRhdGEuZmF2b3JpdGVzICYmIEFycmF5LmlzQXJyYXkocmVzcG9uc2UuZGF0YS5mYXZvcml0ZXMpKSB7XG4gICAgICAgIGZhdm9yaXRlc0RhdGEgPSByZXNwb25zZS5kYXRhLmZhdm9yaXRlc1xuICAgICAgfVxuXG4gICAgICAvLyBDaGVjayBpZiBmYXZvcml0ZXNEYXRhIGlzIGFuIGFycmF5IGJlZm9yZSBtYXBwaW5nXG4gICAgICBpZiAoQXJyYXkuaXNBcnJheShmYXZvcml0ZXNEYXRhKSkge1xuICAgICAgICBjb25zdCBzYXZlZElkcyA9IG5ldyBTZXQ8c3RyaW5nPihmYXZvcml0ZXNEYXRhLm1hcCgoZmF2OiBhbnkpID0+IGZhdi5pdGVtSWQgfHwgZmF2Ll9pZCB8fCBmYXYuaWQpKVxuICAgICAgICBzZXRTYXZlZEF1Y3Rpb25zKHNhdmVkSWRzKVxuICAgICAgfSBlbHNlIHtcbiAgICAgICAgY29uc29sZS53YXJuKCdGYXZvcml0ZXMgZGF0YSBpcyBub3QgYW4gYXJyYXk6JywgZmF2b3JpdGVzRGF0YSlcbiAgICAgICAgc2V0U2F2ZWRBdWN0aW9ucyhuZXcgU2V0KCkpXG4gICAgICB9XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGxvYWRpbmcgc2F2ZWQgYXVjdGlvbnM6JywgZXJyb3IpXG4gICAgICAvLyBEb24ndCBzaG93IGVycm9yIHRvYXN0IGZvciBmYXZvcml0ZXMgYXMgaXQncyBub3QgY3JpdGljYWxcbiAgICAgIHNldFNhdmVkQXVjdGlvbnMobmV3IFNldCgpKVxuICAgIH1cbiAgfVxuXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgLy8gRW5zdXJlIGF1Y3Rpb25zIGlzIGFuIGFycmF5IGJlZm9yZSBmaWx0ZXJpbmdcbiAgICBpZiAoQXJyYXkuaXNBcnJheShhdWN0aW9ucykpIHtcbiAgICAgIGxldCBmaWx0ZXJlZCA9IGF1Y3Rpb25zLmZpbHRlcihhdWN0aW9uID0+IHtcbiAgICAgICAgY29uc3QgbWF0Y2hlc1NlYXJjaCA9IGF1Y3Rpb24udGl0bGU/LnRvTG93ZXJDYXNlKCkuaW5jbHVkZXMoc2VhcmNoVGVybS50b0xvd2VyQ2FzZSgpKSB8fFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICBhdWN0aW9uLmRlc2NyaXB0aW9uPy50b0xvd2VyQ2FzZSgpLmluY2x1ZGVzKHNlYXJjaFRlcm0udG9Mb3dlckNhc2UoKSlcbiAgICAgICAgY29uc3QgbWF0Y2hlc0NhdGVnb3J5ID0gIWZpbHRlckNhdGVnb3J5IHx8IGF1Y3Rpb24uY2F0ZWdvcnkgPT09IGZpbHRlckNhdGVnb3J5XG4gICAgICAgIHJldHVybiBtYXRjaGVzU2VhcmNoICYmIG1hdGNoZXNDYXRlZ29yeVxuICAgICAgfSlcblxuICAgICAgLy8gU29ydCB0aGUgZmlsdGVyZWQgcmVzdWx0c1xuICAgICAgZmlsdGVyZWQgPSBmaWx0ZXJlZC5zb3J0KChhLCBiKSA9PiB7XG4gICAgICAgIHN3aXRjaCAoc29ydEJ5KSB7XG4gICAgICAgICAgY2FzZSAnZW5kRGF0ZSc6XG4gICAgICAgICAgICByZXR1cm4gbmV3IERhdGUoYS5lbmREYXRlKS5nZXRUaW1lKCkgLSBuZXcgRGF0ZShiLmVuZERhdGUpLmdldFRpbWUoKVxuICAgICAgICAgIGNhc2UgJ2N1cnJlbnRCaWQnOlxuICAgICAgICAgICAgcmV0dXJuIChiLmN1cnJlbnRCaWQgfHwgMCkgLSAoYS5jdXJyZW50QmlkIHx8IDApXG4gICAgICAgICAgY2FzZSAnYmlkc0NvdW50JzpcbiAgICAgICAgICAgIHJldHVybiAoYi5iaWRzQ291bnQgfHwgMCkgLSAoYS5iaWRzQ291bnQgfHwgMClcbiAgICAgICAgICBjYXNlICd0aXRsZSc6XG4gICAgICAgICAgICByZXR1cm4gYS50aXRsZS5sb2NhbGVDb21wYXJlKGIudGl0bGUpXG4gICAgICAgICAgZGVmYXVsdDpcbiAgICAgICAgICAgIHJldHVybiAwXG4gICAgICAgIH1cbiAgICAgIH0pXG5cbiAgICAgIHNldEZpbHRlcmVkQXVjdGlvbnMoZmlsdGVyZWQpXG4gICAgICBjb25zb2xlLmxvZygn8J+UjSBGaWx0ZXJlZCBhdWN0aW9uczonLCBmaWx0ZXJlZC5sZW5ndGgsICdmcm9tJywgYXVjdGlvbnMubGVuZ3RoKVxuICAgIH0gZWxzZSB7XG4gICAgICBzZXRGaWx0ZXJlZEF1Y3Rpb25zKFtdKVxuICAgIH1cbiAgfSwgW3NlYXJjaFRlcm0sIGF1Y3Rpb25zLCBmaWx0ZXJDYXRlZ29yeSwgc29ydEJ5XSlcblxuICBjb25zdCBnZXRUaW1lUmVtYWluaW5nID0gKGVuZERhdGU6IHN0cmluZykgPT4ge1xuICAgIGNvbnN0IG5vdyA9IG5ldyBEYXRlKClcbiAgICBjb25zdCBlbmQgPSBuZXcgRGF0ZShlbmREYXRlKVxuICAgIGNvbnN0IGRpZmYgPSBlbmQuZ2V0VGltZSgpIC0gbm93LmdldFRpbWUoKVxuXG4gICAgaWYgKGRpZmYgPD0gMCkgcmV0dXJuICfYp9mG2KrZh9mJJ1xuXG4gICAgY29uc3QgaG91cnMgPSBNYXRoLmZsb29yKGRpZmYgLyAoMTAwMCAqIDYwICogNjApKVxuICAgIGNvbnN0IGRheXMgPSBNYXRoLmZsb29yKGhvdXJzIC8gMjQpXG5cbiAgICBpZiAoZGF5cyA+IDApIHJldHVybiBgJHtkYXlzfSDZitmI2YVgXG4gICAgcmV0dXJuIGAke2hvdXJzfSDYs9in2LnYqWBcbiAgfVxuXG4gIC8vIENoZWNrIGlmIHVzZXIgY2FuIGJpZFxuICBjb25zdCBjYW5Vc2VyQmlkID0gKGF1Y3Rpb246IGFueSkgPT4ge1xuICAgIGlmICghdXNlcikgcmV0dXJuIGZhbHNlXG5cbiAgICAvLyBPbmx5IGluZGl2aWR1YWxzIGFuZCBjb21wYW5pZXMgY2FuIGJpZFxuICAgIGlmICh1c2VyLnJvbGUgIT09ICdpbmRpdmlkdWFsJyAmJiB1c2VyLnJvbGUgIT09ICdjb21wYW55Jykge1xuICAgICAgcmV0dXJuIGZhbHNlXG4gICAgfVxuXG4gICAgLy8gQ29tcGFuaWVzIGNhbm5vdCBiaWQgb24gdGhlaXIgb3duIGF1Y3Rpb25zXG4gICAgaWYgKHVzZXIucm9sZSA9PT0gJ2NvbXBhbnknICYmIGF1Y3Rpb24uc2VsbGVyID09PSB1c2VyLnByb2ZpbGU/LmNvbXBhbnlOYW1lKSB7XG4gICAgICByZXR1cm4gZmFsc2VcbiAgICB9XG5cbiAgICByZXR1cm4gdHJ1ZVxuICB9XG5cbiAgY29uc3QgaGFuZGxlQmlkID0gYXN5bmMgKGF1Y3Rpb25JZDogc3RyaW5nKSA9PiB7XG4gICAgY29uc29sZS5sb2coJ/Cfjq8gQmlkIGJ1dHRvbiBjbGlja2VkIGZvciBhdWN0aW9uOicsIGF1Y3Rpb25JZClcblxuICAgIHRyeSB7XG4gICAgICAvLyBGZXRjaCBmcmVzaCBhdWN0aW9uIGRhdGEgYmVmb3JlIG9wZW5pbmcgbW9kYWxcbiAgICAgIHNldExvYWRpbmcodHJ1ZSlcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgYXVjdGlvbkFQSS5nZXRCeUlkKGF1Y3Rpb25JZClcblxuICAgICAgbGV0IGZyZXNoQXVjdGlvbiA9IG51bGxcbiAgICAgIGlmIChyZXNwb25zZS5kYXRhICYmIHJlc3BvbnNlLmRhdGEuc3VjY2Vzcykge1xuICAgICAgICBmcmVzaEF1Y3Rpb24gPSByZXNwb25zZS5kYXRhLmRhdGEuYXVjdGlvblxuICAgICAgfSBlbHNlIGlmIChyZXNwb25zZS5kYXRhICYmIHJlc3BvbnNlLmRhdGEuYXVjdGlvbikge1xuICAgICAgICBmcmVzaEF1Y3Rpb24gPSByZXNwb25zZS5kYXRhLmF1Y3Rpb25cbiAgICAgIH1cblxuICAgICAgaWYgKCFmcmVzaEF1Y3Rpb24pIHtcbiAgICAgICAgdG9hc3Qoe1xuICAgICAgICAgIHRpdGxlOiAn2K7Yt9ijJyxcbiAgICAgICAgICBkZXNjcmlwdGlvbjogJ9mE2YUg2YrYqtmFINin2YTYudir2YjYsSDYudmE2Ykg2KfZhNmF2LLYp9ivJyxcbiAgICAgICAgICB2YXJpYW50OiAnZGVzdHJ1Y3RpdmUnXG4gICAgICAgIH0pXG4gICAgICAgIHJldHVyblxuICAgICAgfVxuXG4gICAgICAvLyBUcmFuc2Zvcm0gZnJlc2ggZGF0YSB0byBtYXRjaCBvdXIgZm9ybWF0XG4gICAgICBjb25zdCB0cmFuc2Zvcm1lZEF1Y3Rpb24gPSB7XG4gICAgICAgIGlkOiBmcmVzaEF1Y3Rpb24uX2lkIHx8IGZyZXNoQXVjdGlvbi5pZCxcbiAgICAgICAgdGl0bGU6IGZyZXNoQXVjdGlvbi50aXRsZSxcbiAgICAgICAgZGVzY3JpcHRpb246IGZyZXNoQXVjdGlvbi5kZXNjcmlwdGlvbixcbiAgICAgICAgY3VycmVudEJpZDogZnJlc2hBdWN0aW9uLmN1cnJlbnRQcmljZSB8fCBmcmVzaEF1Y3Rpb24uY3VycmVudEJpZCB8fCBmcmVzaEF1Y3Rpb24uc3RhcnRpbmdQcmljZSB8fCAwLFxuICAgICAgICBlbmREYXRlOiBmcmVzaEF1Y3Rpb24uZW5kRGF0ZSxcbiAgICAgICAgYmlkc0NvdW50OiBmcmVzaEF1Y3Rpb24uYmlkcz8ubGVuZ3RoIHx8IGZyZXNoQXVjdGlvbi5iaWRzQ291bnQgfHwgMCxcbiAgICAgICAgc2VsbGVyOiBmcmVzaEF1Y3Rpb24uc2VsbGVyPy5wcm9maWxlPy5mdWxsTmFtZSB8fCBmcmVzaEF1Y3Rpb24uc2VsbGVyPy5wcm9maWxlPy5jb21wYW55TmFtZSB8fCBmcmVzaEF1Y3Rpb24uc2VsbGVyPy5uYW1lIHx8ICfYutmK2LEg2YXYrdiv2K8nLFxuICAgICAgICBjYXRlZ29yeTogZnJlc2hBdWN0aW9uLmNhdGVnb3J5LFxuICAgICAgICBzdGF0dXM6IGZyZXNoQXVjdGlvbi5zdGF0dXNcbiAgICAgIH1cblxuICAgICAgLy8gQ2hlY2sgaWYgdXNlciBjYW4gYmlkXG4gICAgICBpZiAoIWNhblVzZXJCaWQodHJhbnNmb3JtZWRBdWN0aW9uKSkge1xuICAgICAgICBpZiAodXNlcj8ucm9sZSA9PT0gJ2FkbWluJyB8fCB1c2VyPy5yb2xlID09PSAnc3VwZXJfYWRtaW4nKSB7XG4gICAgICAgICAgdG9hc3Qoe1xuICAgICAgICAgICAgdGl0bGU6ICfZiNi12YjZhCDYutmK2LEg2YXYs9mF2YjYrScsXG4gICAgICAgICAgICBkZXNjcmlwdGlvbjogJ9in2YTZhdiv2YrYsdmI2YYg2YTYpyDZitmF2YPZhtmH2YUg2KfZhNmF2LLYp9mK2K/YqSDYudmE2Ykg2KfZhNmF2LLYp9iv2KfYqicsXG4gICAgICAgICAgICB2YXJpYW50OiAnZGVzdHJ1Y3RpdmUnXG4gICAgICAgICAgfSlcbiAgICAgICAgfSBlbHNlIGlmICh1c2VyPy5yb2xlID09PSAnZ292ZXJubWVudCcpIHtcbiAgICAgICAgICB0b2FzdCh7XG4gICAgICAgICAgICB0aXRsZTogJ9mI2LXZiNmEINi62YrYsSDZhdiz2YXZiNitJyxcbiAgICAgICAgICAgIGRlc2NyaXB0aW9uOiAn2KfZhNis2YfYp9iqINin2YTYrdmD2YjZhdmK2Kkg2YTYpyDYqtiy2KfZitivINi52YTZiSDYp9mE2YXYstin2K/Yp9iqJyxcbiAgICAgICAgICAgIHZhcmlhbnQ6ICdkZXN0cnVjdGl2ZSdcbiAgICAgICAgICB9KVxuICAgICAgICB9IGVsc2UgaWYgKHVzZXI/LnJvbGUgPT09ICdjb21wYW55Jykge1xuICAgICAgICAgIHRvYXN0KHtcbiAgICAgICAgICAgIHRpdGxlOiAn2YjYtdmI2YQg2LrZitixINmF2LPZhdmI2K0nLFxuICAgICAgICAgICAgZGVzY3JpcHRpb246ICfZhNinINmK2YXZg9mG2YMg2KfZhNmF2LLYp9mK2K/YqSDYudmE2Ykg2YXYstin2K/ZgyDYp9mE2K7Yp9i1JyxcbiAgICAgICAgICAgIHZhcmlhbnQ6ICdkZXN0cnVjdGl2ZSdcbiAgICAgICAgICB9KVxuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgIHRvYXN0KHtcbiAgICAgICAgICAgIHRpdGxlOiAn2YjYtdmI2YQg2LrZitixINmF2LPZhdmI2K0nLFxuICAgICAgICAgICAgZGVzY3JpcHRpb246ICfYutmK2LEg2YXYs9mF2YjYrSDZhNmDINio2KfZhNmF2LLYp9mK2K/YqScsXG4gICAgICAgICAgICB2YXJpYW50OiAnZGVzdHJ1Y3RpdmUnXG4gICAgICAgICAgfSlcbiAgICAgICAgfVxuICAgICAgICByZXR1cm5cbiAgICAgIH1cblxuICAgICAgLy8gT3BlbiBiaWRkaW5nIG1vZGFsIHdpdGggZnJlc2ggZGF0YVxuICAgICAgc2V0U2VsZWN0ZWRBdWN0aW9uKHRyYW5zZm9ybWVkQXVjdGlvbilcbiAgICAgIHNldEJpZEFtb3VudChTdHJpbmcoKHRyYW5zZm9ybWVkQXVjdGlvbi5jdXJyZW50QmlkIHx8IDApICsgMTAwMCkpXG4gICAgICBzZXRTaG93QmlkTW9kYWwodHJ1ZSlcblxuICAgICAgdG9hc3Qoe1xuICAgICAgICB0aXRsZTogJ/Cfk4og2KrZhSDYqtit2K/ZitirINio2YrYp9mG2KfYqiDYp9mE2YXYstin2K8nLFxuICAgICAgICBkZXNjcmlwdGlvbjogYNin2YTZhdiy2KfZitiv2Kkg2KfZhNit2KfZhNmK2Kk6ICR7KHRyYW5zZm9ybWVkQXVjdGlvbi5jdXJyZW50QmlkIHx8IDApLnRvTG9jYWxlU3RyaW5nKCl9INixLtizYFxuICAgICAgfSlcblxuICAgIH0gY2F0Y2ggKGVycm9yOiBhbnkpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGZldGNoaW5nIGZyZXNoIGF1Y3Rpb24gZGF0YTonLCBlcnJvcilcbiAgICAgIHRvYXN0KHtcbiAgICAgICAgdGl0bGU6ICfYrti32KMg2YHZiiDYqtit2K/ZitirINin2YTYqNmK2KfZhtin2KonLFxuICAgICAgICBkZXNjcmlwdGlvbjogJ9iz2YrYqtmFINin2LPYqtiu2K/Yp9mFINin2YTYqNmK2KfZhtin2Kog2KfZhNmF2K3ZgdmI2LjYqSDZhdit2YTZitin2YsnLFxuICAgICAgICB2YXJpYW50OiAnZGVzdHJ1Y3RpdmUnXG4gICAgICB9KVxuXG4gICAgICAvLyBGYWxsYmFjayB0byBjYWNoZWQgZGF0YVxuICAgICAgY29uc3QgY3VycmVudEF1Y3Rpb24gPSBhdWN0aW9ucy5maW5kKGEgPT4gYS5pZCA9PT0gYXVjdGlvbklkKVxuICAgICAgaWYgKGN1cnJlbnRBdWN0aW9uICYmIGNhblVzZXJCaWQoY3VycmVudEF1Y3Rpb24pKSB7XG4gICAgICAgIHNldFNlbGVjdGVkQXVjdGlvbihjdXJyZW50QXVjdGlvbilcbiAgICAgICAgc2V0QmlkQW1vdW50KFN0cmluZygoY3VycmVudEF1Y3Rpb24uY3VycmVudEJpZCB8fCAwKSArIDEwMDApKVxuICAgICAgICBzZXRTaG93QmlkTW9kYWwodHJ1ZSlcbiAgICAgIH1cbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0TG9hZGluZyhmYWxzZSlcbiAgICB9XG4gIH1cblxuICBjb25zdCBzdWJtaXRCaWQgPSBhc3luYyAoKSA9PiB7XG4gICAgaWYgKCFzZWxlY3RlZEF1Y3Rpb24pIHJldHVyblxuXG4gICAgY29uc3QgYmlkQW1vdW50TnVtID0gcGFyc2VGbG9hdChiaWRBbW91bnQpXG4gICAgaWYgKGlzTmFOKGJpZEFtb3VudE51bSkgfHwgYmlkQW1vdW50TnVtIDw9IChzZWxlY3RlZEF1Y3Rpb24uY3VycmVudEJpZCB8fCAwKSkge1xuICAgICAgdG9hc3Qoe1xuICAgICAgICB0aXRsZTogJ9mF2LLYp9mK2K/YqSDYutmK2LEg2LXYp9mE2K3YqScsXG4gICAgICAgIGRlc2NyaXB0aW9uOiAn2YrYrNioINij2YYg2KrZg9mI2YYg2KfZhNmF2LLYp9mK2K/YqSDYo9mD2KjYsSDZhdmGINin2YTZhdiy2KfZitiv2Kkg2KfZhNit2KfZhNmK2KknLFxuICAgICAgICB2YXJpYW50OiAnZGVzdHJ1Y3RpdmUnXG4gICAgICB9KVxuICAgICAgcmV0dXJuXG4gICAgfVxuXG4gICAgdHJ5IHtcbiAgICAgIHNldEJpZExvYWRpbmcodHJ1ZSlcblxuICAgICAgLy8gR2V0IGF1Y3Rpb24gdmVyc2lvbiBmb3Igb3B0aW1pc3RpYyBsb2NraW5nXG4gICAgICBjb25zdCBhdWN0aW9uVmVyc2lvbiA9IGF1Y3Rpb25WZXJzaW9uc1tzZWxlY3RlZEF1Y3Rpb24uaWRdXG5cbiAgICAgIC8vIFByZXBhcmUgYmlkIGRhdGEgd2l0aCBjdXJyZW5jeSBhbmQgdmVyc2lvblxuICAgICAgY29uc3QgYmlkRGF0YSA9IHtcbiAgICAgICAgYmlkQW1vdW50OiBiaWRBbW91bnROdW0sXG4gICAgICAgIGN1cnJlbmN5OiB1c2VyQ3VycmVuY3ksXG4gICAgICAgIHZlcnNpb246IGF1Y3Rpb25WZXJzaW9uXG4gICAgICB9XG5cbiAgICAgIC8vIFN1Ym1pdCBiaWQgYW5kIHdhaXQgZm9yIHNlcnZlciByZXNwb25zZVxuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaChgL2FwaS9hdWN0aW9ucy8ke3NlbGVjdGVkQXVjdGlvbi5pZH0vYmlkYCwge1xuICAgICAgICBtZXRob2Q6ICdQT1NUJyxcbiAgICAgICAgaGVhZGVyczoge1xuICAgICAgICAgICdDb250ZW50LVR5cGUnOiAnYXBwbGljYXRpb24vanNvbicsXG4gICAgICAgICAgJ0F1dGhvcml6YXRpb24nOiBgQmVhcmVyICR7bG9jYWxTdG9yYWdlLmdldEl0ZW0oJ3Rva2VuJyl9YFxuICAgICAgICB9LFxuICAgICAgICBib2R5OiBKU09OLnN0cmluZ2lmeShiaWREYXRhKVxuICAgICAgfSlcblxuICAgICAgY29uc3QgcmVzdWx0ID0gYXdhaXQgcmVzcG9uc2UuanNvbigpXG5cbiAgICAgIGlmICghcmVzcG9uc2Uub2spIHtcbiAgICAgICAgLy8gSGFuZGxlIHNwZWNpZmljIGVycm9yIGNhc2VzXG4gICAgICAgIGlmIChyZXNwb25zZS5zdGF0dXMgPT09IDQwOSkge1xuICAgICAgICAgIC8vIE9wdGltaXN0aWMgbG9ja2luZyBjb25mbGljdFxuICAgICAgICAgIHRvYXN0KHtcbiAgICAgICAgICAgIHRpdGxlOiAn2KrZhSDYqtit2K/ZitirINin2YTZhdiy2KfYryDimqDvuI8nLFxuICAgICAgICAgICAgZGVzY3JpcHRpb246ICfZgtin2YUg2LTYrti1INii2K7YsSDYqNiq2K3Yr9mK2Ksg2KfZhNmF2LLYp9ivLiDYs9mK2KrZhSDYqtit2K/ZitirINin2YTYqNmK2KfZhtin2KouJyxcbiAgICAgICAgICAgIHZhcmlhbnQ6ICdkZXN0cnVjdGl2ZSdcbiAgICAgICAgICB9KVxuICAgICAgICAgIGF3YWl0IGxvYWRBdWN0aW9ucygpXG4gICAgICAgICAgc2V0U2hvd0JpZE1vZGFsKGZhbHNlKVxuICAgICAgICAgIHNldEJpZEFtb3VudCgnJylcbiAgICAgICAgICBzZXRTZWxlY3RlZEF1Y3Rpb24obnVsbClcbiAgICAgICAgICByZXR1cm5cbiAgICAgICAgfVxuICAgICAgICB0aHJvdyBuZXcgRXJyb3IocmVzdWx0Lm1lc3NhZ2UgfHwgJ0JpZCBmYWlsZWQnKVxuICAgICAgfVxuXG4gICAgICAvLyBDbG9zZSBtb2RhbCBmaXJzdFxuICAgICAgc2V0U2hvd0JpZE1vZGFsKGZhbHNlKVxuICAgICAgc2V0QmlkQW1vdW50KCcnKVxuICAgICAgc2V0U2VsZWN0ZWRBdWN0aW9uKG51bGwpXG5cbiAgICAgIC8vIFJlbG9hZCBmcmVzaCBkYXRhIGZyb20gc2VydmVyXG4gICAgICBhd2FpdCBsb2FkQXVjdGlvbnMoKVxuXG4gICAgICAvLyBTaG93IHN1Y2Nlc3MgbWVzc2FnZSB3aXRoIGN1cnJlbmN5IGZvcm1hdHRpbmdcbiAgICAgIGNvbnN0IGZvcm1hdHRlZEFtb3VudCA9IGN1cnJlbmN5U2VydmljZS5mb3JtYXRBbW91bnQoYmlkQW1vdW50TnVtLCB1c2VyQ3VycmVuY3kpXG4gICAgICB0b2FzdCh7XG4gICAgICAgIHRpdGxlOiAn2KrZhSDYqtmC2K/ZitmFINin2YTZhdiy2KfZitiv2Kkg2KjZhtis2KfYrSEg8J+OiScsXG4gICAgICAgIGRlc2NyaXB0aW9uOiBg2YXZgtiv2KfYsSDYp9mE2YXYstin2YrYr9ipOiAke2Zvcm1hdHRlZEFtb3VudH1gXG4gICAgICB9KVxuXG4gICAgfSBjYXRjaCAoZXJyb3I6IGFueSkge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgcGxhY2luZyBiaWQ6JywgZXJyb3IpXG5cbiAgICAgIC8vIENsb3NlIG1vZGFsIG9uIGVycm9yIHRvb1xuICAgICAgc2V0U2hvd0JpZE1vZGFsKGZhbHNlKVxuICAgICAgc2V0QmlkQW1vdW50KCcnKVxuICAgICAgc2V0U2VsZWN0ZWRBdWN0aW9uKG51bGwpXG5cbiAgICAgIC8vIFNob3cgc3BlY2lmaWMgZXJyb3IgbWVzc2FnZVxuICAgICAgY29uc3QgZXJyb3JNZXNzYWdlID0gZXJyb3IucmVzcG9uc2U/LmRhdGE/Lm1lc3NhZ2UgfHwgJ9mK2LHYrNmJINin2YTZhdit2KfZiNmE2Kkg2YXYsdipINij2K7YsdmJJ1xuXG4gICAgICBpZiAoZXJyb3JNZXNzYWdlLmluY2x1ZGVzKCdoaWdoZXIgdGhhbiBjdXJyZW50IHByaWNlJykgfHwgZXJyb3JNZXNzYWdlLmluY2x1ZGVzKCfYo9mD2KjYsSDZhdmGINin2YTYs9i52LEg2KfZhNit2KfZhNmKJykpIHtcbiAgICAgICAgdG9hc3Qoe1xuICAgICAgICAgIHRpdGxlOiAn2YXYstin2YrYr9ipINmF2YbYrtmB2LbYqSDimqDvuI8nLFxuICAgICAgICAgIGRlc2NyaXB0aW9uOiAn2YLYp9mFINi02K7YtSDYotiu2LEg2KjZhdiy2KfZitiv2Kkg2KPYudmE2YkuINmK2LHYrNmJINin2YTZhdit2KfZiNmE2Kkg2KjZhdio2YTYuiDYo9mD2KjYsS4nLFxuICAgICAgICAgIHZhcmlhbnQ6ICdkZXN0cnVjdGl2ZSdcbiAgICAgICAgfSlcbiAgICAgIH0gZWxzZSBpZiAoZXJyb3JNZXNzYWdlLmluY2x1ZGVzKCdlbmRlZCcpIHx8IGVycm9yTWVzc2FnZS5pbmNsdWRlcygn2KfZhtiq2YfZiScpKSB7XG4gICAgICAgIHRvYXN0KHtcbiAgICAgICAgICB0aXRsZTogJ9in2YbYqtmH2Ykg2KfZhNmF2LLYp9ivIOKPsCcsXG4gICAgICAgICAgZGVzY3JpcHRpb246ICfZhNmC2K8g2KfZhtiq2YfZiSDZh9iw2Kcg2KfZhNmF2LLYp9ivINmI2YTYpyDZitmF2YPZhiDZgtio2YjZhCDZhdiy2KfZitiv2KfYqiDYrNiv2YrYr9ipLicsXG4gICAgICAgICAgdmFyaWFudDogJ2Rlc3RydWN0aXZlJ1xuICAgICAgICB9KVxuICAgICAgfSBlbHNlIHtcbiAgICAgICAgdG9hc3Qoe1xuICAgICAgICAgIHRpdGxlOiAn2YHYtNmEINmB2Yog2KrZgtiv2YrZhSDYp9mE2YXYstin2YrYr9ipJyxcbiAgICAgICAgICBkZXNjcmlwdGlvbjogZXJyb3JNZXNzYWdlLFxuICAgICAgICAgIHZhcmlhbnQ6ICdkZXN0cnVjdGl2ZSdcbiAgICAgICAgfSlcbiAgICAgIH1cblxuICAgICAgLy8gQWx3YXlzIHJlbG9hZCBkYXRhIG9uIGVycm9yIHRvIHN5bmMgd2l0aCBzZXJ2ZXJcbiAgICAgIGF3YWl0IGxvYWRBdWN0aW9ucygpXG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHNldEJpZExvYWRpbmcoZmFsc2UpXG4gICAgfVxuICB9XG5cbiAgY29uc3QgaGFuZGxlU2F2ZUF1Y3Rpb24gPSBhc3luYyAoYXVjdGlvbklkOiBzdHJpbmcpID0+IHtcbiAgICBjb25zb2xlLmxvZygn4p2k77iPIFNhdmUgYnV0dG9uIGNsaWNrZWQgZm9yIGF1Y3Rpb246JywgYXVjdGlvbklkKVxuICAgIFxuICAgIC8vIFNob3cgaW1tZWRpYXRlIGZlZWRiYWNrIHRoYXQgYnV0dG9uIHdhcyBjbGlja2VkXG4gICAgdG9hc3Qoe1xuICAgICAgdGl0bGU6ICfwn5KGINiq2YUg2KfZhNi22LrYtyDYudmE2Ykg2LLYsSDYp9mE2K3Zgdi4JyxcbiAgICAgIGRlc2NyaXB0aW9uOiAn2KzYp9ix2Yog2YXYudin2YTYrNipINin2YTYt9mE2KguLi4nXG4gICAgfSlcbiAgICBcbiAgICB0cnkge1xuICAgICAgY29uc3QgYXVjdGlvbiA9IGF1Y3Rpb25zLmZpbmQoYSA9PiBhLmlkID09PSBhdWN0aW9uSWQpXG4gICAgICBpZiAoIWF1Y3Rpb24pIHtcbiAgICAgICAgdG9hc3Qoe1xuICAgICAgICAgIHRpdGxlOiAn2K7Yt9ijJyxcbiAgICAgICAgICBkZXNjcmlwdGlvbjogJ9mE2YUg2YrYqtmFINin2YTYudir2YjYsSDYudmE2Ykg2KfZhNmF2LLYp9ivJyxcbiAgICAgICAgICB2YXJpYW50OiAnZGVzdHJ1Y3RpdmUnXG4gICAgICAgIH0pXG4gICAgICAgIHJldHVyblxuICAgICAgfVxuICAgICAgXG4gICAgICBjb25zdCBpc0N1cnJlbnRseVNhdmVkID0gc2F2ZWRBdWN0aW9ucy5oYXMoYXVjdGlvbklkKVxuICAgICAgXG4gICAgICAvLyBPcHRpbWlzdGljIFVJIHVwZGF0ZVxuICAgICAgc2V0U2F2ZWRBdWN0aW9ucyhwcmV2ID0+IHtcbiAgICAgICAgY29uc3QgbmV3U2V0ID0gbmV3IFNldChwcmV2KVxuICAgICAgICBpZiAoaXNDdXJyZW50bHlTYXZlZCkge1xuICAgICAgICAgIG5ld1NldC5kZWxldGUoYXVjdGlvbklkKVxuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgIG5ld1NldC5hZGQoYXVjdGlvbklkKVxuICAgICAgICB9XG4gICAgICAgIHJldHVybiBuZXdTZXRcbiAgICAgIH0pXG4gICAgICBcbiAgICAgIGlmIChpc0N1cnJlbnRseVNhdmVkKSB7XG4gICAgICAgIC8vIFJlbW92ZSBmcm9tIGZhdm9yaXRlc1xuICAgICAgICBhd2FpdCBmYXZvcml0ZXNBUEkucmVtb3ZlRmF2b3JpdGUoJ2F1Y3Rpb24nLCBhdWN0aW9uSWQpXG4gICAgICAgIHRvYXN0KHtcbiAgICAgICAgICB0aXRsZTogJ+KdpO+4jyDYqtmFINil2LLYp9mE2Kkg2YXZhiDYp9mE2YXZgdi22YTYqScsXG4gICAgICAgICAgZGVzY3JpcHRpb246IGDYqtmFINil2LLYp9mE2KkgXCIke2F1Y3Rpb24udGl0bGV9XCIg2YXZhiDYp9mE2YXZgdi22YTYqWBcbiAgICAgICAgfSlcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIC8vIEFkZCB0byBmYXZvcml0ZXNcbiAgICAgICAgYXdhaXQgZmF2b3JpdGVzQVBJLmFkZEZhdm9yaXRlKHtcbiAgICAgICAgICBpdGVtVHlwZTogJ2F1Y3Rpb24nLFxuICAgICAgICAgIGl0ZW1JZDogYXVjdGlvbklkLFxuICAgICAgICAgIG5vdGlmaWNhdGlvbnM6IHtcbiAgICAgICAgICAgIGJpZFVwZGF0ZXM6IHRydWUsXG4gICAgICAgICAgICBzdGF0dXNDaGFuZ2VzOiB0cnVlLFxuICAgICAgICAgICAgZW5kaW5nU29vbjogdHJ1ZVxuICAgICAgICAgIH1cbiAgICAgICAgfSlcbiAgICAgICAgdG9hc3Qoe1xuICAgICAgICAgIHRpdGxlOiAn8J+SliDYqtmFINil2LbYp9mB2Kkg2KXZhNmJINin2YTZhdmB2LbZhNipJyxcbiAgICAgICAgICBkZXNjcmlwdGlvbjogYNiq2YUg2KXYttin2YHYqSBcIiR7YXVjdGlvbi50aXRsZX1cIiDYpdmE2Ykg2KfZhNmF2YHYttmE2KlgXG4gICAgICAgIH0pXG4gICAgICB9XG4gICAgfSBjYXRjaCAoZXJyb3I6IGFueSkge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3Igc2F2aW5nIGF1Y3Rpb246JywgZXJyb3IpXG4gICAgICAvLyBSZXZlcnQgb3B0aW1pc3RpYyB1cGRhdGUgb24gZXJyb3JcbiAgICAgIHNldFNhdmVkQXVjdGlvbnMocHJldiA9PiB7XG4gICAgICAgIGNvbnN0IG5ld1NldCA9IG5ldyBTZXQocHJldilcbiAgICAgICAgaWYgKHNhdmVkQXVjdGlvbnMuaGFzKGF1Y3Rpb25JZCkpIHtcbiAgICAgICAgICBuZXdTZXQuYWRkKGF1Y3Rpb25JZClcbiAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICBuZXdTZXQuZGVsZXRlKGF1Y3Rpb25JZClcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gbmV3U2V0XG4gICAgICB9KVxuICAgICAgdG9hc3Qoe1xuICAgICAgICB0aXRsZTogJ9mB2LTZhCDZgdmKINit2YHYuCDYp9mE2YXYstin2K8nLFxuICAgICAgICBkZXNjcmlwdGlvbjogZXJyb3IucmVzcG9uc2U/LmRhdGE/Lm1lc3NhZ2UgfHwgJ9mK2LHYrNmJINin2YTZhdit2KfZiNmE2Kkg2YXYsdipINij2K7YsdmJJyxcbiAgICAgICAgdmFyaWFudDogJ2Rlc3RydWN0aXZlJ1xuICAgICAgfSlcbiAgICB9XG4gIH1cblxuICBjb25zdCBoYW5kbGVWaWV3QXVjdGlvbiA9IGFzeW5jIChhdWN0aW9uSWQ6IHN0cmluZykgPT4ge1xuICAgIGNvbnNvbGUubG9nKCfwn5GB77iPIFZpZXcgYnV0dG9uIGNsaWNrZWQgZm9yIGF1Y3Rpb246JywgYXVjdGlvbklkKVxuXG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IGF1Y3Rpb24gPSBhdWN0aW9ucy5maW5kKGEgPT4gYS5pZCA9PT0gYXVjdGlvbklkKVxuICAgICAgaWYgKCFhdWN0aW9uKSB7XG4gICAgICAgIHRvYXN0KHtcbiAgICAgICAgICB0aXRsZTogJ9iu2LfYoycsXG4gICAgICAgICAgZGVzY3JpcHRpb246ICfZhNmFINmK2KrZhSDYp9mE2LnYq9mI2LEg2LnZhNmJINin2YTZhdiy2KfYrycsXG4gICAgICAgICAgdmFyaWFudDogJ2Rlc3RydWN0aXZlJ1xuICAgICAgICB9KVxuICAgICAgICByZXR1cm5cbiAgICAgIH1cblxuICAgICAgLy8gTmF2aWdhdGUgdG8gYXVjdGlvbiBkZXRhaWwgcGFnZVxuICAgICAgcm91dGVyLnB1c2goYC9hdWN0aW9ucy8ke2F1Y3Rpb25JZH1gKVxuXG4gICAgICB0b2FzdCh7XG4gICAgICAgIHRpdGxlOiAn8J+Rge+4jyDYrNin2LHZiiDYqtit2YXZitmEINin2YTYqtmB2KfYtdmK2YQnLFxuICAgICAgICBkZXNjcmlwdGlvbjogJ9iz2YrYqtmFINi52LHYtiDYqtmB2KfYtdmK2YQg2KfZhNmF2LLYp9ivJ1xuICAgICAgfSlcbiAgICB9IGNhdGNoIChlcnJvcjogYW55KSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciB2aWV3aW5nIGF1Y3Rpb246JywgZXJyb3IpXG4gICAgICB0b2FzdCh7XG4gICAgICAgIHRpdGxlOiAn2YHYtNmEINmB2Yog2LnYsdi2INiq2YHYp9i12YrZhCDYp9mE2YXYstin2K8nLFxuICAgICAgICBkZXNjcmlwdGlvbjogZXJyb3IucmVzcG9uc2U/LmRhdGE/Lm1lc3NhZ2UgfHwgJ9mK2LHYrNmJINin2YTZhdit2KfZiNmE2Kkg2YXYsdipINij2K7YsdmJJyxcbiAgICAgICAgdmFyaWFudDogJ2Rlc3RydWN0aXZlJ1xuICAgICAgfSlcbiAgICB9XG4gIH1cblxuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS02XCI+XG4gICAgICAgIDxoZWFkZXI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW5cIj5cbiAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgIDxoMSBjbGFzc05hbWU9XCJ0ZXh0LTN4bCBmb250LWJvbGRcIj7Yp9mE2YXYstin2K/Yp9iqINin2YTZhdiq2KfYrdipPC9oMT5cbiAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1tdXRlZC1mb3JlZ3JvdW5kXCI+XG4gICAgICAgICAgICAgICAg2KfYs9iq2YPYtNmBINin2YTZhdiy2KfYr9in2Kog2KfZhNit2KfZhNmK2Kkg2YjYtNin2LHZgyDZgdmKINin2YTZhdiy2KfZitiv2KlcbiAgICAgICAgICAgICAgICB7YXV0b1JlZnJlc2ggJiYgKFxuICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LWdyZWVuLTYwMCBtbC0yXCI+XG4gICAgICAgICAgICAgICAgICAgIOKAoiDYqtit2K/ZitirINiq2YTZgtin2KbZiiDZg9mEIDMwINir2KfZhtmK2KlcbiAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTJcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMlwiPlxuICAgICAgICAgICAgICAgIDxMYWJlbCBodG1sRm9yPVwiY3VycmVuY3lcIiBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtXCI+2KfZhNi52YXZhNipOjwvTGFiZWw+XG4gICAgICAgICAgICAgICAgPHNlbGVjdFxuICAgICAgICAgICAgICAgICAgaWQ9XCJjdXJyZW5jeVwiXG4gICAgICAgICAgICAgICAgICB2YWx1ZT17dXNlckN1cnJlbmN5fVxuICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9e2FzeW5jIChlKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IG5ld0N1cnJlbmN5ID0gZS50YXJnZXQudmFsdWVcbiAgICAgICAgICAgICAgICAgICAgc2V0VXNlckN1cnJlbmN5KG5ld0N1cnJlbmN5KVxuICAgICAgICAgICAgICAgICAgICBjdXJyZW5jeVNlcnZpY2Uuc2V0VXNlckN1cnJlbmN5KG5ld0N1cnJlbmN5KVxuICAgICAgICAgICAgICAgICAgICBzZXRMb2FkaW5nKHRydWUpXG4gICAgICAgICAgICAgICAgICAgIGF3YWl0IGxvYWRBdWN0aW9ucygpXG4gICAgICAgICAgICAgICAgICAgIHNldExvYWRpbmcoZmFsc2UpXG4gICAgICAgICAgICAgICAgICAgIHRvYXN0KHtcbiAgICAgICAgICAgICAgICAgICAgICB0aXRsZTogJ/CfkrEg2KrZhSDYqti62YrZitixINin2YTYudmF2YTYqScsXG4gICAgICAgICAgICAgICAgICAgICAgZGVzY3JpcHRpb246IGDYqtmFINin2YTYqtit2YjZitmEINil2YTZiSAke2N1cnJlbmN5U2VydmljZS5nZXRDdXJyZW5jeVN5bWJvbChuZXdDdXJyZW5jeSl9YFxuICAgICAgICAgICAgICAgICAgICB9KVxuICAgICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInB4LTIgcHktMSBib3JkZXIgYm9yZGVyLWdyYXktMzAwIHJvdW5kZWQgdGV4dC1zbSBmb2N1czpvdXRsaW5lLW5vbmUgZm9jdXM6cmluZy0yIGZvY3VzOnJpbmctYmx1ZS01MDBcIlxuICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgIHtjdXJyZW5jeVNlcnZpY2UuZ2V0U3VwcG9ydGVkQ3VycmVuY2llcygpLm1hcChjdXJyZW5jeSA9PiAoXG4gICAgICAgICAgICAgICAgICAgIDxvcHRpb24ga2V5PXtjdXJyZW5jeS5jb2RlfSB2YWx1ZT17Y3VycmVuY3kuY29kZX0+XG4gICAgICAgICAgICAgICAgICAgICAge2N1cnJlbmN5LnN5bWJvbH0ge2N1cnJlbmN5LmNvZGV9XG4gICAgICAgICAgICAgICAgICAgIDwvb3B0aW9uPlxuICAgICAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICAgICAgPC9zZWxlY3Q+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICAgICAgb25DbGljaz17YXN5bmMgKCkgPT4ge1xuICAgICAgICAgICAgICAgICAgYXdhaXQgbG9hZEF1Y3Rpb25zKHRydWUpIC8vIFNob3cgbG9hZGluZyBzdGF0ZVxuICAgICAgICAgICAgICAgICAgc2V0TGFzdFJlZnJlc2gobmV3IERhdGUoKSlcbiAgICAgICAgICAgICAgICAgIHRvYXN0KHtcbiAgICAgICAgICAgICAgICAgICAgdGl0bGU6ICfwn5SEINiq2YUg2KrYrdiv2YrYqyDYp9mE2KjZitin2YbYp9iqJyxcbiAgICAgICAgICAgICAgICAgICAgZGVzY3JpcHRpb246IGDYqtmFINin2YTYudir2YjYsSDYudmE2YkgJHtmaWx0ZXJlZEF1Y3Rpb25zLmxlbmd0aH0g2YXYstin2K8g2YXYqtin2K1gXG4gICAgICAgICAgICAgICAgICB9KVxuICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgICAgdmFyaWFudD1cIm91dGxpbmVcIlxuICAgICAgICAgICAgICAgIGRpc2FibGVkPXtsb2FkaW5nfVxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgPFJlZnJlc2hDdyBjbGFzc05hbWU9e2BoLTQgdy00IG1sLTIgJHtsb2FkaW5nID8gJ2FuaW1hdGUtc3BpbicgOiAnJ31gfSAvPlxuICAgICAgICAgICAgICAgINiq2K3Yr9mK2KtcbiAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiB7XG4gICAgICAgICAgICAgICAgICBzZXRBdXRvUmVmcmVzaCghYXV0b1JlZnJlc2gpXG4gICAgICAgICAgICAgICAgICB0b2FzdCh7XG4gICAgICAgICAgICAgICAgICAgIHRpdGxlOiBhdXRvUmVmcmVzaCA/ICfij7jvuI8g2KrZhSDYpdmK2YLYp9mBINin2YTYqtit2K/ZitirINin2YTYqtmE2YLYp9im2YonIDogJ+KWtu+4jyDYqtmFINiq2LTYutmK2YQg2KfZhNiq2K3Yr9mK2Ksg2KfZhNiq2YTZgtin2KbZiicsXG4gICAgICAgICAgICAgICAgICAgIGRlc2NyaXB0aW9uOiBhdXRvUmVmcmVzaCA/ICfZitmF2YPZhtmDINin2YTYqtit2K/ZitirINmK2K/ZiNmK2KfZiycgOiAn2LPZitiq2YUg2KfZhNiq2K3Yr9mK2Ksg2YPZhCAzMCDYq9in2YbZitipJ1xuICAgICAgICAgICAgICAgICAgfSlcbiAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgIHZhcmlhbnQ9e2F1dG9SZWZyZXNoID8gXCJkZWZhdWx0XCIgOiBcIm91dGxpbmVcIn1cbiAgICAgICAgICAgICAgICBzaXplPVwiaWNvblwiXG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICB7YXV0b1JlZnJlc2ggPyA8UGF1c2UgY2xhc3NOYW1lPVwiaC00IHctNFwiIC8+IDogPFBsYXkgY2xhc3NOYW1lPVwiaC00IHctNFwiIC8+fVxuICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2hlYWRlcj5cblxuICAgICAgICB7LyogU2VhcmNoIGFuZCBGaWx0ZXJzICovfVxuICAgICAgICA8Q2FyZD5cbiAgICAgICAgICA8Q2FyZENvbnRlbnQgY2xhc3NOYW1lPVwicHQtNlwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTRcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJyZWxhdGl2ZVwiPlxuICAgICAgICAgICAgICAgIDxTZWFyY2ggY2xhc3NOYW1lPVwiYWJzb2x1dGUgcmlnaHQtMyB0b3AtMyBoLTQgdy00IHRleHQtbXV0ZWQtZm9yZWdyb3VuZFwiIC8+XG4gICAgICAgICAgICAgICAgPElucHV0XG4gICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cItin2KjYrdirINmB2Yog2KfZhNmF2LLYp9iv2KfYqi4uLlwiXG4gICAgICAgICAgICAgICAgICB2YWx1ZT17c2VhcmNoVGVybX1cbiAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0U2VhcmNoVGVybShlLnRhcmdldC52YWx1ZSl9XG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJwci0xMFwiXG4gICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGZsZXgtY29sIHNtOmZsZXgtcm93IGdhcC00XCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTFcIj5cbiAgICAgICAgICAgICAgICAgIDxMYWJlbCBodG1sRm9yPVwiY2F0ZWdvcnlcIiBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtXCI+2KfZhNmB2KbYqTwvTGFiZWw+XG4gICAgICAgICAgICAgICAgICA8c2VsZWN0XG4gICAgICAgICAgICAgICAgICAgIGlkPVwiY2F0ZWdvcnlcIlxuICAgICAgICAgICAgICAgICAgICB2YWx1ZT17ZmlsdGVyQ2F0ZWdvcnl9XG4gICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0RmlsdGVyQ2F0ZWdvcnkoZS50YXJnZXQudmFsdWUpfVxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgbXQtMSBweC0zIHB5LTIgYm9yZGVyIGJvcmRlci1ncmF5LTMwMCByb3VuZGVkLW1kIGZvY3VzOm91dGxpbmUtbm9uZSBmb2N1czpyaW5nLTIgZm9jdXM6cmluZy1ibHVlLTUwMFwiXG4gICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJcIj7YrNmF2YrYuSDYp9mE2YHYptin2Ko8L29wdGlvbj5cbiAgICAgICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cImVsZWN0cm9uaWNzXCI+2KXZhNmD2KrYsdmI2YbZitin2Ko8L29wdGlvbj5cbiAgICAgICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cInZlaGljbGVzXCI+2LPZitin2LHYp9iqPC9vcHRpb24+XG4gICAgICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJyZWFsX2VzdGF0ZVwiPti52YLYp9ix2KfYqjwvb3B0aW9uPlxuICAgICAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwiYXJ0X2NvbGxlY3RpYmxlc1wiPtmB2YbZiNmGINmI2YXZgtiq2YbZitin2Ko8L29wdGlvbj5cbiAgICAgICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cIm1hY2hpbmVyeVwiPtmF2LnYr9in2Ko8L29wdGlvbj5cbiAgICAgICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cImZ1cm5pdHVyZVwiPtij2KvYp9irPC9vcHRpb24+XG4gICAgICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJqZXdlbHJ5XCI+2YXYrNmI2YfYsdin2Ko8L29wdGlvbj5cbiAgICAgICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cImJvb2tzX21lZGlhXCI+2YPYqtioINmI2YjYs9in2KbYtzwvb3B0aW9uPlxuICAgICAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwiY2xvdGhpbmdcIj7ZhdmE2KfYqNizPC9vcHRpb24+XG4gICAgICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJzcG9ydHNcIj7YsdmK2KfYttipPC9vcHRpb24+XG4gICAgICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJvdGhlclwiPtij2K7YsdmJPC9vcHRpb24+XG4gICAgICAgICAgICAgICAgICA8L3NlbGVjdD5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC0xXCI+XG4gICAgICAgICAgICAgICAgICA8TGFiZWwgaHRtbEZvcj1cInNvcnRcIiBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtXCI+2KrYsdiq2YrYqCDYrdiz2Kg8L0xhYmVsPlxuICAgICAgICAgICAgICAgICAgPHNlbGVjdFxuICAgICAgICAgICAgICAgICAgICBpZD1cInNvcnRcIlxuICAgICAgICAgICAgICAgICAgICB2YWx1ZT17c29ydEJ5fVxuICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldFNvcnRCeShlLnRhcmdldC52YWx1ZSl9XG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBtdC0xIHB4LTMgcHktMiBib3JkZXIgYm9yZGVyLWdyYXktMzAwIHJvdW5kZWQtbWQgZm9jdXM6b3V0bGluZS1ub25lIGZvY3VzOnJpbmctMiBmb2N1czpyaW5nLWJsdWUtNTAwXCJcbiAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cImVuZERhdGVcIj7Yqtin2LHZitiuINin2YTYp9mG2KrZh9in2KE8L29wdGlvbj5cbiAgICAgICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cImN1cnJlbnRCaWRcIj7Yo9i52YTZiSDZhdiy2KfZitiv2Kk8L29wdGlvbj5cbiAgICAgICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cImJpZHNDb3VudFwiPti52K/YryDYp9mE2YXYstin2YrYr9in2Ko8L29wdGlvbj5cbiAgICAgICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cInRpdGxlXCI+2KfZhNin2LPZhTwvb3B0aW9uPlxuICAgICAgICAgICAgICAgICAgPC9zZWxlY3Q+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9DYXJkQ29udGVudD5cbiAgICAgICAgPC9DYXJkPlxuXG4gICAgICAgIHsvKiBTdGF0cyAqL31cbiAgICAgICAge0FycmF5LmlzQXJyYXkoZmlsdGVyZWRBdWN0aW9ucykgJiYgZmlsdGVyZWRBdWN0aW9ucy5sZW5ndGggPiAwICYmIChcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgbWQ6Z3JpZC1jb2xzLTMgZ2FwLTZcIj5cbiAgICAgICAgICAgIDxDYXJkPlxuICAgICAgICAgICAgICA8Q2FyZEhlYWRlcj5cbiAgICAgICAgICAgICAgICA8Q2FyZFRpdGxlIGNsYXNzTmFtZT1cInRleHQtbGdcIj7Ypdis2YXYp9mE2Yog2KfZhNmF2LLYp9iv2KfYqjwvQ2FyZFRpdGxlPlxuICAgICAgICAgICAgICAgIDxDYXJkRGVzY3JpcHRpb24gY2xhc3NOYW1lPVwidGV4dC0yeGwgZm9udC1ib2xkIHRleHQtYmx1ZS02MDBcIj5cbiAgICAgICAgICAgICAgICAgIHtmaWx0ZXJlZEF1Y3Rpb25zLmxlbmd0aH1cbiAgICAgICAgICAgICAgICA8L0NhcmREZXNjcmlwdGlvbj5cbiAgICAgICAgICAgICAgPC9DYXJkSGVhZGVyPlxuICAgICAgICAgICAgPC9DYXJkPlxuICAgICAgICAgICAgPENhcmQ+XG4gICAgICAgICAgICAgIDxDYXJkSGVhZGVyPlxuICAgICAgICAgICAgICAgIDxDYXJkVGl0bGUgY2xhc3NOYW1lPVwidGV4dC1sZ1wiPtin2YTZhdiy2KfZitiv2KfYqiDYp9mE2YbYtNi32Kk8L0NhcmRUaXRsZT5cbiAgICAgICAgICAgICAgICA8Q2FyZERlc2NyaXB0aW9uIGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZCB0ZXh0LWdyZWVuLTYwMFwiPlxuICAgICAgICAgICAgICAgICAge2ZpbHRlcmVkQXVjdGlvbnMucmVkdWNlKChzdW0sIGEpID0+IHN1bSArIChhLmJpZHNDb3VudCB8fCAwKSwgMCl9XG4gICAgICAgICAgICAgICAgPC9DYXJkRGVzY3JpcHRpb24+XG4gICAgICAgICAgICAgIDwvQ2FyZEhlYWRlcj5cbiAgICAgICAgICAgIDwvQ2FyZD5cbiAgICAgICAgICAgIDxDYXJkPlxuICAgICAgICAgICAgICA8Q2FyZEhlYWRlcj5cbiAgICAgICAgICAgICAgICA8Q2FyZFRpdGxlIGNsYXNzTmFtZT1cInRleHQtbGdcIj7Yo9i52YTZiSDZhdiy2KfZitiv2Kk8L0NhcmRUaXRsZT5cbiAgICAgICAgICAgICAgICA8Q2FyZERlc2NyaXB0aW9uIGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZCB0ZXh0LXB1cnBsZS02MDBcIj5cbiAgICAgICAgICAgICAgICAgIHtmaWx0ZXJlZEF1Y3Rpb25zLmxlbmd0aCA+IDBcbiAgICAgICAgICAgICAgICAgICAgPyBjdXJyZW5jeVNlcnZpY2UuZm9ybWF0QW1vdW50KFxuICAgICAgICAgICAgICAgICAgICAgICAgTWF0aC5tYXgoLi4uZmlsdGVyZWRBdWN0aW9ucy5tYXAoYSA9PiBhLmN1cnJlbnRCaWQgfHwgMCkpLFxuICAgICAgICAgICAgICAgICAgICAgICAgdXNlckN1cnJlbmN5XG4gICAgICAgICAgICAgICAgICAgICAgKVxuICAgICAgICAgICAgICAgICAgICA6IGN1cnJlbmN5U2VydmljZS5mb3JtYXRBbW91bnQoMCwgdXNlckN1cnJlbmN5KVxuICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIDwvQ2FyZERlc2NyaXB0aW9uPlxuICAgICAgICAgICAgICA8L0NhcmRIZWFkZXI+XG4gICAgICAgICAgICA8L0NhcmQ+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICl9XG5cbiAgICAgICAgey8qIEF1Y3Rpb25zIEdyaWQgKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBtZDpncmlkLWNvbHMtMiBsZzpncmlkLWNvbHMtMyBnYXAtNlwiPlxuICAgICAgICAgIHtBcnJheS5pc0FycmF5KGZpbHRlcmVkQXVjdGlvbnMpICYmIGZpbHRlcmVkQXVjdGlvbnMubWFwKChhdWN0aW9uKSA9PiAoXG4gICAgICAgICAgICA8Q2FyZCBrZXk9e2F1Y3Rpb24uaWR9IGNsYXNzTmFtZT1cImhvdmVyOnNoYWRvdy1sZyB0cmFuc2l0aW9uLXNoYWRvdyBjdXJzb3ItcG9pbnRlclwiIG9uQ2xpY2s9eygpID0+IGhhbmRsZVZpZXdBdWN0aW9uKGF1Y3Rpb24uaWQpfT5cbiAgICAgICAgICAgICAgPENhcmRIZWFkZXI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhc3BlY3QtdmlkZW8gYmctZ3JhZGllbnQtdG8tYnIgZnJvbS1ncmF5LTEwMCB0by1ncmF5LTIwMCByb3VuZGVkLWxnIG1iLTQgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgcmVsYXRpdmUgb3ZlcmZsb3ctaGlkZGVuXCI+XG4gICAgICAgICAgICAgICAgICB7YXVjdGlvbi5pbWFnZXMgJiYgYXVjdGlvbi5pbWFnZXMubGVuZ3RoID4gMCA/IChcbiAgICAgICAgICAgICAgICAgICAgPGltZ1xuICAgICAgICAgICAgICAgICAgICAgIHNyYz17YXVjdGlvbi5pbWFnZXNbMF0udXJsfVxuICAgICAgICAgICAgICAgICAgICAgIGFsdD17YXVjdGlvbi50aXRsZX1cbiAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgaC1mdWxsIG9iamVjdC1jb3ZlclwiXG4gICAgICAgICAgICAgICAgICAgICAgb25FcnJvcj17KGUpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGUuY3VycmVudFRhcmdldC5zdHlsZS5kaXNwbGF5ID0gJ25vbmUnXG4gICAgICAgICAgICAgICAgICAgICAgICBlLmN1cnJlbnRUYXJnZXQubmV4dEVsZW1lbnRTaWJsaW5nLnN0eWxlLmRpc3BsYXkgPSAnZmxleCdcbiAgICAgICAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgKSA6IG51bGx9XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIHctZnVsbCBoLWZ1bGwgdGV4dC1ncmF5LTUwMFwiIHN0eWxlPXt7ZGlzcGxheTogYXVjdGlvbi5pbWFnZXMgJiYgYXVjdGlvbi5pbWFnZXMubGVuZ3RoID4gMCA/ICdub25lJyA6ICdmbGV4J319PlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPEdhdmVsIGNsYXNzTmFtZT1cImgtOCB3LTggbXgtYXV0byBtYi0yIHRleHQtZ3JheS00MDBcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtc21cIj7YtdmI2LHYqSDYp9mE2YXYstin2K88L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICB7YXVjdGlvbi5zdGF0dXMgPT09ICdhY3RpdmUnICYmIChcbiAgICAgICAgICAgICAgICAgICAgPEJhZGdlIGNsYXNzTmFtZT1cImFic29sdXRlIHRvcC0yIHJpZ2h0LTIgYmctZ3JlZW4tNTAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAg2YbYtNi3XG4gICAgICAgICAgICAgICAgICAgIDwvQmFkZ2U+XG4gICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDxDYXJkVGl0bGUgY2xhc3NOYW1lPVwidGV4dC1sZyBsaW5lLWNsYW1wLTJcIj57YXVjdGlvbi50aXRsZX08L0NhcmRUaXRsZT5cbiAgICAgICAgICAgICAgICA8Q2FyZERlc2NyaXB0aW9uIGNsYXNzTmFtZT1cImxpbmUtY2xhbXAtMlwiPlxuICAgICAgICAgICAgICAgICAge2F1Y3Rpb24uZGVzY3JpcHRpb259XG4gICAgICAgICAgICAgICAgPC9DYXJkRGVzY3JpcHRpb24+XG4gICAgICAgICAgICAgIDwvQ2FyZEhlYWRlcj5cbiAgICAgICAgICAgICAgPENhcmRDb250ZW50IGNsYXNzTmFtZT1cInNwYWNlLXktNFwiPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuXCI+XG4gICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtbXV0ZWQtZm9yZWdyb3VuZFwiPtij2LnZhNmJINmF2LLYp9mK2K/YqTwvcD5cbiAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC14bCBmb250LWJvbGQgdGV4dC1ncmVlbi02MDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICB7Y3VycmVuY3lTZXJ2aWNlLmZvcm1hdEFtb3VudChhdWN0aW9uLmN1cnJlbnRCaWQgfHwgMCwgYXVjdGlvbi5jdXJyZW5jeSB8fCB1c2VyQ3VycmVuY3kpfVxuICAgICAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LW11dGVkLWZvcmVncm91bmRcIj7Yp9mE2YXYstin2YrYr9in2Ko8L3A+XG4gICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1zZW1pYm9sZFwiPnthdWN0aW9uLmJpZHNDb3VudCB8fCAwfTwvcD5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMlwiPlxuICAgICAgICAgICAgICAgICAgPENsb2NrIGNsYXNzTmFtZT1cImgtNCB3LTQgdGV4dC1tdXRlZC1mb3JlZ3JvdW5kXCIgLz5cbiAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1tdXRlZC1mb3JlZ3JvdW5kXCI+XG4gICAgICAgICAgICAgICAgICAgINmK2YbYqtmH2Yog2K7ZhNin2YQ6IHtnZXRUaW1lUmVtYWluaW5nKGF1Y3Rpb24uZW5kRGF0ZSl9XG4gICAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1tdXRlZC1mb3JlZ3JvdW5kXCI+XG4gICAgICAgICAgICAgICAgICDYp9mE2KjYp9im2Lk6IHthdWN0aW9uLnNlbGxlcn1cbiAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBnYXAtMlwiPlxuICAgICAgICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoZSkgPT4ge1xuICAgICAgICAgICAgICAgICAgICAgIGUucHJldmVudERlZmF1bHQoKVxuICAgICAgICAgICAgICAgICAgICAgIGUuc3RvcFByb3BhZ2F0aW9uKClcbiAgICAgICAgICAgICAgICAgICAgICBoYW5kbGVCaWQoYXVjdGlvbi5pZClcbiAgICAgICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiZmxleC0xXCJcbiAgICAgICAgICAgICAgICAgICAgZGlzYWJsZWQ9e2xvYWRpbmcgfHwgYmlkTG9hZGluZ31cbiAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgPFRyZW5kaW5nVXAgY2xhc3NOYW1lPVwiaC00IHctNCBtbC0yXCIgLz5cbiAgICAgICAgICAgICAgICAgICAge2JpZExvYWRpbmcgPyAn2KzYp9ix2Yog2KfZhNmF2LLYp9mK2K/YqS4uLicgOiAn2YXYstin2YrYr9ipJ31cbiAgICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICAgICAgICB2YXJpYW50PXtzYXZlZEF1Y3Rpb25zLmhhcyhhdWN0aW9uLmlkKSA/IFwiZGVmYXVsdFwiIDogXCJvdXRsaW5lXCJ9XG4gICAgICAgICAgICAgICAgICAgIHNpemU9XCJpY29uXCJcbiAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KGUpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgICBlLnByZXZlbnREZWZhdWx0KClcbiAgICAgICAgICAgICAgICAgICAgICBlLnN0b3BQcm9wYWdhdGlvbigpXG4gICAgICAgICAgICAgICAgICAgICAgaGFuZGxlU2F2ZUF1Y3Rpb24oYXVjdGlvbi5pZClcbiAgICAgICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgICAgICAgZGlzYWJsZWQ9e2xvYWRpbmd9XG4gICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgIDxIZWFydCBjbGFzc05hbWU9e2BoLTQgdy00ICR7c2F2ZWRBdWN0aW9ucy5oYXMoYXVjdGlvbi5pZCkgPyAnZmlsbC1jdXJyZW50JyA6ICcnfWB9IC8+XG4gICAgICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgICAgICAgICAgdmFyaWFudD1cIm91dGxpbmVcIlxuICAgICAgICAgICAgICAgICAgICBzaXplPVwiaWNvblwiXG4gICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eyhlKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgICAgZS5wcmV2ZW50RGVmYXVsdCgpXG4gICAgICAgICAgICAgICAgICAgICAgZS5zdG9wUHJvcGFnYXRpb24oKVxuICAgICAgICAgICAgICAgICAgICAgIGhhbmRsZVZpZXdBdWN0aW9uKGF1Y3Rpb24uaWQpXG4gICAgICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgICAgICAgIGRpc2FibGVkPXtsb2FkaW5nfVxuICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICA8RXllIGNsYXNzTmFtZT1cImgtNCB3LTRcIiAvPlxuICAgICAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvQ2FyZENvbnRlbnQ+XG4gICAgICAgICAgICA8L0NhcmQ+XG4gICAgICAgICAgKSl9XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIHsvKiBObyBkYXRhIHN0YXRlICovfVxuICAgICAgICB7IWxvYWRpbmcgJiYgKCFBcnJheS5pc0FycmF5KGZpbHRlcmVkQXVjdGlvbnMpIHx8IGZpbHRlcmVkQXVjdGlvbnMubGVuZ3RoID09PSAwKSAmJiAoXG4gICAgICAgICAgPENhcmQ+XG4gICAgICAgICAgICA8Q2FyZENvbnRlbnQgY2xhc3NOYW1lPVwicHktMTYgdGV4dC1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGZsZXgtY29sIGl0ZW1zLWNlbnRlciBnYXAtNFwiPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy0xNiBoLTE2IHJvdW5kZWQtZnVsbCBiZy1ncmF5LTEwMCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgPFRyZW5kaW5nVXAgY2xhc3NOYW1lPVwiaC04IHctOCB0ZXh0LWdyYXktNDAwXCIgLz5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1zZW1pYm9sZCBtYi0yXCI+XG4gICAgICAgICAgICAgICAgICAgIHtzZWFyY2hUZXJtID8gJ9mE2Kcg2KrZiNis2K8g2YXYstin2K/Yp9iqINiq2LfYp9io2YIg2KfZhNio2K3YqycgOiAn2YTYpyDYqtmI2KzYryDZhdiy2KfYr9in2Kog2YXYqtin2K3YqSDYrdin2YTZitin2YsnfVxuICAgICAgICAgICAgICAgICAgPC9oMz5cbiAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtbXV0ZWQtZm9yZWdyb3VuZFwiPlxuICAgICAgICAgICAgICAgICAgICB7c2VhcmNoVGVybSBcbiAgICAgICAgICAgICAgICAgICAgICA/ICfYrNix2Kgg2KfZhNio2K3YqyDYqNmD2YTZhdin2Kog2YXYrtiq2YTZgdipINij2Ygg2KfZhdiz2K0g2YXYsdio2Lkg2KfZhNio2K3YqyDZhNi52LHYtiDYrNmF2YrYuSDYp9mE2YXYstin2K/Yp9iqJ1xuICAgICAgICAgICAgICAgICAgICAgIDogJ9iz2YrYqtmFINi52LHYtiDYp9mE2YXYstin2K/Yp9iqINin2YTZhdiq2KfYrdipINmH2YbYpyDYudmG2K8g2KXYttin2YHYqtmH2Kcg2YXZhiDZgtio2YQg2KfZhNi02LHZg9in2KonXG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L0NhcmRDb250ZW50PlxuICAgICAgICAgIDwvQ2FyZD5cbiAgICAgICAgKX1cblxuICAgICAgICB7LyogTG9hZGluZyBzdGF0ZSAqL31cbiAgICAgICAge2xvYWRpbmcgJiYgKFxuICAgICAgICAgIDxDYXJkPlxuICAgICAgICAgICAgPENhcmRDb250ZW50IGNsYXNzTmFtZT1cInB5LTE2IHRleHQtY2VudGVyXCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBmbGV4LWNvbCBpdGVtcy1jZW50ZXIgZ2FwLTRcIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFuaW1hdGUtc3BpbiByb3VuZGVkLWZ1bGwgaC04IHctOCBib3JkZXItYi0yIGJvcmRlci1ibHVlLTYwMFwiPjwvZGl2PlxuICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtbXV0ZWQtZm9yZWdyb3VuZFwiPtis2KfYsdmKINiq2K3ZhdmK2YQg2KfZhNmF2LLYp9iv2KfYqi4uLjwvcD5cbiAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtZ3JheS00MDBcIj5Mb2FkaW5nOiB7bG9hZGluZy50b1N0cmluZygpfSwgQXVjdGlvbnM6IHthdWN0aW9ucy5sZW5ndGh9LCBGaWx0ZXJlZDoge2ZpbHRlcmVkQXVjdGlvbnMubGVuZ3RofTwvcD5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L0NhcmRDb250ZW50PlxuICAgICAgICAgIDwvQ2FyZD5cbiAgICAgICAgKX1cblxuICAgICAgICB7LyogQmlkZGluZyBNb2RhbCAqL31cbiAgICAgICAgPERpYWxvZyBvcGVuPXtzaG93QmlkTW9kYWx9IG9uT3BlbkNoYW5nZT17c2V0U2hvd0JpZE1vZGFsfT5cbiAgICAgICAgICA8RGlhbG9nQ29udGVudCBjbGFzc05hbWU9XCJzbTptYXgtdy1tZFwiPlxuICAgICAgICAgICAgPERpYWxvZ0hlYWRlcj5cbiAgICAgICAgICAgICAgPERpYWxvZ1RpdGxlIGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0yXCI+XG4gICAgICAgICAgICAgICAgPEdhdmVsIGNsYXNzTmFtZT1cImgtNSB3LTVcIiAvPlxuICAgICAgICAgICAgICAgINiq2YLYr9mK2YUg2YXYstin2YrYr9ipXG4gICAgICAgICAgICAgIDwvRGlhbG9nVGl0bGU+XG4gICAgICAgICAgICA8L0RpYWxvZ0hlYWRlcj5cblxuICAgICAgICAgICAge3NlbGVjdGVkQXVjdGlvbiAmJiAoXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS00XCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1ncmF5LTUwIHAtNCByb3VuZGVkLWxnXCI+XG4gICAgICAgICAgICAgICAgICA8aDQgY2xhc3NOYW1lPVwiZm9udC1zZW1pYm9sZCBtYi0yXCI+e3NlbGVjdGVkQXVjdGlvbi50aXRsZX08L2g0PlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtNCB0ZXh0LXNtIHRleHQtZ3JheS02MDBcIj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMVwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxEb2xsYXJTaWduIGNsYXNzTmFtZT1cImgtNCB3LTRcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgIDxzcGFuPtin2YTZhdiy2KfZitiv2Kkg2KfZhNit2KfZhNmK2Kk6IHsoc2VsZWN0ZWRBdWN0aW9uLmN1cnJlbnRCaWQgfHwgMCkudG9Mb2NhbGVTdHJpbmcoKX0g2LEu2LM8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0xXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPFRyZW5kaW5nVXAgY2xhc3NOYW1lPVwiaC00IHctNFwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgPHNwYW4+e3NlbGVjdGVkQXVjdGlvbi5iaWRzQ291bnQgfHwgMH0g2YXYstin2YrYr9ipPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTJcIj5cbiAgICAgICAgICAgICAgICAgIDxMYWJlbCBodG1sRm9yPVwiYmlkQW1vdW50XCI+2YXZgtiv2KfYsSDYp9mE2YXYstin2YrYr9ipICjYsS7Ysyk8L0xhYmVsPlxuICAgICAgICAgICAgICAgICAgPElucHV0XG4gICAgICAgICAgICAgICAgICAgIGlkPVwiYmlkQW1vdW50XCJcbiAgICAgICAgICAgICAgICAgICAgdHlwZT1cIm51bWJlclwiXG4gICAgICAgICAgICAgICAgICAgIHZhbHVlPXtiaWRBbW91bnR9XG4gICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0QmlkQW1vdW50KGUudGFyZ2V0LnZhbHVlKX1cbiAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCLYo9iv2K7ZhCDZhdmC2K/Yp9ixINin2YTZhdiy2KfZitiv2KlcIlxuICAgICAgICAgICAgICAgICAgICBtaW49eyhzZWxlY3RlZEF1Y3Rpb24uY3VycmVudEJpZCB8fCAwKSArIDF9XG4gICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LWdyYXktNTAwXCI+XG4gICAgICAgICAgICAgICAgICAgINin2YTYrdivINin2YTYo9iv2YbZiTogeygoc2VsZWN0ZWRBdWN0aW9uLmN1cnJlbnRCaWQgfHwgMCkgKyAxKS50b0xvY2FsZVN0cmluZygpfSDYsS7Ys1xuICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICl9XG5cbiAgICAgICAgICAgIDxEaWFsb2dGb290ZXI+XG4gICAgICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgICAgICB2YXJpYW50PVwib3V0bGluZVwiXG4gICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4ge1xuICAgICAgICAgICAgICAgICAgc2V0U2hvd0JpZE1vZGFsKGZhbHNlKVxuICAgICAgICAgICAgICAgICAgc2V0QmlkQW1vdW50KCcnKVxuICAgICAgICAgICAgICAgICAgc2V0U2VsZWN0ZWRBdWN0aW9uKG51bGwpXG4gICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgICBkaXNhYmxlZD17YmlkTG9hZGluZ31cbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgINil2YTYutin2KFcbiAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgICAgICBvbkNsaWNrPXtzdWJtaXRCaWR9XG4gICAgICAgICAgICAgICAgZGlzYWJsZWQ9e2JpZExvYWRpbmcgfHwgIWJpZEFtb3VudH1cbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJiZy1ncmVlbi02MDAgaG92ZXI6YmctZ3JlZW4tNzAwXCJcbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIHtiaWRMb2FkaW5nID8gJ9is2KfYsdmKINin2YTYqtmC2K/ZitmFLi4uJyA6ICfYqtmC2K/ZitmFINin2YTZhdiy2KfZitiv2KknfVxuICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgIDwvRGlhbG9nRm9vdGVyPlxuICAgICAgICAgIDwvRGlhbG9nQ29udGVudD5cbiAgICAgICAgPC9EaWFsb2c+XG4gICAgICA8L2Rpdj5cbiAgKVxufVxuIl0sIm5hbWVzIjpbIlJlYWN0IiwidXNlU3RhdGUiLCJ1c2VFZmZlY3QiLCJDYXJkIiwiQ2FyZENvbnRlbnQiLCJDYXJkSGVhZGVyIiwiQ2FyZFRpdGxlIiwiQ2FyZERlc2NyaXB0aW9uIiwiQnV0dG9uIiwiQmFkZ2UiLCJJbnB1dCIsIkRpYWxvZyIsIkRpYWxvZ0NvbnRlbnQiLCJEaWFsb2dIZWFkZXIiLCJEaWFsb2dUaXRsZSIsIkRpYWxvZ0Zvb3RlciIsIkxhYmVsIiwiU2VhcmNoIiwiRXllIiwiSGVhcnQiLCJDbG9jayIsIlRyZW5kaW5nVXAiLCJHYXZlbCIsIkRvbGxhclNpZ24iLCJSZWZyZXNoQ3ciLCJQYXVzZSIsIlBsYXkiLCJhdWN0aW9uQVBJIiwiZmF2b3JpdGVzQVBJIiwidXNlUm91dGVyIiwidXNlVG9hc3QiLCJjdXJyZW5jeVNlcnZpY2UiLCJVc2VyQXVjdGlvbnNQYWdlIiwiYXVjdGlvbnMiLCJzZXRBdWN0aW9ucyIsInNlYXJjaFRlcm0iLCJzZXRTZWFyY2hUZXJtIiwiZmlsdGVyZWRBdWN0aW9ucyIsInNldEZpbHRlcmVkQXVjdGlvbnMiLCJsb2FkaW5nIiwic2V0TG9hZGluZyIsInNhdmVkQXVjdGlvbnMiLCJzZXRTYXZlZEF1Y3Rpb25zIiwiU2V0IiwidXNlciIsInNldFVzZXIiLCJzaG93QmlkTW9kYWwiLCJzZXRTaG93QmlkTW9kYWwiLCJzZWxlY3RlZEF1Y3Rpb24iLCJzZXRTZWxlY3RlZEF1Y3Rpb24iLCJiaWRBbW91bnQiLCJzZXRCaWRBbW91bnQiLCJiaWRMb2FkaW5nIiwic2V0QmlkTG9hZGluZyIsInNvcnRCeSIsInNldFNvcnRCeSIsImZpbHRlckNhdGVnb3J5Iiwic2V0RmlsdGVyQ2F0ZWdvcnkiLCJhdXRvUmVmcmVzaCIsInNldEF1dG9SZWZyZXNoIiwibGFzdFJlZnJlc2giLCJzZXRMYXN0UmVmcmVzaCIsIkRhdGUiLCJ1c2VyQ3VycmVuY3kiLCJzZXRVc2VyQ3VycmVuY3kiLCJleGNoYW5nZVJhdGVzIiwic2V0RXhjaGFuZ2VSYXRlcyIsImF1Y3Rpb25WZXJzaW9ucyIsInNldEF1Y3Rpb25WZXJzaW9ucyIsInJvdXRlciIsInRvYXN0IiwidXNlckRhdGEiLCJsb2NhbFN0b3JhZ2UiLCJnZXRJdGVtIiwicGFyc2VkVXNlciIsIkpTT04iLCJwYXJzZSIsInJvbGUiLCJ0aXRsZSIsImRlc2NyaXB0aW9uIiwidmFyaWFudCIsInB1c2giLCJzYXZlZEN1cnJlbmN5IiwiZ2V0VXNlckN1cnJlbmN5IiwibG9hZEF1Y3Rpb25zIiwibG9hZFNhdmVkQXVjdGlvbnMiLCJsb2FkRXhjaGFuZ2VSYXRlcyIsInJhdGVzIiwiZ2V0RXhjaGFuZ2VSYXRlcyIsImVycm9yIiwiY29uc29sZSIsImludGVydmFsIiwic2V0SW50ZXJ2YWwiLCJsb2ciLCJjbGVhckludGVydmFsIiwic2hvd0xvYWRpbmciLCJyZXNwb25zZSIsImdldEFsbCIsImRhdGEiLCJhdWN0aW9uRGF0YSIsInN1Y2Nlc3MiLCJBcnJheSIsImlzQXJyYXkiLCJ0cmFuc2Zvcm1lZERhdGEiLCJQcm9taXNlIiwiYWxsIiwibWFwIiwiYXVjdGlvbiIsImJhc2VBdWN0aW9uIiwiaWQiLCJfaWQiLCJjdXJyZW50QmlkIiwiY3VycmVudFByaWNlIiwic3RhcnRpbmdQcmljZSIsImVuZERhdGUiLCJiaWRzQ291bnQiLCJiaWRzIiwibGVuZ3RoIiwic2VsbGVyIiwicHJvZmlsZSIsImZ1bGxOYW1lIiwiY29tcGFueU5hbWUiLCJuYW1lIiwiY2F0ZWdvcnkiLCJzdGF0dXMiLCJ2aWV3cyIsImxvY2F0aW9uIiwiY3VycmVuY3kiLCJ2ZXJzaW9uIiwicHJldiIsImNvbnZlcnRlZEJpZCIsImNvbnZlcnRDdXJyZW5jeSIsIk1hdGgiLCJyb3VuZCIsIm9yaWdpbmFsQ3VycmVuY3kiLCJjb252ZXJzaW9uRXJyb3IiLCJ3YXJuIiwiZ2V0RmF2b3JpdGVzIiwidHlwZSIsImZhdm9yaXRlc0RhdGEiLCJmYXZvcml0ZXMiLCJzYXZlZElkcyIsImZhdiIsIml0ZW1JZCIsImZpbHRlcmVkIiwiZmlsdGVyIiwibWF0Y2hlc1NlYXJjaCIsInRvTG93ZXJDYXNlIiwiaW5jbHVkZXMiLCJtYXRjaGVzQ2F0ZWdvcnkiLCJzb3J0IiwiYSIsImIiLCJnZXRUaW1lIiwibG9jYWxlQ29tcGFyZSIsImdldFRpbWVSZW1haW5pbmciLCJub3ciLCJlbmQiLCJkaWZmIiwiaG91cnMiLCJmbG9vciIsImRheXMiLCJjYW5Vc2VyQmlkIiwiaGFuZGxlQmlkIiwiYXVjdGlvbklkIiwiZnJlc2hBdWN0aW9uIiwiZ2V0QnlJZCIsInRyYW5zZm9ybWVkQXVjdGlvbiIsIlN0cmluZyIsInRvTG9jYWxlU3RyaW5nIiwiY3VycmVudEF1Y3Rpb24iLCJmaW5kIiwic3VibWl0QmlkIiwiYmlkQW1vdW50TnVtIiwicGFyc2VGbG9hdCIsImlzTmFOIiwiYXVjdGlvblZlcnNpb24iLCJiaWREYXRhIiwiZmV0Y2giLCJtZXRob2QiLCJoZWFkZXJzIiwiYm9keSIsInN0cmluZ2lmeSIsInJlc3VsdCIsImpzb24iLCJvayIsIkVycm9yIiwibWVzc2FnZSIsImZvcm1hdHRlZEFtb3VudCIsImZvcm1hdEFtb3VudCIsImVycm9yTWVzc2FnZSIsImhhbmRsZVNhdmVBdWN0aW9uIiwiaXNDdXJyZW50bHlTYXZlZCIsImhhcyIsIm5ld1NldCIsImRlbGV0ZSIsImFkZCIsInJlbW92ZUZhdm9yaXRlIiwiYWRkRmF2b3JpdGUiLCJpdGVtVHlwZSIsIm5vdGlmaWNhdGlvbnMiLCJiaWRVcGRhdGVzIiwic3RhdHVzQ2hhbmdlcyIsImVuZGluZ1Nvb24iLCJoYW5kbGVWaWV3QXVjdGlvbiIsImRpdiIsImNsYXNzTmFtZSIsImhlYWRlciIsImgxIiwicCIsInNwYW4iLCJodG1sRm9yIiwic2VsZWN0IiwidmFsdWUiLCJvbkNoYW5nZSIsImUiLCJuZXdDdXJyZW5jeSIsInRhcmdldCIsImdldEN1cnJlbmN5U3ltYm9sIiwiZ2V0U3VwcG9ydGVkQ3VycmVuY2llcyIsIm9wdGlvbiIsImNvZGUiLCJzeW1ib2wiLCJvbkNsaWNrIiwiZGlzYWJsZWQiLCJzaXplIiwicGxhY2Vob2xkZXIiLCJyZWR1Y2UiLCJzdW0iLCJtYXgiLCJpbWFnZXMiLCJpbWciLCJzcmMiLCJ1cmwiLCJhbHQiLCJvbkVycm9yIiwiY3VycmVudFRhcmdldCIsInN0eWxlIiwiZGlzcGxheSIsIm5leHRFbGVtZW50U2libGluZyIsInByZXZlbnREZWZhdWx0Iiwic3RvcFByb3BhZ2F0aW9uIiwiaDMiLCJ0b1N0cmluZyIsIm9wZW4iLCJvbk9wZW5DaGFuZ2UiLCJoNCIsIm1pbiJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/user/auctions/page.tsx\n"));

/***/ })

});