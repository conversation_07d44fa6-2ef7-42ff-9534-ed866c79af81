"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/user/analytics/page",{

/***/ "(app-pages-browser)/./components/CurrencySelector.tsx":
/*!*****************************************!*\
  !*** ./components/CurrencySelector.tsx ***!
  \*****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CurrencySelector: function() { return /* binding */ CurrencySelector; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./components/ui/select.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./components/ui/label.tsx\");\n/* harmony import */ var _contexts_CurrencyContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/contexts/CurrencyContext */ \"(app-pages-browser)/./contexts/CurrencyContext.tsx\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./hooks/use-toast.ts\");\n/* __next_internal_client_entry_do_not_use__ CurrencySelector auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nconst currencies = [\n    {\n        code: \"SAR\",\n        symbol: \"ر.س\",\n        name: \"SAR\"\n    },\n    {\n        code: \"EUR\",\n        symbol: \"€\",\n        name: \"EUR\"\n    },\n    {\n        code: \"GBP\",\n        symbol: \"\\xa3\",\n        name: \"GBP\"\n    },\n    {\n        code: \"AED\",\n        symbol: \"د.إ\",\n        name: \"AED\"\n    },\n    {\n        code: \"KWD\",\n        symbol: \"د.ك\",\n        name: \"KWD\"\n    },\n    {\n        code: \"QAR\",\n        symbol: \"ر.ق\",\n        name: \"QAR\"\n    },\n    {\n        code: \"BHD\",\n        symbol: \"د.ب\",\n        name: \"BHD\"\n    },\n    {\n        code: \"OMR\",\n        symbol: \"ر.ع\",\n        name: \"OMR\"\n    }\n];\n// All currencies including USD for reference\nconst allCurrencies = [\n    {\n        code: \"USD\",\n        symbol: \"$\",\n        name: \"USD\"\n    },\n    ...currencies\n];\nfunction CurrencySelector(param) {\n    let { showLabel = true, className = \"\" } = param;\n    _s();\n    const { userCurrency, setUserCurrency, isLoading } = (0,_contexts_CurrencyContext__WEBPACK_IMPORTED_MODULE_4__.useCurrency)();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_5__.useToast)();\n    const handleCurrencyChange = async (newCurrency)=>{\n        try {\n            await setUserCurrency(newCurrency);\n            const selectedCurrency = allCurrencies.find((c)=>c.code === newCurrency);\n            toast({\n                title: \"\\uD83D\\uDCB1 تم تغيير العملة\",\n                description: \"تم التحويل إلى \".concat(selectedCurrency === null || selectedCurrency === void 0 ? void 0 : selectedCurrency.symbol, \" (+ USD كعملة مرجعية)\"),\n                duration: 3000\n            });\n        } catch (error) {\n            console.error(\"Failed to change currency:\", error);\n            toast({\n                title: \"❌ خطأ في تغيير العملة\",\n                description: \"حدث خطأ أثناء تغيير العملة\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center gap-2 \".concat(className),\n            children: [\n                showLabel && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_3__.Label, {\n                    children: \"العملة:\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/CurrencySelector.tsx\",\n                    lineNumber: 58,\n                    columnNumber: 23\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-32 h-10 bg-gray-200 animate-pulse rounded\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/CurrencySelector.tsx\",\n                    lineNumber: 59,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/CurrencySelector.tsx\",\n            lineNumber: 57,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-center gap-2 \".concat(className),\n        children: [\n            showLabel && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_3__.Label, {\n                htmlFor: \"currency-selector\",\n                children: \"العملة:\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/CurrencySelector.tsx\",\n                lineNumber: 66,\n                columnNumber: 21\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.Select, {\n                value: userCurrency,\n                onValueChange: handleCurrencyChange,\n                disabled: isLoading,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectTrigger, {\n                        id: \"currency-selector\",\n                        className: \"w-32\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectValue, {}, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/CurrencySelector.tsx\",\n                            lineNumber: 73,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/CurrencySelector.tsx\",\n                        lineNumber: 72,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectContent, {\n                        children: currencies.map((currency)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectItem, {\n                                value: currency.code,\n                                children: [\n                                    currency.symbol,\n                                    \" \",\n                                    currency.name\n                                ]\n                            }, currency.code, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/CurrencySelector.tsx\",\n                                lineNumber: 77,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/CurrencySelector.tsx\",\n                        lineNumber: 75,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/CurrencySelector.tsx\",\n                lineNumber: 67,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/CurrencySelector.tsx\",\n        lineNumber: 65,\n        columnNumber: 5\n    }, this);\n}\n_s(CurrencySelector, \"AFLOWcFHjv8huGXTRXA2/cq33X8=\", false, function() {\n    return [\n        _contexts_CurrencyContext__WEBPACK_IMPORTED_MODULE_4__.useCurrency,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_5__.useToast\n    ];\n});\n_c = CurrencySelector;\nvar _c;\n$RefreshReg$(_c, \"CurrencySelector\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2NvbXBvbmVudHMvQ3VycmVuY3lTZWxlY3Rvci50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7OztBQUV5QjtBQUM2RTtBQUN6RDtBQUNXO0FBQ1o7QUFPNUMsTUFBTVMsYUFBYTtJQUNqQjtRQUFFQyxNQUFNO1FBQU9DLFFBQVE7UUFBT0MsTUFBTTtJQUFNO0lBQzFDO1FBQUVGLE1BQU07UUFBT0MsUUFBUTtRQUFLQyxNQUFNO0lBQU07SUFDeEM7UUFBRUYsTUFBTTtRQUFPQyxRQUFRO1FBQUtDLE1BQU07SUFBTTtJQUN4QztRQUFFRixNQUFNO1FBQU9DLFFBQVE7UUFBT0MsTUFBTTtJQUFNO0lBQzFDO1FBQUVGLE1BQU07UUFBT0MsUUFBUTtRQUFPQyxNQUFNO0lBQU07SUFDMUM7UUFBRUYsTUFBTTtRQUFPQyxRQUFRO1FBQU9DLE1BQU07SUFBTTtJQUMxQztRQUFFRixNQUFNO1FBQU9DLFFBQVE7UUFBT0MsTUFBTTtJQUFNO0lBQzFDO1FBQUVGLE1BQU07UUFBT0MsUUFBUTtRQUFPQyxNQUFNO0lBQU07Q0FDM0M7QUFFRCw2Q0FBNkM7QUFDN0MsTUFBTUMsZ0JBQWdCO0lBQ3BCO1FBQUVILE1BQU07UUFBT0MsUUFBUTtRQUFLQyxNQUFNO0lBQU07T0FDckNIO0NBQ0o7QUFFTSxTQUFTSyxpQkFBaUIsS0FBMkQ7UUFBM0QsRUFBRUMsWUFBWSxJQUFJLEVBQUVDLFlBQVksRUFBRSxFQUF5QixHQUEzRDs7SUFDL0IsTUFBTSxFQUFFQyxZQUFZLEVBQUVDLGVBQWUsRUFBRUMsU0FBUyxFQUFFLEdBQUdaLHNFQUFXQTtJQUNoRSxNQUFNLEVBQUVhLEtBQUssRUFBRSxHQUFHWiwwREFBUUE7SUFFMUIsTUFBTWEsdUJBQXVCLE9BQU9DO1FBQ2xDLElBQUk7WUFDRixNQUFNSixnQkFBZ0JJO1lBRXRCLE1BQU1DLG1CQUFtQlYsY0FBY1csSUFBSSxDQUFDQyxDQUFBQSxJQUFLQSxFQUFFZixJQUFJLEtBQUtZO1lBQzVERixNQUFNO2dCQUNKTSxPQUFPO2dCQUNQQyxhQUFhLGtCQUEyQyxPQUF6QkosNkJBQUFBLHVDQUFBQSxpQkFBa0JaLE1BQU0sRUFBQztnQkFDeERpQixVQUFVO1lBQ1o7UUFDRixFQUFFLE9BQU9DLE9BQU87WUFDZEMsUUFBUUQsS0FBSyxDQUFDLDhCQUE4QkE7WUFDNUNULE1BQU07Z0JBQ0pNLE9BQU87Z0JBQ1BDLGFBQWE7Z0JBQ2JJLFNBQVM7WUFDWDtRQUNGO0lBQ0Y7SUFFQSxJQUFJWixXQUFXO1FBQ2IscUJBQ0UsOERBQUNhO1lBQUloQixXQUFXLDJCQUFxQyxPQUFWQTs7Z0JBQ3hDRCwyQkFBYSw4REFBQ1QsdURBQUtBOzhCQUFDOzs7Ozs7OEJBQ3JCLDhEQUFDMEI7b0JBQUloQixXQUFVOzs7Ozs7Ozs7Ozs7SUFHckI7SUFFQSxxQkFDRSw4REFBQ2dCO1FBQUloQixXQUFXLDJCQUFxQyxPQUFWQTs7WUFDeENELDJCQUFhLDhEQUFDVCx1REFBS0E7Z0JBQUMyQixTQUFROzBCQUFvQjs7Ozs7OzBCQUNqRCw4REFBQ2hDLHlEQUFNQTtnQkFDTGlDLE9BQU9qQjtnQkFDUGtCLGVBQWVkO2dCQUNmZSxVQUFVakI7O2tDQUVWLDhEQUFDZixnRUFBYUE7d0JBQUNpQyxJQUFHO3dCQUFvQnJCLFdBQVU7a0NBQzlDLDRFQUFDWCw4REFBV0E7Ozs7Ozs7Ozs7a0NBRWQsOERBQUNILGdFQUFhQTtrQ0FDWE8sV0FBVzZCLEdBQUcsQ0FBQyxDQUFDQyx5QkFDZiw4REFBQ3BDLDZEQUFVQTtnQ0FBcUIrQixPQUFPSyxTQUFTN0IsSUFBSTs7b0NBQ2pENkIsU0FBUzVCLE1BQU07b0NBQUM7b0NBQUU0QixTQUFTM0IsSUFBSTs7K0JBRGpCMkIsU0FBUzdCLElBQUk7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFRMUM7R0F0RGdCSTs7UUFDdUNQLGtFQUFXQTtRQUM5Q0Msc0RBQVFBOzs7S0FGWk0iLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vY29tcG9uZW50cy9DdXJyZW5jeVNlbGVjdG9yLnRzeD80ZTdmIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50J1xuXG5pbXBvcnQgUmVhY3QgZnJvbSAncmVhY3QnXG5pbXBvcnQgeyBTZWxlY3QsIFNlbGVjdENvbnRlbnQsIFNlbGVjdEl0ZW0sIFNlbGVjdFRyaWdnZXIsIFNlbGVjdFZhbHVlIH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL3NlbGVjdCdcbmltcG9ydCB7IExhYmVsIH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL2xhYmVsJ1xuaW1wb3J0IHsgdXNlQ3VycmVuY3kgfSBmcm9tICdAL2NvbnRleHRzL0N1cnJlbmN5Q29udGV4dCdcbmltcG9ydCB7IHVzZVRvYXN0IH0gZnJvbSAnQC9ob29rcy91c2UtdG9hc3QnXG5cbmludGVyZmFjZSBDdXJyZW5jeVNlbGVjdG9yUHJvcHMge1xuICBzaG93TGFiZWw/OiBib29sZWFuXG4gIGNsYXNzTmFtZT86IHN0cmluZ1xufVxuXG5jb25zdCBjdXJyZW5jaWVzID0gW1xuICB7IGNvZGU6ICdTQVInLCBzeW1ib2w6ICfYsS7YsycsIG5hbWU6ICdTQVInIH0sXG4gIHsgY29kZTogJ0VVUicsIHN5bWJvbDogJ+KCrCcsIG5hbWU6ICdFVVInIH0sXG4gIHsgY29kZTogJ0dCUCcsIHN5bWJvbDogJ8KjJywgbmFtZTogJ0dCUCcgfSxcbiAgeyBjb2RlOiAnQUVEJywgc3ltYm9sOiAn2K8u2KUnLCBuYW1lOiAnQUVEJyB9LFxuICB7IGNvZGU6ICdLV0QnLCBzeW1ib2w6ICfYry7ZgycsIG5hbWU6ICdLV0QnIH0sXG4gIHsgY29kZTogJ1FBUicsIHN5bWJvbDogJ9ixLtmCJywgbmFtZTogJ1FBUicgfSxcbiAgeyBjb2RlOiAnQkhEJywgc3ltYm9sOiAn2K8u2KgnLCBuYW1lOiAnQkhEJyB9LFxuICB7IGNvZGU6ICdPTVInLCBzeW1ib2w6ICfYsS7YuScsIG5hbWU6ICdPTVInIH1cbl1cblxuLy8gQWxsIGN1cnJlbmNpZXMgaW5jbHVkaW5nIFVTRCBmb3IgcmVmZXJlbmNlXG5jb25zdCBhbGxDdXJyZW5jaWVzID0gW1xuICB7IGNvZGU6ICdVU0QnLCBzeW1ib2w6ICckJywgbmFtZTogJ1VTRCcgfSxcbiAgLi4uY3VycmVuY2llc1xuXVxuXG5leHBvcnQgZnVuY3Rpb24gQ3VycmVuY3lTZWxlY3Rvcih7IHNob3dMYWJlbCA9IHRydWUsIGNsYXNzTmFtZSA9ICcnIH06IEN1cnJlbmN5U2VsZWN0b3JQcm9wcykge1xuICBjb25zdCB7IHVzZXJDdXJyZW5jeSwgc2V0VXNlckN1cnJlbmN5LCBpc0xvYWRpbmcgfSA9IHVzZUN1cnJlbmN5KClcbiAgY29uc3QgeyB0b2FzdCB9ID0gdXNlVG9hc3QoKVxuXG4gIGNvbnN0IGhhbmRsZUN1cnJlbmN5Q2hhbmdlID0gYXN5bmMgKG5ld0N1cnJlbmN5OiBzdHJpbmcpID0+IHtcbiAgICB0cnkge1xuICAgICAgYXdhaXQgc2V0VXNlckN1cnJlbmN5KG5ld0N1cnJlbmN5KVxuXG4gICAgICBjb25zdCBzZWxlY3RlZEN1cnJlbmN5ID0gYWxsQ3VycmVuY2llcy5maW5kKGMgPT4gYy5jb2RlID09PSBuZXdDdXJyZW5jeSlcbiAgICAgIHRvYXN0KHtcbiAgICAgICAgdGl0bGU6ICfwn5KxINiq2YUg2KrYutmK2YrYsSDYp9mE2LnZhdmE2KknLFxuICAgICAgICBkZXNjcmlwdGlvbjogYNiq2YUg2KfZhNiq2K3ZiNmK2YQg2KXZhNmJICR7c2VsZWN0ZWRDdXJyZW5jeT8uc3ltYm9sfSAoKyBVU0Qg2YPYudmF2YTYqSDZhdix2KzYudmK2KkpYCxcbiAgICAgICAgZHVyYXRpb246IDMwMDBcbiAgICAgIH0pXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0ZhaWxlZCB0byBjaGFuZ2UgY3VycmVuY3k6JywgZXJyb3IpXG4gICAgICB0b2FzdCh7XG4gICAgICAgIHRpdGxlOiAn4p2MINiu2LfYoyDZgdmKINiq2LrZitmK2LEg2KfZhNi52YXZhNipJyxcbiAgICAgICAgZGVzY3JpcHRpb246ICfYrdiv2Ksg2K7Yt9ijINij2KvZhtin2KEg2KrYutmK2YrYsSDYp9mE2LnZhdmE2KknLFxuICAgICAgICB2YXJpYW50OiAnZGVzdHJ1Y3RpdmUnXG4gICAgICB9KVxuICAgIH1cbiAgfVxuXG4gIGlmIChpc0xvYWRpbmcpIHtcbiAgICByZXR1cm4gKFxuICAgICAgPGRpdiBjbGFzc05hbWU9e2BmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMiAke2NsYXNzTmFtZX1gfT5cbiAgICAgICAge3Nob3dMYWJlbCAmJiA8TGFiZWw+2KfZhNi52YXZhNipOjwvTGFiZWw+fVxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMzIgaC0xMCBiZy1ncmF5LTIwMCBhbmltYXRlLXB1bHNlIHJvdW5kZWRcIj48L2Rpdj5cbiAgICAgIDwvZGl2PlxuICAgIClcbiAgfVxuXG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9e2BmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMiAke2NsYXNzTmFtZX1gfT5cbiAgICAgIHtzaG93TGFiZWwgJiYgPExhYmVsIGh0bWxGb3I9XCJjdXJyZW5jeS1zZWxlY3RvclwiPtin2YTYudmF2YTYqTo8L0xhYmVsPn1cbiAgICAgIDxTZWxlY3RcbiAgICAgICAgdmFsdWU9e3VzZXJDdXJyZW5jeX1cbiAgICAgICAgb25WYWx1ZUNoYW5nZT17aGFuZGxlQ3VycmVuY3lDaGFuZ2V9XG4gICAgICAgIGRpc2FibGVkPXtpc0xvYWRpbmd9XG4gICAgICA+XG4gICAgICAgIDxTZWxlY3RUcmlnZ2VyIGlkPVwiY3VycmVuY3ktc2VsZWN0b3JcIiBjbGFzc05hbWU9XCJ3LTMyXCI+XG4gICAgICAgICAgPFNlbGVjdFZhbHVlIC8+XG4gICAgICAgIDwvU2VsZWN0VHJpZ2dlcj5cbiAgICAgICAgPFNlbGVjdENvbnRlbnQ+XG4gICAgICAgICAge2N1cnJlbmNpZXMubWFwKChjdXJyZW5jeSkgPT4gKFxuICAgICAgICAgICAgPFNlbGVjdEl0ZW0ga2V5PXtjdXJyZW5jeS5jb2RlfSB2YWx1ZT17Y3VycmVuY3kuY29kZX0+XG4gICAgICAgICAgICAgIHtjdXJyZW5jeS5zeW1ib2x9IHtjdXJyZW5jeS5uYW1lfVxuICAgICAgICAgICAgPC9TZWxlY3RJdGVtPlxuICAgICAgICAgICkpfVxuICAgICAgICA8L1NlbGVjdENvbnRlbnQ+XG4gICAgICA8L1NlbGVjdD5cbiAgICA8L2Rpdj5cbiAgKVxufVxuIl0sIm5hbWVzIjpbIlJlYWN0IiwiU2VsZWN0IiwiU2VsZWN0Q29udGVudCIsIlNlbGVjdEl0ZW0iLCJTZWxlY3RUcmlnZ2VyIiwiU2VsZWN0VmFsdWUiLCJMYWJlbCIsInVzZUN1cnJlbmN5IiwidXNlVG9hc3QiLCJjdXJyZW5jaWVzIiwiY29kZSIsInN5bWJvbCIsIm5hbWUiLCJhbGxDdXJyZW5jaWVzIiwiQ3VycmVuY3lTZWxlY3RvciIsInNob3dMYWJlbCIsImNsYXNzTmFtZSIsInVzZXJDdXJyZW5jeSIsInNldFVzZXJDdXJyZW5jeSIsImlzTG9hZGluZyIsInRvYXN0IiwiaGFuZGxlQ3VycmVuY3lDaGFuZ2UiLCJuZXdDdXJyZW5jeSIsInNlbGVjdGVkQ3VycmVuY3kiLCJmaW5kIiwiYyIsInRpdGxlIiwiZGVzY3JpcHRpb24iLCJkdXJhdGlvbiIsImVycm9yIiwiY29uc29sZSIsInZhcmlhbnQiLCJkaXYiLCJodG1sRm9yIiwidmFsdWUiLCJvblZhbHVlQ2hhbmdlIiwiZGlzYWJsZWQiLCJpZCIsIm1hcCIsImN1cnJlbmN5Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/CurrencySelector.tsx\n"));

/***/ })

});