"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/user/analytics/page",{

/***/ "(app-pages-browser)/./app/user/analytics/page.tsx":
/*!*************************************!*\
  !*** ./app/user/analytics/page.tsx ***!
  \*************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ UserAnalyticsPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_progress__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/progress */ \"(app-pages-browser)/./components/ui/progress.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./components/ui/select.tsx\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_Calendar_DollarSign_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,Calendar,DollarSign,Target,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_Calendar_DollarSign_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,Calendar,DollarSign,Target,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_Calendar_DollarSign_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,Calendar,DollarSign,Target,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_Calendar_DollarSign_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,Calendar,DollarSign,Target,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_Calendar_DollarSign_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,Calendar,DollarSign,Target,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_Calendar_DollarSign_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,Calendar,DollarSign,Target,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_Calendar_DollarSign_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,Calendar,DollarSign,Target,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./lib/api.ts\");\n/* harmony import */ var _contexts_CurrencyContext__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/contexts/CurrencyContext */ \"(app-pages-browser)/./contexts/CurrencyContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction UserAnalyticsPage() {\n    _s();\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [activities, setActivities] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [achievements, setAchievements] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [timeRange, setTimeRange] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"30d\");\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const { formatAmount } = (0,_contexts_CurrencyContext__WEBPACK_IMPORTED_MODULE_7__.useCurrency)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadAnalytics();\n    }, [\n        timeRange\n    ]);\n    const loadAnalytics = async ()=>{\n        try {\n            setLoading(true);\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_6__[\"default\"].get(\"/user/analytics?range=\".concat(timeRange));\n            if (response.data.success) {\n                setStats(response.data.data.stats);\n                setActivities(response.data.data.activities || []);\n                setAchievements(response.data.data.achievements || []);\n            }\n        } catch (error) {\n            console.error(\"Error loading analytics:\", error);\n            // Demo data\n            setStats({\n                totalBids: 45,\n                wonAuctions: 8,\n                totalSpent: 125000,\n                averageBidAmount: 2780,\n                successRate: 17.8,\n                activeApplications: 3,\n                rank: 156,\n                totalUsers: 1250\n            });\n            setActivities([\n                {\n                    date: \"2025-01-01\",\n                    bids: 3,\n                    applications: 1,\n                    spent: 15000\n                },\n                {\n                    date: \"2025-01-02\",\n                    bids: 2,\n                    applications: 0,\n                    spent: 8000\n                },\n                {\n                    date: \"2025-01-03\",\n                    bids: 5,\n                    applications: 2,\n                    spent: 22000\n                },\n                {\n                    date: \"2025-01-04\",\n                    bids: 1,\n                    applications: 1,\n                    spent: 5000\n                },\n                {\n                    date: \"2025-01-05\",\n                    bids: 4,\n                    applications: 0,\n                    spent: 18000\n                }\n            ]);\n            setAchievements([\n                {\n                    id: \"1\",\n                    title: \"أول مزايدة\",\n                    description: \"قم بأول مزايدة لك\",\n                    icon: \"\\uD83C\\uDFAF\",\n                    earned: true,\n                    earnedDate: \"2024-12-15\"\n                },\n                {\n                    id: \"2\",\n                    title: \"الفائز\",\n                    description: \"اربح أول مزاد لك\",\n                    icon: \"\\uD83C\\uDFC6\",\n                    earned: true,\n                    earnedDate: \"2024-12-20\"\n                },\n                {\n                    id: \"3\",\n                    title: \"المزايد النشط\",\n                    description: \"شارك في 50 مزاد\",\n                    icon: \"⚡\",\n                    earned: false,\n                    progress: 45,\n                    target: 50\n                },\n                {\n                    id: \"4\",\n                    title: \"الخبير\",\n                    description: \"اربح 10 مزادات\",\n                    icon: \"\\uD83C\\uDF96️\",\n                    earned: false,\n                    progress: 8,\n                    target: 10\n                }\n            ]);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const formatCurrency = (amount)=>{\n        return formatAmount(amount);\n    };\n    const getTimeRangeText = (range)=>{\n        switch(range){\n            case \"7d\":\n                return \"آخر 7 أيام\";\n            case \"30d\":\n                return \"آخر 30 يوم\";\n            case \"90d\":\n                return \"آخر 3 أشهر\";\n            case \"1y\":\n                return \"آخر سنة\";\n            default:\n                return \"آخر 30 يوم\";\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto p-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-pulse space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-8 bg-gray-200 rounded w-1/4\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/analytics/page.tsx\",\n                        lineNumber: 143,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\",\n                        children: [\n                            1,\n                            2,\n                            3,\n                            4\n                        ].map((i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-32 bg-gray-200 rounded\"\n                            }, i, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/analytics/page.tsx\",\n                                lineNumber: 146,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/analytics/page.tsx\",\n                        lineNumber: 144,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/analytics/page.tsx\",\n                lineNumber: 142,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/analytics/page.tsx\",\n            lineNumber: 141,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto p-6 max-w-6xl\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-center mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold\",\n                                children: \"إحصائياتي\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/analytics/page.tsx\",\n                                lineNumber: 158,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mt-2\",\n                                children: \"تتبع أداءك ونشاطك على المنصة\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/analytics/page.tsx\",\n                                lineNumber: 159,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/analytics/page.tsx\",\n                        lineNumber: 157,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CurrencySelector, {}, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/analytics/page.tsx\",\n                                lineNumber: 163,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.Select, {\n                                value: timeRange,\n                                onValueChange: setTimeRange,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectTrigger, {\n                                        className: \"w-40\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectValue, {}, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/analytics/page.tsx\",\n                                            lineNumber: 166,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/analytics/page.tsx\",\n                                        lineNumber: 165,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectContent, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                value: \"7d\",\n                                                children: \"آخر 7 أيام\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/analytics/page.tsx\",\n                                                lineNumber: 169,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                value: \"30d\",\n                                                children: \"آخر 30 يوم\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/analytics/page.tsx\",\n                                                lineNumber: 170,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                value: \"90d\",\n                                                children: \"آخر 3 أشهر\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/analytics/page.tsx\",\n                                                lineNumber: 171,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                value: \"1y\",\n                                                children: \"آخر سنة\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/analytics/page.tsx\",\n                                                lineNumber: 172,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/analytics/page.tsx\",\n                                        lineNumber: 168,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/analytics/page.tsx\",\n                                lineNumber: 164,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/analytics/page.tsx\",\n                        lineNumber: 162,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/analytics/page.tsx\",\n                lineNumber: 156,\n                columnNumber: 7\n            }, this),\n            stats && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            className: \"p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-gray-600\",\n                                                children: \"إجمالي المزايدات\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/analytics/page.tsx\",\n                                                lineNumber: 185,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold\",\n                                                children: stats.totalBids\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/analytics/page.tsx\",\n                                                lineNumber: 186,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/analytics/page.tsx\",\n                                        lineNumber: 184,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_DollarSign_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"h-8 w-8 text-blue-600\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/analytics/page.tsx\",\n                                        lineNumber: 188,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/analytics/page.tsx\",\n                                lineNumber: 183,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/analytics/page.tsx\",\n                            lineNumber: 182,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/analytics/page.tsx\",\n                        lineNumber: 181,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            className: \"p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-gray-600\",\n                                                children: \"المزادات المربوحة\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/analytics/page.tsx\",\n                                                lineNumber: 197,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold\",\n                                                children: stats.wonAuctions\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/analytics/page.tsx\",\n                                                lineNumber: 198,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/analytics/page.tsx\",\n                                        lineNumber: 196,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_DollarSign_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"h-8 w-8 text-green-600\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/analytics/page.tsx\",\n                                        lineNumber: 200,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/analytics/page.tsx\",\n                                lineNumber: 195,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/analytics/page.tsx\",\n                            lineNumber: 194,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/analytics/page.tsx\",\n                        lineNumber: 193,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            className: \"p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-gray-600\",\n                                                children: \"إجمالي الإنفاق\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/analytics/page.tsx\",\n                                                lineNumber: 209,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold\",\n                                                children: formatCurrency(stats.totalSpent)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/analytics/page.tsx\",\n                                                lineNumber: 210,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/analytics/page.tsx\",\n                                        lineNumber: 208,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_DollarSign_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"h-8 w-8 text-purple-600\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/analytics/page.tsx\",\n                                        lineNumber: 212,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/analytics/page.tsx\",\n                                lineNumber: 207,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/analytics/page.tsx\",\n                            lineNumber: 206,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/analytics/page.tsx\",\n                        lineNumber: 205,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            className: \"p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-gray-600\",\n                                                children: \"معدل النجاح\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/analytics/page.tsx\",\n                                                lineNumber: 221,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold\",\n                                                children: [\n                                                    stats.successRate,\n                                                    \"%\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/analytics/page.tsx\",\n                                                lineNumber: 222,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/analytics/page.tsx\",\n                                        lineNumber: 220,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_DollarSign_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"h-8 w-8 text-orange-600\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/analytics/page.tsx\",\n                                        lineNumber: 224,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/analytics/page.tsx\",\n                                lineNumber: 219,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/analytics/page.tsx\",\n                            lineNumber: 218,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/analytics/page.tsx\",\n                        lineNumber: 217,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/analytics/page.tsx\",\n                lineNumber: 180,\n                columnNumber: 9\n            }, this),\n            stats && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                        children: \"نظرة عامة على الأداء\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/analytics/page.tsx\",\n                                        lineNumber: 236,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                        children: getTimeRangeText(timeRange)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/analytics/page.tsx\",\n                                        lineNumber: 237,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/analytics/page.tsx\",\n                                lineNumber: 235,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between mb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-medium\",\n                                                        children: \"معدل النجاح\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/analytics/page.tsx\",\n                                                        lineNumber: 242,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: [\n                                                            stats.successRate,\n                                                            \"%\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/analytics/page.tsx\",\n                                                        lineNumber: 243,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/analytics/page.tsx\",\n                                                lineNumber: 241,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_progress__WEBPACK_IMPORTED_MODULE_4__.Progress, {\n                                                value: stats.successRate,\n                                                className: \"h-2\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/analytics/page.tsx\",\n                                                lineNumber: 245,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/analytics/page.tsx\",\n                                        lineNumber: 240,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between mb-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-medium\",\n                                                    children: \"متوسط المزايدة\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/analytics/page.tsx\",\n                                                    lineNumber: 250,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: formatCurrency(stats.averageBidAmount)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/analytics/page.tsx\",\n                                                    lineNumber: 251,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/analytics/page.tsx\",\n                                            lineNumber: 249,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/analytics/page.tsx\",\n                                        lineNumber: 248,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between mb-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-medium\",\n                                                    children: \"التطبيقات النشطة\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/analytics/page.tsx\",\n                                                    lineNumber: 257,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: stats.activeApplications\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/analytics/page.tsx\",\n                                                    lineNumber: 258,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/analytics/page.tsx\",\n                                            lineNumber: 256,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/analytics/page.tsx\",\n                                        lineNumber: 255,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/analytics/page.tsx\",\n                                lineNumber: 239,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/analytics/page.tsx\",\n                        lineNumber: 234,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                        children: \"ترتيبك\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/analytics/page.tsx\",\n                                        lineNumber: 266,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                        children: \"موقعك بين جميع المستخدمين\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/analytics/page.tsx\",\n                                        lineNumber: 267,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/analytics/page.tsx\",\n                                lineNumber: 265,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-4xl font-bold text-blue-600 mb-2\",\n                                            children: [\n                                                \"#\",\n                                                stats.rank\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/analytics/page.tsx\",\n                                            lineNumber: 271,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600\",\n                                            children: [\n                                                \"من أصل \",\n                                                stats.totalUsers.toLocaleString(),\n                                                \" مستخدم\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/analytics/page.tsx\",\n                                            lineNumber: 272,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                                variant: \"secondary\",\n                                                className: \"text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_DollarSign_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"h-3 w-3 mr-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/analytics/page.tsx\",\n                                                        lineNumber: 275,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"أفضل \",\n                                                    Math.round((1 - stats.rank / stats.totalUsers) * 100),\n                                                    \"%\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/analytics/page.tsx\",\n                                                lineNumber: 274,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/analytics/page.tsx\",\n                                            lineNumber: 273,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/analytics/page.tsx\",\n                                    lineNumber: 270,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/analytics/page.tsx\",\n                                lineNumber: 269,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/analytics/page.tsx\",\n                        lineNumber: 264,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/analytics/page.tsx\",\n                lineNumber: 233,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                className: \"mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                children: \"النشاط اليومي\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/analytics/page.tsx\",\n                                lineNumber: 288,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                children: \"تتبع نشاطك اليومي على المنصة\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/analytics/page.tsx\",\n                                lineNumber: 289,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/analytics/page.tsx\",\n                        lineNumber: 287,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: activities.map((activity, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between p-3 border rounded-lg\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_DollarSign_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    className: \"h-4 w-4 text-gray-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/analytics/page.tsx\",\n                                                    lineNumber: 296,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-medium\",\n                                                    children: new Date(activity.date).toLocaleDateString(\"ar-SA\")\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/analytics/page.tsx\",\n                                                    lineNumber: 297,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/analytics/page.tsx\",\n                                            lineNumber: 295,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-6 text-sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_DollarSign_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                            className: \"h-4 w-4 text-blue-600\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/analytics/page.tsx\",\n                                                            lineNumber: 303,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: [\n                                                                activity.bids,\n                                                                \" مزايدة\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/analytics/page.tsx\",\n                                                            lineNumber: 304,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/analytics/page.tsx\",\n                                                    lineNumber: 302,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_DollarSign_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                            className: \"h-4 w-4 text-green-600\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/analytics/page.tsx\",\n                                                            lineNumber: 307,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: [\n                                                                activity.applications,\n                                                                \" تطبيق\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/analytics/page.tsx\",\n                                                            lineNumber: 308,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/analytics/page.tsx\",\n                                                    lineNumber: 306,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_Calendar_DollarSign_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                            className: \"h-4 w-4 text-purple-600\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/analytics/page.tsx\",\n                                                            lineNumber: 311,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: formatCurrency(activity.spent)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/analytics/page.tsx\",\n                                                            lineNumber: 312,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/analytics/page.tsx\",\n                                                    lineNumber: 310,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/analytics/page.tsx\",\n                                            lineNumber: 301,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/analytics/page.tsx\",\n                                    lineNumber: 294,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/analytics/page.tsx\",\n                            lineNumber: 292,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/analytics/page.tsx\",\n                        lineNumber: 291,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/analytics/page.tsx\",\n                lineNumber: 286,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                children: \"الإنجازات\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/analytics/page.tsx\",\n                                lineNumber: 324,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                children: \"تتبع إنجازاتك وأهدافك\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/analytics/page.tsx\",\n                                lineNumber: 325,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/analytics/page.tsx\",\n                        lineNumber: 323,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                            children: achievements.map((achievement)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-4 border rounded-lg \".concat(achievement.earned ? \"bg-green-50 border-green-200\" : \"bg-gray-50\"),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-start gap-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl\",\n                                                children: achievement.icon\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/analytics/page.tsx\",\n                                                lineNumber: 337,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"font-semibold mb-1\",\n                                                        children: achievement.title\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/analytics/page.tsx\",\n                                                        lineNumber: 339,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600 mb-2\",\n                                                        children: achievement.description\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/analytics/page.tsx\",\n                                                        lineNumber: 340,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    achievement.earned ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                                        variant: \"default\",\n                                                        className: \"text-xs\",\n                                                        children: [\n                                                            \"تم الإنجاز في \",\n                                                            new Date(achievement.earnedDate).toLocaleDateString(\"ar-SA\")\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/analytics/page.tsx\",\n                                                        lineNumber: 343,\n                                                        columnNumber: 23\n                                                    }, this) : achievement.progress !== undefined && achievement.target !== undefined ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between text-xs\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"التقدم\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/analytics/page.tsx\",\n                                                                        lineNumber: 349,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: [\n                                                                            achievement.progress,\n                                                                            \"/\",\n                                                                            achievement.target\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/analytics/page.tsx\",\n                                                                        lineNumber: 350,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/analytics/page.tsx\",\n                                                                lineNumber: 348,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_progress__WEBPACK_IMPORTED_MODULE_4__.Progress, {\n                                                                value: achievement.progress / achievement.target * 100,\n                                                                className: \"h-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/analytics/page.tsx\",\n                                                                lineNumber: 352,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/analytics/page.tsx\",\n                                                        lineNumber: 347,\n                                                        columnNumber: 23\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                                        variant: \"secondary\",\n                                                        className: \"text-xs\",\n                                                        children: \"لم يتم الإنجاز بعد\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/analytics/page.tsx\",\n                                                        lineNumber: 358,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/analytics/page.tsx\",\n                                                lineNumber: 338,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/analytics/page.tsx\",\n                                        lineNumber: 336,\n                                        columnNumber: 17\n                                    }, this)\n                                }, achievement.id, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/analytics/page.tsx\",\n                                    lineNumber: 330,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/analytics/page.tsx\",\n                            lineNumber: 328,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/analytics/page.tsx\",\n                        lineNumber: 327,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/analytics/page.tsx\",\n                lineNumber: 322,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/analytics/page.tsx\",\n        lineNumber: 155,\n        columnNumber: 5\n    }, this);\n}\n_s(UserAnalyticsPage, \"O++omV2hUsNydsVUtuqM214HECQ=\", false, function() {\n    return [\n        _contexts_CurrencyContext__WEBPACK_IMPORTED_MODULE_7__.useCurrency\n    ];\n});\n_c = UserAnalyticsPage;\nvar _c;\n$RefreshReg$(_c, \"UserAnalyticsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/user/analytics/page.tsx\n"));

/***/ })

});