"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/user/dashboard/page",{

/***/ "(app-pages-browser)/./app/user/dashboard/page.tsx":
/*!*************************************!*\
  !*** ./app/user/dashboard/page.tsx ***!
  \*************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ UserDashboard; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/use-toast */ \"(app-pages-browser)/./components/ui/use-toast.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./lib/api.ts\");\n/* harmony import */ var _contexts_CurrencyContext__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/contexts/CurrencyContext */ \"(app-pages-browser)/./contexts/CurrencyContext.tsx\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart3_Bell_Clock_DollarSign_Eye_Gavel_Heart_LineChart_PieChart_Search_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart3,Bell,Clock,DollarSign,Eye,Gavel,Heart,LineChart,PieChart,Search,Target,TrendingUp,Trophy,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart3_Bell_Clock_DollarSign_Eye_Gavel_Heart_LineChart_PieChart_Search_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart3,Bell,Clock,DollarSign,Eye,Gavel,Heart,LineChart,PieChart,Search,Target,TrendingUp,Trophy,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart3_Bell_Clock_DollarSign_Eye_Gavel_Heart_LineChart_PieChart_Search_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart3,Bell,Clock,DollarSign,Eye,Gavel,Heart,LineChart,PieChart,Search,Target,TrendingUp,Trophy,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart3_Bell_Clock_DollarSign_Eye_Gavel_Heart_LineChart_PieChart_Search_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart3,Bell,Clock,DollarSign,Eye,Gavel,Heart,LineChart,PieChart,Search,Target,TrendingUp,Trophy,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart3_Bell_Clock_DollarSign_Eye_Gavel_Heart_LineChart_PieChart_Search_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart3,Bell,Clock,DollarSign,Eye,Gavel,Heart,LineChart,PieChart,Search,Target,TrendingUp,Trophy,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart3_Bell_Clock_DollarSign_Eye_Gavel_Heart_LineChart_PieChart_Search_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart3,Bell,Clock,DollarSign,Eye,Gavel,Heart,LineChart,PieChart,Search,Target,TrendingUp,Trophy,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trophy.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart3_Bell_Clock_DollarSign_Eye_Gavel_Heart_LineChart_PieChart_Search_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart3,Bell,Clock,DollarSign,Eye,Gavel,Heart,LineChart,PieChart,Search,Target,TrendingUp,Trophy,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart3_Bell_Clock_DollarSign_Eye_Gavel_Heart_LineChart_PieChart_Search_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart3,Bell,Clock,DollarSign,Eye,Gavel,Heart,LineChart,PieChart,Search,Target,TrendingUp,Trophy,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart3_Bell_Clock_DollarSign_Eye_Gavel_Heart_LineChart_PieChart_Search_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart3,Bell,Clock,DollarSign,Eye,Gavel,Heart,LineChart,PieChart,Search,Target,TrendingUp,Trophy,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart3_Bell_Clock_DollarSign_Eye_Gavel_Heart_LineChart_PieChart_Search_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart3,Bell,Clock,DollarSign,Eye,Gavel,Heart,LineChart,PieChart,Search,Target,TrendingUp,Trophy,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/line-chart.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart3_Bell_Clock_DollarSign_Eye_Gavel_Heart_LineChart_PieChart_Search_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart3,Bell,Clock,DollarSign,Eye,Gavel,Heart,LineChart,PieChart,Search,Target,TrendingUp,Trophy,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pie-chart.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart3_Bell_Clock_DollarSign_Eye_Gavel_Heart_LineChart_PieChart_Search_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart3,Bell,Clock,DollarSign,Eye,Gavel,Heart,LineChart,PieChart,Search,Target,TrendingUp,Trophy,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bar-chart-3.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart3_Bell_Clock_DollarSign_Eye_Gavel_Heart_LineChart_PieChart_Search_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_34__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart3,Bell,Clock,DollarSign,Eye,Gavel,Heart,LineChart,PieChart,Search,Target,TrendingUp,Trophy,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart3_Bell_Clock_DollarSign_Eye_Gavel_Heart_LineChart_PieChart_Search_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_35__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart3,Bell,Clock,DollarSign,Eye,Gavel,Heart,LineChart,PieChart,Search,Target,TrendingUp,Trophy,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart3_Bell_Clock_DollarSign_Eye_Gavel_Heart_LineChart_PieChart_Search_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_36__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart3,Bell,Clock,DollarSign,Eye,Gavel,Heart,LineChart,PieChart,Search,Target,TrendingUp,Trophy,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart3_Bell_Clock_DollarSign_Eye_Gavel_Heart_LineChart_PieChart_Search_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_37__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart3,Bell,Clock,DollarSign,Eye,Gavel,Heart,LineChart,PieChart,Search,Target,TrendingUp,Trophy,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart3_Bell_Clock_DollarSign_Eye_Gavel_Heart_LineChart_PieChart_Search_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_38__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart3,Bell,Clock,DollarSign,Eye,Gavel,Heart,LineChart,PieChart,Search,Target,TrendingUp,Trophy,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/gavel.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,Bar,BarChart,CartesianGrid,Cell,Legend,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/component/ResponsiveContainer.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,Bar,BarChart,CartesianGrid,Cell,Legend,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/chart/AreaChart.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,Bar,BarChart,CartesianGrid,Cell,Legend,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/cartesian/CartesianGrid.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,Bar,BarChart,CartesianGrid,Cell,Legend,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/cartesian/XAxis.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,Bar,BarChart,CartesianGrid,Cell,Legend,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/cartesian/YAxis.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,Bar,BarChart,CartesianGrid,Cell,Legend,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/component/Tooltip.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,Bar,BarChart,CartesianGrid,Cell,Legend,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/component/Legend.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,Bar,BarChart,CartesianGrid,Cell,Legend,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/cartesian/Area.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,Bar,BarChart,CartesianGrid,Cell,Legend,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/chart/PieChart.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,Bar,BarChart,CartesianGrid,Cell,Legend,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/polar/Pie.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,Bar,BarChart,CartesianGrid,Cell,Legend,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/component/Cell.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,Bar,BarChart,CartesianGrid,Cell,Legend,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/chart/BarChart.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,Bar,BarChart,CartesianGrid,Cell,Legend,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/cartesian/Bar.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nfunction UserDashboard() {\n    var _user_profile, _chartData_winLossRatio_, _chartData_winLossRatio_1;\n    _s();\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [recentBids, setRecentBids] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [recentAuctions, setRecentAuctions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [notifications, setNotifications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [chartData, setChartData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        biddingActivity: [],\n        categorySpending: [],\n        monthlyPerformance: [],\n        winLossRatio: []\n    });\n    const { toast } = (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_5__.useToast)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_6__.useRouter)();\n    // Chart colors\n    const COLORS = {\n        primary: \"#3B82F6\",\n        secondary: \"#8B5CF6\",\n        success: \"#10B981\",\n        warning: \"#F59E0B\",\n        danger: \"#EF4444\",\n        info: \"#06B6D4\"\n    };\n    const PIE_COLORS = [\n        \"#3B82F6\",\n        \"#8B5CF6\",\n        \"#10B981\",\n        \"#F59E0B\",\n        \"#EF4444\",\n        \"#06B6D4\"\n    ];\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const userData = localStorage.getItem(\"user\");\n        if (userData) {\n            setUser(JSON.parse(userData));\n        }\n        loadDashboardData();\n    }, []);\n    const loadDashboardData = async ()=>{\n        try {\n            setLoading(true);\n            // Load user stats\n            const statsResponse = await _lib_api__WEBPACK_IMPORTED_MODULE_7__[\"default\"].get(\"/users/dashboard/stats\");\n            if (statsResponse.data.success) {\n                setStats(statsResponse.data.data);\n            }\n            // Load recent bids\n            const bidsResponse = await _lib_api__WEBPACK_IMPORTED_MODULE_7__[\"default\"].get(\"/users/bids?limit=5\");\n            if (bidsResponse.data.success) {\n                setRecentBids(bidsResponse.data.data.bids);\n            }\n            // Load recent auctions (recommended)\n            const auctionsResponse = await _lib_api__WEBPACK_IMPORTED_MODULE_7__[\"default\"].get(\"/auctions?limit=6&status=active\");\n            if (auctionsResponse.data.success) {\n                setRecentAuctions(auctionsResponse.data.data.auctions);\n            }\n            // Load notifications\n            const notificationsResponse = await _lib_api__WEBPACK_IMPORTED_MODULE_7__[\"default\"].get(\"/notifications?limit=5\");\n            if (notificationsResponse.data.success) {\n                setNotifications(notificationsResponse.data.data.notifications);\n            }\n            // Load chart data\n            const chartResponse = await _lib_api__WEBPACK_IMPORTED_MODULE_7__[\"default\"].get(\"/users/analytics/charts\");\n            if (chartResponse.data.success) {\n                setChartData(chartResponse.data.data);\n            }\n        } catch (error) {\n            console.error(\"Error loading dashboard data:\", error);\n            // Fallback to sample data\n            setStats({\n                activeBids: 5,\n                wonAuctions: 3,\n                totalSpent: 45000,\n                savedAuctions: 12,\n                totalBids: 28,\n                successRate: 75\n            });\n            setRecentBids([]);\n            setRecentAuctions([]);\n            setNotifications([]);\n            // Sample chart data\n            setChartData({\n                biddingActivity: [\n                    {\n                        month: \"يناير\",\n                        bids: 12,\n                        wins: 3\n                    },\n                    {\n                        month: \"فبراير\",\n                        bids: 19,\n                        wins: 5\n                    },\n                    {\n                        month: \"مارس\",\n                        bids: 15,\n                        wins: 2\n                    },\n                    {\n                        month: \"أبريل\",\n                        bids: 22,\n                        wins: 7\n                    },\n                    {\n                        month: \"مايو\",\n                        bids: 18,\n                        wins: 4\n                    },\n                    {\n                        month: \"يونيو\",\n                        bids: 25,\n                        wins: 8\n                    }\n                ],\n                categorySpending: [\n                    {\n                        name: \"إلكترونيات\",\n                        value: 15000,\n                        color: PIE_COLORS[0]\n                    },\n                    {\n                        name: \"سيارات\",\n                        value: 25000,\n                        color: PIE_COLORS[1]\n                    },\n                    {\n                        name: \"أثاث\",\n                        value: 8000,\n                        color: PIE_COLORS[2]\n                    },\n                    {\n                        name: \"مجوهرات\",\n                        value: 12000,\n                        color: PIE_COLORS[3]\n                    },\n                    {\n                        name: \"أخرى\",\n                        value: 5000,\n                        color: PIE_COLORS[4]\n                    }\n                ],\n                monthlyPerformance: [\n                    {\n                        month: \"يناير\",\n                        spent: 8000,\n                        saved: 2000\n                    },\n                    {\n                        month: \"فبراير\",\n                        spent: 12000,\n                        saved: 3000\n                    },\n                    {\n                        month: \"مارس\",\n                        spent: 6000,\n                        saved: 1500\n                    },\n                    {\n                        month: \"أبريل\",\n                        spent: 15000,\n                        saved: 4000\n                    },\n                    {\n                        month: \"مايو\",\n                        spent: 9000,\n                        saved: 2500\n                    },\n                    {\n                        month: \"يونيو\",\n                        spent: 18000,\n                        saved: 5000\n                    }\n                ],\n                winLossRatio: [\n                    {\n                        name: \"فوز\",\n                        value: 65,\n                        color: COLORS.success\n                    },\n                    {\n                        name: \"خسارة\",\n                        value: 35,\n                        color: COLORS.danger\n                    }\n                ]\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    const { formatAmount } = (0,_contexts_CurrencyContext__WEBPACK_IMPORTED_MODULE_8__.useCurrency)();\n    const formatPrice = (price)=>{\n        // Handle null/undefined prices\n        if (price === null || price === undefined || isNaN(price)) {\n            price = 0;\n        }\n        return formatAmount(price);\n    };\n    const formatTimeRemaining = (endTime)=>{\n        const now = new Date();\n        const end = new Date(endTime);\n        const diff = end.getTime() - now.getTime();\n        if (diff <= 0) return \"انتهى\";\n        const days = Math.floor(diff / (1000 * 60 * 60 * 24));\n        const hours = Math.floor(diff % (1000 * 60 * 60 * 24) / (1000 * 60 * 60));\n        const minutes = Math.floor(diff % (1000 * 60 * 60) / (1000 * 60));\n        if (days > 0) return \"\".concat(days, \" يوم\");\n        if (hours > 0) return \"\".concat(hours, \" ساعة\");\n        return \"\".concat(minutes, \" دقيقة\");\n    };\n    const formatRelativeTime = (dateString)=>{\n        const now = new Date();\n        const date = new Date(dateString);\n        const diff = now.getTime() - date.getTime();\n        const minutes = Math.floor(diff / (1000 * 60));\n        const hours = Math.floor(diff / (1000 * 60 * 60));\n        const days = Math.floor(diff / (1000 * 60 * 60 * 24));\n        if (days > 0) return \"منذ \".concat(days, \" يوم\");\n        if (hours > 0) return \"منذ \".concat(hours, \" ساعة\");\n        if (minutes > 0) return \"منذ \".concat(minutes, \" دقيقة\");\n        return \"الآن\";\n    };\n    const getBidStatus = (bid)=>{\n        if (bid.isWinning) return {\n            label: \"أعلى مزايدة\",\n            variant: \"default\",\n            color: \"text-green-600\"\n        };\n        if (bid.auction.status === \"active\") return {\n            label: \"متنافس\",\n            variant: \"secondary\",\n            color: \"text-blue-600\"\n        };\n        return {\n            label: \"تم تجاوزها\",\n            variant: \"outline\",\n            color: \"text-gray-600\"\n        };\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                children: \"جاري تحميل الإحصائيات...\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                lineNumber: 267,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n            lineNumber: 266,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg p-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-3xl font-bold\",\n                                    children: [\n                                        \"مرحباً، \",\n                                        (user === null || user === void 0 ? void 0 : (_user_profile = user.profile) === null || _user_profile === void 0 ? void 0 : _user_profile.fullName) || \"المستخدم\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                    lineNumber: 278,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-blue-100 mt-2\",\n                                    children: \"استكشف المزادات المتاحة وتابع مزايداتك\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                    lineNumber: 281,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                            lineNumber: 277,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden md:flex items-center gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold\",\n                                            children: [\n                                                (stats === null || stats === void 0 ? void 0 : stats.successRate) || 0,\n                                                \"%\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                            lineNumber: 287,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-blue-100\",\n                                            children: \"معدل النجاح\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                            lineNumber: 288,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                    lineNumber: 286,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold\",\n                                            children: (stats === null || stats === void 0 ? void 0 : stats.totalBids) || 0\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                            lineNumber: 291,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-blue-100\",\n                                            children: \"إجمالي المزايدات\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                            lineNumber: 292,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                    lineNumber: 290,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                            lineNumber: 285,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                    lineNumber: 276,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                lineNumber: 275,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-4 gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                        className: \"h-16 text-lg bg-blue-600 hover:bg-blue-700\",\n                        size: \"lg\",\n                        onClick: ()=>router.push(\"/user/auctions\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_Bell_Clock_DollarSign_Eye_Gavel_Heart_LineChart_PieChart_Search_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                className: \"h-6 w-6 ml-2\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                lineNumber: 306,\n                                columnNumber: 13\n                            }, this),\n                            \"استكشاف المزادات\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                        lineNumber: 301,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                        variant: \"outline\",\n                        className: \"h-16 text-lg border-green-200 text-green-700 hover:bg-green-50\",\n                        size: \"lg\",\n                        onClick: ()=>router.push(\"/user/my-bids\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_Bell_Clock_DollarSign_Eye_Gavel_Heart_LineChart_PieChart_Search_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                className: \"h-6 w-6 ml-2\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                lineNumber: 315,\n                                columnNumber: 13\n                            }, this),\n                            \"مزايداتي\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                        lineNumber: 309,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                        variant: \"outline\",\n                        className: \"h-16 text-lg border-purple-200 text-purple-700 hover:bg-purple-50\",\n                        size: \"lg\",\n                        onClick: ()=>router.push(\"/user/notifications\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_Bell_Clock_DollarSign_Eye_Gavel_Heart_LineChart_PieChart_Search_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                className: \"h-6 w-6 ml-2\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                lineNumber: 324,\n                                columnNumber: 13\n                            }, this),\n                            \"التنبيهات\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                        lineNumber: 318,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                        variant: \"outline\",\n                        className: \"h-16 text-lg border-orange-200 text-orange-700 hover:bg-orange-50\",\n                        size: \"lg\",\n                        onClick: ()=>router.push(\"/user/profile\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_Bell_Clock_DollarSign_Eye_Gavel_Heart_LineChart_PieChart_Search_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                className: \"h-6 w-6 ml-2\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                lineNumber: 333,\n                                columnNumber: 13\n                            }, this),\n                            \"الملف الشخصي\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                        lineNumber: 327,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                lineNumber: 300,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        className: \"bg-gradient-to-br from-blue-50 to-blue-100 border-blue-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            className: \"p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-blue-600\",\n                                                children: \"مزايداتي النشطة\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                lineNumber: 344,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-3xl font-bold text-blue-900\",\n                                                children: (stats === null || stats === void 0 ? void 0 : stats.activeBids) || 0\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                lineNumber: 345,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-blue-600 mt-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_Bell_Clock_DollarSign_Eye_Gavel_Heart_LineChart_PieChart_Search_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        className: \"h-3 w-3 inline ml-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                        lineNumber: 347,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"نشط الآن\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                lineNumber: 346,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                        lineNumber: 343,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-12 w-12 bg-blue-200 rounded-full flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_Bell_Clock_DollarSign_Eye_Gavel_Heart_LineChart_PieChart_Search_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"h-6 w-6 text-blue-600\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                            lineNumber: 352,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                        lineNumber: 351,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                lineNumber: 342,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                            lineNumber: 341,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                        lineNumber: 340,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        className: \"bg-gradient-to-br from-green-50 to-green-100 border-green-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            className: \"p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-green-600\",\n                                                children: \"المزادات المكسوبة\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                lineNumber: 362,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-3xl font-bold text-green-900\",\n                                                children: (stats === null || stats === void 0 ? void 0 : stats.wonAuctions) || 0\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                lineNumber: 363,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-green-600 mt-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_Bell_Clock_DollarSign_Eye_Gavel_Heart_LineChart_PieChart_Search_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        className: \"h-3 w-3 inline ml-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                        lineNumber: 365,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"إنجاز رائع\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                lineNumber: 364,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                        lineNumber: 361,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-12 w-12 bg-green-200 rounded-full flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_Bell_Clock_DollarSign_Eye_Gavel_Heart_LineChart_PieChart_Search_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            className: \"h-6 w-6 text-green-600\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                            lineNumber: 370,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                        lineNumber: 369,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                lineNumber: 360,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                            lineNumber: 359,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                        lineNumber: 358,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        className: \"bg-gradient-to-br from-purple-50 to-purple-100 border-purple-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            className: \"p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-purple-600\",\n                                                children: \"إجمالي المبلغ المنفق\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                lineNumber: 380,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-3xl font-bold text-purple-900\",\n                                                children: formatPrice((stats === null || stats === void 0 ? void 0 : stats.totalSpent) || 0)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                lineNumber: 381,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-purple-600 mt-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_Bell_Clock_DollarSign_Eye_Gavel_Heart_LineChart_PieChart_Search_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                        className: \"h-3 w-3 inline ml-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                        lineNumber: 383,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"استثمار ذكي\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                lineNumber: 382,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                        lineNumber: 379,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-12 w-12 bg-purple-200 rounded-full flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_Bell_Clock_DollarSign_Eye_Gavel_Heart_LineChart_PieChart_Search_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            className: \"h-6 w-6 text-purple-600\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                            lineNumber: 388,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                        lineNumber: 387,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                lineNumber: 378,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                            lineNumber: 377,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                        lineNumber: 376,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        className: \"bg-gradient-to-br from-orange-50 to-orange-100 border-orange-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            className: \"p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-orange-600\",\n                                                children: \"المزادات المحفوظة\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                lineNumber: 398,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-3xl font-bold text-orange-900\",\n                                                children: (stats === null || stats === void 0 ? void 0 : stats.savedAuctions) || 0\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                lineNumber: 399,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-orange-600 mt-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_Bell_Clock_DollarSign_Eye_Gavel_Heart_LineChart_PieChart_Search_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                        className: \"h-3 w-3 inline ml-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                        lineNumber: 401,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"مفضلة\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                lineNumber: 400,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                        lineNumber: 397,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-12 w-12 bg-orange-200 rounded-full flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_Bell_Clock_DollarSign_Eye_Gavel_Heart_LineChart_PieChart_Search_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            className: \"h-6 w-6 text-orange-600\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                            lineNumber: 406,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                        lineNumber: 405,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                lineNumber: 396,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                            lineNumber: 395,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                        lineNumber: 394,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                lineNumber: 339,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_Bell_Clock_DollarSign_Eye_Gavel_Heart_LineChart_PieChart_Search_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                className: \"h-5 w-5 text-blue-600\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                lineNumber: 419,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"نشاط المزايدات الشهري\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                        lineNumber: 418,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                        children: \"عدد المزايدات والانتصارات خلال الأشهر الماضية\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                        lineNumber: 422,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                lineNumber: 417,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_19__.ResponsiveContainer, {\n                                    width: \"100%\",\n                                    height: 300,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_20__.AreaChart, {\n                                        data: chartData.biddingActivity,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_21__.CartesianGrid, {\n                                                strokeDasharray: \"3 3\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                lineNumber: 429,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_22__.XAxis, {\n                                                dataKey: \"month\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                lineNumber: 430,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_23__.YAxis, {}, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                lineNumber: 431,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_24__.Tooltip, {}, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                lineNumber: 432,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_25__.Legend, {}, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                lineNumber: 433,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_26__.Area, {\n                                                type: \"monotone\",\n                                                dataKey: \"bids\",\n                                                stackId: \"1\",\n                                                stroke: COLORS.primary,\n                                                fill: COLORS.primary,\n                                                fillOpacity: 0.6,\n                                                name: \"المزايدات\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                lineNumber: 434,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_26__.Area, {\n                                                type: \"monotone\",\n                                                dataKey: \"wins\",\n                                                stackId: \"2\",\n                                                stroke: COLORS.success,\n                                                fill: COLORS.success,\n                                                fillOpacity: 0.8,\n                                                name: \"الانتصارات\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                lineNumber: 443,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                        lineNumber: 428,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                    lineNumber: 427,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                lineNumber: 426,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                        lineNumber: 416,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_Bell_Clock_DollarSign_Eye_Gavel_Heart_LineChart_PieChart_Search_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                className: \"h-5 w-5 text-purple-600\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                lineNumber: 461,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"الإنفاق حسب الفئة\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                        lineNumber: 460,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                        children: \"توزيع إنفاقك على الفئات المختلفة\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                        lineNumber: 464,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                lineNumber: 459,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_19__.ResponsiveContainer, {\n                                    width: \"100%\",\n                                    height: 300,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_28__.PieChart, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_29__.Pie, {\n                                                data: chartData.categorySpending,\n                                                cx: \"50%\",\n                                                cy: \"50%\",\n                                                labelLine: false,\n                                                label: (param)=>{\n                                                    let { name, percent } = param;\n                                                    return \"\".concat(name, \" \").concat((percent * 100).toFixed(0), \"%\");\n                                                },\n                                                outerRadius: 80,\n                                                fill: \"#8884d8\",\n                                                dataKey: \"value\",\n                                                children: chartData.categorySpending.map((entry, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_30__.Cell, {\n                                                        fill: entry.color\n                                                    }, \"cell-\".concat(index), false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                        lineNumber: 482,\n                                                        columnNumber: 23\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                lineNumber: 471,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_24__.Tooltip, {\n                                                formatter: (value)=>formatPrice(value)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                lineNumber: 485,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                        lineNumber: 470,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                    lineNumber: 469,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                lineNumber: 468,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                        lineNumber: 458,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                lineNumber: 414,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_Bell_Clock_DollarSign_Eye_Gavel_Heart_LineChart_PieChart_Search_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n                                                className: \"h-5 w-5 text-green-600\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                lineNumber: 498,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"الأداء الشهري\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                        lineNumber: 497,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                        children: \"المبلغ المنفق والمدخر شهرياً\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                        lineNumber: 501,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                lineNumber: 496,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_19__.ResponsiveContainer, {\n                                    width: \"100%\",\n                                    height: 300,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_32__.BarChart, {\n                                        data: chartData.monthlyPerformance,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_21__.CartesianGrid, {\n                                                strokeDasharray: \"3 3\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                lineNumber: 508,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_22__.XAxis, {\n                                                dataKey: \"month\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                lineNumber: 509,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_23__.YAxis, {}, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                lineNumber: 510,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_24__.Tooltip, {\n                                                formatter: (value)=>formatPrice(value)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                lineNumber: 511,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_25__.Legend, {}, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                lineNumber: 512,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_33__.Bar, {\n                                                dataKey: \"spent\",\n                                                fill: COLORS.danger,\n                                                name: \"المنفق\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                lineNumber: 513,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_33__.Bar, {\n                                                dataKey: \"saved\",\n                                                fill: COLORS.success,\n                                                name: \"المدخر\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                lineNumber: 514,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                        lineNumber: 507,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                    lineNumber: 506,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                lineNumber: 505,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                        lineNumber: 495,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_Bell_Clock_DollarSign_Eye_Gavel_Heart_LineChart_PieChart_Search_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                className: \"h-5 w-5 text-yellow-600\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                lineNumber: 524,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"نسبة النجاح\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                        lineNumber: 523,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                        children: \"نسبة الفوز إلى الخسارة في المزايدات\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                        lineNumber: 527,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                lineNumber: 522,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_19__.ResponsiveContainer, {\n                                        width: \"100%\",\n                                        height: 300,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_28__.PieChart, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_29__.Pie, {\n                                                    data: chartData.winLossRatio,\n                                                    cx: \"50%\",\n                                                    cy: \"50%\",\n                                                    innerRadius: 60,\n                                                    outerRadius: 100,\n                                                    paddingAngle: 5,\n                                                    dataKey: \"value\",\n                                                    children: chartData.winLossRatio.map((entry, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_30__.Cell, {\n                                                            fill: entry.color\n                                                        }, \"cell-\".concat(index), false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                            lineNumber: 544,\n                                                            columnNumber: 23\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                    lineNumber: 534,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_24__.Tooltip, {\n                                                    formatter: (value)=>\"\".concat(value, \"%\")\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                    lineNumber: 547,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_25__.Legend, {}, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                    lineNumber: 548,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                            lineNumber: 533,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                        lineNumber: 532,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-4 flex justify-center gap-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-2xl font-bold text-green-600\",\n                                                        children: [\n                                                            ((_chartData_winLossRatio_ = chartData.winLossRatio[0]) === null || _chartData_winLossRatio_ === void 0 ? void 0 : _chartData_winLossRatio_.value) || 0,\n                                                            \"%\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                        lineNumber: 553,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: \"معدل النجاح\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                        lineNumber: 554,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                lineNumber: 552,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-2xl font-bold text-red-600\",\n                                                        children: [\n                                                            ((_chartData_winLossRatio_1 = chartData.winLossRatio[1]) === null || _chartData_winLossRatio_1 === void 0 ? void 0 : _chartData_winLossRatio_1.value) || 0,\n                                                            \"%\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                        lineNumber: 557,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: \"معدل الخسارة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                        lineNumber: 558,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                lineNumber: 556,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                        lineNumber: 551,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                lineNumber: 531,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                        lineNumber: 521,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                lineNumber: 493,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_Bell_Clock_DollarSign_Eye_Gavel_Heart_LineChart_PieChart_Search_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        className: \"h-5 w-5 text-blue-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                        lineNumber: 572,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"مزايداتي الحديثة\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                lineNumber: 571,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                variant: \"ghost\",\n                                                size: \"sm\",\n                                                onClick: ()=>router.push(\"/user/my-bids\"),\n                                                children: [\n                                                    \"عرض الكل\",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_Bell_Clock_DollarSign_Eye_Gavel_Heart_LineChart_PieChart_Search_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_34__[\"default\"], {\n                                                        className: \"h-4 w-4 mr-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                        lineNumber: 581,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                lineNumber: 575,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                        lineNumber: 570,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                        children: \"آخر المزايدات التي شاركت فيها\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                        lineNumber: 584,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                lineNumber: 569,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                children: recentBids.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3\",\n                                    children: recentBids.slice(0, 3).map((bid)=>{\n                                        const status = getBidStatus(bid);\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-medium text-gray-900\",\n                                                            children: bid.auction.title\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                            lineNumber: 596,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-4 mt-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-gray-600\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_Bell_Clock_DollarSign_Eye_Gavel_Heart_LineChart_PieChart_Search_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_35__[\"default\"], {\n                                                                            className: \"h-4 w-4 inline ml-1\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                                            lineNumber: 599,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        formatTimeRemaining(bid.auction.endTime)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                                    lineNumber: 598,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-gray-600\",\n                                                                    children: [\n                                                                        \"مزايدتي: \",\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"font-medium\",\n                                                                            children: formatPrice(bid.amount)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                                            lineNumber: 603,\n                                                                            columnNumber: 40\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                                    lineNumber: 602,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                            lineNumber: 597,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-gray-500 mt-1\",\n                                                            children: formatRelativeTime(bid.placedAt)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                            lineNumber: 606,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                    lineNumber: 595,\n                                                    columnNumber: 25\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex flex-col items-end gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                            variant: status.variant,\n                                                            className: status.color,\n                                                            children: status.label\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                            lineNumber: 611,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                            size: \"sm\",\n                                                            variant: \"outline\",\n                                                            onClick: ()=>router.push(\"/auctions/\".concat(bid.auction._id)),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_Bell_Clock_DollarSign_Eye_Gavel_Heart_LineChart_PieChart_Search_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_36__[\"default\"], {\n                                                                    className: \"h-4 w-4 ml-1\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                                    lineNumber: 619,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                \"عرض\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                            lineNumber: 614,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                    lineNumber: 610,\n                                                    columnNumber: 25\n                                                }, this)\n                                            ]\n                                        }, bid._id, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                            lineNumber: 594,\n                                            columnNumber: 23\n                                        }, this);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                    lineNumber: 590,\n                                    columnNumber: 17\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center py-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_Bell_Clock_DollarSign_Eye_Gavel_Heart_LineChart_PieChart_Search_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"h-12 w-12 text-gray-400 mx-auto mb-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                            lineNumber: 629,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-medium text-gray-900 mb-2\",\n                                            children: \"لا توجد مزايدات حديثة\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                            lineNumber: 630,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 mb-4\",\n                                            children: \"ابدأ بالمشاركة في المزادات المتاحة\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                            lineNumber: 631,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            onClick: ()=>router.push(\"/user/auctions\"),\n                                            children: \"استكشاف المزادات\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                            lineNumber: 632,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                    lineNumber: 628,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                lineNumber: 588,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                        lineNumber: 568,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_Bell_Clock_DollarSign_Eye_Gavel_Heart_LineChart_PieChart_Search_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"h-5 w-5 text-orange-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                        lineNumber: 645,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"التنبيهات\",\n                                                    notifications.filter((n)=>!n.read).length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                        variant: \"destructive\",\n                                                        className: \"text-xs\",\n                                                        children: notifications.filter((n)=>!n.read).length\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                        lineNumber: 648,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                lineNumber: 644,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                variant: \"ghost\",\n                                                size: \"sm\",\n                                                onClick: ()=>router.push(\"/user/notifications\"),\n                                                children: [\n                                                    \"عرض الكل\",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_Bell_Clock_DollarSign_Eye_Gavel_Heart_LineChart_PieChart_Search_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_34__[\"default\"], {\n                                                        className: \"h-4 w-4 mr-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                        lineNumber: 659,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                lineNumber: 653,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                        lineNumber: 643,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                        children: \"آخر التحديثات والإشعارات\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                        lineNumber: 662,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                lineNumber: 642,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                children: notifications.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3\",\n                                    children: notifications.slice(0, 3).map((notification)=>{\n                                        const getNotificationStyle = (type)=>{\n                                            switch(type){\n                                                case \"auction_won\":\n                                                    return \"bg-green-50 border-green-200 text-green-800\";\n                                                case \"auction_ending\":\n                                                    return \"bg-yellow-50 border-yellow-200 text-yellow-800\";\n                                                case \"new_auction\":\n                                                    return \"bg-blue-50 border-blue-200 text-blue-800\";\n                                                case \"bid_outbid\":\n                                                    return \"bg-red-50 border-red-200 text-red-800\";\n                                                default:\n                                                    return \"bg-gray-50 border-gray-200 text-gray-800\";\n                                            }\n                                        };\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-3 border rounded-lg \".concat(getNotificationStyle(notification.type), \" \").concat(!notification.read ? \"ring-2 ring-blue-200\" : \"\"),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm font-medium\",\n                                                                children: notification.title\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                                lineNumber: 692,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs mt-1\",\n                                                                children: notification.message\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                                lineNumber: 693,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs opacity-75 mt-2\",\n                                                                children: formatRelativeTime(notification.createdAt)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                                lineNumber: 694,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                        lineNumber: 691,\n                                                        columnNumber: 27\n                                                    }, this),\n                                                    !notification.read && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-2 h-2 bg-blue-600 rounded-full mt-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                        lineNumber: 699,\n                                                        columnNumber: 29\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                lineNumber: 690,\n                                                columnNumber: 25\n                                            }, this)\n                                        }, notification._id, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                            lineNumber: 686,\n                                            columnNumber: 23\n                                        }, this);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                    lineNumber: 668,\n                                    columnNumber: 17\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center py-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_Bell_Clock_DollarSign_Eye_Gavel_Heart_LineChart_PieChart_Search_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"h-12 w-12 text-gray-400 mx-auto mb-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                            lineNumber: 708,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-medium text-gray-900 mb-2\",\n                                            children: \"لا توجد تنبيهات\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                            lineNumber: 709,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600\",\n                                            children: \"ستظهر هنا التحديثات المهمة\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                            lineNumber: 710,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                    lineNumber: 707,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                lineNumber: 666,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                        lineNumber: 641,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                lineNumber: 566,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_Bell_Clock_DollarSign_Eye_Gavel_Heart_LineChart_PieChart_Search_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_37__[\"default\"], {\n                                                className: \"h-5 w-5 text-purple-600\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                lineNumber: 722,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"مزادات مقترحة لك\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                        lineNumber: 721,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        onClick: ()=>router.push(\"/user/auctions\"),\n                                        children: [\n                                            \"عرض المزيد\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_Bell_Clock_DollarSign_Eye_Gavel_Heart_LineChart_PieChart_Search_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_34__[\"default\"], {\n                                                className: \"h-4 w-4 mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                lineNumber: 731,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                        lineNumber: 725,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                lineNumber: 720,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                children: \"مزادات قد تهمك بناءً على اهتماماتك السابقة\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                lineNumber: 734,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                        lineNumber: 719,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        children: recentAuctions.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\",\n                            children: recentAuctions.slice(0, 6).map((auction)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"article\", {\n                                    className: \"border rounded-lg p-4 hover:shadow-lg transition-all duration-200 hover:border-blue-300 cursor-pointer\",\n                                    onClick: ()=>router.push(\"/auctions/\".concat(auction._id)),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"aspect-video bg-gradient-to-br from-gray-100 to-gray-200 rounded mb-3 flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_Bell_Clock_DollarSign_Eye_Gavel_Heart_LineChart_PieChart_Search_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_38__[\"default\"], {\n                                                className: \"h-8 w-8 text-gray-400\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                lineNumber: 748,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                            lineNumber: 747,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-medium text-gray-900 line-clamp-2\",\n                                                    children: auction.title\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                    lineNumber: 752,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-gray-600\",\n                                                                    children: [\n                                                                        \"السعر الحالي: \",\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"font-medium text-green-600\",\n                                                                            children: formatPrice(auction.currentBid)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                                            lineNumber: 757,\n                                                                            columnNumber: 43\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                                    lineNumber: 756,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-gray-500\",\n                                                                    children: [\n                                                                        \"يبدأ من: \",\n                                                                        formatPrice(auction.startingBid)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                                    lineNumber: 759,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                            lineNumber: 755,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                            variant: \"outline\",\n                                                            className: \"text-xs\",\n                                                            children: auction.category\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                            lineNumber: 763,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                    lineNumber: 754,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between pt-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-1 text-xs text-gray-500\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_Bell_Clock_DollarSign_Eye_Gavel_Heart_LineChart_PieChart_Search_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_35__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                                    lineNumber: 770,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                formatTimeRemaining(auction.endTime)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                            lineNumber: 769,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-1 text-xs text-gray-500\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_Bell_Clock_DollarSign_Eye_Gavel_Heart_LineChart_PieChart_Search_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                                    lineNumber: 774,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                auction.bidsCount,\n                                                                \" مزايدة\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                            lineNumber: 773,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                    lineNumber: 768,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    size: \"sm\",\n                                                    className: \"w-full mt-3\",\n                                                    onClick: (e)=>{\n                                                        e.stopPropagation();\n                                                        router.push(\"/auctions/\".concat(auction._id));\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_Bell_Clock_DollarSign_Eye_Gavel_Heart_LineChart_PieChart_Search_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_36__[\"default\"], {\n                                                            className: \"h-4 w-4 ml-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                            lineNumber: 787,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        \"شاهد التفاصيل\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                    lineNumber: 779,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                            lineNumber: 751,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, auction._id, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                    lineNumber: 742,\n                                    columnNumber: 19\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                            lineNumber: 740,\n                            columnNumber: 15\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center py-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_Bell_Clock_DollarSign_Eye_Gavel_Heart_LineChart_PieChart_Search_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_37__[\"default\"], {\n                                    className: \"h-16 w-16 text-gray-400 mx-auto mb-4\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                    lineNumber: 796,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-medium text-gray-900 mb-2\",\n                                    children: \"لا توجد مزادات متاحة حالياً\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                    lineNumber: 797,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 mb-6\",\n                                    children: \"تحقق مرة أخرى لاحقاً أو استكشف الفئات المختلفة\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                    lineNumber: 798,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    onClick: ()=>router.push(\"/user/auctions\"),\n                                    children: \"استكشاف المزادات\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                    lineNumber: 799,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                            lineNumber: 795,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                        lineNumber: 738,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                lineNumber: 718,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n        lineNumber: 273,\n        columnNumber: 5\n    }, this);\n}\n_s(UserDashboard, \"m/Vw8DQQyp2mq6xEF53eRK4Izqc=\", false, function() {\n    return [\n        _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_5__.useToast,\n        next_navigation__WEBPACK_IMPORTED_MODULE_6__.useRouter,\n        _contexts_CurrencyContext__WEBPACK_IMPORTED_MODULE_8__.useCurrency\n    ];\n});\n_c = UserDashboard;\nvar _c;\n$RefreshReg$(_c, \"UserDashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/user/dashboard/page.tsx\n"));

/***/ })

});