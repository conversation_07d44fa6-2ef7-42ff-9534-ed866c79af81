"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/user/dashboard/page",{

/***/ "(app-pages-browser)/./app/user/dashboard/page.tsx":
/*!*************************************!*\
  !*** ./app/user/dashboard/page.tsx ***!
  \*************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ UserDashboard; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/use-toast */ \"(app-pages-browser)/./components/ui/use-toast.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./lib/api.ts\");\n/* harmony import */ var _contexts_CurrencyContext__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/contexts/CurrencyContext */ \"(app-pages-browser)/./contexts/CurrencyContext.tsx\");\n/* harmony import */ var _components_CurrencySelector__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/CurrencySelector */ \"(app-pages-browser)/./components/CurrencySelector.tsx\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart3_Bell_Clock_DollarSign_Eye_Gavel_Heart_LineChart_PieChart_Search_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart3,Bell,Clock,DollarSign,Eye,Gavel,Heart,LineChart,PieChart,Search,Target,TrendingUp,Trophy,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart3_Bell_Clock_DollarSign_Eye_Gavel_Heart_LineChart_PieChart_Search_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart3,Bell,Clock,DollarSign,Eye,Gavel,Heart,LineChart,PieChart,Search,Target,TrendingUp,Trophy,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart3_Bell_Clock_DollarSign_Eye_Gavel_Heart_LineChart_PieChart_Search_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart3,Bell,Clock,DollarSign,Eye,Gavel,Heart,LineChart,PieChart,Search,Target,TrendingUp,Trophy,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart3_Bell_Clock_DollarSign_Eye_Gavel_Heart_LineChart_PieChart_Search_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart3,Bell,Clock,DollarSign,Eye,Gavel,Heart,LineChart,PieChart,Search,Target,TrendingUp,Trophy,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart3_Bell_Clock_DollarSign_Eye_Gavel_Heart_LineChart_PieChart_Search_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart3,Bell,Clock,DollarSign,Eye,Gavel,Heart,LineChart,PieChart,Search,Target,TrendingUp,Trophy,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart3_Bell_Clock_DollarSign_Eye_Gavel_Heart_LineChart_PieChart_Search_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart3,Bell,Clock,DollarSign,Eye,Gavel,Heart,LineChart,PieChart,Search,Target,TrendingUp,Trophy,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trophy.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart3_Bell_Clock_DollarSign_Eye_Gavel_Heart_LineChart_PieChart_Search_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart3,Bell,Clock,DollarSign,Eye,Gavel,Heart,LineChart,PieChart,Search,Target,TrendingUp,Trophy,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart3_Bell_Clock_DollarSign_Eye_Gavel_Heart_LineChart_PieChart_Search_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart3,Bell,Clock,DollarSign,Eye,Gavel,Heart,LineChart,PieChart,Search,Target,TrendingUp,Trophy,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart3_Bell_Clock_DollarSign_Eye_Gavel_Heart_LineChart_PieChart_Search_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart3,Bell,Clock,DollarSign,Eye,Gavel,Heart,LineChart,PieChart,Search,Target,TrendingUp,Trophy,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart3_Bell_Clock_DollarSign_Eye_Gavel_Heart_LineChart_PieChart_Search_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart3,Bell,Clock,DollarSign,Eye,Gavel,Heart,LineChart,PieChart,Search,Target,TrendingUp,Trophy,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/line-chart.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart3_Bell_Clock_DollarSign_Eye_Gavel_Heart_LineChart_PieChart_Search_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart3,Bell,Clock,DollarSign,Eye,Gavel,Heart,LineChart,PieChart,Search,Target,TrendingUp,Trophy,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pie-chart.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart3_Bell_Clock_DollarSign_Eye_Gavel_Heart_LineChart_PieChart_Search_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart3,Bell,Clock,DollarSign,Eye,Gavel,Heart,LineChart,PieChart,Search,Target,TrendingUp,Trophy,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bar-chart-3.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart3_Bell_Clock_DollarSign_Eye_Gavel_Heart_LineChart_PieChart_Search_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_35__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart3,Bell,Clock,DollarSign,Eye,Gavel,Heart,LineChart,PieChart,Search,Target,TrendingUp,Trophy,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart3_Bell_Clock_DollarSign_Eye_Gavel_Heart_LineChart_PieChart_Search_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_36__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart3,Bell,Clock,DollarSign,Eye,Gavel,Heart,LineChart,PieChart,Search,Target,TrendingUp,Trophy,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart3_Bell_Clock_DollarSign_Eye_Gavel_Heart_LineChart_PieChart_Search_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_37__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart3,Bell,Clock,DollarSign,Eye,Gavel,Heart,LineChart,PieChart,Search,Target,TrendingUp,Trophy,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart3_Bell_Clock_DollarSign_Eye_Gavel_Heart_LineChart_PieChart_Search_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_38__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart3,Bell,Clock,DollarSign,Eye,Gavel,Heart,LineChart,PieChart,Search,Target,TrendingUp,Trophy,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart3_Bell_Clock_DollarSign_Eye_Gavel_Heart_LineChart_PieChart_Search_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_39__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart3,Bell,Clock,DollarSign,Eye,Gavel,Heart,LineChart,PieChart,Search,Target,TrendingUp,Trophy,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/gavel.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,Bar,BarChart,CartesianGrid,Cell,Legend,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/component/ResponsiveContainer.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,Bar,BarChart,CartesianGrid,Cell,Legend,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/chart/AreaChart.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,Bar,BarChart,CartesianGrid,Cell,Legend,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/cartesian/CartesianGrid.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,Bar,BarChart,CartesianGrid,Cell,Legend,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/cartesian/XAxis.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,Bar,BarChart,CartesianGrid,Cell,Legend,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/cartesian/YAxis.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,Bar,BarChart,CartesianGrid,Cell,Legend,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/component/Tooltip.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,Bar,BarChart,CartesianGrid,Cell,Legend,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/component/Legend.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,Bar,BarChart,CartesianGrid,Cell,Legend,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/cartesian/Area.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,Bar,BarChart,CartesianGrid,Cell,Legend,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/chart/PieChart.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,Bar,BarChart,CartesianGrid,Cell,Legend,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/polar/Pie.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,Bar,BarChart,CartesianGrid,Cell,Legend,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/component/Cell.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,Bar,BarChart,CartesianGrid,Cell,Legend,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/chart/BarChart.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_34__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,Bar,BarChart,CartesianGrid,Cell,Legend,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/cartesian/Bar.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nfunction UserDashboard() {\n    var _user_profile, _chartData_winLossRatio_, _chartData_winLossRatio_1;\n    _s();\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [recentBids, setRecentBids] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [recentAuctions, setRecentAuctions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [notifications, setNotifications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [chartData, setChartData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        biddingActivity: [],\n        categorySpending: [],\n        monthlyPerformance: [],\n        winLossRatio: []\n    });\n    const { toast } = (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_5__.useToast)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_6__.useRouter)();\n    // Chart colors\n    const COLORS = {\n        primary: \"#3B82F6\",\n        secondary: \"#8B5CF6\",\n        success: \"#10B981\",\n        warning: \"#F59E0B\",\n        danger: \"#EF4444\",\n        info: \"#06B6D4\"\n    };\n    const PIE_COLORS = [\n        \"#3B82F6\",\n        \"#8B5CF6\",\n        \"#10B981\",\n        \"#F59E0B\",\n        \"#EF4444\",\n        \"#06B6D4\"\n    ];\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const userData = localStorage.getItem(\"user\");\n        if (userData) {\n            setUser(JSON.parse(userData));\n        }\n        loadDashboardData();\n    }, []);\n    const loadDashboardData = async ()=>{\n        try {\n            setLoading(true);\n            // Load user stats\n            const statsResponse = await _lib_api__WEBPACK_IMPORTED_MODULE_7__[\"default\"].get(\"/users/dashboard/stats\");\n            if (statsResponse.data.success) {\n                setStats(statsResponse.data.data);\n            }\n            // Load recent bids\n            const bidsResponse = await _lib_api__WEBPACK_IMPORTED_MODULE_7__[\"default\"].get(\"/user/bids?limit=5\");\n            if (bidsResponse.data.success) {\n                setRecentBids(bidsResponse.data.data.bids);\n            }\n            // Load recent auctions (recommended)\n            const auctionsResponse = await _lib_api__WEBPACK_IMPORTED_MODULE_7__[\"default\"].get(\"/auctions?limit=6&status=active\");\n            if (auctionsResponse.data.success) {\n                setRecentAuctions(auctionsResponse.data.data.auctions);\n            }\n            // Load notifications\n            const notificationsResponse = await _lib_api__WEBPACK_IMPORTED_MODULE_7__[\"default\"].get(\"/user/notifications?limit=5\");\n            if (notificationsResponse.data.success) {\n                setNotifications(notificationsResponse.data.data.notifications);\n            }\n            // Load chart data\n            const chartResponse = await _lib_api__WEBPACK_IMPORTED_MODULE_7__[\"default\"].get(\"/user/analytics/charts\");\n            if (chartResponse.data.success) {\n                setChartData(chartResponse.data.data);\n            }\n        } catch (error) {\n            console.error(\"Error loading dashboard data:\", error);\n            // Fallback to sample data\n            setStats({\n                activeBids: 5,\n                wonAuctions: 3,\n                totalSpent: 45000,\n                savedAuctions: 12,\n                totalBids: 28,\n                successRate: 75\n            });\n            setRecentBids([]);\n            setRecentAuctions([]);\n            setNotifications([]);\n            // Sample chart data\n            setChartData({\n                biddingActivity: [\n                    {\n                        month: \"يناير\",\n                        bids: 12,\n                        wins: 3\n                    },\n                    {\n                        month: \"فبراير\",\n                        bids: 19,\n                        wins: 5\n                    },\n                    {\n                        month: \"مارس\",\n                        bids: 15,\n                        wins: 2\n                    },\n                    {\n                        month: \"أبريل\",\n                        bids: 22,\n                        wins: 7\n                    },\n                    {\n                        month: \"مايو\",\n                        bids: 18,\n                        wins: 4\n                    },\n                    {\n                        month: \"يونيو\",\n                        bids: 25,\n                        wins: 8\n                    }\n                ],\n                categorySpending: [\n                    {\n                        name: \"إلكترونيات\",\n                        value: 15000,\n                        color: PIE_COLORS[0]\n                    },\n                    {\n                        name: \"سيارات\",\n                        value: 25000,\n                        color: PIE_COLORS[1]\n                    },\n                    {\n                        name: \"أثاث\",\n                        value: 8000,\n                        color: PIE_COLORS[2]\n                    },\n                    {\n                        name: \"مجوهرات\",\n                        value: 12000,\n                        color: PIE_COLORS[3]\n                    },\n                    {\n                        name: \"أخرى\",\n                        value: 5000,\n                        color: PIE_COLORS[4]\n                    }\n                ],\n                monthlyPerformance: [\n                    {\n                        month: \"يناير\",\n                        spent: 8000,\n                        saved: 2000\n                    },\n                    {\n                        month: \"فبراير\",\n                        spent: 12000,\n                        saved: 3000\n                    },\n                    {\n                        month: \"مارس\",\n                        spent: 6000,\n                        saved: 1500\n                    },\n                    {\n                        month: \"أبريل\",\n                        spent: 15000,\n                        saved: 4000\n                    },\n                    {\n                        month: \"مايو\",\n                        spent: 9000,\n                        saved: 2500\n                    },\n                    {\n                        month: \"يونيو\",\n                        spent: 18000,\n                        saved: 5000\n                    }\n                ],\n                winLossRatio: [\n                    {\n                        name: \"فوز\",\n                        value: 65,\n                        color: COLORS.success\n                    },\n                    {\n                        name: \"خسارة\",\n                        value: 35,\n                        color: COLORS.danger\n                    }\n                ]\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    const { formatAmount } = (0,_contexts_CurrencyContext__WEBPACK_IMPORTED_MODULE_8__.useCurrency)();\n    const formatPrice = (price)=>{\n        return formatAmount(price);\n    };\n    const formatTimeRemaining = (endTime)=>{\n        const now = new Date();\n        const end = new Date(endTime);\n        const diff = end.getTime() - now.getTime();\n        if (diff <= 0) return \"انتهى\";\n        const days = Math.floor(diff / (1000 * 60 * 60 * 24));\n        const hours = Math.floor(diff % (1000 * 60 * 60 * 24) / (1000 * 60 * 60));\n        const minutes = Math.floor(diff % (1000 * 60 * 60) / (1000 * 60));\n        if (days > 0) return \"\".concat(days, \" يوم\");\n        if (hours > 0) return \"\".concat(hours, \" ساعة\");\n        return \"\".concat(minutes, \" دقيقة\");\n    };\n    const formatRelativeTime = (dateString)=>{\n        const now = new Date();\n        const date = new Date(dateString);\n        const diff = now.getTime() - date.getTime();\n        const minutes = Math.floor(diff / (1000 * 60));\n        const hours = Math.floor(diff / (1000 * 60 * 60));\n        const days = Math.floor(diff / (1000 * 60 * 60 * 24));\n        if (days > 0) return \"منذ \".concat(days, \" يوم\");\n        if (hours > 0) return \"منذ \".concat(hours, \" ساعة\");\n        if (minutes > 0) return \"منذ \".concat(minutes, \" دقيقة\");\n        return \"الآن\";\n    };\n    const getBidStatus = (bid)=>{\n        if (bid.isWinning) return {\n            label: \"أعلى مزايدة\",\n            variant: \"default\",\n            color: \"text-green-600\"\n        };\n        if (bid.auction.status === \"active\") return {\n            label: \"متنافس\",\n            variant: \"secondary\",\n            color: \"text-blue-600\"\n        };\n        return {\n            label: \"تم تجاوزها\",\n            variant: \"outline\",\n            color: \"text-gray-600\"\n        };\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                children: \"جاري تحميل الإحصائيات...\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                lineNumber: 264,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n            lineNumber: 263,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg p-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-3xl font-bold\",\n                                    children: [\n                                        \"مرحباً، \",\n                                        (user === null || user === void 0 ? void 0 : (_user_profile = user.profile) === null || _user_profile === void 0 ? void 0 : _user_profile.fullName) || \"المستخدم\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                    lineNumber: 275,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-blue-100 mt-2\",\n                                    children: \"استكشف المزادات المتاحة وتابع مزايداتك\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                    lineNumber: 278,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                            lineNumber: 274,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden md:flex items-center gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold\",\n                                            children: [\n                                                (stats === null || stats === void 0 ? void 0 : stats.successRate) || 0,\n                                                \"%\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                            lineNumber: 284,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-blue-100\",\n                                            children: \"معدل النجاح\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                            lineNumber: 285,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                    lineNumber: 283,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold\",\n                                            children: (stats === null || stats === void 0 ? void 0 : stats.totalBids) || 0\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                            lineNumber: 288,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-blue-100\",\n                                            children: \"إجمالي المزايدات\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                            lineNumber: 289,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                    lineNumber: 287,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white/10 rounded-lg p-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_CurrencySelector__WEBPACK_IMPORTED_MODULE_9__.CurrencySelector, {\n                                        showLabel: false,\n                                        className: \"text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                        lineNumber: 292,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                    lineNumber: 291,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                            lineNumber: 282,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                    lineNumber: 273,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                lineNumber: 272,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-4 gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                        className: \"h-16 text-lg bg-blue-600 hover:bg-blue-700\",\n                        size: \"lg\",\n                        onClick: ()=>router.push(\"/user/auctions\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_Bell_Clock_DollarSign_Eye_Gavel_Heart_LineChart_PieChart_Search_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                className: \"h-6 w-6 ml-2\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                lineNumber: 305,\n                                columnNumber: 13\n                            }, this),\n                            \"استكشاف المزادات\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                        lineNumber: 300,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                        variant: \"outline\",\n                        className: \"h-16 text-lg border-green-200 text-green-700 hover:bg-green-50\",\n                        size: \"lg\",\n                        onClick: ()=>router.push(\"/user/my-bids\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_Bell_Clock_DollarSign_Eye_Gavel_Heart_LineChart_PieChart_Search_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                className: \"h-6 w-6 ml-2\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                lineNumber: 314,\n                                columnNumber: 13\n                            }, this),\n                            \"مزايداتي\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                        lineNumber: 308,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                        variant: \"outline\",\n                        className: \"h-16 text-lg border-purple-200 text-purple-700 hover:bg-purple-50\",\n                        size: \"lg\",\n                        onClick: ()=>router.push(\"/user/notifications\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_Bell_Clock_DollarSign_Eye_Gavel_Heart_LineChart_PieChart_Search_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                className: \"h-6 w-6 ml-2\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                lineNumber: 323,\n                                columnNumber: 13\n                            }, this),\n                            \"التنبيهات\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                        lineNumber: 317,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                        variant: \"outline\",\n                        className: \"h-16 text-lg border-orange-200 text-orange-700 hover:bg-orange-50\",\n                        size: \"lg\",\n                        onClick: ()=>router.push(\"/user/profile\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_Bell_Clock_DollarSign_Eye_Gavel_Heart_LineChart_PieChart_Search_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                className: \"h-6 w-6 ml-2\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                lineNumber: 332,\n                                columnNumber: 13\n                            }, this),\n                            \"الملف الشخصي\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                        lineNumber: 326,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                lineNumber: 299,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        className: \"bg-gradient-to-br from-blue-50 to-blue-100 border-blue-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            className: \"p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-blue-600\",\n                                                children: \"مزايداتي النشطة\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                lineNumber: 343,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-3xl font-bold text-blue-900\",\n                                                children: (stats === null || stats === void 0 ? void 0 : stats.activeBids) || 0\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                lineNumber: 344,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-blue-600 mt-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_Bell_Clock_DollarSign_Eye_Gavel_Heart_LineChart_PieChart_Search_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        className: \"h-3 w-3 inline ml-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                        lineNumber: 346,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"نشط الآن\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                lineNumber: 345,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                        lineNumber: 342,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-12 w-12 bg-blue-200 rounded-full flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_Bell_Clock_DollarSign_Eye_Gavel_Heart_LineChart_PieChart_Search_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"h-6 w-6 text-blue-600\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                            lineNumber: 351,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                        lineNumber: 350,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                lineNumber: 341,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                            lineNumber: 340,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                        lineNumber: 339,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        className: \"bg-gradient-to-br from-green-50 to-green-100 border-green-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            className: \"p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-green-600\",\n                                                children: \"المزادات المكسوبة\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                lineNumber: 361,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-3xl font-bold text-green-900\",\n                                                children: (stats === null || stats === void 0 ? void 0 : stats.wonAuctions) || 0\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                lineNumber: 362,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-green-600 mt-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_Bell_Clock_DollarSign_Eye_Gavel_Heart_LineChart_PieChart_Search_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                        className: \"h-3 w-3 inline ml-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                        lineNumber: 364,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"إنجاز رائع\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                lineNumber: 363,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                        lineNumber: 360,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-12 w-12 bg-green-200 rounded-full flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_Bell_Clock_DollarSign_Eye_Gavel_Heart_LineChart_PieChart_Search_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"h-6 w-6 text-green-600\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                            lineNumber: 369,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                        lineNumber: 368,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                lineNumber: 359,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                            lineNumber: 358,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                        lineNumber: 357,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        className: \"bg-gradient-to-br from-purple-50 to-purple-100 border-purple-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            className: \"p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-purple-600\",\n                                                children: \"إجمالي المبلغ المنفق\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                lineNumber: 379,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-3xl font-bold text-purple-900\",\n                                                children: formatPrice((stats === null || stats === void 0 ? void 0 : stats.totalSpent) || 0)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                lineNumber: 380,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-purple-600 mt-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_Bell_Clock_DollarSign_Eye_Gavel_Heart_LineChart_PieChart_Search_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                        className: \"h-3 w-3 inline ml-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                        lineNumber: 382,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"استثمار ذكي\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                lineNumber: 381,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                        lineNumber: 378,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-12 w-12 bg-purple-200 rounded-full flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_Bell_Clock_DollarSign_Eye_Gavel_Heart_LineChart_PieChart_Search_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            className: \"h-6 w-6 text-purple-600\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                            lineNumber: 387,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                        lineNumber: 386,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                lineNumber: 377,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                            lineNumber: 376,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                        lineNumber: 375,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        className: \"bg-gradient-to-br from-orange-50 to-orange-100 border-orange-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            className: \"p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-orange-600\",\n                                                children: \"المزادات المحفوظة\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                lineNumber: 397,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-3xl font-bold text-orange-900\",\n                                                children: (stats === null || stats === void 0 ? void 0 : stats.savedAuctions) || 0\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                lineNumber: 398,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-orange-600 mt-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_Bell_Clock_DollarSign_Eye_Gavel_Heart_LineChart_PieChart_Search_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                        className: \"h-3 w-3 inline ml-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                        lineNumber: 400,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"مفضلة\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                lineNumber: 399,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                        lineNumber: 396,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-12 w-12 bg-orange-200 rounded-full flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_Bell_Clock_DollarSign_Eye_Gavel_Heart_LineChart_PieChart_Search_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                            className: \"h-6 w-6 text-orange-600\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                            lineNumber: 405,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                        lineNumber: 404,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                lineNumber: 395,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                            lineNumber: 394,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                        lineNumber: 393,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                lineNumber: 338,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_Bell_Clock_DollarSign_Eye_Gavel_Heart_LineChart_PieChart_Search_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                className: \"h-5 w-5 text-blue-600\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                lineNumber: 418,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"نشاط المزايدات الشهري\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                        lineNumber: 417,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                        children: \"عدد المزايدات والانتصارات خلال الأشهر الماضية\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                        lineNumber: 421,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                lineNumber: 416,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_20__.ResponsiveContainer, {\n                                    width: \"100%\",\n                                    height: 300,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_21__.AreaChart, {\n                                        data: chartData.biddingActivity,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_22__.CartesianGrid, {\n                                                strokeDasharray: \"3 3\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                lineNumber: 428,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_23__.XAxis, {\n                                                dataKey: \"month\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                lineNumber: 429,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_24__.YAxis, {}, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                lineNumber: 430,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_25__.Tooltip, {}, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                lineNumber: 431,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_26__.Legend, {}, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                lineNumber: 432,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_27__.Area, {\n                                                type: \"monotone\",\n                                                dataKey: \"bids\",\n                                                stackId: \"1\",\n                                                stroke: COLORS.primary,\n                                                fill: COLORS.primary,\n                                                fillOpacity: 0.6,\n                                                name: \"المزايدات\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                lineNumber: 433,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_27__.Area, {\n                                                type: \"monotone\",\n                                                dataKey: \"wins\",\n                                                stackId: \"2\",\n                                                stroke: COLORS.success,\n                                                fill: COLORS.success,\n                                                fillOpacity: 0.8,\n                                                name: \"الانتصارات\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                lineNumber: 442,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                        lineNumber: 427,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                    lineNumber: 426,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                lineNumber: 425,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                        lineNumber: 415,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_Bell_Clock_DollarSign_Eye_Gavel_Heart_LineChart_PieChart_Search_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                className: \"h-5 w-5 text-purple-600\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                lineNumber: 460,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"الإنفاق حسب الفئة\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                        lineNumber: 459,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                        children: \"توزيع إنفاقك على الفئات المختلفة\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                        lineNumber: 463,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                lineNumber: 458,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_20__.ResponsiveContainer, {\n                                    width: \"100%\",\n                                    height: 300,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_29__.PieChart, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_30__.Pie, {\n                                                data: chartData.categorySpending,\n                                                cx: \"50%\",\n                                                cy: \"50%\",\n                                                labelLine: false,\n                                                label: (param)=>{\n                                                    let { name, percent } = param;\n                                                    return \"\".concat(name, \" \").concat((percent * 100).toFixed(0), \"%\");\n                                                },\n                                                outerRadius: 80,\n                                                fill: \"#8884d8\",\n                                                dataKey: \"value\",\n                                                children: chartData.categorySpending.map((entry, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_31__.Cell, {\n                                                        fill: entry.color\n                                                    }, \"cell-\".concat(index), false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                        lineNumber: 481,\n                                                        columnNumber: 23\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                lineNumber: 470,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_25__.Tooltip, {\n                                                formatter: (value)=>formatPrice(value)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                lineNumber: 484,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                        lineNumber: 469,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                    lineNumber: 468,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                lineNumber: 467,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                        lineNumber: 457,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                lineNumber: 413,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_Bell_Clock_DollarSign_Eye_Gavel_Heart_LineChart_PieChart_Search_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_32__[\"default\"], {\n                                                className: \"h-5 w-5 text-green-600\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                lineNumber: 497,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"الأداء الشهري\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                        lineNumber: 496,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                        children: \"المبلغ المنفق والمدخر شهرياً\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                        lineNumber: 500,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                lineNumber: 495,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_20__.ResponsiveContainer, {\n                                    width: \"100%\",\n                                    height: 300,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_33__.BarChart, {\n                                        data: chartData.monthlyPerformance,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_22__.CartesianGrid, {\n                                                strokeDasharray: \"3 3\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                lineNumber: 507,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_23__.XAxis, {\n                                                dataKey: \"month\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                lineNumber: 508,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_24__.YAxis, {}, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                lineNumber: 509,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_25__.Tooltip, {\n                                                formatter: (value)=>formatPrice(value)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                lineNumber: 510,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_26__.Legend, {}, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                lineNumber: 511,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_34__.Bar, {\n                                                dataKey: \"spent\",\n                                                fill: COLORS.danger,\n                                                name: \"المنفق\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                lineNumber: 512,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_34__.Bar, {\n                                                dataKey: \"saved\",\n                                                fill: COLORS.success,\n                                                name: \"المدخر\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                lineNumber: 513,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                        lineNumber: 506,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                    lineNumber: 505,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                lineNumber: 504,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                        lineNumber: 494,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_Bell_Clock_DollarSign_Eye_Gavel_Heart_LineChart_PieChart_Search_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                className: \"h-5 w-5 text-yellow-600\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                lineNumber: 523,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"نسبة النجاح\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                        lineNumber: 522,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                        children: \"نسبة الفوز إلى الخسارة في المزايدات\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                        lineNumber: 526,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                lineNumber: 521,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_20__.ResponsiveContainer, {\n                                        width: \"100%\",\n                                        height: 300,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_29__.PieChart, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_30__.Pie, {\n                                                    data: chartData.winLossRatio,\n                                                    cx: \"50%\",\n                                                    cy: \"50%\",\n                                                    innerRadius: 60,\n                                                    outerRadius: 100,\n                                                    paddingAngle: 5,\n                                                    dataKey: \"value\",\n                                                    children: chartData.winLossRatio.map((entry, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_31__.Cell, {\n                                                            fill: entry.color\n                                                        }, \"cell-\".concat(index), false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                            lineNumber: 543,\n                                                            columnNumber: 23\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                    lineNumber: 533,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_25__.Tooltip, {\n                                                    formatter: (value)=>\"\".concat(value, \"%\")\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                    lineNumber: 546,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_26__.Legend, {}, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                    lineNumber: 547,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                            lineNumber: 532,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                        lineNumber: 531,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-4 flex justify-center gap-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-2xl font-bold text-green-600\",\n                                                        children: [\n                                                            ((_chartData_winLossRatio_ = chartData.winLossRatio[0]) === null || _chartData_winLossRatio_ === void 0 ? void 0 : _chartData_winLossRatio_.value) || 0,\n                                                            \"%\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                        lineNumber: 552,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: \"معدل النجاح\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                        lineNumber: 553,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                lineNumber: 551,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-2xl font-bold text-red-600\",\n                                                        children: [\n                                                            ((_chartData_winLossRatio_1 = chartData.winLossRatio[1]) === null || _chartData_winLossRatio_1 === void 0 ? void 0 : _chartData_winLossRatio_1.value) || 0,\n                                                            \"%\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                        lineNumber: 556,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: \"معدل الخسارة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                        lineNumber: 557,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                lineNumber: 555,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                        lineNumber: 550,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                lineNumber: 530,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                        lineNumber: 520,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                lineNumber: 492,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_Bell_Clock_DollarSign_Eye_Gavel_Heart_LineChart_PieChart_Search_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"h-5 w-5 text-blue-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                        lineNumber: 571,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"مزايداتي الحديثة\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                lineNumber: 570,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                variant: \"ghost\",\n                                                size: \"sm\",\n                                                onClick: ()=>router.push(\"/user/my-bids\"),\n                                                children: [\n                                                    \"عرض الكل\",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_Bell_Clock_DollarSign_Eye_Gavel_Heart_LineChart_PieChart_Search_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_35__[\"default\"], {\n                                                        className: \"h-4 w-4 mr-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                        lineNumber: 580,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                lineNumber: 574,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                        lineNumber: 569,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                        children: \"آخر المزايدات التي شاركت فيها\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                        lineNumber: 583,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                lineNumber: 568,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                children: recentBids.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3\",\n                                    children: recentBids.slice(0, 3).map((bid)=>{\n                                        const status = getBidStatus(bid);\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-medium text-gray-900\",\n                                                            children: bid.auction.title\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                            lineNumber: 595,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-4 mt-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-gray-600\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_Bell_Clock_DollarSign_Eye_Gavel_Heart_LineChart_PieChart_Search_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_36__[\"default\"], {\n                                                                            className: \"h-4 w-4 inline ml-1\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                                            lineNumber: 598,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        formatTimeRemaining(bid.auction.endTime)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                                    lineNumber: 597,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-gray-600\",\n                                                                    children: [\n                                                                        \"مزايدتي: \",\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"font-medium\",\n                                                                            children: formatPrice(bid.amount)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                                            lineNumber: 602,\n                                                                            columnNumber: 40\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                                    lineNumber: 601,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                            lineNumber: 596,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-gray-500 mt-1\",\n                                                            children: formatRelativeTime(bid.placedAt)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                            lineNumber: 605,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                    lineNumber: 594,\n                                                    columnNumber: 25\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex flex-col items-end gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                            variant: status.variant,\n                                                            className: status.color,\n                                                            children: status.label\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                            lineNumber: 610,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                            size: \"sm\",\n                                                            variant: \"outline\",\n                                                            onClick: ()=>router.push(\"/auctions/\".concat(bid.auction._id)),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_Bell_Clock_DollarSign_Eye_Gavel_Heart_LineChart_PieChart_Search_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_37__[\"default\"], {\n                                                                    className: \"h-4 w-4 ml-1\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                                    lineNumber: 618,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                \"عرض\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                            lineNumber: 613,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                    lineNumber: 609,\n                                                    columnNumber: 25\n                                                }, this)\n                                            ]\n                                        }, bid._id, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                            lineNumber: 593,\n                                            columnNumber: 23\n                                        }, this);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                    lineNumber: 589,\n                                    columnNumber: 17\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center py-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_Bell_Clock_DollarSign_Eye_Gavel_Heart_LineChart_PieChart_Search_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"h-12 w-12 text-gray-400 mx-auto mb-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                            lineNumber: 628,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-medium text-gray-900 mb-2\",\n                                            children: \"لا توجد مزايدات حديثة\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                            lineNumber: 629,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 mb-4\",\n                                            children: \"ابدأ بالمشاركة في المزادات المتاحة\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                            lineNumber: 630,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            onClick: ()=>router.push(\"/user/auctions\"),\n                                            children: \"استكشاف المزادات\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                            lineNumber: 631,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                    lineNumber: 627,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                lineNumber: 587,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                        lineNumber: 567,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_Bell_Clock_DollarSign_Eye_Gavel_Heart_LineChart_PieChart_Search_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"h-5 w-5 text-orange-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                        lineNumber: 644,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"التنبيهات\",\n                                                    notifications.filter((n)=>!n.read).length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                        variant: \"destructive\",\n                                                        className: \"text-xs\",\n                                                        children: notifications.filter((n)=>!n.read).length\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                        lineNumber: 647,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                lineNumber: 643,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                variant: \"ghost\",\n                                                size: \"sm\",\n                                                onClick: ()=>router.push(\"/user/notifications\"),\n                                                children: [\n                                                    \"عرض الكل\",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_Bell_Clock_DollarSign_Eye_Gavel_Heart_LineChart_PieChart_Search_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_35__[\"default\"], {\n                                                        className: \"h-4 w-4 mr-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                        lineNumber: 658,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                lineNumber: 652,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                        lineNumber: 642,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                        children: \"آخر التحديثات والإشعارات\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                        lineNumber: 661,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                lineNumber: 641,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                children: notifications.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3\",\n                                    children: notifications.slice(0, 3).map((notification)=>{\n                                        const getNotificationStyle = (type)=>{\n                                            switch(type){\n                                                case \"auction_won\":\n                                                    return \"bg-green-50 border-green-200 text-green-800\";\n                                                case \"auction_ending\":\n                                                    return \"bg-yellow-50 border-yellow-200 text-yellow-800\";\n                                                case \"new_auction\":\n                                                    return \"bg-blue-50 border-blue-200 text-blue-800\";\n                                                case \"bid_outbid\":\n                                                    return \"bg-red-50 border-red-200 text-red-800\";\n                                                default:\n                                                    return \"bg-gray-50 border-gray-200 text-gray-800\";\n                                            }\n                                        };\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-3 border rounded-lg \".concat(getNotificationStyle(notification.type), \" \").concat(!notification.read ? \"ring-2 ring-blue-200\" : \"\"),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm font-medium\",\n                                                                children: notification.title\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                                lineNumber: 691,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs mt-1\",\n                                                                children: notification.message\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                                lineNumber: 692,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs opacity-75 mt-2\",\n                                                                children: formatRelativeTime(notification.createdAt)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                                lineNumber: 693,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                        lineNumber: 690,\n                                                        columnNumber: 27\n                                                    }, this),\n                                                    !notification.read && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-2 h-2 bg-blue-600 rounded-full mt-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                        lineNumber: 698,\n                                                        columnNumber: 29\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                lineNumber: 689,\n                                                columnNumber: 25\n                                            }, this)\n                                        }, notification._id, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                            lineNumber: 685,\n                                            columnNumber: 23\n                                        }, this);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                    lineNumber: 667,\n                                    columnNumber: 17\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center py-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_Bell_Clock_DollarSign_Eye_Gavel_Heart_LineChart_PieChart_Search_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"h-12 w-12 text-gray-400 mx-auto mb-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                            lineNumber: 707,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-medium text-gray-900 mb-2\",\n                                            children: \"لا توجد تنبيهات\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                            lineNumber: 708,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600\",\n                                            children: \"ستظهر هنا التحديثات المهمة\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                            lineNumber: 709,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                    lineNumber: 706,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                lineNumber: 665,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                        lineNumber: 640,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                lineNumber: 565,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_Bell_Clock_DollarSign_Eye_Gavel_Heart_LineChart_PieChart_Search_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_38__[\"default\"], {\n                                                className: \"h-5 w-5 text-purple-600\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                lineNumber: 721,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"مزادات مقترحة لك\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                        lineNumber: 720,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        onClick: ()=>router.push(\"/user/auctions\"),\n                                        children: [\n                                            \"عرض المزيد\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_Bell_Clock_DollarSign_Eye_Gavel_Heart_LineChart_PieChart_Search_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_35__[\"default\"], {\n                                                className: \"h-4 w-4 mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                lineNumber: 730,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                        lineNumber: 724,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                lineNumber: 719,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                children: \"مزادات قد تهمك بناءً على اهتماماتك السابقة\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                lineNumber: 733,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                        lineNumber: 718,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        children: recentAuctions.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\",\n                            children: recentAuctions.slice(0, 6).map((auction)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"article\", {\n                                    className: \"border rounded-lg p-4 hover:shadow-lg transition-all duration-200 hover:border-blue-300 cursor-pointer\",\n                                    onClick: ()=>router.push(\"/auctions/\".concat(auction._id)),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"aspect-video bg-gradient-to-br from-gray-100 to-gray-200 rounded mb-3 flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_Bell_Clock_DollarSign_Eye_Gavel_Heart_LineChart_PieChart_Search_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_39__[\"default\"], {\n                                                className: \"h-8 w-8 text-gray-400\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                lineNumber: 747,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                            lineNumber: 746,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-medium text-gray-900 line-clamp-2\",\n                                                    children: auction.title\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                    lineNumber: 751,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-gray-600\",\n                                                                    children: [\n                                                                        \"السعر الحالي: \",\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"font-medium text-green-600\",\n                                                                            children: formatPrice(auction.currentBid)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                                            lineNumber: 756,\n                                                                            columnNumber: 43\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                                    lineNumber: 755,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-gray-500\",\n                                                                    children: [\n                                                                        \"يبدأ من: \",\n                                                                        formatPrice(auction.startingBid)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                                    lineNumber: 758,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                            lineNumber: 754,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                            variant: \"outline\",\n                                                            className: \"text-xs\",\n                                                            children: auction.category\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                            lineNumber: 762,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                    lineNumber: 753,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between pt-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-1 text-xs text-gray-500\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_Bell_Clock_DollarSign_Eye_Gavel_Heart_LineChart_PieChart_Search_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_36__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                                    lineNumber: 769,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                formatTimeRemaining(auction.endTime)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                            lineNumber: 768,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-1 text-xs text-gray-500\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_Bell_Clock_DollarSign_Eye_Gavel_Heart_LineChart_PieChart_Search_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                                    lineNumber: 773,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                auction.bidsCount,\n                                                                \" مزايدة\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                            lineNumber: 772,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                    lineNumber: 767,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    size: \"sm\",\n                                                    className: \"w-full mt-3\",\n                                                    onClick: (e)=>{\n                                                        e.stopPropagation();\n                                                        router.push(\"/auctions/\".concat(auction._id));\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_Bell_Clock_DollarSign_Eye_Gavel_Heart_LineChart_PieChart_Search_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_37__[\"default\"], {\n                                                            className: \"h-4 w-4 ml-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                            lineNumber: 786,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        \"شاهد التفاصيل\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                    lineNumber: 778,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                            lineNumber: 750,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, auction._id, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                    lineNumber: 741,\n                                    columnNumber: 19\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                            lineNumber: 739,\n                            columnNumber: 15\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center py-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_Bell_Clock_DollarSign_Eye_Gavel_Heart_LineChart_PieChart_Search_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_38__[\"default\"], {\n                                    className: \"h-16 w-16 text-gray-400 mx-auto mb-4\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                    lineNumber: 795,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-medium text-gray-900 mb-2\",\n                                    children: \"لا توجد مزادات متاحة حالياً\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                    lineNumber: 796,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 mb-6\",\n                                    children: \"تحقق مرة أخرى لاحقاً أو استكشف الفئات المختلفة\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                    lineNumber: 797,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    onClick: ()=>router.push(\"/user/auctions\"),\n                                    children: \"استكشاف المزادات\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                    lineNumber: 798,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                            lineNumber: 794,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                        lineNumber: 737,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                lineNumber: 717,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n        lineNumber: 270,\n        columnNumber: 5\n    }, this);\n}\n_s(UserDashboard, \"m/Vw8DQQyp2mq6xEF53eRK4Izqc=\", false, function() {\n    return [\n        _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_5__.useToast,\n        next_navigation__WEBPACK_IMPORTED_MODULE_6__.useRouter,\n        _contexts_CurrencyContext__WEBPACK_IMPORTED_MODULE_8__.useCurrency\n    ];\n});\n_c = UserDashboard;\nvar _c;\n$RefreshReg$(_c, \"UserDashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FwcC91c2VyL2Rhc2hib2FyZC9wYWdlLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUUyQztBQUNxRDtBQUNqRDtBQUNGO0FBQ087QUFFVDtBQUNoQjtBQUM2QjtBQUNRO0FBc0IzQztBQWlCSjtBQThDRixTQUFTNEM7UUFtTENDLGVBb1I2Q0MsMEJBSUZBOztJQTFjbEUsTUFBTSxDQUFDQyxPQUFPQyxTQUFTLEdBQUcvQywrQ0FBUUEsQ0FBd0I7SUFDMUQsTUFBTSxDQUFDZ0QsWUFBWUMsY0FBYyxHQUFHakQsK0NBQVFBLENBQWMsRUFBRTtJQUM1RCxNQUFNLENBQUNrRCxnQkFBZ0JDLGtCQUFrQixHQUFHbkQsK0NBQVFBLENBQWtCLEVBQUU7SUFDeEUsTUFBTSxDQUFDb0QsZUFBZUMsaUJBQWlCLEdBQUdyRCwrQ0FBUUEsQ0FBaUIsRUFBRTtJQUNyRSxNQUFNLENBQUNzRCxTQUFTQyxXQUFXLEdBQUd2RCwrQ0FBUUEsQ0FBQztJQUN2QyxNQUFNLENBQUM0QyxNQUFNWSxRQUFRLEdBQUd4RCwrQ0FBUUEsQ0FBTTtJQUN0QyxNQUFNLENBQUM2QyxXQUFXWSxhQUFhLEdBQUd6RCwrQ0FBUUEsQ0FBTTtRQUM5QzBELGlCQUFpQixFQUFFO1FBQ25CQyxrQkFBa0IsRUFBRTtRQUNwQkMsb0JBQW9CLEVBQUU7UUFDdEJDLGNBQWMsRUFBRTtJQUNsQjtJQUNBLE1BQU0sRUFBRUMsS0FBSyxFQUFFLEdBQUd0RCxrRUFBUUE7SUFDMUIsTUFBTXVELFNBQVN0RCwwREFBU0E7SUFFeEIsZUFBZTtJQUNmLE1BQU11RCxTQUFTO1FBQ2JDLFNBQVM7UUFDVEMsV0FBVztRQUNYQyxTQUFTO1FBQ1RDLFNBQVM7UUFDVEMsUUFBUTtRQUNSQyxNQUFNO0lBQ1I7SUFFQSxNQUFNQyxhQUFhO1FBQUM7UUFBVztRQUFXO1FBQVc7UUFBVztRQUFXO0tBQVU7SUFFckZ4RSxnREFBU0EsQ0FBQztRQUNSLE1BQU15RSxXQUFXQyxhQUFhQyxPQUFPLENBQUM7UUFDdEMsSUFBSUYsVUFBVTtZQUNaaEIsUUFBUW1CLEtBQUtDLEtBQUssQ0FBQ0o7UUFDckI7UUFFQUs7SUFDRixHQUFHLEVBQUU7SUFFTCxNQUFNQSxvQkFBb0I7UUFDeEIsSUFBSTtZQUNGdEIsV0FBVztZQUVYLGtCQUFrQjtZQUNsQixNQUFNdUIsZ0JBQWdCLE1BQU1wRSxnREFBR0EsQ0FBQ3FFLEdBQUcsQ0FBQztZQUNwQyxJQUFJRCxjQUFjRSxJQUFJLENBQUNiLE9BQU8sRUFBRTtnQkFDOUJwQixTQUFTK0IsY0FBY0UsSUFBSSxDQUFDQSxJQUFJO1lBQ2xDO1lBRUEsbUJBQW1CO1lBQ25CLE1BQU1DLGVBQWUsTUFBTXZFLGdEQUFHQSxDQUFDcUUsR0FBRyxDQUFDO1lBQ25DLElBQUlFLGFBQWFELElBQUksQ0FBQ2IsT0FBTyxFQUFFO2dCQUM3QmxCLGNBQWNnQyxhQUFhRCxJQUFJLENBQUNBLElBQUksQ0FBQ0UsSUFBSTtZQUMzQztZQUVBLHFDQUFxQztZQUNyQyxNQUFNQyxtQkFBbUIsTUFBTXpFLGdEQUFHQSxDQUFDcUUsR0FBRyxDQUFDO1lBQ3ZDLElBQUlJLGlCQUFpQkgsSUFBSSxDQUFDYixPQUFPLEVBQUU7Z0JBQ2pDaEIsa0JBQWtCZ0MsaUJBQWlCSCxJQUFJLENBQUNBLElBQUksQ0FBQ0ksUUFBUTtZQUN2RDtZQUVBLHFCQUFxQjtZQUNyQixNQUFNQyx3QkFBd0IsTUFBTTNFLGdEQUFHQSxDQUFDcUUsR0FBRyxDQUFDO1lBQzVDLElBQUlNLHNCQUFzQkwsSUFBSSxDQUFDYixPQUFPLEVBQUU7Z0JBQ3RDZCxpQkFBaUJnQyxzQkFBc0JMLElBQUksQ0FBQ0EsSUFBSSxDQUFDNUIsYUFBYTtZQUNoRTtZQUVBLGtCQUFrQjtZQUNsQixNQUFNa0MsZ0JBQWdCLE1BQU01RSxnREFBR0EsQ0FBQ3FFLEdBQUcsQ0FBQztZQUNwQyxJQUFJTyxjQUFjTixJQUFJLENBQUNiLE9BQU8sRUFBRTtnQkFDOUJWLGFBQWE2QixjQUFjTixJQUFJLENBQUNBLElBQUk7WUFDdEM7UUFFRixFQUFFLE9BQU9PLE9BQU87WUFDZEMsUUFBUUQsS0FBSyxDQUFDLGlDQUFpQ0E7WUFDL0MsMEJBQTBCO1lBQzFCeEMsU0FBUztnQkFDUDBDLFlBQVk7Z0JBQ1pDLGFBQWE7Z0JBQ2JDLFlBQVk7Z0JBQ1pDLGVBQWU7Z0JBQ2ZDLFdBQVc7Z0JBQ1hDLGFBQWE7WUFDZjtZQUNBN0MsY0FBYyxFQUFFO1lBQ2hCRSxrQkFBa0IsRUFBRTtZQUNwQkUsaUJBQWlCLEVBQUU7WUFFbkIsb0JBQW9CO1lBQ3BCSSxhQUFhO2dCQUNYQyxpQkFBaUI7b0JBQ2Y7d0JBQUVxQyxPQUFPO3dCQUFTYixNQUFNO3dCQUFJYyxNQUFNO29CQUFFO29CQUNwQzt3QkFBRUQsT0FBTzt3QkFBVWIsTUFBTTt3QkFBSWMsTUFBTTtvQkFBRTtvQkFDckM7d0JBQUVELE9BQU87d0JBQVFiLE1BQU07d0JBQUljLE1BQU07b0JBQUU7b0JBQ25DO3dCQUFFRCxPQUFPO3dCQUFTYixNQUFNO3dCQUFJYyxNQUFNO29CQUFFO29CQUNwQzt3QkFBRUQsT0FBTzt3QkFBUWIsTUFBTTt3QkFBSWMsTUFBTTtvQkFBRTtvQkFDbkM7d0JBQUVELE9BQU87d0JBQVNiLE1BQU07d0JBQUljLE1BQU07b0JBQUU7aUJBQ3JDO2dCQUNEckMsa0JBQWtCO29CQUNoQjt3QkFBRXNDLE1BQU07d0JBQWNDLE9BQU87d0JBQU9DLE9BQU81QixVQUFVLENBQUMsRUFBRTtvQkFBQztvQkFDekQ7d0JBQUUwQixNQUFNO3dCQUFVQyxPQUFPO3dCQUFPQyxPQUFPNUIsVUFBVSxDQUFDLEVBQUU7b0JBQUM7b0JBQ3JEO3dCQUFFMEIsTUFBTTt3QkFBUUMsT0FBTzt3QkFBTUMsT0FBTzVCLFVBQVUsQ0FBQyxFQUFFO29CQUFDO29CQUNsRDt3QkFBRTBCLE1BQU07d0JBQVdDLE9BQU87d0JBQU9DLE9BQU81QixVQUFVLENBQUMsRUFBRTtvQkFBQztvQkFDdEQ7d0JBQUUwQixNQUFNO3dCQUFRQyxPQUFPO3dCQUFNQyxPQUFPNUIsVUFBVSxDQUFDLEVBQUU7b0JBQUM7aUJBQ25EO2dCQUNEWCxvQkFBb0I7b0JBQ2xCO3dCQUFFbUMsT0FBTzt3QkFBU0ssT0FBTzt3QkFBTUMsT0FBTztvQkFBSztvQkFDM0M7d0JBQUVOLE9BQU87d0JBQVVLLE9BQU87d0JBQU9DLE9BQU87b0JBQUs7b0JBQzdDO3dCQUFFTixPQUFPO3dCQUFRSyxPQUFPO3dCQUFNQyxPQUFPO29CQUFLO29CQUMxQzt3QkFBRU4sT0FBTzt3QkFBU0ssT0FBTzt3QkFBT0MsT0FBTztvQkFBSztvQkFDNUM7d0JBQUVOLE9BQU87d0JBQVFLLE9BQU87d0JBQU1DLE9BQU87b0JBQUs7b0JBQzFDO3dCQUFFTixPQUFPO3dCQUFTSyxPQUFPO3dCQUFPQyxPQUFPO29CQUFLO2lCQUM3QztnQkFDRHhDLGNBQWM7b0JBQ1o7d0JBQUVvQyxNQUFNO3dCQUFPQyxPQUFPO3dCQUFJQyxPQUFPbkMsT0FBT0csT0FBTztvQkFBQztvQkFDaEQ7d0JBQUU4QixNQUFNO3dCQUFTQyxPQUFPO3dCQUFJQyxPQUFPbkMsT0FBT0ssTUFBTTtvQkFBQztpQkFDbEQ7WUFDSDtRQUNGLFNBQVU7WUFDUmQsV0FBVztRQUNiO0lBQ0Y7SUFFQSxNQUFNLEVBQUUrQyxZQUFZLEVBQUUsR0FBRzNGLHNFQUFXQTtJQUVwQyxNQUFNNEYsY0FBYyxDQUFDQztRQUNuQixPQUFPRixhQUFhRTtJQUN0QjtJQUVBLE1BQU1DLHNCQUFzQixDQUFDQztRQUMzQixNQUFNQyxNQUFNLElBQUlDO1FBQ2hCLE1BQU1DLE1BQU0sSUFBSUQsS0FBS0Y7UUFDckIsTUFBTUksT0FBT0QsSUFBSUUsT0FBTyxLQUFLSixJQUFJSSxPQUFPO1FBRXhDLElBQUlELFFBQVEsR0FBRyxPQUFPO1FBRXRCLE1BQU1FLE9BQU9DLEtBQUtDLEtBQUssQ0FBQ0osT0FBUSxRQUFPLEtBQUssS0FBSyxFQUFDO1FBQ2xELE1BQU1LLFFBQVFGLEtBQUtDLEtBQUssQ0FBQyxPQUFTLFFBQU8sS0FBSyxLQUFLLEVBQUMsSUFBTyxRQUFPLEtBQUssRUFBQztRQUN4RSxNQUFNRSxVQUFVSCxLQUFLQyxLQUFLLENBQUMsT0FBUyxRQUFPLEtBQUssRUFBQyxJQUFPLFFBQU8sRUFBQztRQUVoRSxJQUFJRixPQUFPLEdBQUcsT0FBTyxHQUFRLE9BQUxBLE1BQUs7UUFDN0IsSUFBSUcsUUFBUSxHQUFHLE9BQU8sR0FBUyxPQUFOQSxPQUFNO1FBQy9CLE9BQU8sR0FBVyxPQUFSQyxTQUFRO0lBQ3BCO0lBRUEsTUFBTUMscUJBQXFCLENBQUNDO1FBQzFCLE1BQU1YLE1BQU0sSUFBSUM7UUFDaEIsTUFBTVcsT0FBTyxJQUFJWCxLQUFLVTtRQUN0QixNQUFNUixPQUFPSCxJQUFJSSxPQUFPLEtBQUtRLEtBQUtSLE9BQU87UUFFekMsTUFBTUssVUFBVUgsS0FBS0MsS0FBSyxDQUFDSixPQUFRLFFBQU8sRUFBQztRQUMzQyxNQUFNSyxRQUFRRixLQUFLQyxLQUFLLENBQUNKLE9BQVEsUUFBTyxLQUFLLEVBQUM7UUFDOUMsTUFBTUUsT0FBT0MsS0FBS0MsS0FBSyxDQUFDSixPQUFRLFFBQU8sS0FBSyxLQUFLLEVBQUM7UUFFbEQsSUFBSUUsT0FBTyxHQUFHLE9BQU8sT0FBWSxPQUFMQSxNQUFLO1FBQ2pDLElBQUlHLFFBQVEsR0FBRyxPQUFPLE9BQWEsT0FBTkEsT0FBTTtRQUNuQyxJQUFJQyxVQUFVLEdBQUcsT0FBTyxPQUFlLE9BQVJBLFNBQVE7UUFDdkMsT0FBTztJQUNUO0lBRUEsTUFBTUksZUFBZSxDQUFDQztRQUNwQixJQUFJQSxJQUFJQyxTQUFTLEVBQUUsT0FBTztZQUFFQyxPQUFPO1lBQWVDLFNBQVM7WUFBb0J6QixPQUFPO1FBQWlCO1FBQ3ZHLElBQUlzQixJQUFJSSxPQUFPLENBQUNDLE1BQU0sS0FBSyxVQUFVLE9BQU87WUFBRUgsT0FBTztZQUFVQyxTQUFTO1lBQXNCekIsT0FBTztRQUFnQjtRQUNySCxPQUFPO1lBQUV3QixPQUFPO1lBQWNDLFNBQVM7WUFBb0J6QixPQUFPO1FBQWdCO0lBQ3BGO0lBRUEsSUFBSTdDLFNBQVM7UUFDWCxxQkFDRSw4REFBQ3lFO1lBQUlDLFdBQVU7c0JBQ2IsNEVBQUNDOzBCQUFFOzs7Ozs7Ozs7OztJQUdUO0lBRUEscUJBQ0UsOERBQUNGO1FBQUlDLFdBQVU7OzBCQUVYLDhEQUFDRTtnQkFBT0YsV0FBVTswQkFDaEIsNEVBQUNEO29CQUFJQyxXQUFVOztzQ0FDYiw4REFBQ0Q7OzhDQUNDLDhEQUFDSTtvQ0FBR0gsV0FBVTs7d0NBQXFCO3dDQUN4QnBGLENBQUFBLGlCQUFBQSw0QkFBQUEsZ0JBQUFBLEtBQU13RixPQUFPLGNBQWJ4RixvQ0FBQUEsY0FBZXlGLFFBQVEsS0FBSTs7Ozs7Ozs4Q0FFdEMsOERBQUNKO29DQUFFRCxXQUFVOzhDQUFxQjs7Ozs7Ozs7Ozs7O3NDQUlwQyw4REFBQ0Q7NEJBQUlDLFdBQVU7OzhDQUNiLDhEQUFDRDtvQ0FBSUMsV0FBVTs7c0RBQ2IsOERBQUNEOzRDQUFJQyxXQUFVOztnREFBc0JsRixDQUFBQSxrQkFBQUEsNEJBQUFBLE1BQU9nRCxXQUFXLEtBQUk7Z0RBQUU7Ozs7Ozs7c0RBQzdELDhEQUFDaUM7NENBQUlDLFdBQVU7c0RBQXdCOzs7Ozs7Ozs7Ozs7OENBRXpDLDhEQUFDRDtvQ0FBSUMsV0FBVTs7c0RBQ2IsOERBQUNEOzRDQUFJQyxXQUFVO3NEQUFzQmxGLENBQUFBLGtCQUFBQSw0QkFBQUEsTUFBTytDLFNBQVMsS0FBSTs7Ozs7O3NEQUN6RCw4REFBQ2tDOzRDQUFJQyxXQUFVO3NEQUF3Qjs7Ozs7Ozs7Ozs7OzhDQUV6Qyw4REFBQ0Q7b0NBQUlDLFdBQVU7OENBQ2IsNEVBQUNwSCwwRUFBZ0JBO3dDQUFDMEgsV0FBVzt3Q0FBT04sV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFPdEQsOERBQUNEO2dCQUFJQyxXQUFVOztrQ0FDYiw4REFBQzFILHlEQUFNQTt3QkFDTDBILFdBQVU7d0JBQ1ZPLE1BQUs7d0JBQ0xDLFNBQVMsSUFBTXpFLE9BQU8wRSxJQUFJLENBQUM7OzBDQUUzQiw4REFBQ3RILDZNQUFNQTtnQ0FBQzZHLFdBQVU7Ozs7Ozs0QkFBaUI7Ozs7Ozs7a0NBR3JDLDhEQUFDMUgseURBQU1BO3dCQUNMc0gsU0FBUTt3QkFDUkksV0FBVTt3QkFDVk8sTUFBSzt3QkFDTEMsU0FBUyxJQUFNekUsT0FBTzBFLElBQUksQ0FBQzs7MENBRTNCLDhEQUFDM0gsNk1BQVFBO2dDQUFDa0gsV0FBVTs7Ozs7OzRCQUFpQjs7Ozs7OztrQ0FHdkMsOERBQUMxSCx5REFBTUE7d0JBQ0xzSCxTQUFRO3dCQUNSSSxXQUFVO3dCQUNWTyxNQUFLO3dCQUNMQyxTQUFTLElBQU16RSxPQUFPMEUsSUFBSSxDQUFDOzswQ0FFM0IsOERBQUMxSCw2TUFBSUE7Z0NBQUNpSCxXQUFVOzs7Ozs7NEJBQWlCOzs7Ozs7O2tDQUduQyw4REFBQzFILHlEQUFNQTt3QkFDTHNILFNBQVE7d0JBQ1JJLFdBQVU7d0JBQ1ZPLE1BQUs7d0JBQ0xDLFNBQVMsSUFBTXpFLE9BQU8wRSxJQUFJLENBQUM7OzBDQUUzQiw4REFBQ2xILDZNQUFLQTtnQ0FBQ3lHLFdBQVU7Ozs7Ozs0QkFBaUI7Ozs7Ozs7Ozs7Ozs7MEJBTXRDLDhEQUFDRDtnQkFBSUMsV0FBVTs7a0NBQ2IsOERBQUMvSCxxREFBSUE7d0JBQUMrSCxXQUFVO2tDQUNkLDRFQUFDOUgsNERBQVdBOzRCQUFDOEgsV0FBVTtzQ0FDckIsNEVBQUNEO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQ0Q7OzBEQUNDLDhEQUFDRTtnREFBRUQsV0FBVTswREFBb0M7Ozs7OzswREFDakQsOERBQUNDO2dEQUFFRCxXQUFVOzBEQUFvQ2xGLENBQUFBLGtCQUFBQSw0QkFBQUEsTUFBTzJDLFVBQVUsS0FBSTs7Ozs7OzBEQUN0RSw4REFBQ3dDO2dEQUFFRCxXQUFVOztrRUFDWCw4REFBQ3RHLDZNQUFHQTt3REFBQ3NHLFdBQVU7Ozs7OztvREFBd0I7Ozs7Ozs7Ozs7Ozs7a0RBSTNDLDhEQUFDRDt3Q0FBSUMsV0FBVTtrREFDYiw0RUFBQ2xILDZNQUFRQTs0Q0FBQ2tILFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztrQ0FNNUIsOERBQUMvSCxxREFBSUE7d0JBQUMrSCxXQUFVO2tDQUNkLDRFQUFDOUgsNERBQVdBOzRCQUFDOEgsV0FBVTtzQ0FDckIsNEVBQUNEO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQ0Q7OzBEQUNDLDhEQUFDRTtnREFBRUQsV0FBVTswREFBcUM7Ozs7OzswREFDbEQsOERBQUNDO2dEQUFFRCxXQUFVOzBEQUFxQ2xGLENBQUFBLGtCQUFBQSw0QkFBQUEsTUFBTzRDLFdBQVcsS0FBSTs7Ozs7OzBEQUN4RSw4REFBQ3VDO2dEQUFFRCxXQUFVOztrRUFDWCw4REFBQ2hILDZNQUFNQTt3REFBQ2dILFdBQVU7Ozs7OztvREFBd0I7Ozs7Ozs7Ozs7Ozs7a0RBSTlDLDhEQUFDRDt3Q0FBSUMsV0FBVTtrREFDYiw0RUFBQ2hILDZNQUFNQTs0Q0FBQ2dILFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztrQ0FNMUIsOERBQUMvSCxxREFBSUE7d0JBQUMrSCxXQUFVO2tDQUNkLDRFQUFDOUgsNERBQVdBOzRCQUFDOEgsV0FBVTtzQ0FDckIsNEVBQUNEO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQ0Q7OzBEQUNDLDhEQUFDRTtnREFBRUQsV0FBVTswREFBc0M7Ozs7OzswREFDbkQsOERBQUNDO2dEQUFFRCxXQUFVOzBEQUFzQ3pCLFlBQVl6RCxDQUFBQSxrQkFBQUEsNEJBQUFBLE1BQU82QyxVQUFVLEtBQUk7Ozs7OzswREFDcEYsOERBQUNzQztnREFBRUQsV0FBVTs7a0VBQ1gsOERBQUMvRyw2TUFBVUE7d0RBQUMrRyxXQUFVOzs7Ozs7b0RBQXdCOzs7Ozs7Ozs7Ozs7O2tEQUlsRCw4REFBQ0Q7d0NBQUlDLFdBQVU7a0RBQ2IsNEVBQUMxRyw2TUFBVUE7NENBQUMwRyxXQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7a0NBTTlCLDhEQUFDL0gscURBQUlBO3dCQUFDK0gsV0FBVTtrQ0FDZCw0RUFBQzlILDREQUFXQTs0QkFBQzhILFdBQVU7c0NBQ3JCLDRFQUFDRDtnQ0FBSUMsV0FBVTs7a0RBQ2IsOERBQUNEOzswREFDQyw4REFBQ0U7Z0RBQUVELFdBQVU7MERBQXNDOzs7Ozs7MERBQ25ELDhEQUFDQztnREFBRUQsV0FBVTswREFBc0NsRixDQUFBQSxrQkFBQUEsNEJBQUFBLE1BQU84QyxhQUFhLEtBQUk7Ozs7OzswREFDM0UsOERBQUNxQztnREFBRUQsV0FBVTs7a0VBQ1gsOERBQUMzRyw2TUFBS0E7d0RBQUMyRyxXQUFVOzs7Ozs7b0RBQXdCOzs7Ozs7Ozs7Ozs7O2tEQUk3Qyw4REFBQ0Q7d0NBQUlDLFdBQVU7a0RBQ2IsNEVBQUMzRyw2TUFBS0E7NENBQUMyRyxXQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBUTNCLDhEQUFDRDtnQkFBSUMsV0FBVTs7a0NBRWIsOERBQUMvSCxxREFBSUE7OzBDQUNILDhEQUFDRywyREFBVUE7O2tEQUNULDhEQUFDQywwREFBU0E7d0NBQUMySCxXQUFVOzswREFDbkIsOERBQUNuRyw2TUFBU0E7Z0RBQUNtRyxXQUFVOzs7Ozs7NENBQTBCOzs7Ozs7O2tEQUdqRCw4REFBQzdILGdFQUFlQTtrREFBQzs7Ozs7Ozs7Ozs7OzBDQUluQiw4REFBQ0QsNERBQVdBOzBDQUNWLDRFQUFDd0MsNExBQW1CQTtvQ0FBQ2dHLE9BQU07b0NBQU9DLFFBQVE7OENBQ3hDLDRFQUFDN0csa0xBQVNBO3dDQUFDa0QsTUFBTW5DLFVBQVVhLGVBQWU7OzBEQUN4Qyw4REFBQ25CLHNMQUFhQTtnREFBQ3FHLGlCQUFnQjs7Ozs7OzBEQUMvQiw4REFBQ3ZHLDhLQUFLQTtnREFBQ3dHLFNBQVE7Ozs7OzswREFDZiw4REFBQ3ZHLDhLQUFLQTs7Ozs7MERBQ04sOERBQUNFLGdMQUFPQTs7Ozs7MERBQ1IsOERBQUNDLCtLQUFNQTs7Ozs7MERBQ1AsOERBQUNWLDZLQUFJQTtnREFDSCtHLE1BQUs7Z0RBQ0xELFNBQVE7Z0RBQ1JFLFNBQVE7Z0RBQ1JDLFFBQVFoRixPQUFPQyxPQUFPO2dEQUN0QmdGLE1BQU1qRixPQUFPQyxPQUFPO2dEQUNwQmlGLGFBQWE7Z0RBQ2JqRCxNQUFLOzs7Ozs7MERBRVAsOERBQUNsRSw2S0FBSUE7Z0RBQ0grRyxNQUFLO2dEQUNMRCxTQUFRO2dEQUNSRSxTQUFRO2dEQUNSQyxRQUFRaEYsT0FBT0csT0FBTztnREFDdEI4RSxNQUFNakYsT0FBT0csT0FBTztnREFDcEIrRSxhQUFhO2dEQUNiakQsTUFBSzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztrQ0FRZiw4REFBQ2hHLHFEQUFJQTs7MENBQ0gsOERBQUNHLDJEQUFVQTs7a0RBQ1QsOERBQUNDLDBEQUFTQTt3Q0FBQzJILFdBQVU7OzBEQUNuQiw4REFBQ3BHLDZNQUFRQTtnREFBQ29HLFdBQVU7Ozs7Ozs0Q0FBNEI7Ozs7Ozs7a0RBR2xELDhEQUFDN0gsZ0VBQWVBO2tEQUFDOzs7Ozs7Ozs7Ozs7MENBSW5CLDhEQUFDRCw0REFBV0E7MENBQ1YsNEVBQUN3Qyw0TEFBbUJBO29DQUFDZ0csT0FBTTtvQ0FBT0MsUUFBUTs4Q0FDeEMsNEVBQUN6RyxpTEFBZ0JBOzswREFDZiw4REFBQ0MsNEtBQUdBO2dEQUNGNkMsTUFBTW5DLFVBQVVjLGdCQUFnQjtnREFDaEN3RixJQUFHO2dEQUNIQyxJQUFHO2dEQUNIQyxXQUFXO2dEQUNYMUIsT0FBTzt3REFBQyxFQUFFMUIsSUFBSSxFQUFFcUQsT0FBTyxFQUFFOzJEQUFLLEdBQVcsT0FBUnJELE1BQUssS0FBOEIsT0FBM0IsQ0FBQ3FELFVBQVUsR0FBRSxFQUFHQyxPQUFPLENBQUMsSUFBRzs7Z0RBQ3BFQyxhQUFhO2dEQUNiUCxNQUFLO2dEQUNMSixTQUFROzBEQUVQaEcsVUFBVWMsZ0JBQWdCLENBQUM4RixHQUFHLENBQUMsQ0FBQ0MsT0FBWUMsc0JBQzNDLDhEQUFDdkgsNktBQUlBO3dEQUF1QjZHLE1BQU1TLE1BQU12RCxLQUFLO3VEQUFsQyxRQUFjLE9BQU53RDs7Ozs7Ozs7OzswREFHdkIsOERBQUNuSCxnTEFBT0E7Z0RBQUNvSCxXQUFXLENBQUMxRCxRQUFlSyxZQUFZTDs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFRMUQsOERBQUM2QjtnQkFBSUMsV0FBVTs7a0NBRWIsOERBQUMvSCxxREFBSUE7OzBDQUNILDhEQUFDRywyREFBVUE7O2tEQUNULDhEQUFDQywwREFBU0E7d0NBQUMySCxXQUFVOzswREFDbkIsOERBQUNyRyw2TUFBU0E7Z0RBQUNxRyxXQUFVOzs7Ozs7NENBQTJCOzs7Ozs7O2tEQUdsRCw4REFBQzdILGdFQUFlQTtrREFBQzs7Ozs7Ozs7Ozs7OzBDQUluQiw4REFBQ0QsNERBQVdBOzBDQUNWLDRFQUFDd0MsNExBQW1CQTtvQ0FBQ2dHLE9BQU07b0NBQU9DLFFBQVE7OENBQ3hDLDRFQUFDM0csaUxBQVFBO3dDQUFDZ0QsTUFBTW5DLFVBQVVlLGtCQUFrQjs7MERBQzFDLDhEQUFDckIsc0xBQWFBO2dEQUFDcUcsaUJBQWdCOzs7Ozs7MERBQy9CLDhEQUFDdkcsOEtBQUtBO2dEQUFDd0csU0FBUTs7Ozs7OzBEQUNmLDhEQUFDdkcsOEtBQUtBOzs7OzswREFDTiw4REFBQ0UsZ0xBQU9BO2dEQUFDb0gsV0FBVyxDQUFDMUQsUUFBZUssWUFBWUw7Ozs7OzswREFDaEQsOERBQUN6RCwrS0FBTUE7Ozs7OzBEQUNQLDhEQUFDUiw0S0FBR0E7Z0RBQUM0RyxTQUFRO2dEQUFRSSxNQUFNakYsT0FBT0ssTUFBTTtnREFBRTRCLE1BQUs7Ozs7OzswREFDL0MsOERBQUNoRSw0S0FBR0E7Z0RBQUM0RyxTQUFRO2dEQUFRSSxNQUFNakYsT0FBT0csT0FBTztnREFBRThCLE1BQUs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7a0NBT3hELDhEQUFDaEcscURBQUlBOzswQ0FDSCw4REFBQ0csMkRBQVVBOztrREFDVCw4REFBQ0MsMERBQVNBO3dDQUFDMkgsV0FBVTs7MERBQ25CLDhEQUFDaEgsNk1BQU1BO2dEQUFDZ0gsV0FBVTs7Ozs7OzRDQUE0Qjs7Ozs7OztrREFHaEQsOERBQUM3SCxnRUFBZUE7a0RBQUM7Ozs7Ozs7Ozs7OzswQ0FJbkIsOERBQUNELDREQUFXQTs7a0RBQ1YsOERBQUN3Qyw0TEFBbUJBO3dDQUFDZ0csT0FBTTt3Q0FBT0MsUUFBUTtrREFDeEMsNEVBQUN6RyxpTEFBZ0JBOzs4REFDZiw4REFBQ0MsNEtBQUdBO29EQUNGNkMsTUFBTW5DLFVBQVVnQixZQUFZO29EQUM1QnNGLElBQUc7b0RBQ0hDLElBQUc7b0RBQ0hTLGFBQWE7b0RBQ2JMLGFBQWE7b0RBQ2JNLGNBQWM7b0RBQ2RqQixTQUFROzhEQUVQaEcsVUFBVWdCLFlBQVksQ0FBQzRGLEdBQUcsQ0FBQyxDQUFDQyxPQUFZQyxzQkFDdkMsOERBQUN2SCw2S0FBSUE7NERBQXVCNkcsTUFBTVMsTUFBTXZELEtBQUs7MkRBQWxDLFFBQWMsT0FBTndEOzs7Ozs7Ozs7OzhEQUd2Qiw4REFBQ25ILGdMQUFPQTtvREFBQ29ILFdBQVcsQ0FBQzFELFFBQWUsR0FBUyxPQUFOQSxPQUFNOzs7Ozs7OERBQzdDLDhEQUFDekQsK0tBQU1BOzs7Ozs7Ozs7Ozs7Ozs7O2tEQUdYLDhEQUFDc0Y7d0NBQUlDLFdBQVU7OzBEQUNiLDhEQUFDRDtnREFBSUMsV0FBVTs7a0VBQ2IsOERBQUNEO3dEQUFJQyxXQUFVOzs0REFBcUNuRixFQUFBQSwyQkFBQUEsVUFBVWdCLFlBQVksQ0FBQyxFQUFFLGNBQXpCaEIsK0NBQUFBLHlCQUEyQnFELEtBQUssS0FBSTs0REFBRTs7Ozs7OztrRUFDMUYsOERBQUM2Qjt3REFBSUMsV0FBVTtrRUFBd0I7Ozs7Ozs7Ozs7OzswREFFekMsOERBQUNEO2dEQUFJQyxXQUFVOztrRUFDYiw4REFBQ0Q7d0RBQUlDLFdBQVU7OzREQUFtQ25GLEVBQUFBLDRCQUFBQSxVQUFVZ0IsWUFBWSxDQUFDLEVBQUUsY0FBekJoQixnREFBQUEsMEJBQTJCcUQsS0FBSyxLQUFJOzREQUFFOzs7Ozs7O2tFQUN4Riw4REFBQzZCO3dEQUFJQyxXQUFVO2tFQUF3Qjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQVFqRCw4REFBQ0Q7Z0JBQUlDLFdBQVU7O2tDQUViLDhEQUFDL0gscURBQUlBOzswQ0FDSCw4REFBQ0csMkRBQVVBOztrREFDVCw4REFBQ0MsMERBQVNBO3dDQUFDMkgsV0FBVTs7MERBQ25CLDhEQUFDRDtnREFBSUMsV0FBVTs7a0VBQ2IsOERBQUNsSCw2TUFBUUE7d0RBQUNrSCxXQUFVOzs7Ozs7b0RBQTBCOzs7Ozs7OzBEQUdoRCw4REFBQzFILHlEQUFNQTtnREFDTHNILFNBQVE7Z0RBQ1JXLE1BQUs7Z0RBQ0xDLFNBQVMsSUFBTXpFLE9BQU8wRSxJQUFJLENBQUM7O29EQUM1QjtrRUFFQyw4REFBQ2pILDZNQUFVQTt3REFBQ3dHLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7OztrREFHMUIsOERBQUM3SCxnRUFBZUE7a0RBQUM7Ozs7Ozs7Ozs7OzswQ0FJbkIsOERBQUNELDREQUFXQTswQ0FDVDhDLFdBQVcrRyxNQUFNLEdBQUcsa0JBQ25CLDhEQUFDaEM7b0NBQUlDLFdBQVU7OENBQ1poRixXQUFXZ0gsS0FBSyxDQUFDLEdBQUcsR0FBR1AsR0FBRyxDQUFDLENBQUNoQzt3Q0FDM0IsTUFBTUssU0FBU04sYUFBYUM7d0NBQzVCLHFCQUNFLDhEQUFDTTs0Q0FBa0JDLFdBQVU7OzhEQUMzQiw4REFBQ0Q7b0RBQUlDLFdBQVU7O3NFQUNiLDhEQUFDaUM7NERBQUdqQyxXQUFVO3NFQUE2QlAsSUFBSUksT0FBTyxDQUFDcUMsS0FBSzs7Ozs7O3NFQUM1RCw4REFBQ25DOzREQUFJQyxXQUFVOzs4RUFDYiw4REFBQ0M7b0VBQUVELFdBQVU7O3NGQUNYLDhEQUFDOUcsNk1BQUtBOzRFQUFDOEcsV0FBVTs7Ozs7O3dFQUNoQnZCLG9CQUFvQmdCLElBQUlJLE9BQU8sQ0FBQ25CLE9BQU87Ozs7Ozs7OEVBRTFDLDhEQUFDdUI7b0VBQUVELFdBQVU7O3dFQUF3QjtzRkFDMUIsOERBQUNtQzs0RUFBS25DLFdBQVU7c0ZBQWV6QixZQUFZa0IsSUFBSTJDLE1BQU07Ozs7Ozs7Ozs7Ozs7Ozs7OztzRUFHbEUsOERBQUNuQzs0REFBRUQsV0FBVTtzRUFDVlgsbUJBQW1CSSxJQUFJNEMsUUFBUTs7Ozs7Ozs7Ozs7OzhEQUdwQyw4REFBQ3RDO29EQUFJQyxXQUFVOztzRUFDYiw4REFBQ3pILHVEQUFLQTs0REFBQ3FILFNBQVNFLE9BQU9GLE9BQU87NERBQUVJLFdBQVdGLE9BQU8zQixLQUFLO3NFQUNwRDJCLE9BQU9ILEtBQUs7Ozs7OztzRUFFZiw4REFBQ3JILHlEQUFNQTs0REFDTGlJLE1BQUs7NERBQ0xYLFNBQVE7NERBQ1JZLFNBQVMsSUFBTXpFLE9BQU8wRSxJQUFJLENBQUMsYUFBNkIsT0FBaEJoQixJQUFJSSxPQUFPLENBQUN5QyxHQUFHOzs4RUFFdkQsOERBQUNsSiw2TUFBR0E7b0VBQUM0RyxXQUFVOzs7Ozs7Z0VBQWlCOzs7Ozs7Ozs7Ozs7OzsyQ0F6QjVCUCxJQUFJNkMsR0FBRzs7Ozs7b0NBK0JyQjs7Ozs7eURBR0YsOERBQUN2QztvQ0FBSUMsV0FBVTs7c0RBQ2IsOERBQUNsSCw2TUFBUUE7NENBQUNrSCxXQUFVOzs7Ozs7c0RBQ3BCLDhEQUFDdUM7NENBQUd2QyxXQUFVO3NEQUF5Qzs7Ozs7O3NEQUN2RCw4REFBQ0M7NENBQUVELFdBQVU7c0RBQXFCOzs7Ozs7c0RBQ2xDLDhEQUFDMUgseURBQU1BOzRDQUFDa0ksU0FBUyxJQUFNekUsT0FBTzBFLElBQUksQ0FBQztzREFBbUI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tDQVM5RCw4REFBQ3hJLHFEQUFJQTs7MENBQ0gsOERBQUNHLDJEQUFVQTs7a0RBQ1QsOERBQUNDLDBEQUFTQTt3Q0FBQzJILFdBQVU7OzBEQUNuQiw4REFBQ0Q7Z0RBQUlDLFdBQVU7O2tFQUNiLDhEQUFDakgsNk1BQUlBO3dEQUFDaUgsV0FBVTs7Ozs7O29EQUE0QjtvREFFM0M1RSxjQUFjb0gsTUFBTSxDQUFDQyxDQUFBQSxJQUFLLENBQUNBLEVBQUVDLElBQUksRUFBRVgsTUFBTSxHQUFHLG1CQUMzQyw4REFBQ3hKLHVEQUFLQTt3REFBQ3FILFNBQVE7d0RBQWNJLFdBQVU7a0VBQ3BDNUUsY0FBY29ILE1BQU0sQ0FBQ0MsQ0FBQUEsSUFBSyxDQUFDQSxFQUFFQyxJQUFJLEVBQUVYLE1BQU07Ozs7Ozs7Ozs7OzswREFJaEQsOERBQUN6Six5REFBTUE7Z0RBQ0xzSCxTQUFRO2dEQUNSVyxNQUFLO2dEQUNMQyxTQUFTLElBQU16RSxPQUFPMEUsSUFBSSxDQUFDOztvREFDNUI7a0VBRUMsOERBQUNqSCw2TUFBVUE7d0RBQUN3RyxXQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7a0RBRzFCLDhEQUFDN0gsZ0VBQWVBO2tEQUFDOzs7Ozs7Ozs7Ozs7MENBSW5CLDhEQUFDRCw0REFBV0E7MENBQ1RrRCxjQUFjMkcsTUFBTSxHQUFHLGtCQUN0Qiw4REFBQ2hDO29DQUFJQyxXQUFVOzhDQUNaNUUsY0FBYzRHLEtBQUssQ0FBQyxHQUFHLEdBQUdQLEdBQUcsQ0FBQyxDQUFDa0I7d0NBQzlCLE1BQU1DLHVCQUF1QixDQUFDOUI7NENBQzVCLE9BQVFBO2dEQUNOLEtBQUs7b0RBQ0gsT0FBTztnREFDVCxLQUFLO29EQUNILE9BQU87Z0RBQ1QsS0FBSztvREFDSCxPQUFPO2dEQUNULEtBQUs7b0RBQ0gsT0FBTztnREFDVDtvREFDRSxPQUFPOzRDQUNYO3dDQUNGO3dDQUVBLHFCQUNFLDhEQUFDZjs0Q0FFQ0MsV0FBVyx5QkFBb0UsT0FBM0M0QyxxQkFBcUJELGFBQWE3QixJQUFJLEdBQUUsS0FBb0QsT0FBakQsQ0FBQzZCLGFBQWFELElBQUksR0FBRyx5QkFBeUI7c0RBRTdILDRFQUFDM0M7Z0RBQUlDLFdBQVU7O2tFQUNiLDhEQUFDRDt3REFBSUMsV0FBVTs7MEVBQ2IsOERBQUNDO2dFQUFFRCxXQUFVOzBFQUF1QjJDLGFBQWFULEtBQUs7Ozs7OzswRUFDdEQsOERBQUNqQztnRUFBRUQsV0FBVTswRUFBZ0IyQyxhQUFhRSxPQUFPOzs7Ozs7MEVBQ2pELDhEQUFDNUM7Z0VBQUVELFdBQVU7MEVBQ1ZYLG1CQUFtQnNELGFBQWFHLFNBQVM7Ozs7Ozs7Ozs7OztvREFHN0MsQ0FBQ0gsYUFBYUQsSUFBSSxrQkFDakIsOERBQUMzQzt3REFBSUMsV0FBVTs7Ozs7Ozs7Ozs7OzJDQVpkMkMsYUFBYUwsR0FBRzs7Ozs7b0NBaUIzQjs7Ozs7eURBR0YsOERBQUN2QztvQ0FBSUMsV0FBVTs7c0RBQ2IsOERBQUNqSCw2TUFBSUE7NENBQUNpSCxXQUFVOzs7Ozs7c0RBQ2hCLDhEQUFDdUM7NENBQUd2QyxXQUFVO3NEQUF5Qzs7Ozs7O3NEQUN2RCw4REFBQ0M7NENBQUVELFdBQVU7c0RBQWdCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFRdkMsOERBQUMvSCxxREFBSUE7O2tDQUNILDhEQUFDRywyREFBVUE7OzBDQUNULDhEQUFDQywwREFBU0E7Z0NBQUMySCxXQUFVOztrREFDbkIsOERBQUNEO3dDQUFJQyxXQUFVOzswREFDYiw4REFBQ3ZHLDZNQUFNQTtnREFBQ3VHLFdBQVU7Ozs7Ozs0Q0FBNEI7Ozs7Ozs7a0RBR2hELDhEQUFDMUgseURBQU1BO3dDQUNMc0gsU0FBUTt3Q0FDUlcsTUFBSzt3Q0FDTEMsU0FBUyxJQUFNekUsT0FBTzBFLElBQUksQ0FBQzs7NENBQzVCOzBEQUVDLDhEQUFDakgsNk1BQVVBO2dEQUFDd0csV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBDQUcxQiw4REFBQzdILGdFQUFlQTswQ0FBQzs7Ozs7Ozs7Ozs7O2tDQUluQiw4REFBQ0QsNERBQVdBO2tDQUNUZ0QsZUFBZTZHLE1BQU0sR0FBRyxrQkFDdkIsOERBQUNoQzs0QkFBSUMsV0FBVTtzQ0FDWjlFLGVBQWU4RyxLQUFLLENBQUMsR0FBRyxHQUFHUCxHQUFHLENBQUMsQ0FBQzVCLHdCQUMvQiw4REFBQ2tEO29DQUVDL0MsV0FBVTtvQ0FDVlEsU0FBUyxJQUFNekUsT0FBTzBFLElBQUksQ0FBQyxhQUF5QixPQUFaWixRQUFReUMsR0FBRzs7c0RBRW5ELDhEQUFDdkM7NENBQUlDLFdBQVU7c0RBQ2IsNEVBQUNuSCw2TUFBS0E7Z0RBQUNtSCxXQUFVOzs7Ozs7Ozs7OztzREFHbkIsOERBQUNEOzRDQUFJQyxXQUFVOzs4REFDYiw4REFBQ2lDO29EQUFHakMsV0FBVTs4REFBMENILFFBQVFxQyxLQUFLOzs7Ozs7OERBRXJFLDhEQUFDbkM7b0RBQUlDLFdBQVU7O3NFQUNiLDhEQUFDRDs7OEVBQ0MsOERBQUNFO29FQUFFRCxXQUFVOzt3RUFBd0I7c0ZBQ3JCLDhEQUFDbUM7NEVBQUtuQyxXQUFVO3NGQUE4QnpCLFlBQVlzQixRQUFRbUQsVUFBVTs7Ozs7Ozs7Ozs7OzhFQUU1Riw4REFBQy9DO29FQUFFRCxXQUFVOzt3RUFBd0I7d0VBQ3pCekIsWUFBWXNCLFFBQVFvRCxXQUFXOzs7Ozs7Ozs7Ozs7O3NFQUc3Qyw4REFBQzFLLHVEQUFLQTs0REFBQ3FILFNBQVE7NERBQVVJLFdBQVU7c0VBQ2hDSCxRQUFRcUQsUUFBUTs7Ozs7Ozs7Ozs7OzhEQUlyQiw4REFBQ25EO29EQUFJQyxXQUFVOztzRUFDYiw4REFBQ0Q7NERBQUlDLFdBQVU7OzhFQUNiLDhEQUFDOUcsNk1BQUtBO29FQUFDOEcsV0FBVTs7Ozs7O2dFQUNoQnZCLG9CQUFvQm9CLFFBQVFuQixPQUFPOzs7Ozs7O3NFQUV0Qyw4REFBQ3FCOzREQUFJQyxXQUFVOzs4RUFDYiw4REFBQ3pHLDZNQUFLQTtvRUFBQ3lHLFdBQVU7Ozs7OztnRUFDaEJILFFBQVFzRCxTQUFTO2dFQUFDOzs7Ozs7Ozs7Ozs7OzhEQUl2Qiw4REFBQzdLLHlEQUFNQTtvREFDTGlJLE1BQUs7b0RBQ0xQLFdBQVU7b0RBQ1ZRLFNBQVMsQ0FBQzRDO3dEQUNSQSxFQUFFQyxlQUFlO3dEQUNqQnRILE9BQU8wRSxJQUFJLENBQUMsYUFBeUIsT0FBWlosUUFBUXlDLEdBQUc7b0RBQ3RDOztzRUFFQSw4REFBQ2xKLDZNQUFHQTs0REFBQzRHLFdBQVU7Ozs7Ozt3REFBaUI7Ozs7Ozs7Ozs7Ozs7O21DQTVDL0JILFFBQVF5QyxHQUFHOzs7Ozs7Ozs7aURBb0R0Qiw4REFBQ3ZDOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQ3ZHLDZNQUFNQTtvQ0FBQ3VHLFdBQVU7Ozs7Ozs4Q0FDbEIsOERBQUN1QztvQ0FBR3ZDLFdBQVU7OENBQXlDOzs7Ozs7OENBQ3ZELDhEQUFDQztvQ0FBRUQsV0FBVTs4Q0FBcUI7Ozs7Ozs4Q0FDbEMsOERBQUMxSCx5REFBTUE7b0NBQUNrSSxTQUFTLElBQU16RSxPQUFPMEUsSUFBSSxDQUFDOzhDQUFtQjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFTdEU7R0F0c0J3QjlGOztRQWFKbkMsOERBQVFBO1FBQ1hDLHNEQUFTQTtRQTJHQ0Usa0VBQVdBOzs7S0F6SGRnQyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9hcHAvdXNlci9kYXNoYm9hcmQvcGFnZS50c3g/ZTc5OCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCdcblxuaW1wb3J0IHsgdXNlRWZmZWN0LCB1c2VTdGF0ZSB9IGZyb20gJ3JlYWN0J1xuaW1wb3J0IHsgQ2FyZCwgQ2FyZENvbnRlbnQsIENhcmREZXNjcmlwdGlvbiwgQ2FyZEhlYWRlciwgQ2FyZFRpdGxlIH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL2NhcmQnXG5pbXBvcnQgeyBCdXR0b24gfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvYnV0dG9uJ1xuaW1wb3J0IHsgQmFkZ2UgfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvYmFkZ2UnXG5pbXBvcnQgeyB1c2VUb2FzdCB9IGZyb20gJ0AvY29tcG9uZW50cy91aS91c2UtdG9hc3QnXG5cbmltcG9ydCB7IHVzZVJvdXRlciB9IGZyb20gJ25leHQvbmF2aWdhdGlvbidcbmltcG9ydCBhcGkgZnJvbSAnQC9saWIvYXBpJ1xuaW1wb3J0IHsgdXNlQ3VycmVuY3kgfSBmcm9tICdAL2NvbnRleHRzL0N1cnJlbmN5Q29udGV4dCdcbmltcG9ydCB7IEN1cnJlbmN5U2VsZWN0b3IgfSBmcm9tICdAL2NvbXBvbmVudHMvQ3VycmVuY3lTZWxlY3RvcidcbmltcG9ydCB7XG4gIEdhdmVsLFxuICBBY3Rpdml0eSxcbiAgQmVsbCxcbiAgVHJvcGh5LFxuICBDYWxlbmRhcixcbiAgVHJlbmRpbmdVcCxcbiAgQ2xvY2ssXG4gIFNlYXJjaCxcbiAgRXllLFxuICBIZWFydCxcbiAgRG9sbGFyU2lnbixcbiAgVXNlcnMsXG4gIFN0YXIsXG4gIEFycm93UmlnaHQsXG4gIEZpbGVUZXh0LFxuICBUYXJnZXQsXG4gIFphcCxcbiAgQmFyQ2hhcnQzLFxuICBQaWVDaGFydCxcbiAgTGluZUNoYXJ0XG59IGZyb20gJ2x1Y2lkZS1yZWFjdCdcbmltcG9ydCB7XG4gIExpbmVDaGFydCBhcyBSZWNoYXJ0c0xpbmVDaGFydCxcbiAgTGluZSxcbiAgQXJlYUNoYXJ0LFxuICBBcmVhLFxuICBCYXJDaGFydCxcbiAgQmFyLFxuICBQaWVDaGFydCBhcyBSZWNoYXJ0c1BpZUNoYXJ0LFxuICBQaWUsXG4gIENlbGwsXG4gIFhBeGlzLFxuICBZQXhpcyxcbiAgQ2FydGVzaWFuR3JpZCxcbiAgVG9vbHRpcCxcbiAgTGVnZW5kLFxuICBSZXNwb25zaXZlQ29udGFpbmVyXG59IGZyb20gJ3JlY2hhcnRzJ1xuXG5pbnRlcmZhY2UgRGFzaGJvYXJkU3RhdHMge1xuICBhY3RpdmVCaWRzOiBudW1iZXJcbiAgd29uQXVjdGlvbnM6IG51bWJlclxuICB0b3RhbFNwZW50OiBudW1iZXJcbiAgc2F2ZWRBdWN0aW9uczogbnVtYmVyXG4gIHRvdGFsQmlkczogbnVtYmVyXG4gIHN1Y2Nlc3NSYXRlOiBudW1iZXJcbn1cblxuaW50ZXJmYWNlIFJlY2VudEJpZCB7XG4gIF9pZDogc3RyaW5nXG4gIGF1Y3Rpb246IHtcbiAgICBfaWQ6IHN0cmluZ1xuICAgIHRpdGxlOiBzdHJpbmdcbiAgICBjdXJyZW50QmlkOiBudW1iZXJcbiAgICBlbmRUaW1lOiBzdHJpbmdcbiAgICBzdGF0dXM6IHN0cmluZ1xuICB9XG4gIGFtb3VudDogbnVtYmVyXG4gIHBsYWNlZEF0OiBzdHJpbmdcbiAgaXNXaW5uaW5nOiBib29sZWFuXG59XG5cbmludGVyZmFjZSBSZWNlbnRBdWN0aW9uIHtcbiAgX2lkOiBzdHJpbmdcbiAgdGl0bGU6IHN0cmluZ1xuICBkZXNjcmlwdGlvbjogc3RyaW5nXG4gIHN0YXJ0aW5nQmlkOiBudW1iZXJcbiAgY3VycmVudEJpZDogbnVtYmVyXG4gIGVuZFRpbWU6IHN0cmluZ1xuICBzdGF0dXM6IHN0cmluZ1xuICBjYXRlZ29yeTogc3RyaW5nXG4gIGJpZHNDb3VudDogbnVtYmVyXG59XG5cbmludGVyZmFjZSBOb3RpZmljYXRpb24ge1xuICBfaWQ6IHN0cmluZ1xuICB0eXBlOiBzdHJpbmdcbiAgdGl0bGU6IHN0cmluZ1xuICBtZXNzYWdlOiBzdHJpbmdcbiAgY3JlYXRlZEF0OiBzdHJpbmdcbiAgcmVhZDogYm9vbGVhblxufVxuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBVc2VyRGFzaGJvYXJkKCkge1xuICBjb25zdCBbc3RhdHMsIHNldFN0YXRzXSA9IHVzZVN0YXRlPERhc2hib2FyZFN0YXRzIHwgbnVsbD4obnVsbClcbiAgY29uc3QgW3JlY2VudEJpZHMsIHNldFJlY2VudEJpZHNdID0gdXNlU3RhdGU8UmVjZW50QmlkW10+KFtdKVxuICBjb25zdCBbcmVjZW50QXVjdGlvbnMsIHNldFJlY2VudEF1Y3Rpb25zXSA9IHVzZVN0YXRlPFJlY2VudEF1Y3Rpb25bXT4oW10pXG4gIGNvbnN0IFtub3RpZmljYXRpb25zLCBzZXROb3RpZmljYXRpb25zXSA9IHVzZVN0YXRlPE5vdGlmaWNhdGlvbltdPihbXSlcbiAgY29uc3QgW2xvYWRpbmcsIHNldExvYWRpbmddID0gdXNlU3RhdGUodHJ1ZSlcbiAgY29uc3QgW3VzZXIsIHNldFVzZXJdID0gdXNlU3RhdGU8YW55PihudWxsKVxuICBjb25zdCBbY2hhcnREYXRhLCBzZXRDaGFydERhdGFdID0gdXNlU3RhdGU8YW55Pih7XG4gICAgYmlkZGluZ0FjdGl2aXR5OiBbXSxcbiAgICBjYXRlZ29yeVNwZW5kaW5nOiBbXSxcbiAgICBtb250aGx5UGVyZm9ybWFuY2U6IFtdLFxuICAgIHdpbkxvc3NSYXRpbzogW11cbiAgfSlcbiAgY29uc3QgeyB0b2FzdCB9ID0gdXNlVG9hc3QoKVxuICBjb25zdCByb3V0ZXIgPSB1c2VSb3V0ZXIoKVxuXG4gIC8vIENoYXJ0IGNvbG9yc1xuICBjb25zdCBDT0xPUlMgPSB7XG4gICAgcHJpbWFyeTogJyMzQjgyRjYnLFxuICAgIHNlY29uZGFyeTogJyM4QjVDRjYnLFxuICAgIHN1Y2Nlc3M6ICcjMTBCOTgxJyxcbiAgICB3YXJuaW5nOiAnI0Y1OUUwQicsXG4gICAgZGFuZ2VyOiAnI0VGNDQ0NCcsXG4gICAgaW5mbzogJyMwNkI2RDQnXG4gIH1cblxuICBjb25zdCBQSUVfQ09MT1JTID0gWycjM0I4MkY2JywgJyM4QjVDRjYnLCAnIzEwQjk4MScsICcjRjU5RTBCJywgJyNFRjQ0NDQnLCAnIzA2QjZENCddXG5cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBjb25zdCB1c2VyRGF0YSA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKCd1c2VyJylcbiAgICBpZiAodXNlckRhdGEpIHtcbiAgICAgIHNldFVzZXIoSlNPTi5wYXJzZSh1c2VyRGF0YSkpXG4gICAgfVxuXG4gICAgbG9hZERhc2hib2FyZERhdGEoKVxuICB9LCBbXSlcblxuICBjb25zdCBsb2FkRGFzaGJvYXJkRGF0YSA9IGFzeW5jICgpID0+IHtcbiAgICB0cnkge1xuICAgICAgc2V0TG9hZGluZyh0cnVlKVxuXG4gICAgICAvLyBMb2FkIHVzZXIgc3RhdHNcbiAgICAgIGNvbnN0IHN0YXRzUmVzcG9uc2UgPSBhd2FpdCBhcGkuZ2V0KCcvdXNlcnMvZGFzaGJvYXJkL3N0YXRzJylcbiAgICAgIGlmIChzdGF0c1Jlc3BvbnNlLmRhdGEuc3VjY2Vzcykge1xuICAgICAgICBzZXRTdGF0cyhzdGF0c1Jlc3BvbnNlLmRhdGEuZGF0YSlcbiAgICAgIH1cblxuICAgICAgLy8gTG9hZCByZWNlbnQgYmlkc1xuICAgICAgY29uc3QgYmlkc1Jlc3BvbnNlID0gYXdhaXQgYXBpLmdldCgnL3VzZXIvYmlkcz9saW1pdD01JylcbiAgICAgIGlmIChiaWRzUmVzcG9uc2UuZGF0YS5zdWNjZXNzKSB7XG4gICAgICAgIHNldFJlY2VudEJpZHMoYmlkc1Jlc3BvbnNlLmRhdGEuZGF0YS5iaWRzKVxuICAgICAgfVxuXG4gICAgICAvLyBMb2FkIHJlY2VudCBhdWN0aW9ucyAocmVjb21tZW5kZWQpXG4gICAgICBjb25zdCBhdWN0aW9uc1Jlc3BvbnNlID0gYXdhaXQgYXBpLmdldCgnL2F1Y3Rpb25zP2xpbWl0PTYmc3RhdHVzPWFjdGl2ZScpXG4gICAgICBpZiAoYXVjdGlvbnNSZXNwb25zZS5kYXRhLnN1Y2Nlc3MpIHtcbiAgICAgICAgc2V0UmVjZW50QXVjdGlvbnMoYXVjdGlvbnNSZXNwb25zZS5kYXRhLmRhdGEuYXVjdGlvbnMpXG4gICAgICB9XG5cbiAgICAgIC8vIExvYWQgbm90aWZpY2F0aW9uc1xuICAgICAgY29uc3Qgbm90aWZpY2F0aW9uc1Jlc3BvbnNlID0gYXdhaXQgYXBpLmdldCgnL3VzZXIvbm90aWZpY2F0aW9ucz9saW1pdD01JylcbiAgICAgIGlmIChub3RpZmljYXRpb25zUmVzcG9uc2UuZGF0YS5zdWNjZXNzKSB7XG4gICAgICAgIHNldE5vdGlmaWNhdGlvbnMobm90aWZpY2F0aW9uc1Jlc3BvbnNlLmRhdGEuZGF0YS5ub3RpZmljYXRpb25zKVxuICAgICAgfVxuXG4gICAgICAvLyBMb2FkIGNoYXJ0IGRhdGFcbiAgICAgIGNvbnN0IGNoYXJ0UmVzcG9uc2UgPSBhd2FpdCBhcGkuZ2V0KCcvdXNlci9hbmFseXRpY3MvY2hhcnRzJylcbiAgICAgIGlmIChjaGFydFJlc3BvbnNlLmRhdGEuc3VjY2Vzcykge1xuICAgICAgICBzZXRDaGFydERhdGEoY2hhcnRSZXNwb25zZS5kYXRhLmRhdGEpXG4gICAgICB9XG5cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgbG9hZGluZyBkYXNoYm9hcmQgZGF0YTonLCBlcnJvcilcbiAgICAgIC8vIEZhbGxiYWNrIHRvIHNhbXBsZSBkYXRhXG4gICAgICBzZXRTdGF0cyh7XG4gICAgICAgIGFjdGl2ZUJpZHM6IDUsXG4gICAgICAgIHdvbkF1Y3Rpb25zOiAzLFxuICAgICAgICB0b3RhbFNwZW50OiA0NTAwMCxcbiAgICAgICAgc2F2ZWRBdWN0aW9uczogMTIsXG4gICAgICAgIHRvdGFsQmlkczogMjgsXG4gICAgICAgIHN1Y2Nlc3NSYXRlOiA3NVxuICAgICAgfSlcbiAgICAgIHNldFJlY2VudEJpZHMoW10pXG4gICAgICBzZXRSZWNlbnRBdWN0aW9ucyhbXSlcbiAgICAgIHNldE5vdGlmaWNhdGlvbnMoW10pXG5cbiAgICAgIC8vIFNhbXBsZSBjaGFydCBkYXRhXG4gICAgICBzZXRDaGFydERhdGEoe1xuICAgICAgICBiaWRkaW5nQWN0aXZpdHk6IFtcbiAgICAgICAgICB7IG1vbnRoOiAn2YrZhtin2YrYsScsIGJpZHM6IDEyLCB3aW5zOiAzIH0sXG4gICAgICAgICAgeyBtb250aDogJ9mB2KjYsdin2YrYsScsIGJpZHM6IDE5LCB3aW5zOiA1IH0sXG4gICAgICAgICAgeyBtb250aDogJ9mF2KfYsdizJywgYmlkczogMTUsIHdpbnM6IDIgfSxcbiAgICAgICAgICB7IG1vbnRoOiAn2KPYqNix2YrZhCcsIGJpZHM6IDIyLCB3aW5zOiA3IH0sXG4gICAgICAgICAgeyBtb250aDogJ9mF2KfZitmIJywgYmlkczogMTgsIHdpbnM6IDQgfSxcbiAgICAgICAgICB7IG1vbnRoOiAn2YrZiNmG2YrZiCcsIGJpZHM6IDI1LCB3aW5zOiA4IH1cbiAgICAgICAgXSxcbiAgICAgICAgY2F0ZWdvcnlTcGVuZGluZzogW1xuICAgICAgICAgIHsgbmFtZTogJ9il2YTZg9iq2LHZiNmG2YrYp9iqJywgdmFsdWU6IDE1MDAwLCBjb2xvcjogUElFX0NPTE9SU1swXSB9LFxuICAgICAgICAgIHsgbmFtZTogJ9iz2YrYp9ix2KfYqicsIHZhbHVlOiAyNTAwMCwgY29sb3I6IFBJRV9DT0xPUlNbMV0gfSxcbiAgICAgICAgICB7IG5hbWU6ICfYo9ir2KfYqycsIHZhbHVlOiA4MDAwLCBjb2xvcjogUElFX0NPTE9SU1syXSB9LFxuICAgICAgICAgIHsgbmFtZTogJ9mF2KzZiNmH2LHYp9iqJywgdmFsdWU6IDEyMDAwLCBjb2xvcjogUElFX0NPTE9SU1szXSB9LFxuICAgICAgICAgIHsgbmFtZTogJ9ij2K7YsdmJJywgdmFsdWU6IDUwMDAsIGNvbG9yOiBQSUVfQ09MT1JTWzRdIH1cbiAgICAgICAgXSxcbiAgICAgICAgbW9udGhseVBlcmZvcm1hbmNlOiBbXG4gICAgICAgICAgeyBtb250aDogJ9mK2YbYp9mK2LEnLCBzcGVudDogODAwMCwgc2F2ZWQ6IDIwMDAgfSxcbiAgICAgICAgICB7IG1vbnRoOiAn2YHYqNix2KfZitixJywgc3BlbnQ6IDEyMDAwLCBzYXZlZDogMzAwMCB9LFxuICAgICAgICAgIHsgbW9udGg6ICfZhdin2LHYsycsIHNwZW50OiA2MDAwLCBzYXZlZDogMTUwMCB9LFxuICAgICAgICAgIHsgbW9udGg6ICfYo9io2LHZitmEJywgc3BlbnQ6IDE1MDAwLCBzYXZlZDogNDAwMCB9LFxuICAgICAgICAgIHsgbW9udGg6ICfZhdin2YrZiCcsIHNwZW50OiA5MDAwLCBzYXZlZDogMjUwMCB9LFxuICAgICAgICAgIHsgbW9udGg6ICfZitmI2YbZitmIJywgc3BlbnQ6IDE4MDAwLCBzYXZlZDogNTAwMCB9XG4gICAgICAgIF0sXG4gICAgICAgIHdpbkxvc3NSYXRpbzogW1xuICAgICAgICAgIHsgbmFtZTogJ9mB2YjYsicsIHZhbHVlOiA2NSwgY29sb3I6IENPTE9SUy5zdWNjZXNzIH0sXG4gICAgICAgICAgeyBuYW1lOiAn2K7Ys9in2LHYqScsIHZhbHVlOiAzNSwgY29sb3I6IENPTE9SUy5kYW5nZXIgfVxuICAgICAgICBdXG4gICAgICB9KVxuICAgIH0gZmluYWxseSB7XG4gICAgICBzZXRMb2FkaW5nKGZhbHNlKVxuICAgIH1cbiAgfVxuXG4gIGNvbnN0IHsgZm9ybWF0QW1vdW50IH0gPSB1c2VDdXJyZW5jeSgpXG5cbiAgY29uc3QgZm9ybWF0UHJpY2UgPSAocHJpY2U6IG51bWJlcikgPT4ge1xuICAgIHJldHVybiBmb3JtYXRBbW91bnQocHJpY2UpXG4gIH1cblxuICBjb25zdCBmb3JtYXRUaW1lUmVtYWluaW5nID0gKGVuZFRpbWU6IHN0cmluZykgPT4ge1xuICAgIGNvbnN0IG5vdyA9IG5ldyBEYXRlKClcbiAgICBjb25zdCBlbmQgPSBuZXcgRGF0ZShlbmRUaW1lKVxuICAgIGNvbnN0IGRpZmYgPSBlbmQuZ2V0VGltZSgpIC0gbm93LmdldFRpbWUoKVxuXG4gICAgaWYgKGRpZmYgPD0gMCkgcmV0dXJuICfYp9mG2KrZh9mJJ1xuXG4gICAgY29uc3QgZGF5cyA9IE1hdGguZmxvb3IoZGlmZiAvICgxMDAwICogNjAgKiA2MCAqIDI0KSlcbiAgICBjb25zdCBob3VycyA9IE1hdGguZmxvb3IoKGRpZmYgJSAoMTAwMCAqIDYwICogNjAgKiAyNCkpIC8gKDEwMDAgKiA2MCAqIDYwKSlcbiAgICBjb25zdCBtaW51dGVzID0gTWF0aC5mbG9vcigoZGlmZiAlICgxMDAwICogNjAgKiA2MCkpIC8gKDEwMDAgKiA2MCkpXG5cbiAgICBpZiAoZGF5cyA+IDApIHJldHVybiBgJHtkYXlzfSDZitmI2YVgXG4gICAgaWYgKGhvdXJzID4gMCkgcmV0dXJuIGAke2hvdXJzfSDYs9in2LnYqWBcbiAgICByZXR1cm4gYCR7bWludXRlc30g2K/ZgtmK2YLYqWBcbiAgfVxuXG4gIGNvbnN0IGZvcm1hdFJlbGF0aXZlVGltZSA9IChkYXRlU3RyaW5nOiBzdHJpbmcpID0+IHtcbiAgICBjb25zdCBub3cgPSBuZXcgRGF0ZSgpXG4gICAgY29uc3QgZGF0ZSA9IG5ldyBEYXRlKGRhdGVTdHJpbmcpXG4gICAgY29uc3QgZGlmZiA9IG5vdy5nZXRUaW1lKCkgLSBkYXRlLmdldFRpbWUoKVxuXG4gICAgY29uc3QgbWludXRlcyA9IE1hdGguZmxvb3IoZGlmZiAvICgxMDAwICogNjApKVxuICAgIGNvbnN0IGhvdXJzID0gTWF0aC5mbG9vcihkaWZmIC8gKDEwMDAgKiA2MCAqIDYwKSlcbiAgICBjb25zdCBkYXlzID0gTWF0aC5mbG9vcihkaWZmIC8gKDEwMDAgKiA2MCAqIDYwICogMjQpKVxuXG4gICAgaWYgKGRheXMgPiAwKSByZXR1cm4gYNmF2YbYsCAke2RheXN9INmK2YjZhWBcbiAgICBpZiAoaG91cnMgPiAwKSByZXR1cm4gYNmF2YbYsCAke2hvdXJzfSDYs9in2LnYqWBcbiAgICBpZiAobWludXRlcyA+IDApIHJldHVybiBg2YXZhtiwICR7bWludXRlc30g2K/ZgtmK2YLYqWBcbiAgICByZXR1cm4gJ9in2YTYotmGJ1xuICB9XG5cbiAgY29uc3QgZ2V0QmlkU3RhdHVzID0gKGJpZDogUmVjZW50QmlkKSA9PiB7XG4gICAgaWYgKGJpZC5pc1dpbm5pbmcpIHJldHVybiB7IGxhYmVsOiAn2KPYudmE2Ykg2YXYstin2YrYr9ipJywgdmFyaWFudDogJ2RlZmF1bHQnIGFzIGNvbnN0LCBjb2xvcjogJ3RleHQtZ3JlZW4tNjAwJyB9XG4gICAgaWYgKGJpZC5hdWN0aW9uLnN0YXR1cyA9PT0gJ2FjdGl2ZScpIHJldHVybiB7IGxhYmVsOiAn2YXYqtmG2KfZgdizJywgdmFyaWFudDogJ3NlY29uZGFyeScgYXMgY29uc3QsIGNvbG9yOiAndGV4dC1ibHVlLTYwMCcgfVxuICAgIHJldHVybiB7IGxhYmVsOiAn2KrZhSDYqtis2KfZiNiy2YfYpycsIHZhcmlhbnQ6ICdvdXRsaW5lJyBhcyBjb25zdCwgY29sb3I6ICd0ZXh0LWdyYXktNjAwJyB9XG4gIH1cblxuICBpZiAobG9hZGluZykge1xuICAgIHJldHVybiAoXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyXCI+XG4gICAgICAgIDxwPtis2KfYsdmKINiq2K3ZhdmK2YQg2KfZhNil2K3Ytdin2KbZitin2KouLi48L3A+XG4gICAgICA8L2Rpdj5cbiAgICApXG4gIH1cblxuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS02XCI+XG4gICAgICAgIHsvKiBXZWxjb21lIEhlYWRlciAqL31cbiAgICAgICAgPGhlYWRlciBjbGFzc05hbWU9XCJiZy1ncmFkaWVudC10by1yIGZyb20tYmx1ZS02MDAgdG8tcHVycGxlLTYwMCB0ZXh0LXdoaXRlIHJvdW5kZWQtbGcgcC02XCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW5cIj5cbiAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgIDxoMSBjbGFzc05hbWU9XCJ0ZXh0LTN4bCBmb250LWJvbGRcIj5cbiAgICAgICAgICAgICAgICDZhdix2K3YqNin2YvYjCB7dXNlcj8ucHJvZmlsZT8uZnVsbE5hbWUgfHwgJ9in2YTZhdiz2KrYrtiv2YUnfVxuICAgICAgICAgICAgICA8L2gxPlxuICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWJsdWUtMTAwIG10LTJcIj5cbiAgICAgICAgICAgICAgICDYp9iz2KrZg9i02YEg2KfZhNmF2LLYp9iv2KfYqiDYp9mE2YXYqtin2K3YqSDZiNiq2KfYqNi5INmF2LLYp9mK2K/Yp9iq2YNcbiAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImhpZGRlbiBtZDpmbGV4IGl0ZW1zLWNlbnRlciBnYXAtNFwiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGRcIj57c3RhdHM/LnN1Y2Nlc3NSYXRlIHx8IDB9JTwvZGl2PlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWJsdWUtMTAwXCI+2YXYudiv2YQg2KfZhNmG2KzYp9itPC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGRcIj57c3RhdHM/LnRvdGFsQmlkcyB8fCAwfTwvZGl2PlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWJsdWUtMTAwXCI+2KXYrNmF2KfZhNmKINin2YTZhdiy2KfZitiv2KfYqjwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy13aGl0ZS8xMCByb3VuZGVkLWxnIHAtMlwiPlxuICAgICAgICAgICAgICAgIDxDdXJyZW5jeVNlbGVjdG9yIHNob3dMYWJlbD17ZmFsc2V9IGNsYXNzTmFtZT1cInRleHQtd2hpdGVcIiAvPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2hlYWRlcj5cblxuICAgICAgICB7LyogUXVpY2sgQWN0aW9ucyAqL31cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIG1kOmdyaWQtY29scy00IGdhcC00XCI+XG4gICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgY2xhc3NOYW1lPVwiaC0xNiB0ZXh0LWxnIGJnLWJsdWUtNjAwIGhvdmVyOmJnLWJsdWUtNzAwXCJcbiAgICAgICAgICAgIHNpemU9XCJsZ1wiXG4gICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiByb3V0ZXIucHVzaCgnL3VzZXIvYXVjdGlvbnMnKX1cbiAgICAgICAgICA+XG4gICAgICAgICAgICA8U2VhcmNoIGNsYXNzTmFtZT1cImgtNiB3LTYgbWwtMlwiIC8+XG4gICAgICAgICAgICDYp9iz2KrZg9i02KfZgSDYp9mE2YXYstin2K/Yp9iqXG4gICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgdmFyaWFudD1cIm91dGxpbmVcIlxuICAgICAgICAgICAgY2xhc3NOYW1lPVwiaC0xNiB0ZXh0LWxnIGJvcmRlci1ncmVlbi0yMDAgdGV4dC1ncmVlbi03MDAgaG92ZXI6YmctZ3JlZW4tNTBcIlxuICAgICAgICAgICAgc2l6ZT1cImxnXCJcbiAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHJvdXRlci5wdXNoKCcvdXNlci9teS1iaWRzJyl9XG4gICAgICAgICAgPlxuICAgICAgICAgICAgPEFjdGl2aXR5IGNsYXNzTmFtZT1cImgtNiB3LTYgbWwtMlwiIC8+XG4gICAgICAgICAgICDZhdiy2KfZitiv2KfYqtmKXG4gICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgdmFyaWFudD1cIm91dGxpbmVcIlxuICAgICAgICAgICAgY2xhc3NOYW1lPVwiaC0xNiB0ZXh0LWxnIGJvcmRlci1wdXJwbGUtMjAwIHRleHQtcHVycGxlLTcwMCBob3ZlcjpiZy1wdXJwbGUtNTBcIlxuICAgICAgICAgICAgc2l6ZT1cImxnXCJcbiAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHJvdXRlci5wdXNoKCcvdXNlci9ub3RpZmljYXRpb25zJyl9XG4gICAgICAgICAgPlxuICAgICAgICAgICAgPEJlbGwgY2xhc3NOYW1lPVwiaC02IHctNiBtbC0yXCIgLz5cbiAgICAgICAgICAgINin2YTYqtmG2KjZitmH2KfYqlxuICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgIHZhcmlhbnQ9XCJvdXRsaW5lXCJcbiAgICAgICAgICAgIGNsYXNzTmFtZT1cImgtMTYgdGV4dC1sZyBib3JkZXItb3JhbmdlLTIwMCB0ZXh0LW9yYW5nZS03MDAgaG92ZXI6Ymctb3JhbmdlLTUwXCJcbiAgICAgICAgICAgIHNpemU9XCJsZ1wiXG4gICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiByb3V0ZXIucHVzaCgnL3VzZXIvcHJvZmlsZScpfVxuICAgICAgICAgID5cbiAgICAgICAgICAgIDxVc2VycyBjbGFzc05hbWU9XCJoLTYgdy02IG1sLTJcIiAvPlxuICAgICAgICAgICAg2KfZhNmF2YTZgSDYp9mE2LTYrti12YpcbiAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgey8qIEVuaGFuY2VkIFN0YXRzIENhcmRzICovfVxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgbWQ6Z3JpZC1jb2xzLTIgbGc6Z3JpZC1jb2xzLTQgZ2FwLTZcIj5cbiAgICAgICAgICA8Q2FyZCBjbGFzc05hbWU9XCJiZy1ncmFkaWVudC10by1iciBmcm9tLWJsdWUtNTAgdG8tYmx1ZS0xMDAgYm9yZGVyLWJsdWUtMjAwXCI+XG4gICAgICAgICAgICA8Q2FyZENvbnRlbnQgY2xhc3NOYW1lPVwicC02XCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuXCI+XG4gICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ibHVlLTYwMFwiPtmF2LLYp9mK2K/Yp9iq2Yog2KfZhNmG2LTYt9ipPC9wPlxuICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC0zeGwgZm9udC1ib2xkIHRleHQtYmx1ZS05MDBcIj57c3RhdHM/LmFjdGl2ZUJpZHMgfHwgMH08L3A+XG4gICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtYmx1ZS02MDAgbXQtMVwiPlxuICAgICAgICAgICAgICAgICAgICA8WmFwIGNsYXNzTmFtZT1cImgtMyB3LTMgaW5saW5lIG1sLTFcIiAvPlxuICAgICAgICAgICAgICAgICAgICDZhti02Lcg2KfZhNii2YZcbiAgICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImgtMTIgdy0xMiBiZy1ibHVlLTIwMCByb3VuZGVkLWZ1bGwgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgIDxBY3Rpdml0eSBjbGFzc05hbWU9XCJoLTYgdy02IHRleHQtYmx1ZS02MDBcIiAvPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvQ2FyZENvbnRlbnQ+XG4gICAgICAgICAgPC9DYXJkPlxuXG4gICAgICAgICAgPENhcmQgY2xhc3NOYW1lPVwiYmctZ3JhZGllbnQtdG8tYnIgZnJvbS1ncmVlbi01MCB0by1ncmVlbi0xMDAgYm9yZGVyLWdyZWVuLTIwMFwiPlxuICAgICAgICAgICAgPENhcmRDb250ZW50IGNsYXNzTmFtZT1cInAtNlwiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlblwiPlxuICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JlZW4tNjAwXCI+2KfZhNmF2LLYp9iv2KfYqiDYp9mE2YXZg9iz2YjYqNipPC9wPlxuICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC0zeGwgZm9udC1ib2xkIHRleHQtZ3JlZW4tOTAwXCI+e3N0YXRzPy53b25BdWN0aW9ucyB8fCAwfTwvcD5cbiAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1ncmVlbi02MDAgbXQtMVwiPlxuICAgICAgICAgICAgICAgICAgICA8VHJvcGh5IGNsYXNzTmFtZT1cImgtMyB3LTMgaW5saW5lIG1sLTFcIiAvPlxuICAgICAgICAgICAgICAgICAgICDYpdmG2KzYp9iyINix2KfYpti5XG4gICAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJoLTEyIHctMTIgYmctZ3JlZW4tMjAwIHJvdW5kZWQtZnVsbCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgPFRyb3BoeSBjbGFzc05hbWU9XCJoLTYgdy02IHRleHQtZ3JlZW4tNjAwXCIgLz5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L0NhcmRDb250ZW50PlxuICAgICAgICAgIDwvQ2FyZD5cblxuICAgICAgICAgIDxDYXJkIGNsYXNzTmFtZT1cImJnLWdyYWRpZW50LXRvLWJyIGZyb20tcHVycGxlLTUwIHRvLXB1cnBsZS0xMDAgYm9yZGVyLXB1cnBsZS0yMDBcIj5cbiAgICAgICAgICAgIDxDYXJkQ29udGVudCBjbGFzc05hbWU9XCJwLTZcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW5cIj5cbiAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LXB1cnBsZS02MDBcIj7Ypdis2YXYp9mE2Yog2KfZhNmF2KjZhNi6INin2YTZhdmG2YHZgjwvcD5cbiAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtM3hsIGZvbnQtYm9sZCB0ZXh0LXB1cnBsZS05MDBcIj57Zm9ybWF0UHJpY2Uoc3RhdHM/LnRvdGFsU3BlbnQgfHwgMCl9PC9wPlxuICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LXB1cnBsZS02MDAgbXQtMVwiPlxuICAgICAgICAgICAgICAgICAgICA8VHJlbmRpbmdVcCBjbGFzc05hbWU9XCJoLTMgdy0zIGlubGluZSBtbC0xXCIgLz5cbiAgICAgICAgICAgICAgICAgICAg2KfYs9iq2KvZhdin2LEg2LDZg9mKXG4gICAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJoLTEyIHctMTIgYmctcHVycGxlLTIwMCByb3VuZGVkLWZ1bGwgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgIDxEb2xsYXJTaWduIGNsYXNzTmFtZT1cImgtNiB3LTYgdGV4dC1wdXJwbGUtNjAwXCIgLz5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L0NhcmRDb250ZW50PlxuICAgICAgICAgIDwvQ2FyZD5cblxuICAgICAgICAgIDxDYXJkIGNsYXNzTmFtZT1cImJnLWdyYWRpZW50LXRvLWJyIGZyb20tb3JhbmdlLTUwIHRvLW9yYW5nZS0xMDAgYm9yZGVyLW9yYW5nZS0yMDBcIj5cbiAgICAgICAgICAgIDxDYXJkQ29udGVudCBjbGFzc05hbWU9XCJwLTZcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW5cIj5cbiAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LW9yYW5nZS02MDBcIj7Yp9mE2YXYstin2K/Yp9iqINin2YTZhdit2YHZiNi42Kk8L3A+XG4gICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LTN4bCBmb250LWJvbGQgdGV4dC1vcmFuZ2UtOTAwXCI+e3N0YXRzPy5zYXZlZEF1Y3Rpb25zIHx8IDB9PC9wPlxuICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LW9yYW5nZS02MDAgbXQtMVwiPlxuICAgICAgICAgICAgICAgICAgICA8SGVhcnQgY2xhc3NOYW1lPVwiaC0zIHctMyBpbmxpbmUgbWwtMVwiIC8+XG4gICAgICAgICAgICAgICAgICAgINmF2YHYttmE2KlcbiAgICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImgtMTIgdy0xMiBiZy1vcmFuZ2UtMjAwIHJvdW5kZWQtZnVsbCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgPEhlYXJ0IGNsYXNzTmFtZT1cImgtNiB3LTYgdGV4dC1vcmFuZ2UtNjAwXCIgLz5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L0NhcmRDb250ZW50PlxuICAgICAgICAgIDwvQ2FyZD5cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgey8qIEFuYWx5dGljcyBDaGFydHMgKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBsZzpncmlkLWNvbHMtMiBnYXAtNlwiPlxuICAgICAgICAgIHsvKiBCaWRkaW5nIEFjdGl2aXR5IENoYXJ0ICovfVxuICAgICAgICAgIDxDYXJkPlxuICAgICAgICAgICAgPENhcmRIZWFkZXI+XG4gICAgICAgICAgICAgIDxDYXJkVGl0bGUgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTJcIj5cbiAgICAgICAgICAgICAgICA8TGluZUNoYXJ0IGNsYXNzTmFtZT1cImgtNSB3LTUgdGV4dC1ibHVlLTYwMFwiIC8+XG4gICAgICAgICAgICAgICAg2YbYtNin2Lcg2KfZhNmF2LLYp9mK2K/Yp9iqINin2YTYtNmH2LHZilxuICAgICAgICAgICAgICA8L0NhcmRUaXRsZT5cbiAgICAgICAgICAgICAgPENhcmREZXNjcmlwdGlvbj5cbiAgICAgICAgICAgICAgICDYudiv2K8g2KfZhNmF2LLYp9mK2K/Yp9iqINmI2KfZhNin2YbYqti12KfYsdin2Kog2K7ZhNin2YQg2KfZhNij2LTZh9ixINin2YTZhdin2LbZitipXG4gICAgICAgICAgICAgIDwvQ2FyZERlc2NyaXB0aW9uPlxuICAgICAgICAgICAgPC9DYXJkSGVhZGVyPlxuICAgICAgICAgICAgPENhcmRDb250ZW50PlxuICAgICAgICAgICAgICA8UmVzcG9uc2l2ZUNvbnRhaW5lciB3aWR0aD1cIjEwMCVcIiBoZWlnaHQ9ezMwMH0+XG4gICAgICAgICAgICAgICAgPEFyZWFDaGFydCBkYXRhPXtjaGFydERhdGEuYmlkZGluZ0FjdGl2aXR5fT5cbiAgICAgICAgICAgICAgICAgIDxDYXJ0ZXNpYW5HcmlkIHN0cm9rZURhc2hhcnJheT1cIjMgM1wiIC8+XG4gICAgICAgICAgICAgICAgICA8WEF4aXMgZGF0YUtleT1cIm1vbnRoXCIgLz5cbiAgICAgICAgICAgICAgICAgIDxZQXhpcyAvPlxuICAgICAgICAgICAgICAgICAgPFRvb2x0aXAgLz5cbiAgICAgICAgICAgICAgICAgIDxMZWdlbmQgLz5cbiAgICAgICAgICAgICAgICAgIDxBcmVhXG4gICAgICAgICAgICAgICAgICAgIHR5cGU9XCJtb25vdG9uZVwiXG4gICAgICAgICAgICAgICAgICAgIGRhdGFLZXk9XCJiaWRzXCJcbiAgICAgICAgICAgICAgICAgICAgc3RhY2tJZD1cIjFcIlxuICAgICAgICAgICAgICAgICAgICBzdHJva2U9e0NPTE9SUy5wcmltYXJ5fVxuICAgICAgICAgICAgICAgICAgICBmaWxsPXtDT0xPUlMucHJpbWFyeX1cbiAgICAgICAgICAgICAgICAgICAgZmlsbE9wYWNpdHk9ezAuNn1cbiAgICAgICAgICAgICAgICAgICAgbmFtZT1cItin2YTZhdiy2KfZitiv2KfYqlwiXG4gICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgPEFyZWFcbiAgICAgICAgICAgICAgICAgICAgdHlwZT1cIm1vbm90b25lXCJcbiAgICAgICAgICAgICAgICAgICAgZGF0YUtleT1cIndpbnNcIlxuICAgICAgICAgICAgICAgICAgICBzdGFja0lkPVwiMlwiXG4gICAgICAgICAgICAgICAgICAgIHN0cm9rZT17Q09MT1JTLnN1Y2Nlc3N9XG4gICAgICAgICAgICAgICAgICAgIGZpbGw9e0NPTE9SUy5zdWNjZXNzfVxuICAgICAgICAgICAgICAgICAgICBmaWxsT3BhY2l0eT17MC44fVxuICAgICAgICAgICAgICAgICAgICBuYW1lPVwi2KfZhNin2YbYqti12KfYsdin2KpcIlxuICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICA8L0FyZWFDaGFydD5cbiAgICAgICAgICAgICAgPC9SZXNwb25zaXZlQ29udGFpbmVyPlxuICAgICAgICAgICAgPC9DYXJkQ29udGVudD5cbiAgICAgICAgICA8L0NhcmQ+XG5cbiAgICAgICAgICB7LyogQ2F0ZWdvcnkgU3BlbmRpbmcgQ2hhcnQgKi99XG4gICAgICAgICAgPENhcmQ+XG4gICAgICAgICAgICA8Q2FyZEhlYWRlcj5cbiAgICAgICAgICAgICAgPENhcmRUaXRsZSBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMlwiPlxuICAgICAgICAgICAgICAgIDxQaWVDaGFydCBjbGFzc05hbWU9XCJoLTUgdy01IHRleHQtcHVycGxlLTYwMFwiIC8+XG4gICAgICAgICAgICAgICAg2KfZhNil2YbZgdin2YIg2K3Ys9ioINin2YTZgdim2KlcbiAgICAgICAgICAgICAgPC9DYXJkVGl0bGU+XG4gICAgICAgICAgICAgIDxDYXJkRGVzY3JpcHRpb24+XG4gICAgICAgICAgICAgICAg2KrZiNiy2YrYuSDYpdmG2YHYp9mC2YMg2LnZhNmJINin2YTZgdim2KfYqiDYp9mE2YXYrtiq2YTZgdipXG4gICAgICAgICAgICAgIDwvQ2FyZERlc2NyaXB0aW9uPlxuICAgICAgICAgICAgPC9DYXJkSGVhZGVyPlxuICAgICAgICAgICAgPENhcmRDb250ZW50PlxuICAgICAgICAgICAgICA8UmVzcG9uc2l2ZUNvbnRhaW5lciB3aWR0aD1cIjEwMCVcIiBoZWlnaHQ9ezMwMH0+XG4gICAgICAgICAgICAgICAgPFJlY2hhcnRzUGllQ2hhcnQ+XG4gICAgICAgICAgICAgICAgICA8UGllXG4gICAgICAgICAgICAgICAgICAgIGRhdGE9e2NoYXJ0RGF0YS5jYXRlZ29yeVNwZW5kaW5nfVxuICAgICAgICAgICAgICAgICAgICBjeD1cIjUwJVwiXG4gICAgICAgICAgICAgICAgICAgIGN5PVwiNTAlXCJcbiAgICAgICAgICAgICAgICAgICAgbGFiZWxMaW5lPXtmYWxzZX1cbiAgICAgICAgICAgICAgICAgICAgbGFiZWw9eyh7IG5hbWUsIHBlcmNlbnQgfSkgPT4gYCR7bmFtZX0gJHsocGVyY2VudCAqIDEwMCkudG9GaXhlZCgwKX0lYH1cbiAgICAgICAgICAgICAgICAgICAgb3V0ZXJSYWRpdXM9ezgwfVxuICAgICAgICAgICAgICAgICAgICBmaWxsPVwiIzg4ODRkOFwiXG4gICAgICAgICAgICAgICAgICAgIGRhdGFLZXk9XCJ2YWx1ZVwiXG4gICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgIHtjaGFydERhdGEuY2F0ZWdvcnlTcGVuZGluZy5tYXAoKGVudHJ5OiBhbnksIGluZGV4OiBudW1iZXIpID0+IChcbiAgICAgICAgICAgICAgICAgICAgICA8Q2VsbCBrZXk9e2BjZWxsLSR7aW5kZXh9YH0gZmlsbD17ZW50cnkuY29sb3J9IC8+XG4gICAgICAgICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgICAgICAgPC9QaWU+XG4gICAgICAgICAgICAgICAgICA8VG9vbHRpcCBmb3JtYXR0ZXI9eyh2YWx1ZTogYW55KSA9PiBmb3JtYXRQcmljZSh2YWx1ZSl9IC8+XG4gICAgICAgICAgICAgICAgPC9SZWNoYXJ0c1BpZUNoYXJ0PlxuICAgICAgICAgICAgICA8L1Jlc3BvbnNpdmVDb250YWluZXI+XG4gICAgICAgICAgICA8L0NhcmRDb250ZW50PlxuICAgICAgICAgIDwvQ2FyZD5cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgey8qIFBlcmZvcm1hbmNlIENoYXJ0cyAqL31cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIGxnOmdyaWQtY29scy0yIGdhcC02XCI+XG4gICAgICAgICAgey8qIE1vbnRobHkgUGVyZm9ybWFuY2UgKi99XG4gICAgICAgICAgPENhcmQ+XG4gICAgICAgICAgICA8Q2FyZEhlYWRlcj5cbiAgICAgICAgICAgICAgPENhcmRUaXRsZSBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMlwiPlxuICAgICAgICAgICAgICAgIDxCYXJDaGFydDMgY2xhc3NOYW1lPVwiaC01IHctNSB0ZXh0LWdyZWVuLTYwMFwiIC8+XG4gICAgICAgICAgICAgICAg2KfZhNij2K/Yp9ihINin2YTYtNmH2LHZilxuICAgICAgICAgICAgICA8L0NhcmRUaXRsZT5cbiAgICAgICAgICAgICAgPENhcmREZXNjcmlwdGlvbj5cbiAgICAgICAgICAgICAgICDYp9mE2YXYqNmE2Log2KfZhNmF2YbZgdmCINmI2KfZhNmF2K/YrtixINi02YfYsdmK2KfZi1xuICAgICAgICAgICAgICA8L0NhcmREZXNjcmlwdGlvbj5cbiAgICAgICAgICAgIDwvQ2FyZEhlYWRlcj5cbiAgICAgICAgICAgIDxDYXJkQ29udGVudD5cbiAgICAgICAgICAgICAgPFJlc3BvbnNpdmVDb250YWluZXIgd2lkdGg9XCIxMDAlXCIgaGVpZ2h0PXszMDB9PlxuICAgICAgICAgICAgICAgIDxCYXJDaGFydCBkYXRhPXtjaGFydERhdGEubW9udGhseVBlcmZvcm1hbmNlfT5cbiAgICAgICAgICAgICAgICAgIDxDYXJ0ZXNpYW5HcmlkIHN0cm9rZURhc2hhcnJheT1cIjMgM1wiIC8+XG4gICAgICAgICAgICAgICAgICA8WEF4aXMgZGF0YUtleT1cIm1vbnRoXCIgLz5cbiAgICAgICAgICAgICAgICAgIDxZQXhpcyAvPlxuICAgICAgICAgICAgICAgICAgPFRvb2x0aXAgZm9ybWF0dGVyPXsodmFsdWU6IGFueSkgPT4gZm9ybWF0UHJpY2UodmFsdWUpfSAvPlxuICAgICAgICAgICAgICAgICAgPExlZ2VuZCAvPlxuICAgICAgICAgICAgICAgICAgPEJhciBkYXRhS2V5PVwic3BlbnRcIiBmaWxsPXtDT0xPUlMuZGFuZ2VyfSBuYW1lPVwi2KfZhNmF2YbZgdmCXCIgLz5cbiAgICAgICAgICAgICAgICAgIDxCYXIgZGF0YUtleT1cInNhdmVkXCIgZmlsbD17Q09MT1JTLnN1Y2Nlc3N9IG5hbWU9XCLYp9mE2YXYr9iu2LFcIiAvPlxuICAgICAgICAgICAgICAgIDwvQmFyQ2hhcnQ+XG4gICAgICAgICAgICAgIDwvUmVzcG9uc2l2ZUNvbnRhaW5lcj5cbiAgICAgICAgICAgIDwvQ2FyZENvbnRlbnQ+XG4gICAgICAgICAgPC9DYXJkPlxuXG4gICAgICAgICAgey8qIFdpbi9Mb3NzIFJhdGlvICovfVxuICAgICAgICAgIDxDYXJkPlxuICAgICAgICAgICAgPENhcmRIZWFkZXI+XG4gICAgICAgICAgICAgIDxDYXJkVGl0bGUgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTJcIj5cbiAgICAgICAgICAgICAgICA8VHJvcGh5IGNsYXNzTmFtZT1cImgtNSB3LTUgdGV4dC15ZWxsb3ctNjAwXCIgLz5cbiAgICAgICAgICAgICAgICDZhtiz2KjYqSDYp9mE2YbYrNin2K1cbiAgICAgICAgICAgICAgPC9DYXJkVGl0bGU+XG4gICAgICAgICAgICAgIDxDYXJkRGVzY3JpcHRpb24+XG4gICAgICAgICAgICAgICAg2YbYs9io2Kkg2KfZhNmB2YjYsiDYpdmE2Ykg2KfZhNiu2LPYp9ix2Kkg2YHZiiDYp9mE2YXYstin2YrYr9in2KpcbiAgICAgICAgICAgICAgPC9DYXJkRGVzY3JpcHRpb24+XG4gICAgICAgICAgICA8L0NhcmRIZWFkZXI+XG4gICAgICAgICAgICA8Q2FyZENvbnRlbnQ+XG4gICAgICAgICAgICAgIDxSZXNwb25zaXZlQ29udGFpbmVyIHdpZHRoPVwiMTAwJVwiIGhlaWdodD17MzAwfT5cbiAgICAgICAgICAgICAgICA8UmVjaGFydHNQaWVDaGFydD5cbiAgICAgICAgICAgICAgICAgIDxQaWVcbiAgICAgICAgICAgICAgICAgICAgZGF0YT17Y2hhcnREYXRhLndpbkxvc3NSYXRpb31cbiAgICAgICAgICAgICAgICAgICAgY3g9XCI1MCVcIlxuICAgICAgICAgICAgICAgICAgICBjeT1cIjUwJVwiXG4gICAgICAgICAgICAgICAgICAgIGlubmVyUmFkaXVzPXs2MH1cbiAgICAgICAgICAgICAgICAgICAgb3V0ZXJSYWRpdXM9ezEwMH1cbiAgICAgICAgICAgICAgICAgICAgcGFkZGluZ0FuZ2xlPXs1fVxuICAgICAgICAgICAgICAgICAgICBkYXRhS2V5PVwidmFsdWVcIlxuICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICB7Y2hhcnREYXRhLndpbkxvc3NSYXRpby5tYXAoKGVudHJ5OiBhbnksIGluZGV4OiBudW1iZXIpID0+IChcbiAgICAgICAgICAgICAgICAgICAgICA8Q2VsbCBrZXk9e2BjZWxsLSR7aW5kZXh9YH0gZmlsbD17ZW50cnkuY29sb3J9IC8+XG4gICAgICAgICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgICAgICAgPC9QaWU+XG4gICAgICAgICAgICAgICAgICA8VG9vbHRpcCBmb3JtYXR0ZXI9eyh2YWx1ZTogYW55KSA9PiBgJHt2YWx1ZX0lYH0gLz5cbiAgICAgICAgICAgICAgICAgIDxMZWdlbmQgLz5cbiAgICAgICAgICAgICAgICA8L1JlY2hhcnRzUGllQ2hhcnQ+XG4gICAgICAgICAgICAgIDwvUmVzcG9uc2l2ZUNvbnRhaW5lcj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtdC00IGZsZXgganVzdGlmeS1jZW50ZXIgZ2FwLTZcIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZCB0ZXh0LWdyZWVuLTYwMFwiPntjaGFydERhdGEud2luTG9zc1JhdGlvWzBdPy52YWx1ZSB8fCAwfSU8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNjAwXCI+2YXYudiv2YQg2KfZhNmG2KzYp9itPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGQgdGV4dC1yZWQtNjAwXCI+e2NoYXJ0RGF0YS53aW5Mb3NzUmF0aW9bMV0/LnZhbHVlIHx8IDB9JTwvZGl2PlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS02MDBcIj7Zhdi52K/ZhCDYp9mE2K7Ys9in2LHYqTwvZGl2PlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvQ2FyZENvbnRlbnQ+XG4gICAgICAgICAgPC9DYXJkPlxuICAgICAgICA8L2Rpdj5cblxuICAgICAgICB7LyogQWN0aXZpdHkgT3ZlcnZpZXcgKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBsZzpncmlkLWNvbHMtMiBnYXAtNlwiPlxuICAgICAgICAgIHsvKiBNeSBSZWNlbnQgQmlkcyAqL31cbiAgICAgICAgICA8Q2FyZD5cbiAgICAgICAgICAgIDxDYXJkSGVhZGVyPlxuICAgICAgICAgICAgICA8Q2FyZFRpdGxlIGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlblwiPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTJcIj5cbiAgICAgICAgICAgICAgICAgIDxBY3Rpdml0eSBjbGFzc05hbWU9XCJoLTUgdy01IHRleHQtYmx1ZS02MDBcIiAvPlxuICAgICAgICAgICAgICAgICAg2YXYstin2YrYr9in2KrZiiDYp9mE2K3Yr9mK2KvYqVxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJnaG9zdFwiXG4gICAgICAgICAgICAgICAgICBzaXplPVwic21cIlxuICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gcm91dGVyLnB1c2goJy91c2VyL215LWJpZHMnKX1cbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICDYudix2LYg2KfZhNmD2YRcbiAgICAgICAgICAgICAgICAgIDxBcnJvd1JpZ2h0IGNsYXNzTmFtZT1cImgtNCB3LTQgbXItMVwiIC8+XG4gICAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICAgIDwvQ2FyZFRpdGxlPlxuICAgICAgICAgICAgICA8Q2FyZERlc2NyaXB0aW9uPlxuICAgICAgICAgICAgICAgINii2K7YsSDYp9mE2YXYstin2YrYr9in2Kog2KfZhNiq2Yog2LTYp9ix2YPYqiDZgdmK2YfYp1xuICAgICAgICAgICAgICA8L0NhcmREZXNjcmlwdGlvbj5cbiAgICAgICAgICAgIDwvQ2FyZEhlYWRlcj5cbiAgICAgICAgICAgIDxDYXJkQ29udGVudD5cbiAgICAgICAgICAgICAge3JlY2VudEJpZHMubGVuZ3RoID4gMCA/IChcbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktM1wiPlxuICAgICAgICAgICAgICAgICAge3JlY2VudEJpZHMuc2xpY2UoMCwgMykubWFwKChiaWQpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgY29uc3Qgc3RhdHVzID0gZ2V0QmlkU3RhdHVzKGJpZClcbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIChcbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGtleT17YmlkLl9pZH0gY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIHAtMyBiZy1ncmF5LTUwIHJvdW5kZWQtbGcgaG92ZXI6YmctZ3JheS0xMDAgdHJhbnNpdGlvbi1jb2xvcnNcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC0xXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxoNCBjbGFzc05hbWU9XCJmb250LW1lZGl1bSB0ZXh0LWdyYXktOTAwXCI+e2JpZC5hdWN0aW9uLnRpdGxlfTwvaDQ+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTQgbXQtMVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTYwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPENsb2NrIGNsYXNzTmFtZT1cImgtNCB3LTQgaW5saW5lIG1sLTFcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge2Zvcm1hdFRpbWVSZW1haW5pbmcoYmlkLmF1Y3Rpb24uZW5kVGltZSl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTYwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAg2YXYstin2YrYr9iq2Yo6IDxzcGFuIGNsYXNzTmFtZT1cImZvbnQtbWVkaXVtXCI+e2Zvcm1hdFByaWNlKGJpZC5hbW91bnQpfTwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtZ3JheS01MDAgbXQtMVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtmb3JtYXRSZWxhdGl2ZVRpbWUoYmlkLnBsYWNlZEF0KX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC1jb2wgaXRlbXMtZW5kIGdhcC0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxCYWRnZSB2YXJpYW50PXtzdGF0dXMudmFyaWFudH0gY2xhc3NOYW1lPXtzdGF0dXMuY29sb3J9PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtzdGF0dXMubGFiZWx9XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvQmFkZ2U+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBzaXplPVwic21cIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJvdXRsaW5lXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiByb3V0ZXIucHVzaChgL2F1Y3Rpb25zLyR7YmlkLmF1Y3Rpb24uX2lkfWApfVxuICAgICAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPEV5ZSBjbGFzc05hbWU9XCJoLTQgdy00IG1sLTFcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgINi52LHYtlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICApXG4gICAgICAgICAgICAgICAgICB9KX1cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyIHB5LThcIj5cbiAgICAgICAgICAgICAgICAgIDxBY3Rpdml0eSBjbGFzc05hbWU9XCJoLTEyIHctMTIgdGV4dC1ncmF5LTQwMCBteC1hdXRvIG1iLTRcIiAvPlxuICAgICAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1tZWRpdW0gdGV4dC1ncmF5LTkwMCBtYi0yXCI+2YTYpyDYqtmI2KzYryDZhdiy2KfZitiv2KfYqiDYrdiv2YrYq9ipPC9oMz5cbiAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS02MDAgbWItNFwiPtin2KjYr9ijINio2KfZhNmF2LTYp9ix2YPYqSDZgdmKINin2YTZhdiy2KfYr9in2Kog2KfZhNmF2KrYp9it2Kk8L3A+XG4gICAgICAgICAgICAgICAgICA8QnV0dG9uIG9uQ2xpY2s9eygpID0+IHJvdXRlci5wdXNoKCcvdXNlci9hdWN0aW9ucycpfT5cbiAgICAgICAgICAgICAgICAgICAg2KfYs9iq2YPYtNin2YEg2KfZhNmF2LLYp9iv2KfYqlxuICAgICAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICl9XG4gICAgICAgICAgICA8L0NhcmRDb250ZW50PlxuICAgICAgICAgIDwvQ2FyZD5cblxuICAgICAgICAgIHsvKiBOb3RpZmljYXRpb25zICovfVxuICAgICAgICAgIDxDYXJkPlxuICAgICAgICAgICAgPENhcmRIZWFkZXI+XG4gICAgICAgICAgICAgIDxDYXJkVGl0bGUgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuXCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMlwiPlxuICAgICAgICAgICAgICAgICAgPEJlbGwgY2xhc3NOYW1lPVwiaC01IHctNSB0ZXh0LW9yYW5nZS02MDBcIiAvPlxuICAgICAgICAgICAgICAgICAg2KfZhNiq2YbYqNmK2YfYp9iqXG4gICAgICAgICAgICAgICAgICB7bm90aWZpY2F0aW9ucy5maWx0ZXIobiA9PiAhbi5yZWFkKS5sZW5ndGggPiAwICYmIChcbiAgICAgICAgICAgICAgICAgICAgPEJhZGdlIHZhcmlhbnQ9XCJkZXN0cnVjdGl2ZVwiIGNsYXNzTmFtZT1cInRleHQteHNcIj5cbiAgICAgICAgICAgICAgICAgICAgICB7bm90aWZpY2F0aW9ucy5maWx0ZXIobiA9PiAhbi5yZWFkKS5sZW5ndGh9XG4gICAgICAgICAgICAgICAgICAgIDwvQmFkZ2U+XG4gICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJnaG9zdFwiXG4gICAgICAgICAgICAgICAgICBzaXplPVwic21cIlxuICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gcm91dGVyLnB1c2goJy91c2VyL25vdGlmaWNhdGlvbnMnKX1cbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICDYudix2LYg2KfZhNmD2YRcbiAgICAgICAgICAgICAgICAgIDxBcnJvd1JpZ2h0IGNsYXNzTmFtZT1cImgtNCB3LTQgbXItMVwiIC8+XG4gICAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICAgIDwvQ2FyZFRpdGxlPlxuICAgICAgICAgICAgICA8Q2FyZERlc2NyaXB0aW9uPlxuICAgICAgICAgICAgICAgINii2K7YsSDYp9mE2KrYrdiv2YrYq9in2Kog2YjYp9mE2KXYtNi52KfYsdin2KpcbiAgICAgICAgICAgICAgPC9DYXJkRGVzY3JpcHRpb24+XG4gICAgICAgICAgICA8L0NhcmRIZWFkZXI+XG4gICAgICAgICAgICA8Q2FyZENvbnRlbnQ+XG4gICAgICAgICAgICAgIHtub3RpZmljYXRpb25zLmxlbmd0aCA+IDAgPyAoXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTNcIj5cbiAgICAgICAgICAgICAgICAgIHtub3RpZmljYXRpb25zLnNsaWNlKDAsIDMpLm1hcCgobm90aWZpY2F0aW9uKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IGdldE5vdGlmaWNhdGlvblN0eWxlID0gKHR5cGU6IHN0cmluZykgPT4ge1xuICAgICAgICAgICAgICAgICAgICAgIHN3aXRjaCAodHlwZSkge1xuICAgICAgICAgICAgICAgICAgICAgICAgY2FzZSAnYXVjdGlvbl93b24nOlxuICAgICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gJ2JnLWdyZWVuLTUwIGJvcmRlci1ncmVlbi0yMDAgdGV4dC1ncmVlbi04MDAnXG4gICAgICAgICAgICAgICAgICAgICAgICBjYXNlICdhdWN0aW9uX2VuZGluZyc6XG4gICAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybiAnYmcteWVsbG93LTUwIGJvcmRlci15ZWxsb3ctMjAwIHRleHQteWVsbG93LTgwMCdcbiAgICAgICAgICAgICAgICAgICAgICAgIGNhc2UgJ25ld19hdWN0aW9uJzpcbiAgICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuICdiZy1ibHVlLTUwIGJvcmRlci1ibHVlLTIwMCB0ZXh0LWJsdWUtODAwJ1xuICAgICAgICAgICAgICAgICAgICAgICAgY2FzZSAnYmlkX291dGJpZCc6XG4gICAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybiAnYmctcmVkLTUwIGJvcmRlci1yZWQtMjAwIHRleHQtcmVkLTgwMCdcbiAgICAgICAgICAgICAgICAgICAgICAgIGRlZmF1bHQ6XG4gICAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybiAnYmctZ3JheS01MCBib3JkZXItZ3JheS0yMDAgdGV4dC1ncmF5LTgwMCdcbiAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgIH1cblxuICAgICAgICAgICAgICAgICAgICByZXR1cm4gKFxuICAgICAgICAgICAgICAgICAgICAgIDxkaXZcbiAgICAgICAgICAgICAgICAgICAgICAgIGtleT17bm90aWZpY2F0aW9uLl9pZH1cbiAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YHAtMyBib3JkZXIgcm91bmRlZC1sZyAke2dldE5vdGlmaWNhdGlvblN0eWxlKG5vdGlmaWNhdGlvbi50eXBlKX0gJHshbm90aWZpY2F0aW9uLnJlYWQgPyAncmluZy0yIHJpbmctYmx1ZS0yMDAnIDogJyd9YH1cbiAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtc3RhcnQganVzdGlmeS1iZXR3ZWVuXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC0xXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bVwiPntub3RpZmljYXRpb24udGl0bGV9PC9wPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQteHMgbXQtMVwiPntub3RpZmljYXRpb24ubWVzc2FnZX08L3A+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC14cyBvcGFjaXR5LTc1IG10LTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtmb3JtYXRSZWxhdGl2ZVRpbWUobm90aWZpY2F0aW9uLmNyZWF0ZWRBdCl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgeyFub3RpZmljYXRpb24ucmVhZCAmJiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTIgaC0yIGJnLWJsdWUtNjAwIHJvdW5kZWQtZnVsbCBtdC0xXCI+PC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgKVxuICAgICAgICAgICAgICAgICAgfSl9XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlciBweS04XCI+XG4gICAgICAgICAgICAgICAgICA8QmVsbCBjbGFzc05hbWU9XCJoLTEyIHctMTIgdGV4dC1ncmF5LTQwMCBteC1hdXRvIG1iLTRcIiAvPlxuICAgICAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1tZWRpdW0gdGV4dC1ncmF5LTkwMCBtYi0yXCI+2YTYpyDYqtmI2KzYryDYqtmG2KjZitmH2KfYqjwvaDM+XG4gICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNjAwXCI+2LPYqti42YfYsSDZh9mG2Kcg2KfZhNiq2K3Yr9mK2KvYp9iqINin2YTZhdmH2YXYqTwvcD5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgIDwvQ2FyZENvbnRlbnQ+XG4gICAgICAgICAgPC9DYXJkPlxuICAgICAgICA8L2Rpdj5cblxuICAgICAgICB7LyogUmVjb21tZW5kZWQgQXVjdGlvbnMgKi99XG4gICAgICAgIDxDYXJkPlxuICAgICAgICAgIDxDYXJkSGVhZGVyPlxuICAgICAgICAgICAgPENhcmRUaXRsZSBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW5cIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMlwiPlxuICAgICAgICAgICAgICAgIDxUYXJnZXQgY2xhc3NOYW1lPVwiaC01IHctNSB0ZXh0LXB1cnBsZS02MDBcIiAvPlxuICAgICAgICAgICAgICAgINmF2LLYp9iv2KfYqiDZhdmC2KrYsdit2Kkg2YTZg1xuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJnaG9zdFwiXG4gICAgICAgICAgICAgICAgc2l6ZT1cInNtXCJcbiAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiByb3V0ZXIucHVzaCgnL3VzZXIvYXVjdGlvbnMnKX1cbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgINi52LHYtiDYp9mE2YXYstmK2K9cbiAgICAgICAgICAgICAgICA8QXJyb3dSaWdodCBjbGFzc05hbWU9XCJoLTQgdy00IG1yLTFcIiAvPlxuICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgIDwvQ2FyZFRpdGxlPlxuICAgICAgICAgICAgPENhcmREZXNjcmlwdGlvbj5cbiAgICAgICAgICAgICAg2YXYstin2K/Yp9iqINmC2K8g2KrZh9mF2YMg2KjZhtin2KHZiyDYudmE2Ykg2KfZh9iq2YXYp9mF2KfYqtmDINin2YTYs9in2KjZgtipXG4gICAgICAgICAgICA8L0NhcmREZXNjcmlwdGlvbj5cbiAgICAgICAgICA8L0NhcmRIZWFkZXI+XG4gICAgICAgICAgPENhcmRDb250ZW50PlxuICAgICAgICAgICAge3JlY2VudEF1Y3Rpb25zLmxlbmd0aCA+IDAgPyAoXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBtZDpncmlkLWNvbHMtMiBsZzpncmlkLWNvbHMtMyBnYXAtNFwiPlxuICAgICAgICAgICAgICAgIHtyZWNlbnRBdWN0aW9ucy5zbGljZSgwLCA2KS5tYXAoKGF1Y3Rpb24pID0+IChcbiAgICAgICAgICAgICAgICAgIDxhcnRpY2xlXG4gICAgICAgICAgICAgICAgICAgIGtleT17YXVjdGlvbi5faWR9XG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJvcmRlciByb3VuZGVkLWxnIHAtNCBob3ZlcjpzaGFkb3ctbGcgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMjAwIGhvdmVyOmJvcmRlci1ibHVlLTMwMCBjdXJzb3ItcG9pbnRlclwiXG4gICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHJvdXRlci5wdXNoKGAvYXVjdGlvbnMvJHthdWN0aW9uLl9pZH1gKX1cbiAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhc3BlY3QtdmlkZW8gYmctZ3JhZGllbnQtdG8tYnIgZnJvbS1ncmF5LTEwMCB0by1ncmF5LTIwMCByb3VuZGVkIG1iLTMgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8R2F2ZWwgY2xhc3NOYW1lPVwiaC04IHctOCB0ZXh0LWdyYXktNDAwXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8aDQgY2xhc3NOYW1lPVwiZm9udC1tZWRpdW0gdGV4dC1ncmF5LTkwMCBsaW5lLWNsYW1wLTJcIj57YXVjdGlvbi50aXRsZX08L2g0PlxuXG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW5cIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTYwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgINin2YTYs9i52LEg2KfZhNit2KfZhNmKOiA8c3BhbiBjbGFzc05hbWU9XCJmb250LW1lZGl1bSB0ZXh0LWdyZWVuLTYwMFwiPntmb3JtYXRQcmljZShhdWN0aW9uLmN1cnJlbnRCaWQpfTwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtZ3JheS01MDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICDZitio2K/YoyDZhdmGOiB7Zm9ybWF0UHJpY2UoYXVjdGlvbi5zdGFydGluZ0JpZCl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgPEJhZGdlIHZhcmlhbnQ9XCJvdXRsaW5lXCIgY2xhc3NOYW1lPVwidGV4dC14c1wiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICB7YXVjdGlvbi5jYXRlZ29yeX1cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvQmFkZ2U+XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlbiBwdC0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0xIHRleHQteHMgdGV4dC1ncmF5LTUwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8Q2xvY2sgY2xhc3NOYW1lPVwiaC0zIHctM1wiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIHtmb3JtYXRUaW1lUmVtYWluaW5nKGF1Y3Rpb24uZW5kVGltZSl9XG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTEgdGV4dC14cyB0ZXh0LWdyYXktNTAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxVc2VycyBjbGFzc05hbWU9XCJoLTMgdy0zXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAge2F1Y3Rpb24uYmlkc0NvdW50fSDZhdiy2KfZitiv2KlcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICAgICAgICAgICAgc2l6ZT1cInNtXCJcbiAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBtdC0zXCJcbiAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eyhlKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgIGUuc3RvcFByb3BhZ2F0aW9uKClcbiAgICAgICAgICAgICAgICAgICAgICAgICAgcm91dGVyLnB1c2goYC9hdWN0aW9ucy8ke2F1Y3Rpb24uX2lkfWApXG4gICAgICAgICAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxFeWUgY2xhc3NOYW1lPVwiaC00IHctNCBtbC0xXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgINi02KfZh9ivINin2YTYqtmB2KfYtdmK2YRcbiAgICAgICAgICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8L2FydGljbGU+XG4gICAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlciBweS0xMlwiPlxuICAgICAgICAgICAgICAgIDxUYXJnZXQgY2xhc3NOYW1lPVwiaC0xNiB3LTE2IHRleHQtZ3JheS00MDAgbXgtYXV0byBtYi00XCIgLz5cbiAgICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC14bCBmb250LW1lZGl1bSB0ZXh0LWdyYXktOTAwIG1iLTJcIj7ZhNinINiq2YjYrNivINmF2LLYp9iv2KfYqiDZhdiq2KfYrdipINit2KfZhNmK2KfZizwvaDM+XG4gICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTYwMCBtYi02XCI+2KrYrdmC2YIg2YXYsdipINij2K7YsdmJINmE2KfYrdmC2KfZiyDYo9mIINin2LPYqtmD2LTZgSDYp9mE2YHYptin2Kog2KfZhNmF2K7YqtmE2YHYqTwvcD5cbiAgICAgICAgICAgICAgICA8QnV0dG9uIG9uQ2xpY2s9eygpID0+IHJvdXRlci5wdXNoKCcvdXNlci9hdWN0aW9ucycpfT5cbiAgICAgICAgICAgICAgICAgINin2LPYqtmD2LTYp9mBINin2YTZhdiy2KfYr9in2KpcbiAgICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICApfVxuICAgICAgICAgIDwvQ2FyZENvbnRlbnQ+XG4gICAgICAgIDwvQ2FyZD5cbiAgICAgIDwvZGl2PlxuICApXG59XG4iXSwibmFtZXMiOlsidXNlRWZmZWN0IiwidXNlU3RhdGUiLCJDYXJkIiwiQ2FyZENvbnRlbnQiLCJDYXJkRGVzY3JpcHRpb24iLCJDYXJkSGVhZGVyIiwiQ2FyZFRpdGxlIiwiQnV0dG9uIiwiQmFkZ2UiLCJ1c2VUb2FzdCIsInVzZVJvdXRlciIsImFwaSIsInVzZUN1cnJlbmN5IiwiQ3VycmVuY3lTZWxlY3RvciIsIkdhdmVsIiwiQWN0aXZpdHkiLCJCZWxsIiwiVHJvcGh5IiwiVHJlbmRpbmdVcCIsIkNsb2NrIiwiU2VhcmNoIiwiRXllIiwiSGVhcnQiLCJEb2xsYXJTaWduIiwiVXNlcnMiLCJBcnJvd1JpZ2h0IiwiVGFyZ2V0IiwiWmFwIiwiQmFyQ2hhcnQzIiwiUGllQ2hhcnQiLCJMaW5lQ2hhcnQiLCJBcmVhQ2hhcnQiLCJBcmVhIiwiQmFyQ2hhcnQiLCJCYXIiLCJSZWNoYXJ0c1BpZUNoYXJ0IiwiUGllIiwiQ2VsbCIsIlhBeGlzIiwiWUF4aXMiLCJDYXJ0ZXNpYW5HcmlkIiwiVG9vbHRpcCIsIkxlZ2VuZCIsIlJlc3BvbnNpdmVDb250YWluZXIiLCJVc2VyRGFzaGJvYXJkIiwidXNlciIsImNoYXJ0RGF0YSIsInN0YXRzIiwic2V0U3RhdHMiLCJyZWNlbnRCaWRzIiwic2V0UmVjZW50QmlkcyIsInJlY2VudEF1Y3Rpb25zIiwic2V0UmVjZW50QXVjdGlvbnMiLCJub3RpZmljYXRpb25zIiwic2V0Tm90aWZpY2F0aW9ucyIsImxvYWRpbmciLCJzZXRMb2FkaW5nIiwic2V0VXNlciIsInNldENoYXJ0RGF0YSIsImJpZGRpbmdBY3Rpdml0eSIsImNhdGVnb3J5U3BlbmRpbmciLCJtb250aGx5UGVyZm9ybWFuY2UiLCJ3aW5Mb3NzUmF0aW8iLCJ0b2FzdCIsInJvdXRlciIsIkNPTE9SUyIsInByaW1hcnkiLCJzZWNvbmRhcnkiLCJzdWNjZXNzIiwid2FybmluZyIsImRhbmdlciIsImluZm8iLCJQSUVfQ09MT1JTIiwidXNlckRhdGEiLCJsb2NhbFN0b3JhZ2UiLCJnZXRJdGVtIiwiSlNPTiIsInBhcnNlIiwibG9hZERhc2hib2FyZERhdGEiLCJzdGF0c1Jlc3BvbnNlIiwiZ2V0IiwiZGF0YSIsImJpZHNSZXNwb25zZSIsImJpZHMiLCJhdWN0aW9uc1Jlc3BvbnNlIiwiYXVjdGlvbnMiLCJub3RpZmljYXRpb25zUmVzcG9uc2UiLCJjaGFydFJlc3BvbnNlIiwiZXJyb3IiLCJjb25zb2xlIiwiYWN0aXZlQmlkcyIsIndvbkF1Y3Rpb25zIiwidG90YWxTcGVudCIsInNhdmVkQXVjdGlvbnMiLCJ0b3RhbEJpZHMiLCJzdWNjZXNzUmF0ZSIsIm1vbnRoIiwid2lucyIsIm5hbWUiLCJ2YWx1ZSIsImNvbG9yIiwic3BlbnQiLCJzYXZlZCIsImZvcm1hdEFtb3VudCIsImZvcm1hdFByaWNlIiwicHJpY2UiLCJmb3JtYXRUaW1lUmVtYWluaW5nIiwiZW5kVGltZSIsIm5vdyIsIkRhdGUiLCJlbmQiLCJkaWZmIiwiZ2V0VGltZSIsImRheXMiLCJNYXRoIiwiZmxvb3IiLCJob3VycyIsIm1pbnV0ZXMiLCJmb3JtYXRSZWxhdGl2ZVRpbWUiLCJkYXRlU3RyaW5nIiwiZGF0ZSIsImdldEJpZFN0YXR1cyIsImJpZCIsImlzV2lubmluZyIsImxhYmVsIiwidmFyaWFudCIsImF1Y3Rpb24iLCJzdGF0dXMiLCJkaXYiLCJjbGFzc05hbWUiLCJwIiwiaGVhZGVyIiwiaDEiLCJwcm9maWxlIiwiZnVsbE5hbWUiLCJzaG93TGFiZWwiLCJzaXplIiwib25DbGljayIsInB1c2giLCJ3aWR0aCIsImhlaWdodCIsInN0cm9rZURhc2hhcnJheSIsImRhdGFLZXkiLCJ0eXBlIiwic3RhY2tJZCIsInN0cm9rZSIsImZpbGwiLCJmaWxsT3BhY2l0eSIsImN4IiwiY3kiLCJsYWJlbExpbmUiLCJwZXJjZW50IiwidG9GaXhlZCIsIm91dGVyUmFkaXVzIiwibWFwIiwiZW50cnkiLCJpbmRleCIsImZvcm1hdHRlciIsImlubmVyUmFkaXVzIiwicGFkZGluZ0FuZ2xlIiwibGVuZ3RoIiwic2xpY2UiLCJoNCIsInRpdGxlIiwic3BhbiIsImFtb3VudCIsInBsYWNlZEF0IiwiX2lkIiwiaDMiLCJmaWx0ZXIiLCJuIiwicmVhZCIsIm5vdGlmaWNhdGlvbiIsImdldE5vdGlmaWNhdGlvblN0eWxlIiwibWVzc2FnZSIsImNyZWF0ZWRBdCIsImFydGljbGUiLCJjdXJyZW50QmlkIiwic3RhcnRpbmdCaWQiLCJjYXRlZ29yeSIsImJpZHNDb3VudCIsImUiLCJzdG9wUHJvcGFnYXRpb24iXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/user/dashboard/page.tsx\n"));

/***/ })

});