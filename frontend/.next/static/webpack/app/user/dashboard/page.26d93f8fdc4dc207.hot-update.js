"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/user/dashboard/page",{

/***/ "(app-pages-browser)/./app/user/dashboard/page.tsx":
/*!*************************************!*\
  !*** ./app/user/dashboard/page.tsx ***!
  \*************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ UserDashboard; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/use-toast */ \"(app-pages-browser)/./components/ui/use-toast.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./lib/api.ts\");\n/* harmony import */ var _contexts_CurrencyContext__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/contexts/CurrencyContext */ \"(app-pages-browser)/./contexts/CurrencyContext.tsx\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart3_Bell_Clock_DollarSign_Eye_Gavel_Heart_LineChart_PieChart_Search_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart3,Bell,Clock,DollarSign,Eye,Gavel,Heart,LineChart,PieChart,Search,Target,TrendingUp,Trophy,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart3_Bell_Clock_DollarSign_Eye_Gavel_Heart_LineChart_PieChart_Search_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart3,Bell,Clock,DollarSign,Eye,Gavel,Heart,LineChart,PieChart,Search,Target,TrendingUp,Trophy,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart3_Bell_Clock_DollarSign_Eye_Gavel_Heart_LineChart_PieChart_Search_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart3,Bell,Clock,DollarSign,Eye,Gavel,Heart,LineChart,PieChart,Search,Target,TrendingUp,Trophy,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart3_Bell_Clock_DollarSign_Eye_Gavel_Heart_LineChart_PieChart_Search_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart3,Bell,Clock,DollarSign,Eye,Gavel,Heart,LineChart,PieChart,Search,Target,TrendingUp,Trophy,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart3_Bell_Clock_DollarSign_Eye_Gavel_Heart_LineChart_PieChart_Search_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart3,Bell,Clock,DollarSign,Eye,Gavel,Heart,LineChart,PieChart,Search,Target,TrendingUp,Trophy,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart3_Bell_Clock_DollarSign_Eye_Gavel_Heart_LineChart_PieChart_Search_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart3,Bell,Clock,DollarSign,Eye,Gavel,Heart,LineChart,PieChart,Search,Target,TrendingUp,Trophy,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trophy.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart3_Bell_Clock_DollarSign_Eye_Gavel_Heart_LineChart_PieChart_Search_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart3,Bell,Clock,DollarSign,Eye,Gavel,Heart,LineChart,PieChart,Search,Target,TrendingUp,Trophy,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart3_Bell_Clock_DollarSign_Eye_Gavel_Heart_LineChart_PieChart_Search_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart3,Bell,Clock,DollarSign,Eye,Gavel,Heart,LineChart,PieChart,Search,Target,TrendingUp,Trophy,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart3_Bell_Clock_DollarSign_Eye_Gavel_Heart_LineChart_PieChart_Search_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart3,Bell,Clock,DollarSign,Eye,Gavel,Heart,LineChart,PieChart,Search,Target,TrendingUp,Trophy,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart3_Bell_Clock_DollarSign_Eye_Gavel_Heart_LineChart_PieChart_Search_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart3,Bell,Clock,DollarSign,Eye,Gavel,Heart,LineChart,PieChart,Search,Target,TrendingUp,Trophy,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/line-chart.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart3_Bell_Clock_DollarSign_Eye_Gavel_Heart_LineChart_PieChart_Search_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart3,Bell,Clock,DollarSign,Eye,Gavel,Heart,LineChart,PieChart,Search,Target,TrendingUp,Trophy,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pie-chart.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart3_Bell_Clock_DollarSign_Eye_Gavel_Heart_LineChart_PieChart_Search_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart3,Bell,Clock,DollarSign,Eye,Gavel,Heart,LineChart,PieChart,Search,Target,TrendingUp,Trophy,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bar-chart-3.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart3_Bell_Clock_DollarSign_Eye_Gavel_Heart_LineChart_PieChart_Search_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_34__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart3,Bell,Clock,DollarSign,Eye,Gavel,Heart,LineChart,PieChart,Search,Target,TrendingUp,Trophy,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart3_Bell_Clock_DollarSign_Eye_Gavel_Heart_LineChart_PieChart_Search_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_35__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart3,Bell,Clock,DollarSign,Eye,Gavel,Heart,LineChart,PieChart,Search,Target,TrendingUp,Trophy,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart3_Bell_Clock_DollarSign_Eye_Gavel_Heart_LineChart_PieChart_Search_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_36__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart3,Bell,Clock,DollarSign,Eye,Gavel,Heart,LineChart,PieChart,Search,Target,TrendingUp,Trophy,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart3_Bell_Clock_DollarSign_Eye_Gavel_Heart_LineChart_PieChart_Search_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_37__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart3,Bell,Clock,DollarSign,Eye,Gavel,Heart,LineChart,PieChart,Search,Target,TrendingUp,Trophy,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart3_Bell_Clock_DollarSign_Eye_Gavel_Heart_LineChart_PieChart_Search_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_38__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart3,Bell,Clock,DollarSign,Eye,Gavel,Heart,LineChart,PieChart,Search,Target,TrendingUp,Trophy,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/gavel.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,Bar,BarChart,CartesianGrid,Cell,Legend,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/component/ResponsiveContainer.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,Bar,BarChart,CartesianGrid,Cell,Legend,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/chart/AreaChart.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,Bar,BarChart,CartesianGrid,Cell,Legend,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/cartesian/CartesianGrid.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,Bar,BarChart,CartesianGrid,Cell,Legend,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/cartesian/XAxis.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,Bar,BarChart,CartesianGrid,Cell,Legend,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/cartesian/YAxis.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,Bar,BarChart,CartesianGrid,Cell,Legend,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/component/Tooltip.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,Bar,BarChart,CartesianGrid,Cell,Legend,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/component/Legend.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,Bar,BarChart,CartesianGrid,Cell,Legend,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/cartesian/Area.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,Bar,BarChart,CartesianGrid,Cell,Legend,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/chart/PieChart.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,Bar,BarChart,CartesianGrid,Cell,Legend,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/polar/Pie.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,Bar,BarChart,CartesianGrid,Cell,Legend,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/component/Cell.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,Bar,BarChart,CartesianGrid,Cell,Legend,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/chart/BarChart.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,Bar,BarChart,CartesianGrid,Cell,Legend,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/cartesian/Bar.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nfunction UserDashboard() {\n    var _user_profile, _chartData_winLossRatio_, _chartData_winLossRatio_1;\n    _s();\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [recentBids, setRecentBids] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [recentAuctions, setRecentAuctions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [notifications, setNotifications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [chartData, setChartData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        biddingActivity: [],\n        categorySpending: [],\n        monthlyPerformance: [],\n        winLossRatio: []\n    });\n    const { toast } = (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_5__.useToast)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_6__.useRouter)();\n    // Chart colors\n    const COLORS = {\n        primary: \"#3B82F6\",\n        secondary: \"#8B5CF6\",\n        success: \"#10B981\",\n        warning: \"#F59E0B\",\n        danger: \"#EF4444\",\n        info: \"#06B6D4\"\n    };\n    const PIE_COLORS = [\n        \"#3B82F6\",\n        \"#8B5CF6\",\n        \"#10B981\",\n        \"#F59E0B\",\n        \"#EF4444\",\n        \"#06B6D4\"\n    ];\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const userData = localStorage.getItem(\"user\");\n        if (userData) {\n            setUser(JSON.parse(userData));\n        }\n        loadDashboardData();\n    }, []);\n    const loadDashboardData = async ()=>{\n        try {\n            setLoading(true);\n            // Load user stats\n            const statsResponse = await _lib_api__WEBPACK_IMPORTED_MODULE_7__[\"default\"].get(\"/user/dashboard/stats\");\n            if (statsResponse.data.success) {\n                setStats(statsResponse.data.data);\n            }\n            // Load recent bids\n            const bidsResponse = await _lib_api__WEBPACK_IMPORTED_MODULE_7__[\"default\"].get(\"/user/bids?limit=5\");\n            if (bidsResponse.data.success) {\n                setRecentBids(bidsResponse.data.data.bids);\n            }\n            // Load recent auctions (recommended)\n            const auctionsResponse = await _lib_api__WEBPACK_IMPORTED_MODULE_7__[\"default\"].get(\"/auctions?limit=6&status=active\");\n            if (auctionsResponse.data.success) {\n                setRecentAuctions(auctionsResponse.data.data.auctions);\n            }\n            // Load notifications\n            const notificationsResponse = await _lib_api__WEBPACK_IMPORTED_MODULE_7__[\"default\"].get(\"/user/notifications?limit=5\");\n            if (notificationsResponse.data.success) {\n                setNotifications(notificationsResponse.data.data.notifications);\n            }\n            // Load chart data\n            const chartResponse = await _lib_api__WEBPACK_IMPORTED_MODULE_7__[\"default\"].get(\"/user/analytics/charts\");\n            if (chartResponse.data.success) {\n                setChartData(chartResponse.data.data);\n            }\n        } catch (error) {\n            console.error(\"Error loading dashboard data:\", error);\n            // Fallback to sample data\n            setStats({\n                activeBids: 5,\n                wonAuctions: 3,\n                totalSpent: 45000,\n                savedAuctions: 12,\n                totalBids: 28,\n                successRate: 75\n            });\n            setRecentBids([]);\n            setRecentAuctions([]);\n            setNotifications([]);\n            // Sample chart data\n            setChartData({\n                biddingActivity: [\n                    {\n                        month: \"يناير\",\n                        bids: 12,\n                        wins: 3\n                    },\n                    {\n                        month: \"فبراير\",\n                        bids: 19,\n                        wins: 5\n                    },\n                    {\n                        month: \"مارس\",\n                        bids: 15,\n                        wins: 2\n                    },\n                    {\n                        month: \"أبريل\",\n                        bids: 22,\n                        wins: 7\n                    },\n                    {\n                        month: \"مايو\",\n                        bids: 18,\n                        wins: 4\n                    },\n                    {\n                        month: \"يونيو\",\n                        bids: 25,\n                        wins: 8\n                    }\n                ],\n                categorySpending: [\n                    {\n                        name: \"إلكترونيات\",\n                        value: 15000,\n                        color: PIE_COLORS[0]\n                    },\n                    {\n                        name: \"سيارات\",\n                        value: 25000,\n                        color: PIE_COLORS[1]\n                    },\n                    {\n                        name: \"أثاث\",\n                        value: 8000,\n                        color: PIE_COLORS[2]\n                    },\n                    {\n                        name: \"مجوهرات\",\n                        value: 12000,\n                        color: PIE_COLORS[3]\n                    },\n                    {\n                        name: \"أخرى\",\n                        value: 5000,\n                        color: PIE_COLORS[4]\n                    }\n                ],\n                monthlyPerformance: [\n                    {\n                        month: \"يناير\",\n                        spent: 8000,\n                        saved: 2000\n                    },\n                    {\n                        month: \"فبراير\",\n                        spent: 12000,\n                        saved: 3000\n                    },\n                    {\n                        month: \"مارس\",\n                        spent: 6000,\n                        saved: 1500\n                    },\n                    {\n                        month: \"أبريل\",\n                        spent: 15000,\n                        saved: 4000\n                    },\n                    {\n                        month: \"مايو\",\n                        spent: 9000,\n                        saved: 2500\n                    },\n                    {\n                        month: \"يونيو\",\n                        spent: 18000,\n                        saved: 5000\n                    }\n                ],\n                winLossRatio: [\n                    {\n                        name: \"فوز\",\n                        value: 65,\n                        color: COLORS.success\n                    },\n                    {\n                        name: \"خسارة\",\n                        value: 35,\n                        color: COLORS.danger\n                    }\n                ]\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    const { formatAmount } = (0,_contexts_CurrencyContext__WEBPACK_IMPORTED_MODULE_8__.useCurrency)();\n    const formatPrice = (price)=>{\n        return formatAmount(price);\n    };\n    const formatTimeRemaining = (endTime)=>{\n        const now = new Date();\n        const end = new Date(endTime);\n        const diff = end.getTime() - now.getTime();\n        if (diff <= 0) return \"انتهى\";\n        const days = Math.floor(diff / (1000 * 60 * 60 * 24));\n        const hours = Math.floor(diff % (1000 * 60 * 60 * 24) / (1000 * 60 * 60));\n        const minutes = Math.floor(diff % (1000 * 60 * 60) / (1000 * 60));\n        if (days > 0) return \"\".concat(days, \" يوم\");\n        if (hours > 0) return \"\".concat(hours, \" ساعة\");\n        return \"\".concat(minutes, \" دقيقة\");\n    };\n    const formatRelativeTime = (dateString)=>{\n        const now = new Date();\n        const date = new Date(dateString);\n        const diff = now.getTime() - date.getTime();\n        const minutes = Math.floor(diff / (1000 * 60));\n        const hours = Math.floor(diff / (1000 * 60 * 60));\n        const days = Math.floor(diff / (1000 * 60 * 60 * 24));\n        if (days > 0) return \"منذ \".concat(days, \" يوم\");\n        if (hours > 0) return \"منذ \".concat(hours, \" ساعة\");\n        if (minutes > 0) return \"منذ \".concat(minutes, \" دقيقة\");\n        return \"الآن\";\n    };\n    const getBidStatus = (bid)=>{\n        if (bid.isWinning) return {\n            label: \"أعلى مزايدة\",\n            variant: \"default\",\n            color: \"text-green-600\"\n        };\n        if (bid.auction.status === \"active\") return {\n            label: \"متنافس\",\n            variant: \"secondary\",\n            color: \"text-blue-600\"\n        };\n        return {\n            label: \"تم تجاوزها\",\n            variant: \"outline\",\n            color: \"text-gray-600\"\n        };\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                children: \"جاري تحميل الإحصائيات...\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                lineNumber: 264,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n            lineNumber: 263,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg p-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-3xl font-bold\",\n                                    children: [\n                                        \"مرحباً، \",\n                                        (user === null || user === void 0 ? void 0 : (_user_profile = user.profile) === null || _user_profile === void 0 ? void 0 : _user_profile.fullName) || \"المستخدم\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                    lineNumber: 275,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-blue-100 mt-2\",\n                                    children: \"استكشف المزادات المتاحة وتابع مزايداتك\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                    lineNumber: 278,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                            lineNumber: 274,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden md:flex items-center gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold\",\n                                            children: [\n                                                (stats === null || stats === void 0 ? void 0 : stats.successRate) || 0,\n                                                \"%\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                            lineNumber: 284,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-blue-100\",\n                                            children: \"معدل النجاح\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                            lineNumber: 285,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                    lineNumber: 283,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold\",\n                                            children: (stats === null || stats === void 0 ? void 0 : stats.totalBids) || 0\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                            lineNumber: 288,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-blue-100\",\n                                            children: \"إجمالي المزايدات\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                            lineNumber: 289,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                    lineNumber: 287,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                            lineNumber: 282,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                    lineNumber: 273,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                lineNumber: 272,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-4 gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                        className: \"h-16 text-lg bg-blue-600 hover:bg-blue-700\",\n                        size: \"lg\",\n                        onClick: ()=>router.push(\"/user/auctions\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_Bell_Clock_DollarSign_Eye_Gavel_Heart_LineChart_PieChart_Search_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                className: \"h-6 w-6 ml-2\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                lineNumber: 302,\n                                columnNumber: 13\n                            }, this),\n                            \"استكشاف المزادات\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                        lineNumber: 297,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                        variant: \"outline\",\n                        className: \"h-16 text-lg border-green-200 text-green-700 hover:bg-green-50\",\n                        size: \"lg\",\n                        onClick: ()=>router.push(\"/user/my-bids\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_Bell_Clock_DollarSign_Eye_Gavel_Heart_LineChart_PieChart_Search_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                className: \"h-6 w-6 ml-2\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                lineNumber: 311,\n                                columnNumber: 13\n                            }, this),\n                            \"مزايداتي\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                        lineNumber: 305,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                        variant: \"outline\",\n                        className: \"h-16 text-lg border-purple-200 text-purple-700 hover:bg-purple-50\",\n                        size: \"lg\",\n                        onClick: ()=>router.push(\"/user/notifications\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_Bell_Clock_DollarSign_Eye_Gavel_Heart_LineChart_PieChart_Search_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                className: \"h-6 w-6 ml-2\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                lineNumber: 320,\n                                columnNumber: 13\n                            }, this),\n                            \"التنبيهات\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                        lineNumber: 314,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                        variant: \"outline\",\n                        className: \"h-16 text-lg border-orange-200 text-orange-700 hover:bg-orange-50\",\n                        size: \"lg\",\n                        onClick: ()=>router.push(\"/user/profile\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_Bell_Clock_DollarSign_Eye_Gavel_Heart_LineChart_PieChart_Search_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                className: \"h-6 w-6 ml-2\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                lineNumber: 329,\n                                columnNumber: 13\n                            }, this),\n                            \"الملف الشخصي\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                        lineNumber: 323,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                lineNumber: 296,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        className: \"bg-gradient-to-br from-blue-50 to-blue-100 border-blue-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            className: \"p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-blue-600\",\n                                                children: \"مزايداتي النشطة\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                lineNumber: 340,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-3xl font-bold text-blue-900\",\n                                                children: (stats === null || stats === void 0 ? void 0 : stats.activeBids) || 0\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                lineNumber: 341,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-blue-600 mt-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_Bell_Clock_DollarSign_Eye_Gavel_Heart_LineChart_PieChart_Search_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        className: \"h-3 w-3 inline ml-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                        lineNumber: 343,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"نشط الآن\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                lineNumber: 342,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                        lineNumber: 339,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-12 w-12 bg-blue-200 rounded-full flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_Bell_Clock_DollarSign_Eye_Gavel_Heart_LineChart_PieChart_Search_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"h-6 w-6 text-blue-600\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                            lineNumber: 348,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                        lineNumber: 347,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                lineNumber: 338,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                            lineNumber: 337,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                        lineNumber: 336,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        className: \"bg-gradient-to-br from-green-50 to-green-100 border-green-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            className: \"p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-green-600\",\n                                                children: \"المزادات المكسوبة\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                lineNumber: 358,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-3xl font-bold text-green-900\",\n                                                children: (stats === null || stats === void 0 ? void 0 : stats.wonAuctions) || 0\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                lineNumber: 359,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-green-600 mt-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_Bell_Clock_DollarSign_Eye_Gavel_Heart_LineChart_PieChart_Search_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        className: \"h-3 w-3 inline ml-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                        lineNumber: 361,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"إنجاز رائع\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                lineNumber: 360,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                        lineNumber: 357,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-12 w-12 bg-green-200 rounded-full flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_Bell_Clock_DollarSign_Eye_Gavel_Heart_LineChart_PieChart_Search_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            className: \"h-6 w-6 text-green-600\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                            lineNumber: 366,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                        lineNumber: 365,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                lineNumber: 356,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                            lineNumber: 355,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                        lineNumber: 354,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        className: \"bg-gradient-to-br from-purple-50 to-purple-100 border-purple-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            className: \"p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-purple-600\",\n                                                children: \"إجمالي المبلغ المنفق\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                lineNumber: 376,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-3xl font-bold text-purple-900\",\n                                                children: formatPrice((stats === null || stats === void 0 ? void 0 : stats.totalSpent) || 0)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                lineNumber: 377,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-purple-600 mt-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_Bell_Clock_DollarSign_Eye_Gavel_Heart_LineChart_PieChart_Search_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                        className: \"h-3 w-3 inline ml-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                        lineNumber: 379,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"استثمار ذكي\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                lineNumber: 378,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                        lineNumber: 375,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-12 w-12 bg-purple-200 rounded-full flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_Bell_Clock_DollarSign_Eye_Gavel_Heart_LineChart_PieChart_Search_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            className: \"h-6 w-6 text-purple-600\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                            lineNumber: 384,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                        lineNumber: 383,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                lineNumber: 374,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                            lineNumber: 373,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                        lineNumber: 372,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        className: \"bg-gradient-to-br from-orange-50 to-orange-100 border-orange-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            className: \"p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-orange-600\",\n                                                children: \"المزادات المحفوظة\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                lineNumber: 394,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-3xl font-bold text-orange-900\",\n                                                children: (stats === null || stats === void 0 ? void 0 : stats.savedAuctions) || 0\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                lineNumber: 395,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-orange-600 mt-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_Bell_Clock_DollarSign_Eye_Gavel_Heart_LineChart_PieChart_Search_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                        className: \"h-3 w-3 inline ml-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                        lineNumber: 397,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"مفضلة\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                lineNumber: 396,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                        lineNumber: 393,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-12 w-12 bg-orange-200 rounded-full flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_Bell_Clock_DollarSign_Eye_Gavel_Heart_LineChart_PieChart_Search_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            className: \"h-6 w-6 text-orange-600\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                            lineNumber: 402,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                        lineNumber: 401,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                lineNumber: 392,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                            lineNumber: 391,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                        lineNumber: 390,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                lineNumber: 335,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_Bell_Clock_DollarSign_Eye_Gavel_Heart_LineChart_PieChart_Search_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                className: \"h-5 w-5 text-blue-600\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                lineNumber: 415,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"نشاط المزايدات الشهري\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                        lineNumber: 414,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                        children: \"عدد المزايدات والانتصارات خلال الأشهر الماضية\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                        lineNumber: 418,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                lineNumber: 413,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_19__.ResponsiveContainer, {\n                                    width: \"100%\",\n                                    height: 300,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_20__.AreaChart, {\n                                        data: chartData.biddingActivity,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_21__.CartesianGrid, {\n                                                strokeDasharray: \"3 3\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                lineNumber: 425,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_22__.XAxis, {\n                                                dataKey: \"month\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                lineNumber: 426,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_23__.YAxis, {}, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                lineNumber: 427,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_24__.Tooltip, {}, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                lineNumber: 428,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_25__.Legend, {}, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                lineNumber: 429,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_26__.Area, {\n                                                type: \"monotone\",\n                                                dataKey: \"bids\",\n                                                stackId: \"1\",\n                                                stroke: COLORS.primary,\n                                                fill: COLORS.primary,\n                                                fillOpacity: 0.6,\n                                                name: \"المزايدات\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                lineNumber: 430,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_26__.Area, {\n                                                type: \"monotone\",\n                                                dataKey: \"wins\",\n                                                stackId: \"2\",\n                                                stroke: COLORS.success,\n                                                fill: COLORS.success,\n                                                fillOpacity: 0.8,\n                                                name: \"الانتصارات\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                lineNumber: 439,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                        lineNumber: 424,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                    lineNumber: 423,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                lineNumber: 422,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                        lineNumber: 412,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_Bell_Clock_DollarSign_Eye_Gavel_Heart_LineChart_PieChart_Search_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                className: \"h-5 w-5 text-purple-600\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                lineNumber: 457,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"الإنفاق حسب الفئة\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                        lineNumber: 456,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                        children: \"توزيع إنفاقك على الفئات المختلفة\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                        lineNumber: 460,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                lineNumber: 455,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_19__.ResponsiveContainer, {\n                                    width: \"100%\",\n                                    height: 300,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_28__.PieChart, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_29__.Pie, {\n                                                data: chartData.categorySpending,\n                                                cx: \"50%\",\n                                                cy: \"50%\",\n                                                labelLine: false,\n                                                label: (param)=>{\n                                                    let { name, percent } = param;\n                                                    return \"\".concat(name, \" \").concat((percent * 100).toFixed(0), \"%\");\n                                                },\n                                                outerRadius: 80,\n                                                fill: \"#8884d8\",\n                                                dataKey: \"value\",\n                                                children: chartData.categorySpending.map((entry, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_30__.Cell, {\n                                                        fill: entry.color\n                                                    }, \"cell-\".concat(index), false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                        lineNumber: 478,\n                                                        columnNumber: 23\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                lineNumber: 467,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_24__.Tooltip, {\n                                                formatter: (value)=>formatPrice(value)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                lineNumber: 481,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                        lineNumber: 466,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                    lineNumber: 465,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                lineNumber: 464,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                        lineNumber: 454,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                lineNumber: 410,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_Bell_Clock_DollarSign_Eye_Gavel_Heart_LineChart_PieChart_Search_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n                                                className: \"h-5 w-5 text-green-600\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                lineNumber: 494,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"الأداء الشهري\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                        lineNumber: 493,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                        children: \"المبلغ المنفق والمدخر شهرياً\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                        lineNumber: 497,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                lineNumber: 492,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_19__.ResponsiveContainer, {\n                                    width: \"100%\",\n                                    height: 300,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_32__.BarChart, {\n                                        data: chartData.monthlyPerformance,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_21__.CartesianGrid, {\n                                                strokeDasharray: \"3 3\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                lineNumber: 504,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_22__.XAxis, {\n                                                dataKey: \"month\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                lineNumber: 505,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_23__.YAxis, {}, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                lineNumber: 506,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_24__.Tooltip, {\n                                                formatter: (value)=>formatPrice(value)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                lineNumber: 507,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_25__.Legend, {}, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                lineNumber: 508,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_33__.Bar, {\n                                                dataKey: \"spent\",\n                                                fill: COLORS.danger,\n                                                name: \"المنفق\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                lineNumber: 509,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_33__.Bar, {\n                                                dataKey: \"saved\",\n                                                fill: COLORS.success,\n                                                name: \"المدخر\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                lineNumber: 510,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                        lineNumber: 503,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                    lineNumber: 502,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                lineNumber: 501,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                        lineNumber: 491,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_Bell_Clock_DollarSign_Eye_Gavel_Heart_LineChart_PieChart_Search_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                className: \"h-5 w-5 text-yellow-600\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                lineNumber: 520,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"نسبة النجاح\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                        lineNumber: 519,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                        children: \"نسبة الفوز إلى الخسارة في المزايدات\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                        lineNumber: 523,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                lineNumber: 518,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_19__.ResponsiveContainer, {\n                                        width: \"100%\",\n                                        height: 300,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_28__.PieChart, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_29__.Pie, {\n                                                    data: chartData.winLossRatio,\n                                                    cx: \"50%\",\n                                                    cy: \"50%\",\n                                                    innerRadius: 60,\n                                                    outerRadius: 100,\n                                                    paddingAngle: 5,\n                                                    dataKey: \"value\",\n                                                    children: chartData.winLossRatio.map((entry, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_30__.Cell, {\n                                                            fill: entry.color\n                                                        }, \"cell-\".concat(index), false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                            lineNumber: 540,\n                                                            columnNumber: 23\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                    lineNumber: 530,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_24__.Tooltip, {\n                                                    formatter: (value)=>\"\".concat(value, \"%\")\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                    lineNumber: 543,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_25__.Legend, {}, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                    lineNumber: 544,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                            lineNumber: 529,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                        lineNumber: 528,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-4 flex justify-center gap-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-2xl font-bold text-green-600\",\n                                                        children: [\n                                                            ((_chartData_winLossRatio_ = chartData.winLossRatio[0]) === null || _chartData_winLossRatio_ === void 0 ? void 0 : _chartData_winLossRatio_.value) || 0,\n                                                            \"%\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                        lineNumber: 549,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: \"معدل النجاح\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                        lineNumber: 550,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                lineNumber: 548,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-2xl font-bold text-red-600\",\n                                                        children: [\n                                                            ((_chartData_winLossRatio_1 = chartData.winLossRatio[1]) === null || _chartData_winLossRatio_1 === void 0 ? void 0 : _chartData_winLossRatio_1.value) || 0,\n                                                            \"%\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                        lineNumber: 553,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: \"معدل الخسارة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                        lineNumber: 554,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                lineNumber: 552,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                        lineNumber: 547,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                lineNumber: 527,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                        lineNumber: 517,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                lineNumber: 489,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_Bell_Clock_DollarSign_Eye_Gavel_Heart_LineChart_PieChart_Search_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        className: \"h-5 w-5 text-blue-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                        lineNumber: 568,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"مزايداتي الحديثة\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                lineNumber: 567,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                variant: \"ghost\",\n                                                size: \"sm\",\n                                                onClick: ()=>router.push(\"/user/my-bids\"),\n                                                children: [\n                                                    \"عرض الكل\",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_Bell_Clock_DollarSign_Eye_Gavel_Heart_LineChart_PieChart_Search_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_34__[\"default\"], {\n                                                        className: \"h-4 w-4 mr-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                        lineNumber: 577,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                lineNumber: 571,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                        lineNumber: 566,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                        children: \"آخر المزايدات التي شاركت فيها\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                        lineNumber: 580,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                lineNumber: 565,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                children: recentBids.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3\",\n                                    children: recentBids.slice(0, 3).map((bid)=>{\n                                        const status = getBidStatus(bid);\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-medium text-gray-900\",\n                                                            children: bid.auction.title\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                            lineNumber: 592,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-4 mt-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-gray-600\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_Bell_Clock_DollarSign_Eye_Gavel_Heart_LineChart_PieChart_Search_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_35__[\"default\"], {\n                                                                            className: \"h-4 w-4 inline ml-1\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                                            lineNumber: 595,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        formatTimeRemaining(bid.auction.endTime)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                                    lineNumber: 594,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-gray-600\",\n                                                                    children: [\n                                                                        \"مزايدتي: \",\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"font-medium\",\n                                                                            children: formatPrice(bid.amount)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                                            lineNumber: 599,\n                                                                            columnNumber: 40\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                                    lineNumber: 598,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                            lineNumber: 593,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-gray-500 mt-1\",\n                                                            children: formatRelativeTime(bid.placedAt)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                            lineNumber: 602,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                    lineNumber: 591,\n                                                    columnNumber: 25\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex flex-col items-end gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                            variant: status.variant,\n                                                            className: status.color,\n                                                            children: status.label\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                            lineNumber: 607,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                            size: \"sm\",\n                                                            variant: \"outline\",\n                                                            onClick: ()=>router.push(\"/auctions/\".concat(bid.auction._id)),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_Bell_Clock_DollarSign_Eye_Gavel_Heart_LineChart_PieChart_Search_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_36__[\"default\"], {\n                                                                    className: \"h-4 w-4 ml-1\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                                    lineNumber: 615,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                \"عرض\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                            lineNumber: 610,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                    lineNumber: 606,\n                                                    columnNumber: 25\n                                                }, this)\n                                            ]\n                                        }, bid._id, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                            lineNumber: 590,\n                                            columnNumber: 23\n                                        }, this);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                    lineNumber: 586,\n                                    columnNumber: 17\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center py-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_Bell_Clock_DollarSign_Eye_Gavel_Heart_LineChart_PieChart_Search_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"h-12 w-12 text-gray-400 mx-auto mb-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                            lineNumber: 625,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-medium text-gray-900 mb-2\",\n                                            children: \"لا توجد مزايدات حديثة\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                            lineNumber: 626,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 mb-4\",\n                                            children: \"ابدأ بالمشاركة في المزادات المتاحة\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                            lineNumber: 627,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            onClick: ()=>router.push(\"/user/auctions\"),\n                                            children: \"استكشاف المزادات\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                            lineNumber: 628,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                    lineNumber: 624,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                lineNumber: 584,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                        lineNumber: 564,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_Bell_Clock_DollarSign_Eye_Gavel_Heart_LineChart_PieChart_Search_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"h-5 w-5 text-orange-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                        lineNumber: 641,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"التنبيهات\",\n                                                    notifications.filter((n)=>!n.read).length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                        variant: \"destructive\",\n                                                        className: \"text-xs\",\n                                                        children: notifications.filter((n)=>!n.read).length\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                        lineNumber: 644,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                lineNumber: 640,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                variant: \"ghost\",\n                                                size: \"sm\",\n                                                onClick: ()=>router.push(\"/user/notifications\"),\n                                                children: [\n                                                    \"عرض الكل\",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_Bell_Clock_DollarSign_Eye_Gavel_Heart_LineChart_PieChart_Search_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_34__[\"default\"], {\n                                                        className: \"h-4 w-4 mr-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                        lineNumber: 655,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                lineNumber: 649,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                        lineNumber: 639,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                        children: \"آخر التحديثات والإشعارات\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                        lineNumber: 658,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                lineNumber: 638,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                children: notifications.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3\",\n                                    children: notifications.slice(0, 3).map((notification)=>{\n                                        const getNotificationStyle = (type)=>{\n                                            switch(type){\n                                                case \"auction_won\":\n                                                    return \"bg-green-50 border-green-200 text-green-800\";\n                                                case \"auction_ending\":\n                                                    return \"bg-yellow-50 border-yellow-200 text-yellow-800\";\n                                                case \"new_auction\":\n                                                    return \"bg-blue-50 border-blue-200 text-blue-800\";\n                                                case \"bid_outbid\":\n                                                    return \"bg-red-50 border-red-200 text-red-800\";\n                                                default:\n                                                    return \"bg-gray-50 border-gray-200 text-gray-800\";\n                                            }\n                                        };\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-3 border rounded-lg \".concat(getNotificationStyle(notification.type), \" \").concat(!notification.read ? \"ring-2 ring-blue-200\" : \"\"),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm font-medium\",\n                                                                children: notification.title\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                                lineNumber: 688,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs mt-1\",\n                                                                children: notification.message\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                                lineNumber: 689,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs opacity-75 mt-2\",\n                                                                children: formatRelativeTime(notification.createdAt)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                                lineNumber: 690,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                        lineNumber: 687,\n                                                        columnNumber: 27\n                                                    }, this),\n                                                    !notification.read && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-2 h-2 bg-blue-600 rounded-full mt-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                        lineNumber: 695,\n                                                        columnNumber: 29\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                lineNumber: 686,\n                                                columnNumber: 25\n                                            }, this)\n                                        }, notification._id, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                            lineNumber: 682,\n                                            columnNumber: 23\n                                        }, this);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                    lineNumber: 664,\n                                    columnNumber: 17\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center py-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_Bell_Clock_DollarSign_Eye_Gavel_Heart_LineChart_PieChart_Search_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"h-12 w-12 text-gray-400 mx-auto mb-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                            lineNumber: 704,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-medium text-gray-900 mb-2\",\n                                            children: \"لا توجد تنبيهات\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                            lineNumber: 705,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600\",\n                                            children: \"ستظهر هنا التحديثات المهمة\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                            lineNumber: 706,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                    lineNumber: 703,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                lineNumber: 662,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                        lineNumber: 637,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                lineNumber: 562,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_Bell_Clock_DollarSign_Eye_Gavel_Heart_LineChart_PieChart_Search_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_37__[\"default\"], {\n                                                className: \"h-5 w-5 text-purple-600\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                lineNumber: 718,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"مزادات مقترحة لك\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                        lineNumber: 717,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        onClick: ()=>router.push(\"/user/auctions\"),\n                                        children: [\n                                            \"عرض المزيد\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_Bell_Clock_DollarSign_Eye_Gavel_Heart_LineChart_PieChart_Search_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_34__[\"default\"], {\n                                                className: \"h-4 w-4 mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                lineNumber: 727,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                        lineNumber: 721,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                lineNumber: 716,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                children: \"مزادات قد تهمك بناءً على اهتماماتك السابقة\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                lineNumber: 730,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                        lineNumber: 715,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        children: recentAuctions.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\",\n                            children: recentAuctions.slice(0, 6).map((auction)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"article\", {\n                                    className: \"border rounded-lg p-4 hover:shadow-lg transition-all duration-200 hover:border-blue-300 cursor-pointer\",\n                                    onClick: ()=>router.push(\"/auctions/\".concat(auction._id)),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"aspect-video bg-gradient-to-br from-gray-100 to-gray-200 rounded mb-3 flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_Bell_Clock_DollarSign_Eye_Gavel_Heart_LineChart_PieChart_Search_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_38__[\"default\"], {\n                                                className: \"h-8 w-8 text-gray-400\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                lineNumber: 744,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                            lineNumber: 743,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-medium text-gray-900 line-clamp-2\",\n                                                    children: auction.title\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                    lineNumber: 748,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-gray-600\",\n                                                                    children: [\n                                                                        \"السعر الحالي: \",\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"font-medium text-green-600\",\n                                                                            children: formatPrice(auction.currentBid)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                                            lineNumber: 753,\n                                                                            columnNumber: 43\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                                    lineNumber: 752,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-gray-500\",\n                                                                    children: [\n                                                                        \"يبدأ من: \",\n                                                                        formatPrice(auction.startingBid)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                                    lineNumber: 755,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                            lineNumber: 751,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                            variant: \"outline\",\n                                                            className: \"text-xs\",\n                                                            children: auction.category\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                            lineNumber: 759,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                    lineNumber: 750,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between pt-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-1 text-xs text-gray-500\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_Bell_Clock_DollarSign_Eye_Gavel_Heart_LineChart_PieChart_Search_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_35__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                                    lineNumber: 766,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                formatTimeRemaining(auction.endTime)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                            lineNumber: 765,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-1 text-xs text-gray-500\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_Bell_Clock_DollarSign_Eye_Gavel_Heart_LineChart_PieChart_Search_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                                    lineNumber: 770,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                auction.bidsCount,\n                                                                \" مزايدة\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                            lineNumber: 769,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                    lineNumber: 764,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    size: \"sm\",\n                                                    className: \"w-full mt-3\",\n                                                    onClick: (e)=>{\n                                                        e.stopPropagation();\n                                                        router.push(\"/auctions/\".concat(auction._id));\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_Bell_Clock_DollarSign_Eye_Gavel_Heart_LineChart_PieChart_Search_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_36__[\"default\"], {\n                                                            className: \"h-4 w-4 ml-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                            lineNumber: 783,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        \"شاهد التفاصيل\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                                    lineNumber: 775,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                            lineNumber: 747,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, auction._id, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                    lineNumber: 738,\n                                    columnNumber: 19\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                            lineNumber: 736,\n                            columnNumber: 15\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center py-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart3_Bell_Clock_DollarSign_Eye_Gavel_Heart_LineChart_PieChart_Search_Target_TrendingUp_Trophy_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_37__[\"default\"], {\n                                    className: \"h-16 w-16 text-gray-400 mx-auto mb-4\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                    lineNumber: 792,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-medium text-gray-900 mb-2\",\n                                    children: \"لا توجد مزادات متاحة حالياً\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                    lineNumber: 793,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 mb-6\",\n                                    children: \"تحقق مرة أخرى لاحقاً أو استكشف الفئات المختلفة\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                    lineNumber: 794,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    onClick: ()=>router.push(\"/user/auctions\"),\n                                    children: \"استكشاف المزادات\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                                    lineNumber: 795,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                            lineNumber: 791,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                        lineNumber: 734,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n                lineNumber: 714,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/dashboard/page.tsx\",\n        lineNumber: 270,\n        columnNumber: 5\n    }, this);\n}\n_s(UserDashboard, \"m/Vw8DQQyp2mq6xEF53eRK4Izqc=\", false, function() {\n    return [\n        _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_5__.useToast,\n        next_navigation__WEBPACK_IMPORTED_MODULE_6__.useRouter,\n        _contexts_CurrencyContext__WEBPACK_IMPORTED_MODULE_8__.useCurrency\n    ];\n});\n_c = UserDashboard;\nvar _c;\n$RefreshReg$(_c, \"UserDashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/user/dashboard/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./contexts/CurrencyContext.tsx":
/*!**************************************!*\
  !*** ./contexts/CurrencyContext.tsx ***!
  \**************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CurrencyProvider: function() { return /* binding */ CurrencyProvider; },\n/* harmony export */   useCurrency: function() { return /* binding */ useCurrency; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_currencyService__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/currencyService */ \"(app-pages-browser)/./lib/currencyService.ts\");\n/* __next_internal_client_entry_do_not_use__ CurrencyProvider,useCurrency auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\nconst CurrencyContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction CurrencyProvider(param) {\n    let { children } = param;\n    _s();\n    const [userCurrency, setUserCurrencyState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"SAR\");\n    const [exchangeRates, setExchangeRates] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // Initialize currency and exchange rates\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const initializeCurrency = async ()=>{\n            try {\n                // Load saved currency\n                const savedCurrency = _lib_currencyService__WEBPACK_IMPORTED_MODULE_2__.currencyService.getUserCurrency();\n                setUserCurrencyState(savedCurrency);\n                // Load exchange rates\n                await _lib_currencyService__WEBPACK_IMPORTED_MODULE_2__.currencyService.updateExchangeRates();\n                setExchangeRates(_lib_currencyService__WEBPACK_IMPORTED_MODULE_2__.currencyService.getExchangeRates());\n            } catch (error) {\n                console.error(\"Failed to initialize currency:\", error);\n            } finally{\n                setIsLoading(false);\n            }\n        };\n        initializeCurrency();\n    }, []);\n    // Update currency and save to localStorage\n    const setUserCurrency = async (currency)=>{\n        try {\n            setUserCurrencyState(currency);\n            _lib_currencyService__WEBPACK_IMPORTED_MODULE_2__.currencyService.setUserCurrency(currency);\n            // Update exchange rates if needed\n            await _lib_currencyService__WEBPACK_IMPORTED_MODULE_2__.currencyService.updateExchangeRates();\n            setExchangeRates(_lib_currencyService__WEBPACK_IMPORTED_MODULE_2__.currencyService.getExchangeRates());\n        } catch (error) {\n            console.error(\"Failed to update currency:\", error);\n        }\n    };\n    // Format amount with currency\n    const formatAmount = (amount, currency)=>{\n        return _lib_currencyService__WEBPACK_IMPORTED_MODULE_2__.currencyService.formatAmount(amount, currency || userCurrency);\n    };\n    // Convert amount between currencies\n    const convertAmount = async (amount, fromCurrency, toCurrency)=>{\n        return _lib_currencyService__WEBPACK_IMPORTED_MODULE_2__.currencyService.convertCurrency(amount, fromCurrency, toCurrency || userCurrency);\n    };\n    const value = {\n        userCurrency,\n        setUserCurrency,\n        formatAmount,\n        convertAmount,\n        exchangeRates,\n        isLoading\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CurrencyContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/brid1/frontend/contexts/CurrencyContext.tsx\",\n        lineNumber: 77,\n        columnNumber: 5\n    }, this);\n}\n_s(CurrencyProvider, \"2wekN+CRrLyAImqR3bXukl0PecE=\");\n_c = CurrencyProvider;\nfunction useCurrency() {\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(CurrencyContext);\n    if (context === undefined) {\n        throw new Error(\"useCurrency must be used within a CurrencyProvider\");\n    }\n    return context;\n}\n_s1(useCurrency, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nvar _c;\n$RefreshReg$(_c, \"CurrencyProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./contexts/CurrencyContext.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./lib/currencyService.ts":
/*!********************************!*\
  !*** ./lib/currencyService.ts ***!
  \********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   currencyService: function() { return /* binding */ currencyService; }\n/* harmony export */ });\n// Frontend Currency Service\nclass CurrencyService {\n    // Get current exchange rates\n    async getExchangeRates() {\n        try {\n            // Check if cache is still valid\n            if (this.lastUpdated && Date.now() - this.lastUpdated < this.CACHE_DURATION) {\n                return this.exchangeRates;\n            }\n            // Try to fetch fresh rates from backend\n            try {\n                const response = await fetch(\"http://localhost:5000/api/currency/rates\");\n                if (response.ok) {\n                    const data = await response.json();\n                    if (data.success && data.data && data.data.rates) {\n                        this.exchangeRates = data.data.rates;\n                        this.lastUpdated = Date.now();\n                        console.log(\"✅ Currency rates updated from backend\");\n                    }\n                }\n            } catch (apiError) {\n                console.warn(\"⚠️ Failed to fetch fresh currency rates, using cached rates:\", apiError);\n            }\n            return this.exchangeRates;\n        } catch (error) {\n            console.error(\"❌ Currency service error:\", error);\n            return this.exchangeRates; // Return cached rates as fallback\n        }\n    }\n    // Convert amount from one currency to another\n    async convertCurrency(amount, fromCurrency, toCurrency) {\n        try {\n            if (fromCurrency === toCurrency) {\n                return amount;\n            }\n            const rates = await this.getExchangeRates();\n            // Convert to USD first, then to target currency\n            const usdAmount = amount / rates[fromCurrency];\n            const convertedAmount = usdAmount * rates[toCurrency];\n            return Math.round(convertedAmount * 100) / 100; // Round to 2 decimal places\n        } catch (error) {\n            console.error(\"❌ Currency conversion error:\", error);\n            throw new Error(\"Currency conversion failed\");\n        }\n    }\n    // Convert amount to USD (base currency for comparisons)\n    async convertToUSD(amount, fromCurrency) {\n        return this.convertCurrency(amount, fromCurrency, \"USD\");\n    }\n    // Convert amount from USD to target currency\n    async convertFromUSD(amount, toCurrency) {\n        return this.convertCurrency(amount, \"USD\", toCurrency);\n    }\n    // Get currency symbol\n    getCurrencySymbol(currency) {\n        const symbols = {\n            USD: \"$\",\n            SAR: \"ر.س\",\n            EUR: \"€\",\n            GBP: \"\\xa3\",\n            AED: \"د.إ\",\n            KWD: \"د.ك\",\n            QAR: \"ر.ق\",\n            BHD: \"د.ب\",\n            OMR: \"ر.ع\"\n        };\n        return symbols[currency] || currency;\n    }\n    // Format amount with currency\n    formatAmount(amount, currency) {\n        const symbol = this.getCurrencySymbol(currency);\n        const formattedAmount = amount.toLocaleString();\n        // For Arabic currencies, put symbol after number\n        if ([\n            \"SAR\",\n            \"AED\",\n            \"KWD\",\n            \"QAR\",\n            \"BHD\",\n            \"OMR\"\n        ].includes(currency)) {\n            return \"\".concat(formattedAmount, \" \").concat(symbol);\n        }\n        // For Western currencies, put symbol before number\n        return \"\".concat(symbol).concat(formattedAmount);\n    }\n    // Get supported currencies\n    getSupportedCurrencies() {\n        return [\n            {\n                code: \"USD\",\n                name: \"US Dollar\",\n                symbol: \"$\"\n            },\n            {\n                code: \"SAR\",\n                name: \"Saudi Riyal\",\n                symbol: \"ر.س\"\n            },\n            {\n                code: \"EUR\",\n                name: \"Euro\",\n                symbol: \"€\"\n            },\n            {\n                code: \"GBP\",\n                name: \"British Pound\",\n                symbol: \"\\xa3\"\n            },\n            {\n                code: \"AED\",\n                name: \"UAE Dirham\",\n                symbol: \"د.إ\"\n            },\n            {\n                code: \"KWD\",\n                name: \"Kuwaiti Dinar\",\n                symbol: \"د.ك\"\n            },\n            {\n                code: \"QAR\",\n                name: \"Qatari Riyal\",\n                symbol: \"ر.ق\"\n            },\n            {\n                code: \"BHD\",\n                name: \"Bahraini Dinar\",\n                symbol: \"د.ب\"\n            },\n            {\n                code: \"OMR\",\n                name: \"Omani Rial\",\n                symbol: \"ر.ع\"\n            }\n        ];\n    }\n    // Validate currency code\n    isValidCurrency(currency) {\n        return Object.keys(this.exchangeRates).includes(currency);\n    }\n    // Get user's preferred currency from localStorage or default\n    getUserCurrency() {\n        try {\n            return localStorage.getItem(\"preferredCurrency\") || \"SAR\";\n        } catch (e) {\n            return \"SAR\";\n        }\n    }\n    // Set user's preferred currency\n    setUserCurrency(currency) {\n        try {\n            if (this.isValidCurrency(currency)) {\n                localStorage.setItem(\"preferredCurrency\", currency);\n            }\n        } catch (error) {\n            console.error(\"Failed to save currency preference:\", error);\n        }\n    }\n    // Convert auction data to user's preferred currency\n    async convertAuctionData(auction, userCurrency) {\n        const targetCurrency = userCurrency || this.getUserCurrency();\n        if (!auction.currency || auction.currency === targetCurrency) {\n            return auction;\n        }\n        try {\n            const convertedAuction = {\n                ...auction\n            };\n            // Convert prices\n            if (auction.currentBid) {\n                convertedAuction.currentBid = await this.convertCurrency(auction.currentBid, auction.currency, targetCurrency);\n            }\n            if (auction.startingPrice) {\n                convertedAuction.startingPrice = await this.convertCurrency(auction.startingPrice, auction.currency, targetCurrency);\n            }\n            if (auction.reservePrice) {\n                convertedAuction.reservePrice = await this.convertCurrency(auction.reservePrice, auction.currency, targetCurrency);\n            }\n            // Update currency\n            convertedAuction.currency = targetCurrency;\n            convertedAuction.originalCurrency = auction.currency;\n            return convertedAuction;\n        } catch (error) {\n            console.error(\"Failed to convert auction data:\", error);\n            return auction; // Return original data if conversion fails\n        }\n    }\n    constructor(){\n        this.exchangeRates = {\n            USD: 1.0,\n            SAR: 3.75,\n            EUR: 0.85,\n            GBP: 0.73,\n            AED: 3.67,\n            KWD: 0.30,\n            QAR: 3.64,\n            BHD: 0.38,\n            OMR: 0.38\n        };\n        this.lastUpdated = null;\n        this.CACHE_DURATION = 60 * 60 * 1000 // 1 hour\n        ;\n    }\n}\nconst currencyService = new CurrencyService();\n/* harmony default export */ __webpack_exports__[\"default\"] = (currencyService);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/currencyService.ts\n"));

/***/ })

});