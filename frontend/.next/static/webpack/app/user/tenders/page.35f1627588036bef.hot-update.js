"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/user/tenders/page",{

/***/ "(app-pages-browser)/./app/user/tenders/page.tsx":
/*!***********************************!*\
  !*** ./app/user/tenders/page.tsx ***!
  \***********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ UserTendersPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./components/ui/select.tsx\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/use-toast */ \"(app-pages-browser)/./components/ui/use-toast.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./lib/api.ts\");\n/* harmony import */ var _contexts_CurrencyContext__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/contexts/CurrencyContext */ \"(app-pages-browser)/./contexts/CurrencyContext.tsx\");\n/* harmony import */ var _barrel_optimize_names_Building_CheckCircle_Clock_Eye_FileText_Heart_MapPin_RefreshCw_Search_Send_Tag_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Building,CheckCircle,Clock,Eye,FileText,Heart,MapPin,RefreshCw,Search,Send,Tag,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Building_CheckCircle_Clock_Eye_FileText_Heart_MapPin_RefreshCw_Search_Send_Tag_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Building,CheckCircle,Clock,Eye,FileText,Heart,MapPin,RefreshCw,Search,Send,Tag,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Building_CheckCircle_Clock_Eye_FileText_Heart_MapPin_RefreshCw_Search_Send_Tag_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Building,CheckCircle,Clock,Eye,FileText,Heart,MapPin,RefreshCw,Search,Send,Tag,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Building_CheckCircle_Clock_Eye_FileText_Heart_MapPin_RefreshCw_Search_Send_Tag_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Building,CheckCircle,Clock,Eye,FileText,Heart,MapPin,RefreshCw,Search,Send,Tag,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_Building_CheckCircle_Clock_Eye_FileText_Heart_MapPin_RefreshCw_Search_Send_Tag_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Building,CheckCircle,Clock,Eye,FileText,Heart,MapPin,RefreshCw,Search,Send,Tag,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Building_CheckCircle_Clock_Eye_FileText_Heart_MapPin_RefreshCw_Search_Send_Tag_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Building,CheckCircle,Clock,Eye,FileText,Heart,MapPin,RefreshCw,Search,Send,Tag,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Building_CheckCircle_Clock_Eye_FileText_Heart_MapPin_RefreshCw_Search_Send_Tag_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Building,CheckCircle,Clock,Eye,FileText,Heart,MapPin,RefreshCw,Search,Send,Tag,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Building_CheckCircle_Clock_Eye_FileText_Heart_MapPin_RefreshCw_Search_Send_Tag_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Building,CheckCircle,Clock,Eye,FileText,Heart,MapPin,RefreshCw,Search,Send,Tag,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/tag.js\");\n/* harmony import */ var _barrel_optimize_names_Building_CheckCircle_Clock_Eye_FileText_Heart_MapPin_RefreshCw_Search_Send_Tag_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Building,CheckCircle,Clock,Eye,FileText,Heart,MapPin,RefreshCw,Search,Send,Tag,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_Building_CheckCircle_Clock_Eye_FileText_Heart_MapPin_RefreshCw_Search_Send_Tag_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Building,CheckCircle,Clock,Eye,FileText,Heart,MapPin,RefreshCw,Search,Send,Tag,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Building_CheckCircle_Clock_Eye_FileText_Heart_MapPin_RefreshCw_Search_Send_Tag_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Building,CheckCircle,Clock,Eye,FileText,Heart,MapPin,RefreshCw,Search,Send,Tag,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building.js\");\n/* harmony import */ var _barrel_optimize_names_Building_CheckCircle_Clock_Eye_FileText_Heart_MapPin_RefreshCw_Search_Send_Tag_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Building,CheckCircle,Clock,Eye,FileText,Heart,MapPin,RefreshCw,Search,Send,Tag,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nfunction UserTendersPage() {\n    _s();\n    const [tenders, setTenders] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [filteredTenders, setFilteredTenders] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [categoryFilter, setCategoryFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [statusFilter, setStatusFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"active\");\n    const [locationFilter, setLocationFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [sortBy, setSortBy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"newest\");\n    const { formatAmount } = (0,_contexts_CurrencyContext__WEBPACK_IMPORTED_MODULE_10__.useCurrency)();\n    const { toast } = (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_7__.useToast)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_8__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadTenders();\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        filterTenders();\n    }, [\n        tenders,\n        searchTerm,\n        categoryFilter,\n        statusFilter,\n        locationFilter,\n        sortBy\n    ]);\n    const loadTenders = async ()=>{\n        try {\n            setLoading(true);\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_9__[\"default\"].get(\"/tenders?status=active&limit=50\");\n            if (response.data.success) {\n                setTenders(response.data.data.tenders);\n            } else {\n                setTenders([]);\n            }\n        } catch (error) {\n            console.error(\"Error loading tenders:\", error);\n            toast({\n                title: \"خطأ في التحميل\",\n                description: \"حدث خطأ في تحميل المناقصات\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    const filterTenders = ()=>{\n        let filtered = tenders;\n        if (searchTerm) {\n            filtered = filtered.filter((tender)=>tender.title.toLowerCase().includes(searchTerm.toLowerCase()) || tender.description.toLowerCase().includes(searchTerm.toLowerCase()));\n        }\n        if (categoryFilter !== \"all\") {\n            filtered = filtered.filter((tender)=>tender.category === categoryFilter);\n        }\n        if (statusFilter !== \"all\") {\n            filtered = filtered.filter((tender)=>tender.status === statusFilter);\n        }\n        if (locationFilter !== \"all\") {\n            filtered = filtered.filter((tender)=>tender.location.includes(locationFilter));\n        }\n        // Sort\n        filtered.sort((a, b)=>{\n            switch(sortBy){\n                case \"newest\":\n                    return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();\n                case \"oldest\":\n                    return new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime();\n                case \"budget_high\":\n                    return b.budget - a.budget;\n                case \"budget_low\":\n                    return a.budget - b.budget;\n                case \"deadline_soon\":\n                    return new Date(a.deadline).getTime() - new Date(b.deadline).getTime();\n                case \"most_viewed\":\n                    return b.viewsCount - a.viewsCount;\n                case \"most_applications\":\n                    return b.applicationsCount - a.applicationsCount;\n                default:\n                    return 0;\n            }\n        });\n        setFilteredTenders(filtered);\n    };\n    const toggleFavorite = async (tenderId)=>{\n        try {\n            const tender = tenders.find((t)=>t._id === tenderId);\n            if (!tender) return;\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_9__[\"default\"].post(\"/favorites/toggle\", {\n                itemId: tenderId,\n                type: \"tender\"\n            });\n            if (response.data.success) {\n                setTenders(tenders.map((t)=>t._id === tenderId ? {\n                        ...t,\n                        isFavorite: !t.isFavorite\n                    } : t));\n                toast({\n                    title: tender.isFavorite ? \"تم الحذف من المفضلة\" : \"تم الإضافة للمفضلة\",\n                    description: tender.isFavorite ? \"تم حذف المناقصة من المفضلة\" : \"تم إضافة المناقصة للمفضلة\"\n                });\n            }\n        } catch (error) {\n            toast({\n                title: \"خطأ\",\n                description: \"حدث خطأ في تحديث المفضلة\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const formatPrice = (price)=>{\n        return formatAmount(price);\n    };\n    const formatTimeRemaining = (deadline)=>{\n        const now = new Date();\n        const end = new Date(deadline);\n        const diff = end.getTime() - now.getTime();\n        if (diff <= 0) return \"انتهى\";\n        const days = Math.floor(diff / (1000 * 60 * 60 * 24));\n        const hours = Math.floor(diff % (1000 * 60 * 60 * 24) / (1000 * 60 * 60));\n        if (days > 0) return \"\".concat(days, \" يوم\");\n        if (hours > 0) return \"\".concat(hours, \" ساعة\");\n        return \"أقل من ساعة\";\n    };\n    const getStatusBadge = (status)=>{\n        switch(status){\n            case \"active\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                    className: \"bg-green-100 text-green-800\",\n                    children: \"نشط\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                    lineNumber: 199,\n                    columnNumber: 16\n                }, this);\n            case \"completed\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                    className: \"bg-blue-100 text-blue-800\",\n                    children: \"مكتمل\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                    lineNumber: 201,\n                    columnNumber: 16\n                }, this);\n            case \"review\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                    className: \"bg-yellow-100 text-yellow-800\",\n                    children: \"قيد المراجعة\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                    lineNumber: 203,\n                    columnNumber: 16\n                }, this);\n            case \"cancelled\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                    variant: \"destructive\",\n                    children: \"ملغي\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                    lineNumber: 205,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                    variant: \"outline\",\n                    children: status\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                    lineNumber: 207,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    const getCategoryName = (category)=>{\n        const categories = {\n            \"construction\": \"إنشاءات\",\n            \"it_technology\": \"تقنية المعلومات\",\n            \"consulting\": \"استشارات\",\n            \"healthcare\": \"رعاية صحية\",\n            \"education\": \"تعليم\",\n            \"transportation\": \"نقل ومواصلات\",\n            \"other\": \"أخرى\"\n        };\n        return categories[category] || category;\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center min-h-96\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                        lineNumber: 228,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-4 text-gray-600\",\n                        children: \"جاري تحميل المناقصات...\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                        lineNumber: 229,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                lineNumber: 227,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n            lineNumber: 226,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gradient-to-r from-green-600 to-emerald-600 text-white rounded-lg p-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-3xl font-bold\",\n                                    children: \"المناقصات المتاحة\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                    lineNumber: 241,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-green-100 mt-1\",\n                                    children: \"تصفح وتقدم للمناقصات الحكومية المناسبة لك\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                    lineNumber: 242,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                            lineNumber: 240,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold\",\n                                            children: filteredTenders.length\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                            lineNumber: 246,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-green-100\",\n                                            children: \"مناقصة متاحة\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                            lineNumber: 247,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                    lineNumber: 245,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white/10 rounded-lg p-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CurrencySelector, {\n                                        showLabel: false,\n                                        className: \"text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                        lineNumber: 250,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                    lineNumber: 249,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    onClick: ()=>router.push(\"/user/applications\"),\n                                    className: \"bg-white text-green-600 hover:bg-gray-100\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_CheckCircle_Clock_Eye_FileText_Heart_MapPin_RefreshCw_Search_Send_Tag_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"h-4 w-4 ml-2\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                            lineNumber: 256,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"طلباتي\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                    lineNumber: 252,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                            lineNumber: 244,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                    lineNumber: 239,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                lineNumber: 238,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                    className: \"p-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-6 gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"md:col-span-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_CheckCircle_Clock_Eye_FileText_Heart_MapPin_RefreshCw_Search_Send_Tag_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                            lineNumber: 269,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                            placeholder: \"البحث في المناقصات...\",\n                                            value: searchTerm,\n                                            onChange: (e)=>setSearchTerm(e.target.value),\n                                            className: \"pr-10\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                            lineNumber: 270,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                    lineNumber: 268,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                lineNumber: 267,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.Select, {\n                                value: categoryFilter,\n                                onValueChange: setCategoryFilter,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectTrigger, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectValue, {\n                                            placeholder: \"الفئة\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                            lineNumber: 281,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                        lineNumber: 280,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectContent, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                value: \"all\",\n                                                children: \"جميع الفئات\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                                lineNumber: 284,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                value: \"construction\",\n                                                children: \"إنشاءات\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                                lineNumber: 285,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                value: \"it_technology\",\n                                                children: \"تقنية المعلومات\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                                lineNumber: 286,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                value: \"consulting\",\n                                                children: \"استشارات\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                                lineNumber: 287,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                value: \"healthcare\",\n                                                children: \"رعاية صحية\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                                lineNumber: 288,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                value: \"education\",\n                                                children: \"تعليم\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                                lineNumber: 289,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                value: \"transportation\",\n                                                children: \"نقل ومواصلات\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                                lineNumber: 290,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                        lineNumber: 283,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                lineNumber: 279,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.Select, {\n                                value: locationFilter,\n                                onValueChange: setLocationFilter,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectTrigger, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectValue, {\n                                            placeholder: \"الموقع\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                            lineNumber: 296,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                        lineNumber: 295,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectContent, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                value: \"all\",\n                                                children: \"جميع المواقع\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                                lineNumber: 299,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                value: \"الرياض\",\n                                                children: \"الرياض\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                                lineNumber: 300,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                value: \"جدة\",\n                                                children: \"جدة\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                                lineNumber: 301,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                value: \"الدمام\",\n                                                children: \"الدمام\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                                lineNumber: 302,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                value: \"مكة\",\n                                                children: \"مكة\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                                lineNumber: 303,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                        lineNumber: 298,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                lineNumber: 294,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.Select, {\n                                value: sortBy,\n                                onValueChange: setSortBy,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectTrigger, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectValue, {\n                                            placeholder: \"ترتيب\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                            lineNumber: 309,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                        lineNumber: 308,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectContent, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                value: \"newest\",\n                                                children: \"الأحدث\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                                lineNumber: 312,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                value: \"oldest\",\n                                                children: \"الأقدم\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                                lineNumber: 313,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                value: \"budget_high\",\n                                                children: \"الميزانية (الأعلى)\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                                lineNumber: 314,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                value: \"budget_low\",\n                                                children: \"الميزانية (الأقل)\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                                lineNumber: 315,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                value: \"deadline_soon\",\n                                                children: \"ينتهي قريباً\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                                lineNumber: 316,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                value: \"most_viewed\",\n                                                children: \"الأكثر مشاهدة\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                                lineNumber: 317,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                value: \"most_applications\",\n                                                children: \"الأكثر طلبات\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                                lineNumber: 318,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                        lineNumber: 311,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                lineNumber: 307,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                onClick: loadTenders,\n                                variant: \"outline\",\n                                className: \"w-full\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_CheckCircle_Clock_Eye_FileText_Heart_MapPin_RefreshCw_Search_Send_Tag_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"h-4 w-4 ml-2\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                        lineNumber: 327,\n                                        columnNumber: 17\n                                    }, this),\n                                    \"تحديث\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                lineNumber: 322,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                        lineNumber: 266,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                    lineNumber: 265,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                lineNumber: 264,\n                columnNumber: 9\n            }, this),\n            filteredTenders.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                children: filteredTenders.map((tender)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        className: \"hover:shadow-lg transition-shadow cursor-pointer relative\",\n                        onClick: ()=>router.push(\"/tenders/\".concat(tender._id)),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-start justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                    className: \"text-lg line-clamp-2\",\n                                                    children: tender.title\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                                    lineNumber: 346,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2 mt-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_CheckCircle_Clock_Eye_FileText_Heart_MapPin_RefreshCw_Search_Send_Tag_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            className: \"h-4 w-4 text-green-600\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                                            lineNumber: 348,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: \"مناقصة\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                                            lineNumber: 349,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        getStatusBadge(tender.status),\n                                                        tender.hasApplied && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                            className: \"bg-blue-100 text-blue-800\",\n                                                            children: \"تقدمت\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                                            lineNumber: 352,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                                    lineNumber: 347,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                            lineNumber: 345,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col items-end gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-right\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-lg font-bold text-green-600\",\n                                                            children: formatPrice(tender.budget)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                                            lineNumber: 358,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-gray-500\",\n                                                            children: \"الميزانية\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                                            lineNumber: 361,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                                    lineNumber: 357,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    variant: \"ghost\",\n                                                    size: \"sm\",\n                                                    onClick: (e)=>{\n                                                        e.stopPropagation();\n                                                        toggleFavorite(tender._id);\n                                                    },\n                                                    className: \"p-1 \".concat(tender.isFavorite ? \"text-red-600\" : \"text-gray-400\"),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_CheckCircle_Clock_Eye_FileText_Heart_MapPin_RefreshCw_Search_Send_Tag_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        className: \"h-4 w-4 \".concat(tender.isFavorite ? \"fill-current\" : \"\")\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                                        lineNumber: 372,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                                    lineNumber: 363,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                            lineNumber: 356,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                    lineNumber: 344,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                lineNumber: 343,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 line-clamp-3 mb-4\",\n                                        children: tender.description\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                        lineNumber: 378,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-2 gap-4 text-sm mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_CheckCircle_Clock_Eye_FileText_Heart_MapPin_RefreshCw_Search_Send_Tag_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                        className: \"h-4 w-4 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                                        lineNumber: 382,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: formatTimeRemaining(tender.deadline)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                                        lineNumber: 383,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                                lineNumber: 381,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_CheckCircle_Clock_Eye_FileText_Heart_MapPin_RefreshCw_Search_Send_Tag_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                        className: \"h-4 w-4 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                                        lineNumber: 386,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            tender.applicationsCount,\n                                                            \" طلب\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                                        lineNumber: 387,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                                lineNumber: 385,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_CheckCircle_Clock_Eye_FileText_Heart_MapPin_RefreshCw_Search_Send_Tag_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                        className: \"h-4 w-4 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                                        lineNumber: 390,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            tender.viewsCount,\n                                                            \" مشاهدة\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                                        lineNumber: 391,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                                lineNumber: 389,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_CheckCircle_Clock_Eye_FileText_Heart_MapPin_RefreshCw_Search_Send_Tag_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                        className: \"h-4 w-4 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                                        lineNumber: 394,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: getCategoryName(tender.category)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                                        lineNumber: 395,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                                lineNumber: 393,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                        lineNumber: 380,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2 text-sm text-gray-500 mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_CheckCircle_Clock_Eye_FileText_Heart_MapPin_RefreshCw_Search_Send_Tag_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                                lineNumber: 400,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: tender.location\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                                lineNumber: 401,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                        lineNumber: 399,\n                                        columnNumber: 19\n                                    }, this),\n                                    tender.requirements && tender.requirements.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm font-medium text-gray-700 mb-2\",\n                                                children: \"المتطلبات:\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                                lineNumber: 407,\n                                                columnNumber: 23\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-1\",\n                                                children: [\n                                                    tender.requirements.slice(0, 2).map((req, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-2 text-xs text-gray-600\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_CheckCircle_Clock_Eye_FileText_Heart_MapPin_RefreshCw_Search_Send_Tag_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                    className: \"h-3 w-3 text-green-600\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                                                    lineNumber: 411,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: req\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                                                    lineNumber: 412,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, index, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                                            lineNumber: 410,\n                                                            columnNumber: 27\n                                                        }, this)),\n                                                    tender.requirements.length > 2 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-gray-500\",\n                                                        children: [\n                                                            \"+\",\n                                                            tender.requirements.length - 2,\n                                                            \" متطلبات أخرى\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                                        lineNumber: 416,\n                                                        columnNumber: 27\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                                lineNumber: 408,\n                                                columnNumber: 23\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                        lineNumber: 406,\n                                        columnNumber: 21\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"pt-4 border-t\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_CheckCircle_Clock_Eye_FileText_Heart_MapPin_RefreshCw_Search_Send_Tag_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                            className: \"h-4 w-4 text-gray-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                                            lineNumber: 427,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: tender.organizer.profile.governmentEntity\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                                            lineNumber: 428,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                                    lineNumber: 426,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs text-gray-500\",\n                                                    children: new Date(tender.createdAt).toLocaleDateString(\"ar-SA\")\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                                    lineNumber: 432,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                            lineNumber: 425,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                        lineNumber: 424,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-2 mt-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                className: \"flex-1\",\n                                                onClick: (e)=>{\n                                                    e.stopPropagation();\n                                                    router.push(\"/tenders/\".concat(tender._id));\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_CheckCircle_Clock_Eye_FileText_Heart_MapPin_RefreshCw_Search_Send_Tag_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                        className: \"h-4 w-4 ml-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                                        lineNumber: 447,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    \"عرض التفاصيل\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                                lineNumber: 440,\n                                                columnNumber: 21\n                                            }, this),\n                                            !tender.hasApplied && tender.status === \"active\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                variant: \"outline\",\n                                                onClick: (e)=>{\n                                                    e.stopPropagation();\n                                                    router.push(\"/tenders/\".concat(tender._id));\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_CheckCircle_Clock_Eye_FileText_Heart_MapPin_RefreshCw_Search_Send_Tag_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                        className: \"h-4 w-4 ml-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                                        lineNumber: 458,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    \"تقدم\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                                lineNumber: 451,\n                                                columnNumber: 23\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                        lineNumber: 439,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                lineNumber: 377,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, tender._id, true, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                        lineNumber: 338,\n                        columnNumber: 15\n                    }, this))\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                lineNumber: 336,\n                columnNumber: 11\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center py-12\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_CheckCircle_Clock_Eye_FileText_Heart_MapPin_RefreshCw_Search_Send_Tag_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                        className: \"h-16 w-16 text-gray-400 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                        lineNumber: 469,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-medium text-gray-900 mb-2\",\n                        children: \"لا توجد مناقصات\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                        lineNumber: 470,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: searchTerm || categoryFilter !== \"all\" || locationFilter !== \"all\" ? \"جرب تغيير معايير البحث أو الفلترة\" : \"لا توجد مناقصات متاحة حالياً\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                        lineNumber: 471,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                lineNumber: 468,\n                columnNumber: 11\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n        lineNumber: 236,\n        columnNumber: 5\n    }, this);\n}\n_s(UserTendersPage, \"xJ3SooJj11SUlrrFzglGXiQEE1A=\", false, function() {\n    return [\n        _contexts_CurrencyContext__WEBPACK_IMPORTED_MODULE_10__.useCurrency,\n        _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_7__.useToast,\n        next_navigation__WEBPACK_IMPORTED_MODULE_8__.useRouter\n    ];\n});\n_c = UserTendersPage;\nvar _c;\n$RefreshReg$(_c, \"UserTendersPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/user/tenders/page.tsx\n"));

/***/ })

});