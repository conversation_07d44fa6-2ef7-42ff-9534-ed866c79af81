"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/user/tenders/page",{

/***/ "(app-pages-browser)/./app/user/tenders/page.tsx":
/*!***********************************!*\
  !*** ./app/user/tenders/page.tsx ***!
  \***********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ UserTendersPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./components/ui/select.tsx\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/use-toast */ \"(app-pages-browser)/./components/ui/use-toast.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./lib/api.ts\");\n/* harmony import */ var _contexts_CurrencyContext__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/contexts/CurrencyContext */ \"(app-pages-browser)/./contexts/CurrencyContext.tsx\");\n/* harmony import */ var _barrel_optimize_names_Building_CheckCircle_Clock_Eye_FileText_Heart_MapPin_RefreshCw_Search_Send_Tag_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Building,CheckCircle,Clock,Eye,FileText,Heart,MapPin,RefreshCw,Search,Send,Tag,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Building_CheckCircle_Clock_Eye_FileText_Heart_MapPin_RefreshCw_Search_Send_Tag_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Building,CheckCircle,Clock,Eye,FileText,Heart,MapPin,RefreshCw,Search,Send,Tag,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Building_CheckCircle_Clock_Eye_FileText_Heart_MapPin_RefreshCw_Search_Send_Tag_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Building,CheckCircle,Clock,Eye,FileText,Heart,MapPin,RefreshCw,Search,Send,Tag,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Building_CheckCircle_Clock_Eye_FileText_Heart_MapPin_RefreshCw_Search_Send_Tag_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Building,CheckCircle,Clock,Eye,FileText,Heart,MapPin,RefreshCw,Search,Send,Tag,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_Building_CheckCircle_Clock_Eye_FileText_Heart_MapPin_RefreshCw_Search_Send_Tag_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Building,CheckCircle,Clock,Eye,FileText,Heart,MapPin,RefreshCw,Search,Send,Tag,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Building_CheckCircle_Clock_Eye_FileText_Heart_MapPin_RefreshCw_Search_Send_Tag_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Building,CheckCircle,Clock,Eye,FileText,Heart,MapPin,RefreshCw,Search,Send,Tag,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Building_CheckCircle_Clock_Eye_FileText_Heart_MapPin_RefreshCw_Search_Send_Tag_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Building,CheckCircle,Clock,Eye,FileText,Heart,MapPin,RefreshCw,Search,Send,Tag,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Building_CheckCircle_Clock_Eye_FileText_Heart_MapPin_RefreshCw_Search_Send_Tag_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Building,CheckCircle,Clock,Eye,FileText,Heart,MapPin,RefreshCw,Search,Send,Tag,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/tag.js\");\n/* harmony import */ var _barrel_optimize_names_Building_CheckCircle_Clock_Eye_FileText_Heart_MapPin_RefreshCw_Search_Send_Tag_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Building,CheckCircle,Clock,Eye,FileText,Heart,MapPin,RefreshCw,Search,Send,Tag,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_Building_CheckCircle_Clock_Eye_FileText_Heart_MapPin_RefreshCw_Search_Send_Tag_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Building,CheckCircle,Clock,Eye,FileText,Heart,MapPin,RefreshCw,Search,Send,Tag,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Building_CheckCircle_Clock_Eye_FileText_Heart_MapPin_RefreshCw_Search_Send_Tag_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Building,CheckCircle,Clock,Eye,FileText,Heart,MapPin,RefreshCw,Search,Send,Tag,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building.js\");\n/* harmony import */ var _barrel_optimize_names_Building_CheckCircle_Clock_Eye_FileText_Heart_MapPin_RefreshCw_Search_Send_Tag_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Building,CheckCircle,Clock,Eye,FileText,Heart,MapPin,RefreshCw,Search,Send,Tag,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nfunction UserTendersPage() {\n    _s();\n    const [tenders, setTenders] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [filteredTenders, setFilteredTenders] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [categoryFilter, setCategoryFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [statusFilter, setStatusFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"active\");\n    const [locationFilter, setLocationFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [sortBy, setSortBy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"newest\");\n    const { formatAmount } = (0,_contexts_CurrencyContext__WEBPACK_IMPORTED_MODULE_10__.useCurrency)();\n    const { toast } = (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_7__.useToast)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_8__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadTenders();\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        filterTenders();\n    }, [\n        tenders,\n        searchTerm,\n        categoryFilter,\n        statusFilter,\n        locationFilter,\n        sortBy\n    ]);\n    const loadTenders = async ()=>{\n        try {\n            setLoading(true);\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_9__[\"default\"].get(\"/tenders?status=active&limit=50\");\n            if (response.data.success) {\n                setTenders(response.data.data.tenders);\n            } else {\n                setTenders([]);\n            }\n        } catch (error) {\n            console.error(\"Error loading tenders:\", error);\n            toast({\n                title: \"خطأ في التحميل\",\n                description: \"حدث خطأ في تحميل المناقصات\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    const filterTenders = ()=>{\n        let filtered = tenders;\n        if (searchTerm) {\n            filtered = filtered.filter((tender)=>tender.title.toLowerCase().includes(searchTerm.toLowerCase()) || tender.description.toLowerCase().includes(searchTerm.toLowerCase()));\n        }\n        if (categoryFilter !== \"all\") {\n            filtered = filtered.filter((tender)=>tender.category === categoryFilter);\n        }\n        if (statusFilter !== \"all\") {\n            filtered = filtered.filter((tender)=>tender.status === statusFilter);\n        }\n        if (locationFilter !== \"all\") {\n            filtered = filtered.filter((tender)=>tender.location.includes(locationFilter));\n        }\n        // Sort\n        filtered.sort((a, b)=>{\n            switch(sortBy){\n                case \"newest\":\n                    return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();\n                case \"oldest\":\n                    return new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime();\n                case \"budget_high\":\n                    return b.budget - a.budget;\n                case \"budget_low\":\n                    return a.budget - b.budget;\n                case \"deadline_soon\":\n                    return new Date(a.deadline).getTime() - new Date(b.deadline).getTime();\n                case \"most_viewed\":\n                    return b.viewsCount - a.viewsCount;\n                case \"most_applications\":\n                    return b.applicationsCount - a.applicationsCount;\n                default:\n                    return 0;\n            }\n        });\n        setFilteredTenders(filtered);\n    };\n    const toggleFavorite = async (tenderId)=>{\n        try {\n            const tender = tenders.find((t)=>t._id === tenderId);\n            if (!tender) return;\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_9__[\"default\"].post(\"/favorites/toggle\", {\n                itemId: tenderId,\n                type: \"tender\"\n            });\n            if (response.data.success) {\n                setTenders(tenders.map((t)=>t._id === tenderId ? {\n                        ...t,\n                        isFavorite: !t.isFavorite\n                    } : t));\n                toast({\n                    title: tender.isFavorite ? \"تم الحذف من المفضلة\" : \"تم الإضافة للمفضلة\",\n                    description: tender.isFavorite ? \"تم حذف المناقصة من المفضلة\" : \"تم إضافة المناقصة للمفضلة\"\n                });\n            }\n        } catch (error) {\n            toast({\n                title: \"خطأ\",\n                description: \"حدث خطأ في تحديث المفضلة\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const formatPrice = (price)=>{\n        return formatAmount(price);\n    };\n    const formatTimeRemaining = (deadline)=>{\n        const now = new Date();\n        const end = new Date(deadline);\n        const diff = end.getTime() - now.getTime();\n        if (diff <= 0) return \"انتهى\";\n        const days = Math.floor(diff / (1000 * 60 * 60 * 24));\n        const hours = Math.floor(diff % (1000 * 60 * 60 * 24) / (1000 * 60 * 60));\n        if (days > 0) return \"\".concat(days, \" يوم\");\n        if (hours > 0) return \"\".concat(hours, \" ساعة\");\n        return \"أقل من ساعة\";\n    };\n    const getStatusBadge = (status)=>{\n        switch(status){\n            case \"active\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                    className: \"bg-green-100 text-green-800\",\n                    children: \"نشط\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                    lineNumber: 199,\n                    columnNumber: 16\n                }, this);\n            case \"completed\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                    className: \"bg-blue-100 text-blue-800\",\n                    children: \"مكتمل\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                    lineNumber: 201,\n                    columnNumber: 16\n                }, this);\n            case \"review\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                    className: \"bg-yellow-100 text-yellow-800\",\n                    children: \"قيد المراجعة\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                    lineNumber: 203,\n                    columnNumber: 16\n                }, this);\n            case \"cancelled\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                    variant: \"destructive\",\n                    children: \"ملغي\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                    lineNumber: 205,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                    variant: \"outline\",\n                    children: status\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                    lineNumber: 207,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    const getCategoryName = (category)=>{\n        const categories = {\n            \"construction\": \"إنشاءات\",\n            \"it_technology\": \"تقنية المعلومات\",\n            \"consulting\": \"استشارات\",\n            \"healthcare\": \"رعاية صحية\",\n            \"education\": \"تعليم\",\n            \"transportation\": \"نقل ومواصلات\",\n            \"other\": \"أخرى\"\n        };\n        return categories[category] || category;\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center min-h-96\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                        lineNumber: 228,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-4 text-gray-600\",\n                        children: \"جاري تحميل المناقصات...\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                        lineNumber: 229,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                lineNumber: 227,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n            lineNumber: 226,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gradient-to-r from-green-600 to-emerald-600 text-white rounded-lg p-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-3xl font-bold\",\n                                    children: \"المناقصات المتاحة\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                    lineNumber: 241,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-green-100 mt-1\",\n                                    children: \"تصفح وتقدم للمناقصات الحكومية المناسبة لك\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                    lineNumber: 242,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                            lineNumber: 240,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold\",\n                                            children: filteredTenders.length\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                            lineNumber: 246,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-green-100\",\n                                            children: \"مناقصة متاحة\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                            lineNumber: 247,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                    lineNumber: 245,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    onClick: ()=>router.push(\"/user/applications\"),\n                                    className: \"bg-white text-green-600 hover:bg-gray-100\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_CheckCircle_Clock_Eye_FileText_Heart_MapPin_RefreshCw_Search_Send_Tag_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"h-4 w-4 ml-2\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                            lineNumber: 254,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"طلباتي\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                    lineNumber: 250,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                            lineNumber: 244,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                    lineNumber: 239,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                lineNumber: 238,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                    className: \"p-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-6 gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"md:col-span-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_CheckCircle_Clock_Eye_FileText_Heart_MapPin_RefreshCw_Search_Send_Tag_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                            lineNumber: 267,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                            placeholder: \"البحث في المناقصات...\",\n                                            value: searchTerm,\n                                            onChange: (e)=>setSearchTerm(e.target.value),\n                                            className: \"pr-10\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                            lineNumber: 268,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                    lineNumber: 266,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                lineNumber: 265,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.Select, {\n                                value: categoryFilter,\n                                onValueChange: setCategoryFilter,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectTrigger, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectValue, {\n                                            placeholder: \"الفئة\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                            lineNumber: 279,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                        lineNumber: 278,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectContent, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                value: \"all\",\n                                                children: \"جميع الفئات\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                                lineNumber: 282,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                value: \"construction\",\n                                                children: \"إنشاءات\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                                lineNumber: 283,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                value: \"it_technology\",\n                                                children: \"تقنية المعلومات\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                                lineNumber: 284,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                value: \"consulting\",\n                                                children: \"استشارات\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                                lineNumber: 285,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                value: \"healthcare\",\n                                                children: \"رعاية صحية\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                                lineNumber: 286,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                value: \"education\",\n                                                children: \"تعليم\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                                lineNumber: 287,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                value: \"transportation\",\n                                                children: \"نقل ومواصلات\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                                lineNumber: 288,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                        lineNumber: 281,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                lineNumber: 277,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.Select, {\n                                value: locationFilter,\n                                onValueChange: setLocationFilter,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectTrigger, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectValue, {\n                                            placeholder: \"الموقع\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                            lineNumber: 294,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                        lineNumber: 293,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectContent, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                value: \"all\",\n                                                children: \"جميع المواقع\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                                lineNumber: 297,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                value: \"الرياض\",\n                                                children: \"الرياض\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                                lineNumber: 298,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                value: \"جدة\",\n                                                children: \"جدة\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                                lineNumber: 299,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                value: \"الدمام\",\n                                                children: \"الدمام\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                                lineNumber: 300,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                value: \"مكة\",\n                                                children: \"مكة\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                                lineNumber: 301,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                        lineNumber: 296,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                lineNumber: 292,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.Select, {\n                                value: sortBy,\n                                onValueChange: setSortBy,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectTrigger, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectValue, {\n                                            placeholder: \"ترتيب\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                            lineNumber: 307,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                        lineNumber: 306,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectContent, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                value: \"newest\",\n                                                children: \"الأحدث\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                                lineNumber: 310,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                value: \"oldest\",\n                                                children: \"الأقدم\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                                lineNumber: 311,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                value: \"budget_high\",\n                                                children: \"الميزانية (الأعلى)\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                                lineNumber: 312,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                value: \"budget_low\",\n                                                children: \"الميزانية (الأقل)\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                                lineNumber: 313,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                value: \"deadline_soon\",\n                                                children: \"ينتهي قريباً\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                                lineNumber: 314,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                value: \"most_viewed\",\n                                                children: \"الأكثر مشاهدة\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                                lineNumber: 315,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                value: \"most_applications\",\n                                                children: \"الأكثر طلبات\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                                lineNumber: 316,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                        lineNumber: 309,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                lineNumber: 305,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                onClick: loadTenders,\n                                variant: \"outline\",\n                                className: \"w-full\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_CheckCircle_Clock_Eye_FileText_Heart_MapPin_RefreshCw_Search_Send_Tag_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"h-4 w-4 ml-2\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                        lineNumber: 325,\n                                        columnNumber: 17\n                                    }, this),\n                                    \"تحديث\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                lineNumber: 320,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                        lineNumber: 264,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                    lineNumber: 263,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                lineNumber: 262,\n                columnNumber: 9\n            }, this),\n            filteredTenders.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                children: filteredTenders.map((tender)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        className: \"hover:shadow-lg transition-shadow cursor-pointer relative\",\n                        onClick: ()=>router.push(\"/tenders/\".concat(tender._id)),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-start justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                    className: \"text-lg line-clamp-2\",\n                                                    children: tender.title\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                                    lineNumber: 344,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2 mt-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_CheckCircle_Clock_Eye_FileText_Heart_MapPin_RefreshCw_Search_Send_Tag_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            className: \"h-4 w-4 text-green-600\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                                            lineNumber: 346,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: \"مناقصة\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                                            lineNumber: 347,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        getStatusBadge(tender.status),\n                                                        tender.hasApplied && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                            className: \"bg-blue-100 text-blue-800\",\n                                                            children: \"تقدمت\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                                            lineNumber: 350,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                                    lineNumber: 345,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                            lineNumber: 343,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col items-end gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-right\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-lg font-bold text-green-600\",\n                                                            children: formatPrice(tender.budget)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                                            lineNumber: 356,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-gray-500\",\n                                                            children: \"الميزانية\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                                            lineNumber: 359,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                                    lineNumber: 355,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    variant: \"ghost\",\n                                                    size: \"sm\",\n                                                    onClick: (e)=>{\n                                                        e.stopPropagation();\n                                                        toggleFavorite(tender._id);\n                                                    },\n                                                    className: \"p-1 \".concat(tender.isFavorite ? \"text-red-600\" : \"text-gray-400\"),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_CheckCircle_Clock_Eye_FileText_Heart_MapPin_RefreshCw_Search_Send_Tag_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        className: \"h-4 w-4 \".concat(tender.isFavorite ? \"fill-current\" : \"\")\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                                        lineNumber: 370,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                                    lineNumber: 361,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                            lineNumber: 354,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                    lineNumber: 342,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                lineNumber: 341,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 line-clamp-3 mb-4\",\n                                        children: tender.description\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                        lineNumber: 376,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-2 gap-4 text-sm mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_CheckCircle_Clock_Eye_FileText_Heart_MapPin_RefreshCw_Search_Send_Tag_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                        className: \"h-4 w-4 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                                        lineNumber: 380,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: formatTimeRemaining(tender.deadline)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                                        lineNumber: 381,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                                lineNumber: 379,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_CheckCircle_Clock_Eye_FileText_Heart_MapPin_RefreshCw_Search_Send_Tag_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                        className: \"h-4 w-4 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                                        lineNumber: 384,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            tender.applicationsCount,\n                                                            \" طلب\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                                        lineNumber: 385,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                                lineNumber: 383,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_CheckCircle_Clock_Eye_FileText_Heart_MapPin_RefreshCw_Search_Send_Tag_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                        className: \"h-4 w-4 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                                        lineNumber: 388,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            tender.viewsCount,\n                                                            \" مشاهدة\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                                        lineNumber: 389,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                                lineNumber: 387,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_CheckCircle_Clock_Eye_FileText_Heart_MapPin_RefreshCw_Search_Send_Tag_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                        className: \"h-4 w-4 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                                        lineNumber: 392,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: getCategoryName(tender.category)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                                        lineNumber: 393,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                                lineNumber: 391,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                        lineNumber: 378,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2 text-sm text-gray-500 mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_CheckCircle_Clock_Eye_FileText_Heart_MapPin_RefreshCw_Search_Send_Tag_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                                lineNumber: 398,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: tender.location\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                                lineNumber: 399,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                        lineNumber: 397,\n                                        columnNumber: 19\n                                    }, this),\n                                    tender.requirements && tender.requirements.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm font-medium text-gray-700 mb-2\",\n                                                children: \"المتطلبات:\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                                lineNumber: 405,\n                                                columnNumber: 23\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-1\",\n                                                children: [\n                                                    tender.requirements.slice(0, 2).map((req, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-2 text-xs text-gray-600\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_CheckCircle_Clock_Eye_FileText_Heart_MapPin_RefreshCw_Search_Send_Tag_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                    className: \"h-3 w-3 text-green-600\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                                                    lineNumber: 409,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: req\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                                                    lineNumber: 410,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, index, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                                            lineNumber: 408,\n                                                            columnNumber: 27\n                                                        }, this)),\n                                                    tender.requirements.length > 2 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-gray-500\",\n                                                        children: [\n                                                            \"+\",\n                                                            tender.requirements.length - 2,\n                                                            \" متطلبات أخرى\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                                        lineNumber: 414,\n                                                        columnNumber: 27\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                                lineNumber: 406,\n                                                columnNumber: 23\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                        lineNumber: 404,\n                                        columnNumber: 21\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"pt-4 border-t\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_CheckCircle_Clock_Eye_FileText_Heart_MapPin_RefreshCw_Search_Send_Tag_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                            className: \"h-4 w-4 text-gray-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                                            lineNumber: 425,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: tender.organizer.profile.governmentEntity\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                                            lineNumber: 426,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                                    lineNumber: 424,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs text-gray-500\",\n                                                    children: new Date(tender.createdAt).toLocaleDateString(\"ar-SA\")\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                                    lineNumber: 430,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                            lineNumber: 423,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                        lineNumber: 422,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-2 mt-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                className: \"flex-1\",\n                                                onClick: (e)=>{\n                                                    e.stopPropagation();\n                                                    router.push(\"/tenders/\".concat(tender._id));\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_CheckCircle_Clock_Eye_FileText_Heart_MapPin_RefreshCw_Search_Send_Tag_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                        className: \"h-4 w-4 ml-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                                        lineNumber: 445,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    \"عرض التفاصيل\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                                lineNumber: 438,\n                                                columnNumber: 21\n                                            }, this),\n                                            !tender.hasApplied && tender.status === \"active\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                variant: \"outline\",\n                                                onClick: (e)=>{\n                                                    e.stopPropagation();\n                                                    router.push(\"/tenders/\".concat(tender._id));\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_CheckCircle_Clock_Eye_FileText_Heart_MapPin_RefreshCw_Search_Send_Tag_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                        className: \"h-4 w-4 ml-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                                        lineNumber: 456,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    \"تقدم\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                                lineNumber: 449,\n                                                columnNumber: 23\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                        lineNumber: 437,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                                lineNumber: 375,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, tender._id, true, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                        lineNumber: 336,\n                        columnNumber: 15\n                    }, this))\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                lineNumber: 334,\n                columnNumber: 11\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center py-12\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_CheckCircle_Clock_Eye_FileText_Heart_MapPin_RefreshCw_Search_Send_Tag_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                        className: \"h-16 w-16 text-gray-400 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                        lineNumber: 467,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-medium text-gray-900 mb-2\",\n                        children: \"لا توجد مناقصات\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                        lineNumber: 468,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: searchTerm || categoryFilter !== \"all\" || locationFilter !== \"all\" ? \"جرب تغيير معايير البحث أو الفلترة\" : \"لا توجد مناقصات متاحة حالياً\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                        lineNumber: 469,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n                lineNumber: 466,\n                columnNumber: 11\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/user/tenders/page.tsx\",\n        lineNumber: 236,\n        columnNumber: 5\n    }, this);\n}\n_s(UserTendersPage, \"xJ3SooJj11SUlrrFzglGXiQEE1A=\", false, function() {\n    return [\n        _contexts_CurrencyContext__WEBPACK_IMPORTED_MODULE_10__.useCurrency,\n        _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_7__.useToast,\n        next_navigation__WEBPACK_IMPORTED_MODULE_8__.useRouter\n    ];\n});\n_c = UserTendersPage;\nvar _c;\n$RefreshReg$(_c, \"UserTendersPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/user/tenders/page.tsx\n"));

/***/ })

});