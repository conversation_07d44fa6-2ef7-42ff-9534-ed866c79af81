"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/tenders/page",{

/***/ "(app-pages-browser)/./components/DashboardLayout.tsx":
/*!****************************************!*\
  !*** ./components/DashboardLayout.tsx ***!
  \****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ DashboardLayout; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_Sidebar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/Sidebar */ \"(app-pages-browser)/./components/Sidebar.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction DashboardLayout(param) {\n    let { children, allowedRoles } = param;\n    _s();\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const token = localStorage.getItem(\"token\");\n        const userData = localStorage.getItem(\"user\");\n        if (!token || !userData) {\n            router.push(\"/auth/login\");\n            return;\n        }\n        try {\n            const parsedUser = JSON.parse(userData);\n            // Debug logging for role checking\n            console.log(\"DashboardLayout - User role:\", parsedUser.role);\n            console.log(\"DashboardLayout - Allowed roles:\", allowedRoles);\n            console.log(\"DashboardLayout - Role allowed:\", allowedRoles.includes(parsedUser.role));\n            // Check if user role is allowed\n            if (!allowedRoles.includes(parsedUser.role)) {\n                console.log(\"DashboardLayout - Role not allowed, redirecting...\");\n                // Redirect to appropriate dashboard based on role\n                switch(parsedUser.role){\n                    case \"admin\":\n                    case \"super_admin\":\n                        router.push(\"/admin/dashboard\");\n                        break;\n                    case \"company\":\n                        router.push(\"/company/dashboard\");\n                        break;\n                    case \"individual\":\n                        router.push(\"/user/dashboard\");\n                        break;\n                    case \"government\":\n                        router.push(\"/government/dashboard\");\n                        break;\n                    default:\n                        router.push(\"/auth/login\");\n                }\n                return;\n            }\n            // Check if account is approved (except for admins)\n            if (parsedUser.role !== \"admin\" && parsedUser.role !== \"super_admin\" && parsedUser.status !== \"approved\") {\n                router.push(\"/account-status\");\n                return;\n            }\n            setUser(parsedUser);\n            setLoading(false);\n        } catch (error) {\n            console.error(\"DashboardLayout - Error parsing user data:\", error);\n            router.push(\"/auth/login\");\n            return;\n        }\n    }, [\n        router,\n        allowedRoles\n    ]);\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-primary mx-auto\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/DashboardLayout.tsx\",\n                        lineNumber: 77,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-4 text-muted-foreground\",\n                        children: \"جاري التحميل...\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/DashboardLayout.tsx\",\n                        lineNumber: 78,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/DashboardLayout.tsx\",\n                lineNumber: 76,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/DashboardLayout.tsx\",\n            lineNumber: 75,\n            columnNumber: 7\n        }, this);\n    }\n    if (!user) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Sidebar__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                userRole: user.role\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/DashboardLayout.tsx\",\n                lineNumber: 90,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"flex-1 overflow-auto\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-6\",\n                    children: children\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/DashboardLayout.tsx\",\n                    lineNumber: 92,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/DashboardLayout.tsx\",\n                lineNumber: 91,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/DashboardLayout.tsx\",\n        lineNumber: 89,\n        columnNumber: 5\n    }, this);\n}\n_s(DashboardLayout, \"J17Kp8z+0ojgAqGoY5o3BCjwWms=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = DashboardLayout;\nvar _c;\n$RefreshReg$(_c, \"DashboardLayout\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/DashboardLayout.tsx\n"));

/***/ })

});