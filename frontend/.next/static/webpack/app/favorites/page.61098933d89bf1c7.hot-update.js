"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/favorites/page",{

/***/ "(app-pages-browser)/./components/DashboardLayout.tsx":
/*!****************************************!*\
  !*** ./components/DashboardLayout.tsx ***!
  \****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ DashboardLayout; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_Sidebar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/Sidebar */ \"(app-pages-browser)/./components/Sidebar.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction DashboardLayout(param) {\n    let { children, allowedRoles } = param;\n    _s();\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const token = localStorage.getItem(\"token\");\n        const userData = localStorage.getItem(\"user\");\n        if (!token || !userData) {\n            router.push(\"/auth/login\");\n            return;\n        }\n        try {\n            const parsedUser1 = JSON.parse(userData);\n            // Debug logging for role checking\n            console.log(\"DashboardLayout - User role:\", parsedUser1.role);\n            console.log(\"DashboardLayout - Allowed roles:\", allowedRoles);\n            console.log(\"DashboardLayout - Role allowed:\", allowedRoles.includes(parsedUser1.role));\n            // Check if user role is allowed\n            if (!allowedRoles.includes(parsedUser1.role)) {\n                console.log(\"DashboardLayout - Role not allowed, redirecting...\");\n                // Redirect to appropriate dashboard based on role\n                switch(parsedUser1.role){\n                    case \"admin\":\n                    case \"super_admin\":\n                        router.push(\"/admin/dashboard\");\n                        break;\n                    case \"company\":\n                        router.push(\"/company/dashboard\");\n                        break;\n                    case \"individual\":\n                        router.push(\"/user/dashboard\");\n                        break;\n                    case \"government\":\n                        router.push(\"/government/dashboard\");\n                        break;\n                    default:\n                        router.push(\"/auth/login\");\n                }\n                return;\n            }\n            // Check if account is approved (except for admins)\n            if (parsedUser1.role !== \"admin\" && parsedUser1.role !== \"super_admin\" && parsedUser1.status !== \"approved\") {\n                router.push(\"/account-status\");\n                return;\n            }\n            setUser(parsedUser1);\n            setLoading(false);\n        } catch (error) {\n            console.error(\"DashboardLayout - Error parsing user data:\", error);\n            router.push(\"/auth/login\");\n            return;\n        }\n        // Check if account is approved (except for admins)\n        if (parsedUser.role !== \"admin\" && parsedUser.role !== \"super_admin\" && parsedUser.status !== \"approved\") {\n            router.push(\"/account-status\");\n            return;\n        }\n        setUser(parsedUser);\n        setLoading(false);\n    }, [\n        router,\n        allowedRoles\n    ]);\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-primary mx-auto\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/DashboardLayout.tsx\",\n                        lineNumber: 86,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-4 text-muted-foreground\",\n                        children: \"جاري التحميل...\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/DashboardLayout.tsx\",\n                        lineNumber: 87,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/DashboardLayout.tsx\",\n                lineNumber: 85,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/DashboardLayout.tsx\",\n            lineNumber: 84,\n            columnNumber: 7\n        }, this);\n    }\n    if (!user) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Sidebar__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                userRole: user.role\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/DashboardLayout.tsx\",\n                lineNumber: 99,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"flex-1 overflow-auto\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-6\",\n                    children: children\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/DashboardLayout.tsx\",\n                    lineNumber: 101,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/DashboardLayout.tsx\",\n                lineNumber: 100,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/DashboardLayout.tsx\",\n        lineNumber: 98,\n        columnNumber: 5\n    }, this);\n}\n_s(DashboardLayout, \"J17Kp8z+0ojgAqGoY5o3BCjwWms=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = DashboardLayout;\nvar _c;\n$RefreshReg$(_c, \"DashboardLayout\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/DashboardLayout.tsx\n"));

/***/ })

});