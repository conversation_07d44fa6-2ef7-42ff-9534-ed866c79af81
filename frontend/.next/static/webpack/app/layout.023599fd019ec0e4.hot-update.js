/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Ffahad%2FDesktop%2Fbrid1%2Ffrontend%2Fcomponents%2Fui%2Ftoaster.tsx%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ffahad%2FDesktop%2Fbrid1%2Ffrontend%2Fcontexts%2FAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ffahad%2FDesktop%2Fbrid1%2Ffrontend%2Fcontexts%2FCurrencyContext.tsx%22%2C%22ids%22%3A%5B%22CurrencyProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ffahad%2FDesktop%2Fbrid1%2Ffrontend%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ffahad%2FDesktop%2Fbrid1%2Ffrontend%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=false!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Ffahad%2FDesktop%2Fbrid1%2Ffrontend%2Fcomponents%2Fui%2Ftoaster.tsx%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ffahad%2FDesktop%2Fbrid1%2Ffrontend%2Fcontexts%2FAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ffahad%2FDesktop%2Fbrid1%2Ffrontend%2Fcontexts%2FCurrencyContext.tsx%22%2C%22ids%22%3A%5B%22CurrencyProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ffahad%2FDesktop%2Fbrid1%2Ffrontend%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ffahad%2FDesktop%2Fbrid1%2Ffrontend%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=false! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/ui/toaster.tsx */ \"(app-pages-browser)/./components/ui/toaster.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./contexts/AuthContext.tsx */ \"(app-pages-browser)/./contexts/AuthContext.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./contexts/CurrencyContext.tsx */ \"(app-pages-browser)/./contexts/CurrencyContext.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/font/google/target.css?{\"path\":\"app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(app-pages-browser)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app/layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/globals.css */ \"(app-pages-browser)/./app/globals.css\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtZmxpZ2h0LWNsaWVudC1lbnRyeS1sb2FkZXIuanM/bW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGVXNlcnMlMkZmYWhhZCUyRkRlc2t0b3AlMkZicmlkMSUyRmZyb250ZW5kJTJGY29tcG9uZW50cyUyRnVpJTJGdG9hc3Rlci50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJUb2FzdGVyJTIyJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGZmFoYWQlMkZEZXNrdG9wJTJGYnJpZDElMkZmcm9udGVuZCUyRmNvbnRleHRzJTJGQXV0aENvbnRleHQudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyQXV0aFByb3ZpZGVyJTIyJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGZmFoYWQlMkZEZXNrdG9wJTJGYnJpZDElMkZmcm9udGVuZCUyRmNvbnRleHRzJTJGQ3VycmVuY3lDb250ZXh0LnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMkN1cnJlbmN5UHJvdmlkZXIlMjIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGVXNlcnMlMkZmYWhhZCUyRkRlc2t0b3AlMkZicmlkMSUyRmZyb250ZW5kJTJGbm9kZV9tb2R1bGVzJTJGbmV4dCUyRmZvbnQlMkZnb29nbGUlMkZ0YXJnZXQuY3NzJTNGJTdCJTVDJTIycGF0aCU1QyUyMiUzQSU1QyUyMmFwcCUyRmxheW91dC50c3glNUMlMjIlMkMlNUMlMjJpbXBvcnQlNUMlMjIlM0ElNUMlMjJJbnRlciU1QyUyMiUyQyU1QyUyMmFyZ3VtZW50cyU1QyUyMiUzQSU1QiU3QiU1QyUyMnN1YnNldHMlNUMlMjIlM0ElNUIlNUMlMjJsYXRpbiU1QyUyMiU1RCU3RCU1RCUyQyU1QyUyMnZhcmlhYmxlTmFtZSU1QyUyMiUzQSU1QyUyMmludGVyJTVDJTIyJTdEJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGZmFoYWQlMkZEZXNrdG9wJTJGYnJpZDElMkZmcm9udGVuZCUyRmFwcCUyRmdsb2JhbHMuY3NzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj1mYWxzZSEiLCJtYXBwaW5ncyI6IkFBQUEsZ0xBQStIO0FBQy9IO0FBQ0EsOEtBQW1JO0FBQ25JO0FBQ0Esc0xBQTJJO0FBQzNJO0FBQ0Esa2JBQXlPO0FBQ3pPO0FBQ0EsNEpBQXdGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8/NjQyNCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcIlRvYXN0ZXJcIl0gKi8gXCIvVXNlcnMvZmFoYWQvRGVza3RvcC9icmlkMS9mcm9udGVuZC9jb21wb25lbnRzL3VpL3RvYXN0ZXIudHN4XCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJBdXRoUHJvdmlkZXJcIl0gKi8gXCIvVXNlcnMvZmFoYWQvRGVza3RvcC9icmlkMS9mcm9udGVuZC9jb250ZXh0cy9BdXRoQ29udGV4dC50c3hcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcIkN1cnJlbmN5UHJvdmlkZXJcIl0gKi8gXCIvVXNlcnMvZmFoYWQvRGVza3RvcC9icmlkMS9mcm9udGVuZC9jb250ZXh0cy9DdXJyZW5jeUNvbnRleHQudHN4XCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvZmFoYWQvRGVza3RvcC9icmlkMS9mcm9udGVuZC9ub2RlX21vZHVsZXMvbmV4dC9mb250L2dvb2dsZS90YXJnZXQuY3NzP3tcXFwicGF0aFxcXCI6XFxcImFwcC9sYXlvdXQudHN4XFxcIixcXFwiaW1wb3J0XFxcIjpcXFwiSW50ZXJcXFwiLFxcXCJhcmd1bWVudHNcXFwiOlt7XFxcInN1YnNldHNcXFwiOltcXFwibGF0aW5cXFwiXX1dLFxcXCJ2YXJpYWJsZU5hbWVcXFwiOlxcXCJpbnRlclxcXCJ9XCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvZmFoYWQvRGVza3RvcC9icmlkMS9mcm9udGVuZC9hcHAvZ2xvYmFscy5jc3NcIik7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Ffahad%2FDesktop%2Fbrid1%2Ffrontend%2Fcomponents%2Fui%2Ftoaster.tsx%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ffahad%2FDesktop%2Fbrid1%2Ffrontend%2Fcontexts%2FAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ffahad%2FDesktop%2Fbrid1%2Ffrontend%2Fcontexts%2FCurrencyContext.tsx%22%2C%22ids%22%3A%5B%22CurrencyProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ffahad%2FDesktop%2Fbrid1%2Ffrontend%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Ffahad%2FDesktop%2Fbrid1%2Ffrontend%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./contexts/CurrencyContext.tsx":
/*!**************************************!*\
  !*** ./contexts/CurrencyContext.tsx ***!
  \**************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CurrencyProvider: function() { return /* binding */ CurrencyProvider; },\n/* harmony export */   useCurrency: function() { return /* binding */ useCurrency; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_currencyService__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/currencyService */ \"(app-pages-browser)/./lib/currencyService.ts\");\n/* __next_internal_client_entry_do_not_use__ CurrencyProvider,useCurrency auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\nconst CurrencyContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction CurrencyProvider(param) {\n    let { children } = param;\n    _s();\n    const [userCurrency, setUserCurrencyState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"SAR\");\n    const [exchangeRates, setExchangeRates] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // Initialize currency and exchange rates\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const initializeCurrency = async ()=>{\n            try {\n                // Load saved currency\n                const savedCurrency = _lib_currencyService__WEBPACK_IMPORTED_MODULE_2__.currencyService.getUserCurrency();\n                setUserCurrencyState(savedCurrency);\n                // Load exchange rates\n                await _lib_currencyService__WEBPACK_IMPORTED_MODULE_2__.currencyService.updateExchangeRates();\n                setExchangeRates(_lib_currencyService__WEBPACK_IMPORTED_MODULE_2__.currencyService.getExchangeRates());\n            } catch (error) {\n                console.error(\"Failed to initialize currency:\", error);\n            } finally{\n                setIsLoading(false);\n            }\n        };\n        initializeCurrency();\n    }, []);\n    // Update currency and save to localStorage\n    const setUserCurrency = async (currency)=>{\n        try {\n            setUserCurrencyState(currency);\n            _lib_currencyService__WEBPACK_IMPORTED_MODULE_2__.currencyService.setUserCurrency(currency);\n            // Update exchange rates if needed\n            await _lib_currencyService__WEBPACK_IMPORTED_MODULE_2__.currencyService.updateExchangeRates();\n            setExchangeRates(_lib_currencyService__WEBPACK_IMPORTED_MODULE_2__.currencyService.getExchangeRates());\n        } catch (error) {\n            console.error(\"Failed to update currency:\", error);\n        }\n    };\n    // Format amount with currency\n    const formatAmount = (amount, currency)=>{\n        return _lib_currencyService__WEBPACK_IMPORTED_MODULE_2__.currencyService.formatAmount(amount, currency || userCurrency);\n    };\n    // Convert amount between currencies\n    const convertAmount = async (amount, fromCurrency, toCurrency)=>{\n        return _lib_currencyService__WEBPACK_IMPORTED_MODULE_2__.currencyService.convertCurrency(amount, fromCurrency, toCurrency || userCurrency);\n    };\n    const value = {\n        userCurrency,\n        setUserCurrency,\n        formatAmount,\n        convertAmount,\n        exchangeRates,\n        isLoading\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CurrencyContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/brid1/frontend/contexts/CurrencyContext.tsx\",\n        lineNumber: 77,\n        columnNumber: 5\n    }, this);\n}\n_s(CurrencyProvider, \"2wekN+CRrLyAImqR3bXukl0PecE=\");\n_c = CurrencyProvider;\nfunction useCurrency() {\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(CurrencyContext);\n    if (context === undefined) {\n        throw new Error(\"useCurrency must be used within a CurrencyProvider\");\n    }\n    return context;\n}\n_s1(useCurrency, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nvar _c;\n$RefreshReg$(_c, \"CurrencyProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./contexts/CurrencyContext.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./lib/currencyService.ts":
/*!********************************!*\
  !*** ./lib/currencyService.ts ***!
  \********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   currencyService: function() { return /* binding */ currencyService; }\n/* harmony export */ });\n// Frontend Currency Service\nclass CurrencyService {\n    // Get current exchange rates\n    async getExchangeRates() {\n        try {\n            // Check if cache is still valid\n            if (this.lastUpdated && Date.now() - this.lastUpdated < this.CACHE_DURATION) {\n                return this.exchangeRates;\n            }\n            // Try to fetch fresh rates from backend\n            try {\n                const response = await fetch(\"http://localhost:5000/api/currency/rates\");\n                if (response.ok) {\n                    const data = await response.json();\n                    if (data.success && data.data && data.data.rates) {\n                        this.exchangeRates = data.data.rates;\n                        this.lastUpdated = Date.now();\n                        console.log(\"✅ Currency rates updated from backend\");\n                    }\n                }\n            } catch (apiError) {\n                console.warn(\"⚠️ Failed to fetch fresh currency rates, using cached rates:\", apiError);\n            }\n            return this.exchangeRates;\n        } catch (error) {\n            console.error(\"❌ Currency service error:\", error);\n            return this.exchangeRates; // Return cached rates as fallback\n        }\n    }\n    // Convert amount from one currency to another\n    async convertCurrency(amount, fromCurrency, toCurrency) {\n        try {\n            if (fromCurrency === toCurrency) {\n                return amount;\n            }\n            const rates = await this.getExchangeRates();\n            // Convert to USD first, then to target currency\n            const usdAmount = amount / rates[fromCurrency];\n            const convertedAmount = usdAmount * rates[toCurrency];\n            return Math.round(convertedAmount * 100) / 100; // Round to 2 decimal places\n        } catch (error) {\n            console.error(\"❌ Currency conversion error:\", error);\n            throw new Error(\"Currency conversion failed\");\n        }\n    }\n    // Convert amount to USD (base currency for comparisons)\n    async convertToUSD(amount, fromCurrency) {\n        return this.convertCurrency(amount, fromCurrency, \"USD\");\n    }\n    // Convert amount from USD to target currency\n    async convertFromUSD(amount, toCurrency) {\n        return this.convertCurrency(amount, \"USD\", toCurrency);\n    }\n    // Get currency symbol\n    getCurrencySymbol(currency) {\n        const symbols = {\n            USD: \"$\",\n            SAR: \"ر.س\",\n            EUR: \"€\",\n            GBP: \"\\xa3\",\n            AED: \"د.إ\",\n            KWD: \"د.ك\",\n            QAR: \"ر.ق\",\n            BHD: \"د.ب\",\n            OMR: \"ر.ع\"\n        };\n        return symbols[currency] || currency;\n    }\n    // Format amount with currency\n    formatAmount(amount, currency) {\n        const symbol = this.getCurrencySymbol(currency);\n        const formattedAmount = amount.toLocaleString();\n        // For Arabic currencies, put symbol after number\n        if ([\n            \"SAR\",\n            \"AED\",\n            \"KWD\",\n            \"QAR\",\n            \"BHD\",\n            \"OMR\"\n        ].includes(currency)) {\n            return \"\".concat(formattedAmount, \" \").concat(symbol);\n        }\n        // For Western currencies, put symbol before number\n        return \"\".concat(symbol).concat(formattedAmount);\n    }\n    // Get supported currencies\n    getSupportedCurrencies() {\n        return [\n            {\n                code: \"USD\",\n                name: \"US Dollar\",\n                symbol: \"$\"\n            },\n            {\n                code: \"SAR\",\n                name: \"Saudi Riyal\",\n                symbol: \"ر.س\"\n            },\n            {\n                code: \"EUR\",\n                name: \"Euro\",\n                symbol: \"€\"\n            },\n            {\n                code: \"GBP\",\n                name: \"British Pound\",\n                symbol: \"\\xa3\"\n            },\n            {\n                code: \"AED\",\n                name: \"UAE Dirham\",\n                symbol: \"د.إ\"\n            },\n            {\n                code: \"KWD\",\n                name: \"Kuwaiti Dinar\",\n                symbol: \"د.ك\"\n            },\n            {\n                code: \"QAR\",\n                name: \"Qatari Riyal\",\n                symbol: \"ر.ق\"\n            },\n            {\n                code: \"BHD\",\n                name: \"Bahraini Dinar\",\n                symbol: \"د.ب\"\n            },\n            {\n                code: \"OMR\",\n                name: \"Omani Rial\",\n                symbol: \"ر.ع\"\n            }\n        ];\n    }\n    // Validate currency code\n    isValidCurrency(currency) {\n        return Object.keys(this.exchangeRates).includes(currency);\n    }\n    // Get user's preferred currency from localStorage or default\n    getUserCurrency() {\n        try {\n            return localStorage.getItem(\"preferredCurrency\") || \"SAR\";\n        } catch (e) {\n            return \"SAR\";\n        }\n    }\n    // Set user's preferred currency\n    setUserCurrency(currency) {\n        try {\n            if (this.isValidCurrency(currency)) {\n                localStorage.setItem(\"preferredCurrency\", currency);\n            }\n        } catch (error) {\n            console.error(\"Failed to save currency preference:\", error);\n        }\n    }\n    // Convert auction data to user's preferred currency\n    async convertAuctionData(auction, userCurrency) {\n        const targetCurrency = userCurrency || this.getUserCurrency();\n        if (!auction.currency || auction.currency === targetCurrency) {\n            return auction;\n        }\n        try {\n            const convertedAuction = {\n                ...auction\n            };\n            // Convert prices\n            if (auction.currentBid) {\n                convertedAuction.currentBid = await this.convertCurrency(auction.currentBid, auction.currency, targetCurrency);\n            }\n            if (auction.startingPrice) {\n                convertedAuction.startingPrice = await this.convertCurrency(auction.startingPrice, auction.currency, targetCurrency);\n            }\n            if (auction.reservePrice) {\n                convertedAuction.reservePrice = await this.convertCurrency(auction.reservePrice, auction.currency, targetCurrency);\n            }\n            // Update currency\n            convertedAuction.currency = targetCurrency;\n            convertedAuction.originalCurrency = auction.currency;\n            return convertedAuction;\n        } catch (error) {\n            console.error(\"Failed to convert auction data:\", error);\n            return auction; // Return original data if conversion fails\n        }\n    }\n    constructor(){\n        this.exchangeRates = {\n            USD: 1.0,\n            SAR: 3.75,\n            EUR: 0.85,\n            GBP: 0.73,\n            AED: 3.67,\n            KWD: 0.30,\n            QAR: 3.64,\n            BHD: 0.38,\n            OMR: 0.38\n        };\n        this.lastUpdated = null;\n        this.CACHE_DURATION = 60 * 60 * 1000 // 1 hour\n        ;\n    }\n}\nconst currencyService = new CurrencyService();\n/* harmony default export */ __webpack_exports__[\"default\"] = (currencyService);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/currencyService.ts\n"));

/***/ })

});