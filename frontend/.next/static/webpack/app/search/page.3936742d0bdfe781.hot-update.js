"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/search/page",{

/***/ "(app-pages-browser)/./components/CurrencyDisplay.tsx":
/*!****************************************!*\
  !*** ./components/CurrencyDisplay.tsx ***!
  \****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CurrencyDisplay: function() { return /* binding */ CurrencyDisplay; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_CurrencyContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/CurrencyContext */ \"(app-pages-browser)/./contexts/CurrencyContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ CurrencyDisplay auto */ \nvar _s = $RefreshSig$();\n\n\nfunction CurrencyDisplay(param) {\n    let { amount, fromCurrency, className = \"\", showOriginal = false, showDualCurrency = true } = param;\n    _s();\n    const { formatAmountWithConversion, userCurrency } = (0,_contexts_CurrencyContext__WEBPACK_IMPORTED_MODULE_2__.useCurrency)();\n    const [displayText, setDisplayText] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [usdDisplayText, setUsdDisplayText] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const convertAndFormat = async ()=>{\n            try {\n                setIsLoading(true);\n                const formattedAmount = await formatAmountWithConversion(amount, fromCurrency, userCurrency);\n                setDisplayText(formattedAmount);\n            } catch (error) {\n                console.error(\"Currency conversion failed:\", error);\n                // Fallback to original amount with target currency symbol\n                setDisplayText(\"\".concat(amount.toLocaleString(), \" \").concat(userCurrency));\n            } finally{\n                setIsLoading(false);\n            }\n        };\n        convertAndFormat();\n    }, [\n        amount,\n        fromCurrency,\n        userCurrency,\n        formatAmountWithConversion\n    ]);\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"animate-pulse \".concat(className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"h-4 bg-gray-200 rounded w-20\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/CurrencyDisplay.tsx\",\n                lineNumber: 47,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/CurrencyDisplay.tsx\",\n            lineNumber: 46,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n        className: className,\n        children: [\n            displayText,\n            showOriginal && fromCurrency !== userCurrency && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"text-xs text-gray-400 ml-1\",\n                children: [\n                    \"(من \",\n                    amount.toLocaleString(),\n                    \" \",\n                    fromCurrency,\n                    \")\"\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/CurrencyDisplay.tsx\",\n                lineNumber: 56,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/CurrencyDisplay.tsx\",\n        lineNumber: 53,\n        columnNumber: 5\n    }, this);\n}\n_s(CurrencyDisplay, \"4Ln3/C9xc+Ue4sBDNnkTE/bzAGI=\", false, function() {\n    return [\n        _contexts_CurrencyContext__WEBPACK_IMPORTED_MODULE_2__.useCurrency\n    ];\n});\n_c = CurrencyDisplay;\nvar _c;\n$RefreshReg$(_c, \"CurrencyDisplay\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/CurrencyDisplay.tsx\n"));

/***/ })

});