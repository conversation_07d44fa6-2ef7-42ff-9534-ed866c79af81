"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/search/page",{

/***/ "(app-pages-browser)/./components/CurrencyDisplay.tsx":
/*!****************************************!*\
  !*** ./components/CurrencyDisplay.tsx ***!
  \****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CurrencyDisplay: function() { return /* binding */ CurrencyDisplay; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_CurrencyContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/CurrencyContext */ \"(app-pages-browser)/./contexts/CurrencyContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ CurrencyDisplay auto */ \nvar _s = $RefreshSig$();\n\n\nfunction CurrencyDisplay(param) {\n    let { amount, fromCurrency, className = \"\", showOriginal = false, showDualCurrency = true } = param;\n    _s();\n    const { formatAmountWithConversion, userCurrency } = (0,_contexts_CurrencyContext__WEBPACK_IMPORTED_MODULE_2__.useCurrency)();\n    const [displayText, setDisplayText] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [usdDisplayText, setUsdDisplayText] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const convertAndFormat = async ()=>{\n            try {\n                setIsLoading(true);\n                // Convert to user's selected currency\n                const formattedAmount = await formatAmountWithConversion(amount, fromCurrency, userCurrency);\n                setDisplayText(formattedAmount);\n                // Convert to USD if user currency is not USD and showDualCurrency is true\n                if (showDualCurrency && userCurrency !== \"USD\") {\n                    const usdFormattedAmount = await formatAmountWithConversion(amount, fromCurrency, \"USD\");\n                    setUsdDisplayText(usdFormattedAmount);\n                } else {\n                    setUsdDisplayText(\"\");\n                }\n            } catch (error) {\n                console.error(\"Currency conversion failed:\", error);\n                // Fallback to original amount with target currency symbol\n                setDisplayText(\"\".concat(amount.toLocaleString(), \" \").concat(userCurrency));\n                setUsdDisplayText(\"\");\n            } finally{\n                setIsLoading(false);\n            }\n        };\n        convertAndFormat();\n    }, [\n        amount,\n        fromCurrency,\n        userCurrency,\n        formatAmountWithConversion,\n        showDualCurrency\n    ]);\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"animate-pulse \".concat(className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"h-4 bg-gray-200 rounded w-20\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/CurrencyDisplay.tsx\",\n                lineNumber: 58,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/CurrencyDisplay.tsx\",\n            lineNumber: 57,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: className,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"font-semibold\",\n                children: displayText\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/CurrencyDisplay.tsx\",\n                lineNumber: 65,\n                columnNumber: 7\n            }, this),\n            showDualCurrency && userCurrency !== \"USD\" && usdDisplayText && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-sm text-gray-500 mt-1\",\n                children: usdDisplayText\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/CurrencyDisplay.tsx\",\n                lineNumber: 69,\n                columnNumber: 9\n            }, this),\n            showOriginal && fromCurrency !== userCurrency && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-xs text-gray-400 mt-1\",\n                children: [\n                    \"(من \",\n                    amount.toLocaleString(),\n                    \" \",\n                    fromCurrency,\n                    \")\"\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/CurrencyDisplay.tsx\",\n                lineNumber: 74,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/CurrencyDisplay.tsx\",\n        lineNumber: 64,\n        columnNumber: 5\n    }, this);\n}\n_s(CurrencyDisplay, \"4Ln3/C9xc+Ue4sBDNnkTE/bzAGI=\", false, function() {\n    return [\n        _contexts_CurrencyContext__WEBPACK_IMPORTED_MODULE_2__.useCurrency\n    ];\n});\n_c = CurrencyDisplay;\nvar _c;\n$RefreshReg$(_c, \"CurrencyDisplay\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/CurrencyDisplay.tsx\n"));

/***/ })

});