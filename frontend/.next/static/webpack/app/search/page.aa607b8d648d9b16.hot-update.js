"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/search/page",{

/***/ "(app-pages-browser)/./components/CurrencyDisplay.tsx":
/*!****************************************!*\
  !*** ./components/CurrencyDisplay.tsx ***!
  \****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CurrencyDisplay: function() { return /* binding */ CurrencyDisplay; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_CurrencyContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/CurrencyContext */ \"(app-pages-browser)/./contexts/CurrencyContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ CurrencyDisplay auto */ \nvar _s = $RefreshSig$();\n\n\nfunction CurrencyDisplay(param) {\n    let { amount, fromCurrency, className = \"\", showOriginal = false } = param;\n    _s();\n    const { formatAmountWithConversion, userCurrency } = (0,_contexts_CurrencyContext__WEBPACK_IMPORTED_MODULE_2__.useCurrency)();\n    const [displayText, setDisplayText] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const convertAndFormat = async ()=>{\n            try {\n                setIsLoading(true);\n                const formattedAmount = await formatAmountWithConversion(amount, fromCurrency, userCurrency);\n                setDisplayText(formattedAmount);\n            } catch (error) {\n                console.error(\"Currency conversion failed:\", error);\n                // Fallback to original amount with target currency symbol\n                setDisplayText(\"\".concat(amount.toLocaleString(), \" \").concat(userCurrency));\n            } finally{\n                setIsLoading(false);\n            }\n        };\n        convertAndFormat();\n    }, [\n        amount,\n        fromCurrency,\n        userCurrency,\n        formatAmountWithConversion\n    ]);\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"animate-pulse \".concat(className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"h-4 bg-gray-200 rounded w-20\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/CurrencyDisplay.tsx\",\n                lineNumber: 45,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/CurrencyDisplay.tsx\",\n            lineNumber: 44,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n        className: className,\n        children: [\n            displayText,\n            showOriginal && fromCurrency !== userCurrency && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"text-xs text-gray-400 ml-1\",\n                children: [\n                    \"(من \",\n                    amount.toLocaleString(),\n                    \" \",\n                    fromCurrency,\n                    \")\"\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/CurrencyDisplay.tsx\",\n                lineNumber: 54,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/brid1/frontend/components/CurrencyDisplay.tsx\",\n        lineNumber: 51,\n        columnNumber: 5\n    }, this);\n}\n_s(CurrencyDisplay, \"uSdoNtlS2KIo3nfa/PqJMsU0WlQ=\", false, function() {\n    return [\n        _contexts_CurrencyContext__WEBPACK_IMPORTED_MODULE_2__.useCurrency\n    ];\n});\n_c = CurrencyDisplay;\nvar _c;\n$RefreshReg$(_c, \"CurrencyDisplay\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/CurrencyDisplay.tsx\n"));

/***/ })

});