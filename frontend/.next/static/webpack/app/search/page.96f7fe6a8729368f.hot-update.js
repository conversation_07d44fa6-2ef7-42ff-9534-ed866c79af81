"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/search/page",{

/***/ "(app-pages-browser)/./lib/currencyService.ts":
/*!********************************!*\
  !*** ./lib/currencyService.ts ***!
  \********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   currencyService: function() { return /* binding */ currencyService; }\n/* harmony export */ });\n// Frontend Currency Service\nclass CurrencyService {\n    // Get current exchange rates\n    async getExchangeRates() {\n        try {\n            // Check if cache is still valid\n            if (this.lastUpdated && Date.now() - this.lastUpdated < this.CACHE_DURATION) {\n                return this.exchangeRates;\n            }\n            // Try to fetch fresh rates from backend\n            try {\n                const response = await fetch(\"http://localhost:5000/api/currency/rates\");\n                if (response.ok) {\n                    const data = await response.json();\n                    if (data.success && data.data && data.data.rates) {\n                        this.exchangeRates = data.data.rates;\n                        this.lastUpdated = Date.now();\n                        console.log(\"✅ Currency rates updated from backend\");\n                    }\n                }\n            } catch (apiError) {\n                console.warn(\"⚠️ Failed to fetch fresh currency rates, using cached rates:\", apiError);\n            }\n            return this.exchangeRates;\n        } catch (error) {\n            console.error(\"❌ Currency service error:\", error);\n            return this.exchangeRates; // Return cached rates as fallback\n        }\n    }\n    // Convert amount from one currency to another\n    async convertCurrency(amount, fromCurrency, toCurrency) {\n        try {\n            if (fromCurrency === toCurrency) {\n                return amount;\n            }\n            const rates = await this.getExchangeRates();\n            // Convert to USD first, then to target currency\n            const usdAmount = amount / rates[fromCurrency];\n            const convertedAmount = usdAmount * rates[toCurrency];\n            return Math.round(convertedAmount * 100) / 100; // Round to 2 decimal places\n        } catch (error) {\n            console.error(\"❌ Currency conversion error:\", error);\n            throw new Error(\"Currency conversion failed\");\n        }\n    }\n    // Convert amount to USD (base currency for comparisons)\n    async convertToUSD(amount, fromCurrency) {\n        return this.convertCurrency(amount, fromCurrency, \"USD\");\n    }\n    // Convert amount from USD to target currency\n    async convertFromUSD(amount, toCurrency) {\n        return this.convertCurrency(amount, \"USD\", toCurrency);\n    }\n    // Get currency symbol\n    getCurrencySymbol(currency) {\n        const symbols = {\n            USD: \"$\",\n            SAR: \"ر.س\",\n            EUR: \"€\",\n            GBP: \"\\xa3\",\n            AED: \"د.إ\",\n            KWD: \"د.ك\",\n            QAR: \"ر.ق\",\n            BHD: \"د.ب\",\n            OMR: \"ر.ع\"\n        };\n        return symbols[currency] || currency;\n    }\n    // Format amount with currency\n    formatAmount(amount, currency) {\n        // Handle null/undefined amounts\n        if (amount === null || amount === undefined || isNaN(amount)) {\n            amount = 0;\n        }\n        const symbol = this.getCurrencySymbol(currency);\n        const formattedAmount = amount.toLocaleString();\n        // For Arabic currencies, put symbol after number\n        if ([\n            \"SAR\",\n            \"AED\",\n            \"KWD\",\n            \"QAR\",\n            \"BHD\",\n            \"OMR\"\n        ].includes(currency)) {\n            return \"\".concat(formattedAmount, \" \").concat(symbol);\n        }\n        // For Western currencies, put symbol before number\n        return \"\".concat(symbol).concat(formattedAmount);\n    }\n    // Get supported currencies\n    getSupportedCurrencies() {\n        return [\n            {\n                code: \"USD\",\n                name: \"US Dollar\",\n                symbol: \"$\"\n            },\n            {\n                code: \"SAR\",\n                name: \"Saudi Riyal\",\n                symbol: \"ر.س\"\n            },\n            {\n                code: \"EUR\",\n                name: \"Euro\",\n                symbol: \"€\"\n            },\n            {\n                code: \"GBP\",\n                name: \"British Pound\",\n                symbol: \"\\xa3\"\n            },\n            {\n                code: \"AED\",\n                name: \"UAE Dirham\",\n                symbol: \"د.إ\"\n            },\n            {\n                code: \"KWD\",\n                name: \"Kuwaiti Dinar\",\n                symbol: \"د.ك\"\n            },\n            {\n                code: \"QAR\",\n                name: \"Qatari Riyal\",\n                symbol: \"ر.ق\"\n            },\n            {\n                code: \"BHD\",\n                name: \"Bahraini Dinar\",\n                symbol: \"د.ب\"\n            },\n            {\n                code: \"OMR\",\n                name: \"Omani Rial\",\n                symbol: \"ر.ع\"\n            }\n        ];\n    }\n    // Validate currency code\n    isValidCurrency(currency) {\n        const supportedCurrencies = [\n            \"USD\",\n            \"SAR\",\n            \"EUR\",\n            \"GBP\",\n            \"AED\",\n            \"KWD\",\n            \"QAR\",\n            \"BHD\",\n            \"OMR\",\n            \"JOD\"\n        ];\n        return supportedCurrencies.includes(currency);\n    }\n    // Get user's preferred currency from localStorage or default\n    getUserCurrency() {\n        try {\n            return localStorage.getItem(\"preferredCurrency\") || \"SAR\";\n        } catch (e) {\n            return \"SAR\";\n        }\n    }\n    // Set user's preferred currency\n    setUserCurrency(currency) {\n        try {\n            if (this.isValidCurrency(currency)) {\n                localStorage.setItem(\"preferredCurrency\", currency);\n            }\n        } catch (error) {\n            console.error(\"Failed to save currency preference:\", error);\n        }\n    }\n    // Convert auction data to user's preferred currency\n    async convertAuctionData(auction, userCurrency) {\n        const targetCurrency = userCurrency || this.getUserCurrency();\n        if (!auction.currency || auction.currency === targetCurrency) {\n            return auction;\n        }\n        try {\n            const convertedAuction = {\n                ...auction\n            };\n            // Convert prices\n            if (auction.currentBid) {\n                convertedAuction.currentBid = await this.convertCurrency(auction.currentBid, auction.currency, targetCurrency);\n            }\n            if (auction.startingPrice) {\n                convertedAuction.startingPrice = await this.convertCurrency(auction.startingPrice, auction.currency, targetCurrency);\n            }\n            if (auction.reservePrice) {\n                convertedAuction.reservePrice = await this.convertCurrency(auction.reservePrice, auction.currency, targetCurrency);\n            }\n            // Update currency\n            convertedAuction.currency = targetCurrency;\n            convertedAuction.originalCurrency = auction.currency;\n            return convertedAuction;\n        } catch (error) {\n            console.error(\"Failed to convert auction data:\", error);\n            return auction; // Return original data if conversion fails\n        }\n    }\n    constructor(){\n        this.exchangeRates = {\n            USD: 1.0,\n            SAR: 3.75,\n            EUR: 0.85,\n            GBP: 0.73,\n            AED: 3.67,\n            KWD: 0.30,\n            QAR: 3.64,\n            BHD: 0.38,\n            OMR: 0.38\n        };\n        this.lastUpdated = null;\n        this.CACHE_DURATION = 60 * 60 * 1000 // 1 hour\n        ;\n    }\n}\nconst currencyService = new CurrencyService();\n/* harmony default export */ __webpack_exports__[\"default\"] = (currencyService);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/currencyService.ts\n"));

/***/ })

});