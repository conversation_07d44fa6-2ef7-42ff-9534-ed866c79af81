"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/wallet/page",{

/***/ "(app-pages-browser)/./app/wallet/page.tsx":
/*!*****************************!*\
  !*** ./app/wallet/page.tsx ***!
  \*****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ WalletPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./components/ui/label.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./components/ui/select.tsx\");\n/* harmony import */ var _components_ui_table__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/table */ \"(app-pages-browser)/./components/ui/table.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownLeft_ArrowUpRight_DollarSign_History_Minus_Plus_TrendingUp_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownLeft,ArrowUpRight,DollarSign,History,Minus,Plus,TrendingUp,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-down-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownLeft_ArrowUpRight_DollarSign_History_Minus_Plus_TrendingUp_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownLeft,ArrowUpRight,DollarSign,History,Minus,Plus,TrendingUp,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-up-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownLeft_ArrowUpRight_DollarSign_History_Minus_Plus_TrendingUp_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownLeft,ArrowUpRight,DollarSign,History,Minus,Plus,TrendingUp,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/minus.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownLeft_ArrowUpRight_DollarSign_History_Minus_Plus_TrendingUp_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownLeft,ArrowUpRight,DollarSign,History,Minus,Plus,TrendingUp,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownLeft_ArrowUpRight_DollarSign_History_Minus_Plus_TrendingUp_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownLeft,ArrowUpRight,DollarSign,History,Minus,Plus,TrendingUp,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownLeft_ArrowUpRight_DollarSign_History_Minus_Plus_TrendingUp_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownLeft,ArrowUpRight,DollarSign,History,Minus,Plus,TrendingUp,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wallet.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownLeft_ArrowUpRight_DollarSign_History_Minus_Plus_TrendingUp_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownLeft,ArrowUpRight,DollarSign,History,Minus,Plus,TrendingUp,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/history.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownLeft_ArrowUpRight_DollarSign_History_Minus_Plus_TrendingUp_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownLeft,ArrowUpRight,DollarSign,History,Minus,Plus,TrendingUp,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./lib/api.ts\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./hooks/use-toast.ts\");\n/* harmony import */ var _contexts_CurrencyContext__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/contexts/CurrencyContext */ \"(app-pages-browser)/./contexts/CurrencyContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nfunction WalletPage() {\n    _s();\n    const [balance, setBalance] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [transactions, setTransactions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [depositAmount, setDepositAmount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [withdrawAmount, setWithdrawAmount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [selectedPaymentMethod, setSelectedPaymentMethod] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isDepositing, setIsDepositing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isWithdrawing, setIsWithdrawing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_10__.useToast)();\n    const { userCurrency } = (0,_contexts_CurrencyContext__WEBPACK_IMPORTED_MODULE_11__.useCurrency)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadWalletData();\n    }, []);\n    const loadWalletData = async ()=>{\n        try {\n            const [balanceResponse, transactionsResponse] = await Promise.all([\n                _lib_api__WEBPACK_IMPORTED_MODULE_9__[\"default\"].get(\"/wallet/balance\"),\n                _lib_api__WEBPACK_IMPORTED_MODULE_9__[\"default\"].get(\"/wallet/transactions\")\n            ]);\n            if (balanceResponse.data.success) {\n                setBalance(balanceResponse.data.data.balance);\n            }\n            if (transactionsResponse.data.success) {\n                setTransactions(transactionsResponse.data.data.transactions || []);\n            }\n        } catch (error) {\n            console.error(\"Error loading wallet data:\", error);\n            // Demo data\n            setBalance({\n                available: 25000,\n                pending: 5000,\n                total: 30000,\n                currency: \"SAR\"\n            });\n            setTransactions([\n                {\n                    id: \"1\",\n                    type: \"deposit\",\n                    amount: 10000,\n                    description: \"إيداع عبر بطاقة مدى\",\n                    status: \"completed\",\n                    date: \"2025-01-10T10:30:00Z\",\n                    reference: \"DEP-001\"\n                },\n                {\n                    id: \"2\",\n                    type: \"payment\",\n                    amount: -15000,\n                    description: \"دفع مزاد لابتوب عالي الأداء\",\n                    status: \"completed\",\n                    date: \"2025-01-09T14:20:00Z\",\n                    auctionId: \"auction-123\"\n                },\n                {\n                    id: \"3\",\n                    type: \"refund\",\n                    amount: 8000,\n                    description: \"استرداد من مزاد ملغي\",\n                    status: \"completed\",\n                    date: \"2025-01-08T09:15:00Z\",\n                    auctionId: \"auction-456\"\n                },\n                {\n                    id: \"4\",\n                    type: \"withdrawal\",\n                    amount: -5000,\n                    description: \"سحب إلى الحساب البنكي\",\n                    status: \"pending\",\n                    date: \"2025-01-07T16:45:00Z\",\n                    reference: \"WTH-001\"\n                }\n            ]);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleDeposit = async ()=>{\n        if (!depositAmount || !selectedPaymentMethod) {\n            toast({\n                title: \"بيانات ناقصة\",\n                description: \"يرجى إدخال المبلغ واختيار طريقة الدفع\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        try {\n            setIsDepositing(true);\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_9__[\"default\"].post(\"/wallet/deposit\", {\n                amount: parseFloat(depositAmount),\n                paymentMethod: selectedPaymentMethod\n            });\n            if (response.data.success) {\n                toast({\n                    title: \"تم الإيداع بنجاح\",\n                    description: \"تم إيداع \".concat(depositAmount, \" ريال في محفظتك\")\n                });\n                setDepositAmount(\"\");\n                setSelectedPaymentMethod(\"\");\n                loadWalletData();\n            }\n        } catch (error) {\n            toast({\n                title: \"خطأ في الإيداع\",\n                description: \"حدث خطأ في عملية الإيداع\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setIsDepositing(false);\n        }\n    };\n    const handleWithdraw = async ()=>{\n        if (!withdrawAmount) {\n            toast({\n                title: \"بيانات ناقصة\",\n                description: \"يرجى إدخال مبلغ السحب\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        const amount = parseFloat(withdrawAmount);\n        if (amount > ((balance === null || balance === void 0 ? void 0 : balance.available) || 0)) {\n            toast({\n                title: \"رصيد غير كافي\",\n                description: \"المبلغ المطلوب أكبر من الرصيد المتاح\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        try {\n            setIsWithdrawing(true);\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_9__[\"default\"].post(\"/wallet/withdraw\", {\n                amount: amount\n            });\n            if (response.data.success) {\n                toast({\n                    title: \"تم طلب السحب\",\n                    description: \"تم إرسال طلب سحب \".concat(withdrawAmount, \" ريال وسيتم معالجته خلال 24 ساعة\")\n                });\n                setWithdrawAmount(\"\");\n                loadWalletData();\n            }\n        } catch (error) {\n            toast({\n                title: \"خطأ في السحب\",\n                description: \"حدث خطأ في عملية السحب\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setIsWithdrawing(false);\n        }\n    };\n    const formatCurrency = (amount)=>{\n        return new Intl.NumberFormat(\"ar-SA\", {\n            style: \"currency\",\n            currency: \"SAR\"\n        }).format(Math.abs(amount));\n    };\n    const getTransactionIcon = (type)=>{\n        switch(type){\n            case \"deposit\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownLeft_ArrowUpRight_DollarSign_History_Minus_Plus_TrendingUp_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                    className: \"h-4 w-4 text-green-600\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/wallet/page.tsx\",\n                    lineNumber: 211,\n                    columnNumber: 16\n                }, this);\n            case \"withdrawal\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownLeft_ArrowUpRight_DollarSign_History_Minus_Plus_TrendingUp_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                    className: \"h-4 w-4 text-red-600\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/wallet/page.tsx\",\n                    lineNumber: 213,\n                    columnNumber: 16\n                }, this);\n            case \"payment\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownLeft_ArrowUpRight_DollarSign_History_Minus_Plus_TrendingUp_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                    className: \"h-4 w-4 text-red-600\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/wallet/page.tsx\",\n                    lineNumber: 215,\n                    columnNumber: 16\n                }, this);\n            case \"refund\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownLeft_ArrowUpRight_DollarSign_History_Minus_Plus_TrendingUp_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                    className: \"h-4 w-4 text-green-600\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/wallet/page.tsx\",\n                    lineNumber: 217,\n                    columnNumber: 16\n                }, this);\n            case \"fee\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownLeft_ArrowUpRight_DollarSign_History_Minus_Plus_TrendingUp_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                    className: \"h-4 w-4 text-orange-600\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/wallet/page.tsx\",\n                    lineNumber: 219,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownLeft_ArrowUpRight_DollarSign_History_Minus_Plus_TrendingUp_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                    className: \"h-4 w-4 text-gray-600\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/wallet/page.tsx\",\n                    lineNumber: 221,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    const getTransactionColor = (type, amount)=>{\n        if (amount > 0) return \"text-green-600\";\n        return \"text-red-600\";\n    };\n    const getStatusColor = (status)=>{\n        switch(status){\n            case \"completed\":\n                return \"bg-green-100 text-green-800\";\n            case \"pending\":\n                return \"bg-yellow-100 text-yellow-800\";\n            case \"failed\":\n                return \"bg-red-100 text-red-800\";\n            default:\n                return \"bg-gray-100 text-gray-800\";\n        }\n    };\n    const getStatusText = (status)=>{\n        switch(status){\n            case \"completed\":\n                return \"مكتملة\";\n            case \"pending\":\n                return \"في الانتظار\";\n            case \"failed\":\n                return \"فشلت\";\n            default:\n                return status;\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto p-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-pulse space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-8 bg-gray-200 rounded w-1/4\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/wallet/page.tsx\",\n                        lineNumber: 260,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                        children: [\n                            1,\n                            2,\n                            3\n                        ].map((i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-32 bg-gray-200 rounded\"\n                            }, i, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/wallet/page.tsx\",\n                                lineNumber: 263,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/wallet/page.tsx\",\n                        lineNumber: 261,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/wallet/page.tsx\",\n                lineNumber: 259,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/wallet/page.tsx\",\n            lineNumber: 258,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto p-6 max-w-6xl\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-center mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-3xl font-bold\",\n                            children: \"المحفظة\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/wallet/page.tsx\",\n                            lineNumber: 275,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mt-2\",\n                            children: \"إدارة رصيدك ومعاملاتك المالية\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/wallet/page.tsx\",\n                            lineNumber: 276,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/wallet/page.tsx\",\n                    lineNumber: 274,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/wallet/page.tsx\",\n                lineNumber: 273,\n                columnNumber: 7\n            }, this),\n            balance && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-3 gap-4 mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            className: \"p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-gray-600\",\n                                                children: \"الرصيد المتاح\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/wallet/page.tsx\",\n                                                lineNumber: 287,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-green-600\",\n                                                children: formatCurrency(balance.available)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/wallet/page.tsx\",\n                                                lineNumber: 288,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/wallet/page.tsx\",\n                                        lineNumber: 286,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownLeft_ArrowUpRight_DollarSign_History_Minus_Plus_TrendingUp_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        className: \"h-8 w-8 text-green-600\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/wallet/page.tsx\",\n                                        lineNumber: 292,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/wallet/page.tsx\",\n                                lineNumber: 285,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/wallet/page.tsx\",\n                            lineNumber: 284,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/wallet/page.tsx\",\n                        lineNumber: 283,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            className: \"p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-gray-600\",\n                                                children: \"الرصيد المعلق\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/wallet/page.tsx\",\n                                                lineNumber: 301,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-yellow-600\",\n                                                children: formatCurrency(balance.pending)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/wallet/page.tsx\",\n                                                lineNumber: 302,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/wallet/page.tsx\",\n                                        lineNumber: 300,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownLeft_ArrowUpRight_DollarSign_History_Minus_Plus_TrendingUp_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        className: \"h-8 w-8 text-yellow-600\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/wallet/page.tsx\",\n                                        lineNumber: 306,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/wallet/page.tsx\",\n                                lineNumber: 299,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/wallet/page.tsx\",\n                            lineNumber: 298,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/wallet/page.tsx\",\n                        lineNumber: 297,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            className: \"p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-gray-600\",\n                                                children: \"إجمالي الرصيد\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/wallet/page.tsx\",\n                                                lineNumber: 315,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-blue-600\",\n                                                children: formatCurrency(balance.total)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/wallet/page.tsx\",\n                                                lineNumber: 316,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/wallet/page.tsx\",\n                                        lineNumber: 314,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownLeft_ArrowUpRight_DollarSign_History_Minus_Plus_TrendingUp_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                        className: \"h-8 w-8 text-blue-600\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/wallet/page.tsx\",\n                                        lineNumber: 320,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/wallet/page.tsx\",\n                                lineNumber: 313,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/wallet/page.tsx\",\n                            lineNumber: 312,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/wallet/page.tsx\",\n                        lineNumber: 311,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/wallet/page.tsx\",\n                lineNumber: 282,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 gap-4 mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownLeft_ArrowUpRight_DollarSign_History_Minus_Plus_TrendingUp_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                className: \"h-5 w-5 text-green-600\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/wallet/page.tsx\",\n                                                lineNumber: 333,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"إيداع أموال\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/wallet/page.tsx\",\n                                        lineNumber: 332,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                        children: \"أضف أموال إلى محفظتك\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/wallet/page.tsx\",\n                                        lineNumber: 336,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/wallet/page.tsx\",\n                                lineNumber: 331,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                htmlFor: \"depositAmount\",\n                                                children: \"المبلغ (ريال سعودي)\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/wallet/page.tsx\",\n                                                lineNumber: 340,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                id: \"depositAmount\",\n                                                type: \"number\",\n                                                placeholder: \"0.00\",\n                                                value: depositAmount,\n                                                onChange: (e)=>setDepositAmount(e.target.value)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/wallet/page.tsx\",\n                                                lineNumber: 341,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/wallet/page.tsx\",\n                                        lineNumber: 339,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                htmlFor: \"paymentMethod\",\n                                                children: \"طريقة الدفع\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/wallet/page.tsx\",\n                                                lineNumber: 351,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                                value: selectedPaymentMethod,\n                                                onValueChange: setSelectedPaymentMethod,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {\n                                                            placeholder: \"اختر طريقة الدفع\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/wallet/page.tsx\",\n                                                            lineNumber: 354,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/wallet/page.tsx\",\n                                                        lineNumber: 353,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                value: \"mada\",\n                                                                children: \"مدى\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/wallet/page.tsx\",\n                                                                lineNumber: 357,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                value: \"visa\",\n                                                                children: \"فيزا\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/wallet/page.tsx\",\n                                                                lineNumber: 358,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                value: \"mastercard\",\n                                                                children: \"ماستركارد\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/wallet/page.tsx\",\n                                                                lineNumber: 359,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                value: \"bank_transfer\",\n                                                                children: \"تحويل بنكي\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/wallet/page.tsx\",\n                                                                lineNumber: 360,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/wallet/page.tsx\",\n                                                        lineNumber: 356,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/wallet/page.tsx\",\n                                                lineNumber: 352,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/wallet/page.tsx\",\n                                        lineNumber: 350,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        onClick: handleDeposit,\n                                        disabled: isDepositing || !depositAmount || !selectedPaymentMethod,\n                                        className: \"w-full\",\n                                        children: isDepositing ? \"جاري الإيداع...\" : \"إيداع\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/wallet/page.tsx\",\n                                        lineNumber: 365,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/wallet/page.tsx\",\n                                lineNumber: 338,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/wallet/page.tsx\",\n                        lineNumber: 330,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownLeft_ArrowUpRight_DollarSign_History_Minus_Plus_TrendingUp_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                className: \"h-5 w-5 text-red-600\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/wallet/page.tsx\",\n                                                lineNumber: 379,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"سحب أموال\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/wallet/page.tsx\",\n                                        lineNumber: 378,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                        children: \"اسحب أموال من محفظتك\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/wallet/page.tsx\",\n                                        lineNumber: 382,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/wallet/page.tsx\",\n                                lineNumber: 377,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                htmlFor: \"withdrawAmount\",\n                                                children: \"المبلغ (ريال سعودي)\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/wallet/page.tsx\",\n                                                lineNumber: 386,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                id: \"withdrawAmount\",\n                                                type: \"number\",\n                                                placeholder: \"0.00\",\n                                                value: withdrawAmount,\n                                                onChange: (e)=>setWithdrawAmount(e.target.value),\n                                                max: (balance === null || balance === void 0 ? void 0 : balance.available) || 0\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/wallet/page.tsx\",\n                                                lineNumber: 387,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-gray-500 mt-1\",\n                                                children: [\n                                                    \"الحد الأقصى: \",\n                                                    formatCurrency((balance === null || balance === void 0 ? void 0 : balance.available) || 0)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/wallet/page.tsx\",\n                                                lineNumber: 395,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/wallet/page.tsx\",\n                                        lineNumber: 385,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-3 bg-yellow-50 border border-yellow-200 rounded-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-yellow-800\",\n                                            children: \"⚠️ عمليات السحب تستغرق 1-3 أيام عمل للمعالجة\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/wallet/page.tsx\",\n                                            lineNumber: 401,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/wallet/page.tsx\",\n                                        lineNumber: 400,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        onClick: handleWithdraw,\n                                        disabled: isWithdrawing || !withdrawAmount,\n                                        variant: \"outline\",\n                                        className: \"w-full\",\n                                        children: isWithdrawing ? \"جاري السحب...\" : \"سحب\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/wallet/page.tsx\",\n                                        lineNumber: 406,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/wallet/page.tsx\",\n                                lineNumber: 384,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/wallet/page.tsx\",\n                        lineNumber: 376,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/wallet/page.tsx\",\n                lineNumber: 328,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownLeft_ArrowUpRight_DollarSign_History_Minus_Plus_TrendingUp_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/wallet/page.tsx\",\n                                        lineNumber: 422,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"سجل المعاملات\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/wallet/page.tsx\",\n                                lineNumber: 421,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                children: \"آخر المعاملات المالية\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/wallet/page.tsx\",\n                                lineNumber: 425,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/wallet/page.tsx\",\n                        lineNumber: 420,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        children: transactions.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center py-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownLeft_ArrowUpRight_DollarSign_History_Minus_Plus_TrendingUp_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                    className: \"h-12 w-12 text-gray-400 mx-auto mb-4\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/wallet/page.tsx\",\n                                    lineNumber: 430,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold mb-2\",\n                                    children: \"لا توجد معاملات\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/wallet/page.tsx\",\n                                    lineNumber: 431,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600\",\n                                    children: \"لم تقم بأي معاملات مالية بعد\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/wallet/page.tsx\",\n                                    lineNumber: 432,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/wallet/page.tsx\",\n                            lineNumber: 429,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.Table, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableRow, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableHead, {\n                                                children: \"النوع\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/wallet/page.tsx\",\n                                                lineNumber: 438,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableHead, {\n                                                children: \"الوصف\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/wallet/page.tsx\",\n                                                lineNumber: 439,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableHead, {\n                                                children: \"المبلغ\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/wallet/page.tsx\",\n                                                lineNumber: 440,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableHead, {\n                                                children: \"الحالة\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/wallet/page.tsx\",\n                                                lineNumber: 441,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableHead, {\n                                                children: \"التاريخ\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/wallet/page.tsx\",\n                                                lineNumber: 442,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/wallet/page.tsx\",\n                                        lineNumber: 437,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/wallet/page.tsx\",\n                                    lineNumber: 436,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableBody, {\n                                    children: transactions.map((transaction)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableRow, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableCell, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2\",\n                                                        children: [\n                                                            getTransactionIcon(transaction.type),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"capitalize\",\n                                                                children: transaction.type\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/wallet/page.tsx\",\n                                                                lineNumber: 451,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/wallet/page.tsx\",\n                                                        lineNumber: 449,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/wallet/page.tsx\",\n                                                    lineNumber: 448,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableCell, {\n                                                    children: transaction.description\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/wallet/page.tsx\",\n                                                    lineNumber: 454,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableCell, {\n                                                    className: \"font-semibold \".concat(getTransactionColor(transaction.type, transaction.amount)),\n                                                    children: [\n                                                        transaction.amount > 0 ? \"+\" : \"\",\n                                                        formatCurrency(transaction.amount)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/wallet/page.tsx\",\n                                                    lineNumber: 455,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableCell, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                        className: getStatusColor(transaction.status),\n                                                        children: getStatusText(transaction.status)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/wallet/page.tsx\",\n                                                        lineNumber: 459,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/wallet/page.tsx\",\n                                                    lineNumber: 458,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableCell, {\n                                                    children: new Date(transaction.date).toLocaleDateString(\"ar-SA\")\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/wallet/page.tsx\",\n                                                    lineNumber: 463,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, transaction.id, true, {\n                                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/wallet/page.tsx\",\n                                            lineNumber: 447,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/wallet/page.tsx\",\n                                    lineNumber: 445,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/wallet/page.tsx\",\n                            lineNumber: 435,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/wallet/page.tsx\",\n                        lineNumber: 427,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/wallet/page.tsx\",\n                lineNumber: 419,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/brid1/frontend/app/wallet/page.tsx\",\n        lineNumber: 272,\n        columnNumber: 5\n    }, this);\n}\n_s(WalletPage, \"y8txbJaZANi9EJnb3LEqEcZJnLg=\", false, function() {\n    return [\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_10__.useToast,\n        _contexts_CurrencyContext__WEBPACK_IMPORTED_MODULE_11__.useCurrency\n    ];\n});\n_c = WalletPage;\nvar _c;\n$RefreshReg$(_c, \"WalletPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/wallet/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./contexts/CurrencyContext.tsx":
/*!**************************************!*\
  !*** ./contexts/CurrencyContext.tsx ***!
  \**************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CurrencyProvider: function() { return /* binding */ CurrencyProvider; },\n/* harmony export */   useCurrency: function() { return /* binding */ useCurrency; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_currencyService__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/currencyService */ \"(app-pages-browser)/./lib/currencyService.ts\");\n/* __next_internal_client_entry_do_not_use__ CurrencyProvider,useCurrency auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\nconst CurrencyContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction CurrencyProvider(param) {\n    let { children } = param;\n    _s();\n    const [userCurrency, setUserCurrencyState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"SAR\");\n    const [exchangeRates, setExchangeRates] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // Initialize currency and exchange rates\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const initializeCurrency = async ()=>{\n            try {\n                // Load saved currency\n                const savedCurrency = _lib_currencyService__WEBPACK_IMPORTED_MODULE_2__.currencyService.getUserCurrency();\n                setUserCurrencyState(savedCurrency);\n                // Load exchange rates\n                const rates = await _lib_currencyService__WEBPACK_IMPORTED_MODULE_2__.currencyService.getExchangeRates();\n                setExchangeRates(rates);\n            } catch (error) {\n                console.error(\"Failed to initialize currency:\", error);\n            } finally{\n                setIsLoading(false);\n            }\n        };\n        initializeCurrency();\n    }, []);\n    // Update currency and save to localStorage\n    const setUserCurrency = async (currency)=>{\n        try {\n            setUserCurrencyState(currency);\n            _lib_currencyService__WEBPACK_IMPORTED_MODULE_2__.currencyService.setUserCurrency(currency);\n            // Update exchange rates if needed\n            const rates = await _lib_currencyService__WEBPACK_IMPORTED_MODULE_2__.currencyService.getExchangeRates();\n            setExchangeRates(rates);\n        } catch (error) {\n            console.error(\"Failed to update currency:\", error);\n        }\n    };\n    // Format amount with currency (no conversion)\n    const formatAmount = (amount, currency)=>{\n        return _lib_currencyService__WEBPACK_IMPORTED_MODULE_2__.currencyService.formatAmount(amount, currency || userCurrency);\n    };\n    // Format amount with currency conversion\n    const formatAmountWithConversion = async (amount, fromCurrency, toCurrency)=>{\n        const targetCurrency = toCurrency || userCurrency;\n        if (fromCurrency === targetCurrency) {\n            return _lib_currencyService__WEBPACK_IMPORTED_MODULE_2__.currencyService.formatAmount(amount, targetCurrency);\n        }\n        try {\n            const convertedAmount = await _lib_currencyService__WEBPACK_IMPORTED_MODULE_2__.currencyService.convertCurrency(amount, fromCurrency, targetCurrency);\n            return _lib_currencyService__WEBPACK_IMPORTED_MODULE_2__.currencyService.formatAmount(convertedAmount, targetCurrency);\n        } catch (error) {\n            console.error(\"Currency conversion failed:\", error);\n            // Fallback to original amount with target currency symbol\n            return _lib_currencyService__WEBPACK_IMPORTED_MODULE_2__.currencyService.formatAmount(amount, targetCurrency);\n        }\n    };\n    // Convert amount between currencies\n    const convertAmount = async (amount, fromCurrency, toCurrency)=>{\n        return _lib_currencyService__WEBPACK_IMPORTED_MODULE_2__.currencyService.convertCurrency(amount, fromCurrency, toCurrency || userCurrency);\n    };\n    // Get currency symbol\n    const getCurrencySymbol = (currency)=>{\n        return _lib_currencyService__WEBPACK_IMPORTED_MODULE_2__.currencyService.getCurrencySymbol(currency);\n    };\n    const value = {\n        userCurrency,\n        setUserCurrency,\n        formatAmount,\n        formatAmountWithConversion,\n        convertAmount,\n        getCurrencySymbol,\n        exchangeRates,\n        isLoading\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CurrencyContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/brid1/frontend/contexts/CurrencyContext.tsx\",\n        lineNumber: 103,\n        columnNumber: 5\n    }, this);\n}\n_s(CurrencyProvider, \"2wekN+CRrLyAImqR3bXukl0PecE=\");\n_c = CurrencyProvider;\nfunction useCurrency() {\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(CurrencyContext);\n    if (context === undefined) {\n        throw new Error(\"useCurrency must be used within a CurrencyProvider\");\n    }\n    return context;\n}\n_s1(useCurrency, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nvar _c;\n$RefreshReg$(_c, \"CurrencyProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./contexts/CurrencyContext.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./lib/currencyService.ts":
/*!********************************!*\
  !*** ./lib/currencyService.ts ***!
  \********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   currencyService: function() { return /* binding */ currencyService; }\n/* harmony export */ });\n// Frontend Currency Service\nclass CurrencyService {\n    // Get current exchange rates\n    async getExchangeRates() {\n        try {\n            // Check if cache is still valid\n            if (this.lastUpdated && Date.now() - this.lastUpdated < this.CACHE_DURATION) {\n                return this.exchangeRates;\n            }\n            // Try to fetch fresh rates from backend\n            try {\n                const response = await fetch(\"http://localhost:5000/api/currency/rates\");\n                if (response.ok) {\n                    const data = await response.json();\n                    if (data.success && data.data && data.data.rates) {\n                        this.exchangeRates = data.data.rates;\n                        this.lastUpdated = Date.now();\n                        console.log(\"✅ Currency rates updated from backend\");\n                    }\n                }\n            } catch (apiError) {\n                console.warn(\"⚠️ Failed to fetch fresh currency rates, using cached rates:\", apiError);\n            }\n            return this.exchangeRates;\n        } catch (error) {\n            console.error(\"❌ Currency service error:\", error);\n            return this.exchangeRates; // Return cached rates as fallback\n        }\n    }\n    // Convert amount from one currency to another\n    async convertCurrency(amount, fromCurrency, toCurrency) {\n        try {\n            if (fromCurrency === toCurrency) {\n                return amount;\n            }\n            const rates = await this.getExchangeRates();\n            // Convert to USD first, then to target currency\n            const usdAmount = amount / rates[fromCurrency];\n            const convertedAmount = usdAmount * rates[toCurrency];\n            return Math.round(convertedAmount * 100) / 100; // Round to 2 decimal places\n        } catch (error) {\n            console.error(\"❌ Currency conversion error:\", error);\n            throw new Error(\"Currency conversion failed\");\n        }\n    }\n    // Convert amount to USD (base currency for comparisons)\n    async convertToUSD(amount, fromCurrency) {\n        return this.convertCurrency(amount, fromCurrency, \"USD\");\n    }\n    // Convert amount from USD to target currency\n    async convertFromUSD(amount, toCurrency) {\n        return this.convertCurrency(amount, \"USD\", toCurrency);\n    }\n    // Get currency symbol\n    getCurrencySymbol(currency) {\n        const symbols = {\n            USD: \"$\",\n            SAR: \"ر.س\",\n            EUR: \"€\",\n            GBP: \"\\xa3\",\n            AED: \"د.إ\",\n            KWD: \"د.ك\",\n            QAR: \"ر.ق\",\n            BHD: \"د.ب\",\n            OMR: \"ر.ع\",\n            JOD: \"د.أ\"\n        };\n        return symbols[currency] || currency;\n    }\n    // Format amount with currency\n    formatAmount(amount, currency) {\n        // Handle null/undefined amounts\n        if (amount === null || amount === undefined || isNaN(amount)) {\n            amount = 0;\n        }\n        const symbol = this.getCurrencySymbol(currency);\n        const formattedAmount = amount.toLocaleString();\n        // For Arabic currencies, put symbol after number\n        if ([\n            \"SAR\",\n            \"AED\",\n            \"KWD\",\n            \"QAR\",\n            \"BHD\",\n            \"OMR\",\n            \"JOD\"\n        ].includes(currency)) {\n            return \"\".concat(formattedAmount, \" \").concat(symbol);\n        }\n        // For Western currencies, put symbol before number\n        return \"\".concat(symbol).concat(formattedAmount);\n    }\n    // Get supported currencies\n    getSupportedCurrencies() {\n        return [\n            {\n                code: \"USD\",\n                name: \"US Dollar\",\n                symbol: \"$\"\n            },\n            {\n                code: \"SAR\",\n                name: \"Saudi Riyal\",\n                symbol: \"ر.س\"\n            },\n            {\n                code: \"EUR\",\n                name: \"Euro\",\n                symbol: \"€\"\n            },\n            {\n                code: \"GBP\",\n                name: \"British Pound\",\n                symbol: \"\\xa3\"\n            },\n            {\n                code: \"AED\",\n                name: \"UAE Dirham\",\n                symbol: \"د.إ\"\n            },\n            {\n                code: \"KWD\",\n                name: \"Kuwaiti Dinar\",\n                symbol: \"د.ك\"\n            },\n            {\n                code: \"QAR\",\n                name: \"Qatari Riyal\",\n                symbol: \"ر.ق\"\n            },\n            {\n                code: \"BHD\",\n                name: \"Bahraini Dinar\",\n                symbol: \"د.ب\"\n            },\n            {\n                code: \"OMR\",\n                name: \"Omani Rial\",\n                symbol: \"ر.ع\"\n            },\n            {\n                code: \"JOD\",\n                name: \"Jordanian Dinar\",\n                symbol: \"د.أ\"\n            }\n        ];\n    }\n    // Validate currency code\n    isValidCurrency(currency) {\n        const supportedCurrencies = [\n            \"USD\",\n            \"SAR\",\n            \"EUR\",\n            \"GBP\",\n            \"AED\",\n            \"KWD\",\n            \"QAR\",\n            \"BHD\",\n            \"OMR\",\n            \"JOD\"\n        ];\n        return supportedCurrencies.includes(currency);\n    }\n    // Get user's preferred currency from localStorage or default\n    getUserCurrency() {\n        try {\n            return localStorage.getItem(\"preferredCurrency\") || \"SAR\";\n        } catch (e) {\n            return \"SAR\";\n        }\n    }\n    // Set user's preferred currency\n    setUserCurrency(currency) {\n        try {\n            if (this.isValidCurrency(currency)) {\n                localStorage.setItem(\"preferredCurrency\", currency);\n            }\n        } catch (error) {\n            console.error(\"Failed to save currency preference:\", error);\n        }\n    }\n    // Convert auction data to user's preferred currency\n    async convertAuctionData(auction, userCurrency) {\n        const targetCurrency = userCurrency || this.getUserCurrency();\n        if (!auction.currency || auction.currency === targetCurrency) {\n            return auction;\n        }\n        try {\n            const convertedAuction = {\n                ...auction\n            };\n            // Convert prices\n            if (auction.currentBid) {\n                convertedAuction.currentBid = await this.convertCurrency(auction.currentBid, auction.currency, targetCurrency);\n            }\n            if (auction.startingPrice) {\n                convertedAuction.startingPrice = await this.convertCurrency(auction.startingPrice, auction.currency, targetCurrency);\n            }\n            if (auction.reservePrice) {\n                convertedAuction.reservePrice = await this.convertCurrency(auction.reservePrice, auction.currency, targetCurrency);\n            }\n            // Update currency\n            convertedAuction.currency = targetCurrency;\n            convertedAuction.originalCurrency = auction.currency;\n            return convertedAuction;\n        } catch (error) {\n            console.error(\"Failed to convert auction data:\", error);\n            return auction; // Return original data if conversion fails\n        }\n    }\n    constructor(){\n        this.exchangeRates = {\n            USD: 1.0,\n            SAR: 3.75,\n            EUR: 0.85,\n            GBP: 0.73,\n            AED: 3.67,\n            KWD: 0.30,\n            QAR: 3.64,\n            BHD: 0.38,\n            OMR: 0.38\n        };\n        this.lastUpdated = null;\n        this.CACHE_DURATION = 60 * 60 * 1000 // 1 hour\n        ;\n    }\n}\nconst currencyService = new CurrencyService();\n/* harmony default export */ __webpack_exports__[\"default\"] = (currencyService);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2xpYi9jdXJyZW5jeVNlcnZpY2UudHMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLDRCQUE0QjtBQUM1QixNQUFNQTtJQWdCSiw2QkFBNkI7SUFDN0IsTUFBTUMsbUJBQXVEO1FBQzNELElBQUk7WUFDRixnQ0FBZ0M7WUFDaEMsSUFBSSxJQUFJLENBQUNDLFdBQVcsSUFBSSxLQUFNRSxHQUFHLEtBQUssSUFBSSxDQUFDRixXQUFXLEdBQUksSUFBSSxDQUFDRyxjQUFjLEVBQUU7Z0JBQzdFLE9BQU8sSUFBSSxDQUFDQyxhQUFhO1lBQzNCO1lBRUEsd0NBQXdDO1lBQ3hDLElBQUk7Z0JBQ0YsTUFBTUMsV0FBVyxNQUFNQyxNQUFNO2dCQUM3QixJQUFJRCxTQUFTRSxFQUFFLEVBQUU7b0JBQ2YsTUFBTUMsT0FBTyxNQUFNSCxTQUFTSSxJQUFJO29CQUNoQyxJQUFJRCxLQUFLRSxPQUFPLElBQUlGLEtBQUtBLElBQUksSUFBSUEsS0FBS0EsSUFBSSxDQUFDRyxLQUFLLEVBQUU7d0JBQ2hELElBQUksQ0FBQ1AsYUFBYSxHQUFHSSxLQUFLQSxJQUFJLENBQUNHLEtBQUs7d0JBQ3BDLElBQUksQ0FBQ1gsV0FBVyxHQUFHQyxLQUFLQyxHQUFHO3dCQUMzQlUsUUFBUUMsR0FBRyxDQUFDO29CQUNkO2dCQUNGO1lBQ0YsRUFBRSxPQUFPQyxVQUFVO2dCQUNqQkYsUUFBUUcsSUFBSSxDQUFDLGdFQUFnRUQ7WUFDL0U7WUFFQSxPQUFPLElBQUksQ0FBQ1YsYUFBYTtRQUMzQixFQUFFLE9BQU9ZLE9BQU87WUFDZEosUUFBUUksS0FBSyxDQUFDLDZCQUE2QkE7WUFDM0MsT0FBTyxJQUFJLENBQUNaLGFBQWEsRUFBRSxrQ0FBa0M7UUFDL0Q7SUFDRjtJQUVBLDhDQUE4QztJQUM5QyxNQUFNYSxnQkFBZ0JDLE1BQWMsRUFBRUMsWUFBb0IsRUFBRUMsVUFBa0IsRUFBbUI7UUFDL0YsSUFBSTtZQUNGLElBQUlELGlCQUFpQkMsWUFBWTtnQkFDL0IsT0FBT0Y7WUFDVDtZQUVBLE1BQU1QLFFBQVEsTUFBTSxJQUFJLENBQUNaLGdCQUFnQjtZQUV6QyxnREFBZ0Q7WUFDaEQsTUFBTXNCLFlBQVlILFNBQVNQLEtBQUssQ0FBQ1EsYUFBYTtZQUM5QyxNQUFNRyxrQkFBa0JELFlBQVlWLEtBQUssQ0FBQ1MsV0FBVztZQUVyRCxPQUFPRyxLQUFLQyxLQUFLLENBQUNGLGtCQUFrQixPQUFPLEtBQUssNEJBQTRCO1FBQzlFLEVBQUUsT0FBT04sT0FBTztZQUNkSixRQUFRSSxLQUFLLENBQUMsZ0NBQWdDQTtZQUM5QyxNQUFNLElBQUlTLE1BQU07UUFDbEI7SUFDRjtJQUVBLHdEQUF3RDtJQUN4RCxNQUFNQyxhQUFhUixNQUFjLEVBQUVDLFlBQW9CLEVBQW1CO1FBQ3hFLE9BQU8sSUFBSSxDQUFDRixlQUFlLENBQUNDLFFBQVFDLGNBQWM7SUFDcEQ7SUFFQSw2Q0FBNkM7SUFDN0MsTUFBTVEsZUFBZVQsTUFBYyxFQUFFRSxVQUFrQixFQUFtQjtRQUN4RSxPQUFPLElBQUksQ0FBQ0gsZUFBZSxDQUFDQyxRQUFRLE9BQU9FO0lBQzdDO0lBRUEsc0JBQXNCO0lBQ3RCUSxrQkFBa0JDLFFBQWdCLEVBQVU7UUFDMUMsTUFBTUMsVUFBcUM7WUFDekNDLEtBQUs7WUFDTEMsS0FBSztZQUNMQyxLQUFLO1lBQ0xDLEtBQUs7WUFDTEMsS0FBSztZQUNMQyxLQUFLO1lBQ0xDLEtBQUs7WUFDTEMsS0FBSztZQUNMQyxLQUFLO1lBQ0xDLEtBQUs7UUFDUDtRQUNBLE9BQU9WLE9BQU8sQ0FBQ0QsU0FBUyxJQUFJQTtJQUM5QjtJQUVBLDhCQUE4QjtJQUM5QlksYUFBYXZCLE1BQWMsRUFBRVcsUUFBZ0IsRUFBVTtRQUNyRCxnQ0FBZ0M7UUFDaEMsSUFBSVgsV0FBVyxRQUFRQSxXQUFXd0IsYUFBYUMsTUFBTXpCLFNBQVM7WUFDNURBLFNBQVM7UUFDWDtRQUVBLE1BQU0wQixTQUFTLElBQUksQ0FBQ2hCLGlCQUFpQixDQUFDQztRQUN0QyxNQUFNZ0Isa0JBQWtCM0IsT0FBTzRCLGNBQWM7UUFFN0MsaURBQWlEO1FBQ2pELElBQUk7WUFBQztZQUFPO1lBQU87WUFBTztZQUFPO1lBQU87WUFBTztTQUFNLENBQUNDLFFBQVEsQ0FBQ2xCLFdBQVc7WUFDeEUsT0FBTyxHQUFzQmUsT0FBbkJDLGlCQUFnQixLQUFVLE9BQVBEO1FBQy9CO1FBRUEsbURBQW1EO1FBQ25ELE9BQU8sR0FBWUMsT0FBVEQsUUFBeUIsT0FBaEJDO0lBQ3JCO0lBRUEsMkJBQTJCO0lBQzNCRyx5QkFBeUI7UUFDdkIsT0FBTztZQUNMO2dCQUFFQyxNQUFNO2dCQUFPQyxNQUFNO2dCQUFhTixRQUFRO1lBQUk7WUFDOUM7Z0JBQUVLLE1BQU07Z0JBQU9DLE1BQU07Z0JBQWVOLFFBQVE7WUFBTTtZQUNsRDtnQkFBRUssTUFBTTtnQkFBT0MsTUFBTTtnQkFBUU4sUUFBUTtZQUFJO1lBQ3pDO2dCQUFFSyxNQUFNO2dCQUFPQyxNQUFNO2dCQUFpQk4sUUFBUTtZQUFJO1lBQ2xEO2dCQUFFSyxNQUFNO2dCQUFPQyxNQUFNO2dCQUFjTixRQUFRO1lBQU07WUFDakQ7Z0JBQUVLLE1BQU07Z0JBQU9DLE1BQU07Z0JBQWlCTixRQUFRO1lBQU07WUFDcEQ7Z0JBQUVLLE1BQU07Z0JBQU9DLE1BQU07Z0JBQWdCTixRQUFRO1lBQU07WUFDbkQ7Z0JBQUVLLE1BQU07Z0JBQU9DLE1BQU07Z0JBQWtCTixRQUFRO1lBQU07WUFDckQ7Z0JBQUVLLE1BQU07Z0JBQU9DLE1BQU07Z0JBQWNOLFFBQVE7WUFBTTtZQUNqRDtnQkFBRUssTUFBTTtnQkFBT0MsTUFBTTtnQkFBbUJOLFFBQVE7WUFBTTtTQUN2RDtJQUNIO0lBRUEseUJBQXlCO0lBQ3pCTyxnQkFBZ0J0QixRQUFnQixFQUFXO1FBQ3pDLE1BQU11QixzQkFBc0I7WUFBQztZQUFPO1lBQU87WUFBTztZQUFPO1lBQU87WUFBTztZQUFPO1lBQU87WUFBTztTQUFNO1FBQ2xHLE9BQU9BLG9CQUFvQkwsUUFBUSxDQUFDbEI7SUFDdEM7SUFFQSw2REFBNkQ7SUFDN0R3QixrQkFBMEI7UUFDeEIsSUFBSTtZQUNGLE9BQU9DLGFBQWFDLE9BQU8sQ0FBQyx3QkFBd0I7UUFDdEQsRUFBRSxVQUFNO1lBQ04sT0FBTztRQUNUO0lBQ0Y7SUFFQSxnQ0FBZ0M7SUFDaENDLGdCQUFnQjNCLFFBQWdCLEVBQVE7UUFDdEMsSUFBSTtZQUNGLElBQUksSUFBSSxDQUFDc0IsZUFBZSxDQUFDdEIsV0FBVztnQkFDbEN5QixhQUFhRyxPQUFPLENBQUMscUJBQXFCNUI7WUFDNUM7UUFDRixFQUFFLE9BQU9iLE9BQU87WUFDZEosUUFBUUksS0FBSyxDQUFDLHVDQUF1Q0E7UUFDdkQ7SUFDRjtJQUVBLG9EQUFvRDtJQUNwRCxNQUFNMEMsbUJBQW1CQyxPQUFZLEVBQUVDLFlBQXFCLEVBQWdCO1FBQzFFLE1BQU1DLGlCQUFpQkQsZ0JBQWdCLElBQUksQ0FBQ1AsZUFBZTtRQUUzRCxJQUFJLENBQUNNLFFBQVE5QixRQUFRLElBQUk4QixRQUFROUIsUUFBUSxLQUFLZ0MsZ0JBQWdCO1lBQzVELE9BQU9GO1FBQ1Q7UUFFQSxJQUFJO1lBQ0YsTUFBTUcsbUJBQW1CO2dCQUFFLEdBQUdILE9BQU87WUFBQztZQUV0QyxpQkFBaUI7WUFDakIsSUFBSUEsUUFBUUksVUFBVSxFQUFFO2dCQUN0QkQsaUJBQWlCQyxVQUFVLEdBQUcsTUFBTSxJQUFJLENBQUM5QyxlQUFlLENBQ3REMEMsUUFBUUksVUFBVSxFQUNsQkosUUFBUTlCLFFBQVEsRUFDaEJnQztZQUVKO1lBRUEsSUFBSUYsUUFBUUssYUFBYSxFQUFFO2dCQUN6QkYsaUJBQWlCRSxhQUFhLEdBQUcsTUFBTSxJQUFJLENBQUMvQyxlQUFlLENBQ3pEMEMsUUFBUUssYUFBYSxFQUNyQkwsUUFBUTlCLFFBQVEsRUFDaEJnQztZQUVKO1lBRUEsSUFBSUYsUUFBUU0sWUFBWSxFQUFFO2dCQUN4QkgsaUJBQWlCRyxZQUFZLEdBQUcsTUFBTSxJQUFJLENBQUNoRCxlQUFlLENBQ3hEMEMsUUFBUU0sWUFBWSxFQUNwQk4sUUFBUTlCLFFBQVEsRUFDaEJnQztZQUVKO1lBRUEsa0JBQWtCO1lBQ2xCQyxpQkFBaUJqQyxRQUFRLEdBQUdnQztZQUM1QkMsaUJBQWlCSSxnQkFBZ0IsR0FBR1AsUUFBUTlCLFFBQVE7WUFFcEQsT0FBT2lDO1FBQ1QsRUFBRSxPQUFPOUMsT0FBTztZQUNkSixRQUFRSSxLQUFLLENBQUMsbUNBQW1DQTtZQUNqRCxPQUFPMkMsU0FBUywyQ0FBMkM7UUFDN0Q7SUFDRjs7YUF0TVF2RCxnQkFBMkM7WUFDakQyQixLQUFLO1lBQ0xDLEtBQUs7WUFDTEMsS0FBSztZQUNMQyxLQUFLO1lBQ0xDLEtBQUs7WUFDTEMsS0FBSztZQUNMQyxLQUFLO1lBQ0xDLEtBQUs7WUFDTEMsS0FBSztRQUNQO2FBRVF2QyxjQUE2QjthQUNwQkcsaUJBQWlCLEtBQUssS0FBSyxLQUFNLFNBQVM7OztBQTBMN0Q7QUFFTyxNQUFNZ0Usa0JBQWtCLElBQUlyRSxrQkFBa0I7QUFDckQsK0RBQWVxRSxlQUFlQSxFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL2xpYi9jdXJyZW5jeVNlcnZpY2UudHM/YWU3MSJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBGcm9udGVuZCBDdXJyZW5jeSBTZXJ2aWNlXG5jbGFzcyBDdXJyZW5jeVNlcnZpY2Uge1xuICBwcml2YXRlIGV4Y2hhbmdlUmF0ZXM6IHsgW2tleTogc3RyaW5nXTogbnVtYmVyIH0gPSB7XG4gICAgVVNEOiAxLjAsXG4gICAgU0FSOiAzLjc1LFxuICAgIEVVUjogMC44NSxcbiAgICBHQlA6IDAuNzMsXG4gICAgQUVEOiAzLjY3LFxuICAgIEtXRDogMC4zMCxcbiAgICBRQVI6IDMuNjQsXG4gICAgQkhEOiAwLjM4LFxuICAgIE9NUjogMC4zOFxuICB9O1xuXG4gIHByaXZhdGUgbGFzdFVwZGF0ZWQ6IG51bWJlciB8IG51bGwgPSBudWxsO1xuICBwcml2YXRlIHJlYWRvbmx5IENBQ0hFX0RVUkFUSU9OID0gNjAgKiA2MCAqIDEwMDA7IC8vIDEgaG91clxuXG4gIC8vIEdldCBjdXJyZW50IGV4Y2hhbmdlIHJhdGVzXG4gIGFzeW5jIGdldEV4Y2hhbmdlUmF0ZXMoKTogUHJvbWlzZTx7IFtrZXk6IHN0cmluZ106IG51bWJlciB9PiB7XG4gICAgdHJ5IHtcbiAgICAgIC8vIENoZWNrIGlmIGNhY2hlIGlzIHN0aWxsIHZhbGlkXG4gICAgICBpZiAodGhpcy5sYXN0VXBkYXRlZCAmJiAoRGF0ZS5ub3coKSAtIHRoaXMubGFzdFVwZGF0ZWQpIDwgdGhpcy5DQUNIRV9EVVJBVElPTikge1xuICAgICAgICByZXR1cm4gdGhpcy5leGNoYW5nZVJhdGVzO1xuICAgICAgfVxuXG4gICAgICAvLyBUcnkgdG8gZmV0Y2ggZnJlc2ggcmF0ZXMgZnJvbSBiYWNrZW5kXG4gICAgICB0cnkge1xuICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKCdodHRwOi8vbG9jYWxob3N0OjUwMDAvYXBpL2N1cnJlbmN5L3JhdGVzJyk7XG4gICAgICAgIGlmIChyZXNwb25zZS5vaykge1xuICAgICAgICAgIGNvbnN0IGRhdGEgPSBhd2FpdCByZXNwb25zZS5qc29uKCk7XG4gICAgICAgICAgaWYgKGRhdGEuc3VjY2VzcyAmJiBkYXRhLmRhdGEgJiYgZGF0YS5kYXRhLnJhdGVzKSB7XG4gICAgICAgICAgICB0aGlzLmV4Y2hhbmdlUmF0ZXMgPSBkYXRhLmRhdGEucmF0ZXM7XG4gICAgICAgICAgICB0aGlzLmxhc3RVcGRhdGVkID0gRGF0ZS5ub3coKTtcbiAgICAgICAgICAgIGNvbnNvbGUubG9nKCfinIUgQ3VycmVuY3kgcmF0ZXMgdXBkYXRlZCBmcm9tIGJhY2tlbmQnKTtcbiAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgIH0gY2F0Y2ggKGFwaUVycm9yKSB7XG4gICAgICAgIGNvbnNvbGUud2Fybign4pqg77iPIEZhaWxlZCB0byBmZXRjaCBmcmVzaCBjdXJyZW5jeSByYXRlcywgdXNpbmcgY2FjaGVkIHJhdGVzOicsIGFwaUVycm9yKTtcbiAgICAgIH1cblxuICAgICAgcmV0dXJuIHRoaXMuZXhjaGFuZ2VSYXRlcztcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcign4p2MIEN1cnJlbmN5IHNlcnZpY2UgZXJyb3I6JywgZXJyb3IpO1xuICAgICAgcmV0dXJuIHRoaXMuZXhjaGFuZ2VSYXRlczsgLy8gUmV0dXJuIGNhY2hlZCByYXRlcyBhcyBmYWxsYmFja1xuICAgIH1cbiAgfVxuXG4gIC8vIENvbnZlcnQgYW1vdW50IGZyb20gb25lIGN1cnJlbmN5IHRvIGFub3RoZXJcbiAgYXN5bmMgY29udmVydEN1cnJlbmN5KGFtb3VudDogbnVtYmVyLCBmcm9tQ3VycmVuY3k6IHN0cmluZywgdG9DdXJyZW5jeTogc3RyaW5nKTogUHJvbWlzZTxudW1iZXI+IHtcbiAgICB0cnkge1xuICAgICAgaWYgKGZyb21DdXJyZW5jeSA9PT0gdG9DdXJyZW5jeSkge1xuICAgICAgICByZXR1cm4gYW1vdW50O1xuICAgICAgfVxuXG4gICAgICBjb25zdCByYXRlcyA9IGF3YWl0IHRoaXMuZ2V0RXhjaGFuZ2VSYXRlcygpO1xuICAgICAgXG4gICAgICAvLyBDb252ZXJ0IHRvIFVTRCBmaXJzdCwgdGhlbiB0byB0YXJnZXQgY3VycmVuY3lcbiAgICAgIGNvbnN0IHVzZEFtb3VudCA9IGFtb3VudCAvIHJhdGVzW2Zyb21DdXJyZW5jeV07XG4gICAgICBjb25zdCBjb252ZXJ0ZWRBbW91bnQgPSB1c2RBbW91bnQgKiByYXRlc1t0b0N1cnJlbmN5XTtcbiAgICAgIFxuICAgICAgcmV0dXJuIE1hdGgucm91bmQoY29udmVydGVkQW1vdW50ICogMTAwKSAvIDEwMDsgLy8gUm91bmQgdG8gMiBkZWNpbWFsIHBsYWNlc1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCfinYwgQ3VycmVuY3kgY29udmVyc2lvbiBlcnJvcjonLCBlcnJvcik7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoJ0N1cnJlbmN5IGNvbnZlcnNpb24gZmFpbGVkJyk7XG4gICAgfVxuICB9XG5cbiAgLy8gQ29udmVydCBhbW91bnQgdG8gVVNEIChiYXNlIGN1cnJlbmN5IGZvciBjb21wYXJpc29ucylcbiAgYXN5bmMgY29udmVydFRvVVNEKGFtb3VudDogbnVtYmVyLCBmcm9tQ3VycmVuY3k6IHN0cmluZyk6IFByb21pc2U8bnVtYmVyPiB7XG4gICAgcmV0dXJuIHRoaXMuY29udmVydEN1cnJlbmN5KGFtb3VudCwgZnJvbUN1cnJlbmN5LCAnVVNEJyk7XG4gIH1cblxuICAvLyBDb252ZXJ0IGFtb3VudCBmcm9tIFVTRCB0byB0YXJnZXQgY3VycmVuY3lcbiAgYXN5bmMgY29udmVydEZyb21VU0QoYW1vdW50OiBudW1iZXIsIHRvQ3VycmVuY3k6IHN0cmluZyk6IFByb21pc2U8bnVtYmVyPiB7XG4gICAgcmV0dXJuIHRoaXMuY29udmVydEN1cnJlbmN5KGFtb3VudCwgJ1VTRCcsIHRvQ3VycmVuY3kpO1xuICB9XG5cbiAgLy8gR2V0IGN1cnJlbmN5IHN5bWJvbFxuICBnZXRDdXJyZW5jeVN5bWJvbChjdXJyZW5jeTogc3RyaW5nKTogc3RyaW5nIHtcbiAgICBjb25zdCBzeW1ib2xzOiB7IFtrZXk6IHN0cmluZ106IHN0cmluZyB9ID0ge1xuICAgICAgVVNEOiAnJCcsXG4gICAgICBTQVI6ICfYsS7YsycsXG4gICAgICBFVVI6ICfigqwnLFxuICAgICAgR0JQOiAnwqMnLFxuICAgICAgQUVEOiAn2K8u2KUnLFxuICAgICAgS1dEOiAn2K8u2YMnLFxuICAgICAgUUFSOiAn2LEu2YInLFxuICAgICAgQkhEOiAn2K8u2KgnLFxuICAgICAgT01SOiAn2LEu2LknLFxuICAgICAgSk9EOiAn2K8u2KMnXG4gICAgfTtcbiAgICByZXR1cm4gc3ltYm9sc1tjdXJyZW5jeV0gfHwgY3VycmVuY3k7XG4gIH1cblxuICAvLyBGb3JtYXQgYW1vdW50IHdpdGggY3VycmVuY3lcbiAgZm9ybWF0QW1vdW50KGFtb3VudDogbnVtYmVyLCBjdXJyZW5jeTogc3RyaW5nKTogc3RyaW5nIHtcbiAgICAvLyBIYW5kbGUgbnVsbC91bmRlZmluZWQgYW1vdW50c1xuICAgIGlmIChhbW91bnQgPT09IG51bGwgfHwgYW1vdW50ID09PSB1bmRlZmluZWQgfHwgaXNOYU4oYW1vdW50KSkge1xuICAgICAgYW1vdW50ID0gMDtcbiAgICB9XG5cbiAgICBjb25zdCBzeW1ib2wgPSB0aGlzLmdldEN1cnJlbmN5U3ltYm9sKGN1cnJlbmN5KTtcbiAgICBjb25zdCBmb3JtYXR0ZWRBbW91bnQgPSBhbW91bnQudG9Mb2NhbGVTdHJpbmcoKTtcbiAgICBcbiAgICAvLyBGb3IgQXJhYmljIGN1cnJlbmNpZXMsIHB1dCBzeW1ib2wgYWZ0ZXIgbnVtYmVyXG4gICAgaWYgKFsnU0FSJywgJ0FFRCcsICdLV0QnLCAnUUFSJywgJ0JIRCcsICdPTVInLCAnSk9EJ10uaW5jbHVkZXMoY3VycmVuY3kpKSB7XG4gICAgICByZXR1cm4gYCR7Zm9ybWF0dGVkQW1vdW50fSAke3N5bWJvbH1gO1xuICAgIH1cbiAgICBcbiAgICAvLyBGb3IgV2VzdGVybiBjdXJyZW5jaWVzLCBwdXQgc3ltYm9sIGJlZm9yZSBudW1iZXJcbiAgICByZXR1cm4gYCR7c3ltYm9sfSR7Zm9ybWF0dGVkQW1vdW50fWA7XG4gIH1cblxuICAvLyBHZXQgc3VwcG9ydGVkIGN1cnJlbmNpZXNcbiAgZ2V0U3VwcG9ydGVkQ3VycmVuY2llcygpIHtcbiAgICByZXR1cm4gW1xuICAgICAgeyBjb2RlOiAnVVNEJywgbmFtZTogJ1VTIERvbGxhcicsIHN5bWJvbDogJyQnIH0sXG4gICAgICB7IGNvZGU6ICdTQVInLCBuYW1lOiAnU2F1ZGkgUml5YWwnLCBzeW1ib2w6ICfYsS7YsycgfSxcbiAgICAgIHsgY29kZTogJ0VVUicsIG5hbWU6ICdFdXJvJywgc3ltYm9sOiAn4oKsJyB9LFxuICAgICAgeyBjb2RlOiAnR0JQJywgbmFtZTogJ0JyaXRpc2ggUG91bmQnLCBzeW1ib2w6ICfCoycgfSxcbiAgICAgIHsgY29kZTogJ0FFRCcsIG5hbWU6ICdVQUUgRGlyaGFtJywgc3ltYm9sOiAn2K8u2KUnIH0sXG4gICAgICB7IGNvZGU6ICdLV0QnLCBuYW1lOiAnS3V3YWl0aSBEaW5hcicsIHN5bWJvbDogJ9ivLtmDJyB9LFxuICAgICAgeyBjb2RlOiAnUUFSJywgbmFtZTogJ1FhdGFyaSBSaXlhbCcsIHN5bWJvbDogJ9ixLtmCJyB9LFxuICAgICAgeyBjb2RlOiAnQkhEJywgbmFtZTogJ0JhaHJhaW5pIERpbmFyJywgc3ltYm9sOiAn2K8u2KgnIH0sXG4gICAgICB7IGNvZGU6ICdPTVInLCBuYW1lOiAnT21hbmkgUmlhbCcsIHN5bWJvbDogJ9ixLti5JyB9LFxuICAgICAgeyBjb2RlOiAnSk9EJywgbmFtZTogJ0pvcmRhbmlhbiBEaW5hcicsIHN5bWJvbDogJ9ivLtijJyB9XG4gICAgXTtcbiAgfVxuXG4gIC8vIFZhbGlkYXRlIGN1cnJlbmN5IGNvZGVcbiAgaXNWYWxpZEN1cnJlbmN5KGN1cnJlbmN5OiBzdHJpbmcpOiBib29sZWFuIHtcbiAgICBjb25zdCBzdXBwb3J0ZWRDdXJyZW5jaWVzID0gWydVU0QnLCAnU0FSJywgJ0VVUicsICdHQlAnLCAnQUVEJywgJ0tXRCcsICdRQVInLCAnQkhEJywgJ09NUicsICdKT0QnXTtcbiAgICByZXR1cm4gc3VwcG9ydGVkQ3VycmVuY2llcy5pbmNsdWRlcyhjdXJyZW5jeSk7XG4gIH1cblxuICAvLyBHZXQgdXNlcidzIHByZWZlcnJlZCBjdXJyZW5jeSBmcm9tIGxvY2FsU3RvcmFnZSBvciBkZWZhdWx0XG4gIGdldFVzZXJDdXJyZW5jeSgpOiBzdHJpbmcge1xuICAgIHRyeSB7XG4gICAgICByZXR1cm4gbG9jYWxTdG9yYWdlLmdldEl0ZW0oJ3ByZWZlcnJlZEN1cnJlbmN5JykgfHwgJ1NBUic7XG4gICAgfSBjYXRjaCB7XG4gICAgICByZXR1cm4gJ1NBUic7XG4gICAgfVxuICB9XG5cbiAgLy8gU2V0IHVzZXIncyBwcmVmZXJyZWQgY3VycmVuY3lcbiAgc2V0VXNlckN1cnJlbmN5KGN1cnJlbmN5OiBzdHJpbmcpOiB2b2lkIHtcbiAgICB0cnkge1xuICAgICAgaWYgKHRoaXMuaXNWYWxpZEN1cnJlbmN5KGN1cnJlbmN5KSkge1xuICAgICAgICBsb2NhbFN0b3JhZ2Uuc2V0SXRlbSgncHJlZmVycmVkQ3VycmVuY3knLCBjdXJyZW5jeSk7XG4gICAgICB9XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0ZhaWxlZCB0byBzYXZlIGN1cnJlbmN5IHByZWZlcmVuY2U6JywgZXJyb3IpO1xuICAgIH1cbiAgfVxuXG4gIC8vIENvbnZlcnQgYXVjdGlvbiBkYXRhIHRvIHVzZXIncyBwcmVmZXJyZWQgY3VycmVuY3lcbiAgYXN5bmMgY29udmVydEF1Y3Rpb25EYXRhKGF1Y3Rpb246IGFueSwgdXNlckN1cnJlbmN5Pzogc3RyaW5nKTogUHJvbWlzZTxhbnk+IHtcbiAgICBjb25zdCB0YXJnZXRDdXJyZW5jeSA9IHVzZXJDdXJyZW5jeSB8fCB0aGlzLmdldFVzZXJDdXJyZW5jeSgpO1xuICAgIFxuICAgIGlmICghYXVjdGlvbi5jdXJyZW5jeSB8fCBhdWN0aW9uLmN1cnJlbmN5ID09PSB0YXJnZXRDdXJyZW5jeSkge1xuICAgICAgcmV0dXJuIGF1Y3Rpb247XG4gICAgfVxuXG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IGNvbnZlcnRlZEF1Y3Rpb24gPSB7IC4uLmF1Y3Rpb24gfTtcbiAgICAgIFxuICAgICAgLy8gQ29udmVydCBwcmljZXNcbiAgICAgIGlmIChhdWN0aW9uLmN1cnJlbnRCaWQpIHtcbiAgICAgICAgY29udmVydGVkQXVjdGlvbi5jdXJyZW50QmlkID0gYXdhaXQgdGhpcy5jb252ZXJ0Q3VycmVuY3koXG4gICAgICAgICAgYXVjdGlvbi5jdXJyZW50QmlkLCBcbiAgICAgICAgICBhdWN0aW9uLmN1cnJlbmN5LCBcbiAgICAgICAgICB0YXJnZXRDdXJyZW5jeVxuICAgICAgICApO1xuICAgICAgfVxuICAgICAgXG4gICAgICBpZiAoYXVjdGlvbi5zdGFydGluZ1ByaWNlKSB7XG4gICAgICAgIGNvbnZlcnRlZEF1Y3Rpb24uc3RhcnRpbmdQcmljZSA9IGF3YWl0IHRoaXMuY29udmVydEN1cnJlbmN5KFxuICAgICAgICAgIGF1Y3Rpb24uc3RhcnRpbmdQcmljZSwgXG4gICAgICAgICAgYXVjdGlvbi5jdXJyZW5jeSwgXG4gICAgICAgICAgdGFyZ2V0Q3VycmVuY3lcbiAgICAgICAgKTtcbiAgICAgIH1cbiAgICAgIFxuICAgICAgaWYgKGF1Y3Rpb24ucmVzZXJ2ZVByaWNlKSB7XG4gICAgICAgIGNvbnZlcnRlZEF1Y3Rpb24ucmVzZXJ2ZVByaWNlID0gYXdhaXQgdGhpcy5jb252ZXJ0Q3VycmVuY3koXG4gICAgICAgICAgYXVjdGlvbi5yZXNlcnZlUHJpY2UsIFxuICAgICAgICAgIGF1Y3Rpb24uY3VycmVuY3ksIFxuICAgICAgICAgIHRhcmdldEN1cnJlbmN5XG4gICAgICAgICk7XG4gICAgICB9XG4gICAgICBcbiAgICAgIC8vIFVwZGF0ZSBjdXJyZW5jeVxuICAgICAgY29udmVydGVkQXVjdGlvbi5jdXJyZW5jeSA9IHRhcmdldEN1cnJlbmN5O1xuICAgICAgY29udmVydGVkQXVjdGlvbi5vcmlnaW5hbEN1cnJlbmN5ID0gYXVjdGlvbi5jdXJyZW5jeTtcbiAgICAgIFxuICAgICAgcmV0dXJuIGNvbnZlcnRlZEF1Y3Rpb247XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0ZhaWxlZCB0byBjb252ZXJ0IGF1Y3Rpb24gZGF0YTonLCBlcnJvcik7XG4gICAgICByZXR1cm4gYXVjdGlvbjsgLy8gUmV0dXJuIG9yaWdpbmFsIGRhdGEgaWYgY29udmVyc2lvbiBmYWlsc1xuICAgIH1cbiAgfVxufVxuXG5leHBvcnQgY29uc3QgY3VycmVuY3lTZXJ2aWNlID0gbmV3IEN1cnJlbmN5U2VydmljZSgpO1xuZXhwb3J0IGRlZmF1bHQgY3VycmVuY3lTZXJ2aWNlO1xuIl0sIm5hbWVzIjpbIkN1cnJlbmN5U2VydmljZSIsImdldEV4Y2hhbmdlUmF0ZXMiLCJsYXN0VXBkYXRlZCIsIkRhdGUiLCJub3ciLCJDQUNIRV9EVVJBVElPTiIsImV4Y2hhbmdlUmF0ZXMiLCJyZXNwb25zZSIsImZldGNoIiwib2siLCJkYXRhIiwianNvbiIsInN1Y2Nlc3MiLCJyYXRlcyIsImNvbnNvbGUiLCJsb2ciLCJhcGlFcnJvciIsIndhcm4iLCJlcnJvciIsImNvbnZlcnRDdXJyZW5jeSIsImFtb3VudCIsImZyb21DdXJyZW5jeSIsInRvQ3VycmVuY3kiLCJ1c2RBbW91bnQiLCJjb252ZXJ0ZWRBbW91bnQiLCJNYXRoIiwicm91bmQiLCJFcnJvciIsImNvbnZlcnRUb1VTRCIsImNvbnZlcnRGcm9tVVNEIiwiZ2V0Q3VycmVuY3lTeW1ib2wiLCJjdXJyZW5jeSIsInN5bWJvbHMiLCJVU0QiLCJTQVIiLCJFVVIiLCJHQlAiLCJBRUQiLCJLV0QiLCJRQVIiLCJCSEQiLCJPTVIiLCJKT0QiLCJmb3JtYXRBbW91bnQiLCJ1bmRlZmluZWQiLCJpc05hTiIsInN5bWJvbCIsImZvcm1hdHRlZEFtb3VudCIsInRvTG9jYWxlU3RyaW5nIiwiaW5jbHVkZXMiLCJnZXRTdXBwb3J0ZWRDdXJyZW5jaWVzIiwiY29kZSIsIm5hbWUiLCJpc1ZhbGlkQ3VycmVuY3kiLCJzdXBwb3J0ZWRDdXJyZW5jaWVzIiwiZ2V0VXNlckN1cnJlbmN5IiwibG9jYWxTdG9yYWdlIiwiZ2V0SXRlbSIsInNldFVzZXJDdXJyZW5jeSIsInNldEl0ZW0iLCJjb252ZXJ0QXVjdGlvbkRhdGEiLCJhdWN0aW9uIiwidXNlckN1cnJlbmN5IiwidGFyZ2V0Q3VycmVuY3kiLCJjb252ZXJ0ZWRBdWN0aW9uIiwiY3VycmVudEJpZCIsInN0YXJ0aW5nUHJpY2UiLCJyZXNlcnZlUHJpY2UiLCJvcmlnaW5hbEN1cnJlbmN5IiwiY3VycmVuY3lTZXJ2aWNlIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/currencyService.ts\n"));

/***/ })

});