{"c": ["app/layout", "app/user/notifications/page", "webpack"], "r": [], "m": ["(app-pages-browser)/./app/user/notifications/page.tsx", "(app-pages-browser)/./node_modules/@socket.io/component-emitter/lib/esm/index.js", "(app-pages-browser)/./node_modules/engine.io-client/build/esm/contrib/has-cors.js", "(app-pages-browser)/./node_modules/engine.io-client/build/esm/contrib/parseqs.js", "(app-pages-browser)/./node_modules/engine.io-client/build/esm/contrib/parseuri.js", "(app-pages-browser)/./node_modules/engine.io-client/build/esm/globals.js", "(app-pages-browser)/./node_modules/engine.io-client/build/esm/index.js", "(app-pages-browser)/./node_modules/engine.io-client/build/esm/socket.js", "(app-pages-browser)/./node_modules/engine.io-client/build/esm/transport.js", "(app-pages-browser)/./node_modules/engine.io-client/build/esm/transports/index.js", "(app-pages-browser)/./node_modules/engine.io-client/build/esm/transports/polling-fetch.js", "(app-pages-browser)/./node_modules/engine.io-client/build/esm/transports/polling-xhr.js", "(app-pages-browser)/./node_modules/engine.io-client/build/esm/transports/polling.js", "(app-pages-browser)/./node_modules/engine.io-client/build/esm/transports/websocket.js", "(app-pages-browser)/./node_modules/engine.io-client/build/esm/transports/webtransport.js", "(app-pages-browser)/./node_modules/engine.io-client/build/esm/util.js", "(app-pages-browser)/./node_modules/engine.io-parser/build/esm/commons.js", "(app-pages-browser)/./node_modules/engine.io-parser/build/esm/contrib/base64-arraybuffer.js", "(app-pages-browser)/./node_modules/engine.io-parser/build/esm/decodePacket.browser.js", "(app-pages-browser)/./node_modules/engine.io-parser/build/esm/encodePacket.browser.js", "(app-pages-browser)/./node_modules/engine.io-parser/build/esm/index.js", "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Ffahad%2FDesktop%2Fbrid1%2Ffrontend%2Fapp%2Fuser%2Fnotifications%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!", "(app-pages-browser)/./node_modules/socket.io-client/build/esm/contrib/backo2.js", "(app-pages-browser)/./node_modules/socket.io-client/build/esm/index.js", "(app-pages-browser)/./node_modules/socket.io-client/build/esm/manager.js", "(app-pages-browser)/./node_modules/socket.io-client/build/esm/on.js", "(app-pages-browser)/./node_modules/socket.io-client/build/esm/socket.js", "(app-pages-browser)/./node_modules/socket.io-client/build/esm/url.js", "(app-pages-browser)/./node_modules/socket.io-parser/build/esm/binary.js", "(app-pages-browser)/./node_modules/socket.io-parser/build/esm/index.js", "(app-pages-browser)/./node_modules/socket.io-parser/build/esm/is-binary.js"]}