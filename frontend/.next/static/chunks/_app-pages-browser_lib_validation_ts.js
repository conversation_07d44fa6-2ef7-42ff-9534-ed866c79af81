"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_lib_validation_ts"],{

/***/ "(app-pages-browser)/./lib/validation.ts":
/*!***************************!*\
  !*** ./lib/validation.ts ***!
  \***************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ValidationError: function() { return /* binding */ ValidationError; },\n/* harmony export */   checkRateLimit: function() { return /* binding */ checkRateLimit; },\n/* harmony export */   preventRapidSubmission: function() { return /* binding */ preventRapidSubmission; },\n/* harmony export */   sanitizeInput: function() { return /* binding */ sanitizeInput; },\n/* harmony export */   validateAmount: function() { return /* binding */ validateAmount; },\n/* harmony export */   validateBidAmount: function() { return /* binding */ validateBidAmount; },\n/* harmony export */   validateCommercialRegister: function() { return /* binding */ validateCommercialRegister; },\n/* harmony export */   validateDateRange: function() { return /* binding */ validateDateRange; },\n/* harmony export */   validateEmail: function() { return /* binding */ validateEmail; },\n/* harmony export */   validateFile: function() { return /* binding */ validateFile; },\n/* harmony export */   validateFormData: function() { return /* binding */ validateFormData; },\n/* harmony export */   validateNationalId: function() { return /* binding */ validateNationalId; },\n/* harmony export */   validatePhone: function() { return /* binding */ validatePhone; },\n/* harmony export */   validateUserPermissions: function() { return /* binding */ validateUserPermissions; }\n/* harmony export */ });\n// Comprehensive validation utilities to prevent user bypass attempts\nclass ValidationError extends Error {\n    constructor(message, field){\n        super(message);\n        this.field = field;\n        this.name = \"ValidationError\";\n    }\n}\n// Sanitize input to prevent XSS and injection attacks\nconst sanitizeInput = (input)=>{\n    if (typeof input !== \"string\") return \"\";\n    return input.trim().replace(/<script\\b[^<]*(?:(?!<\\/script>)<[^<]*)*<\\/script>/gi, \"\").replace(/javascript:/gi, \"\").replace(/on\\w+\\s*=/gi, \"\").replace(/[<>]/g, \"\");\n};\n// Validate email format\nconst validateEmail = (email)=>{\n    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n    return emailRegex.test(email.trim().toLowerCase());\n};\n// Validate phone number (Saudi format)\nconst validatePhone = (phone)=>{\n    const phoneRegex = /^(\\+966|966|0)?[5][0-9]{8}$/;\n    return phoneRegex.test(phone.replace(/\\s/g, \"\"));\n};\n// Validate national ID (Saudi format)\nconst validateNationalId = (id)=>{\n    const idRegex = /^[12][0-9]{9}$/;\n    return idRegex.test(id);\n};\n// Validate commercial register\nconst validateCommercialRegister = (cr)=>{\n    const crRegex = /^[0-9]{10}$/;\n    return crRegex.test(cr);\n};\n// Validate monetary amounts\nconst validateAmount = function(amount) {\n    let min = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 0, max = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : Infinity;\n    const numAmount = typeof amount === \"string\" ? parseFloat(amount) : amount;\n    if (isNaN(numAmount)) return false;\n    if (numAmount < min) return false;\n    if (numAmount > max) return false;\n    if (numAmount !== Math.floor(numAmount * 100) / 100) return false // Max 2 decimal places\n    ;\n    return true;\n};\n// Validate bid amount against current price\nconst validateBidAmount = function(bidAmount, currentPrice) {\n    let minIncrement = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 1;\n    const numBid = typeof bidAmount === \"string\" ? parseFloat(bidAmount) : bidAmount;\n    if (isNaN(numBid)) {\n        return {\n            isValid: false,\n            error: \"مبلغ المزايدة غير صحيح\"\n        };\n    }\n    if (numBid <= currentPrice) {\n        return {\n            isValid: false,\n            error: \"يجب أن تكون المزايدة أعلى من السعر الحالي\"\n        };\n    }\n    if (numBid < currentPrice + minIncrement) {\n        return {\n            isValid: false,\n            error: \"الحد الأدنى للزيادة هو \".concat(minIncrement)\n        };\n    }\n    if (numBid > currentPrice * 10) {\n        return {\n            isValid: false,\n            error: \"مبلغ المزايدة مرتفع جداً\"\n        };\n    }\n    return {\n        isValid: true\n    };\n};\n// Validate date ranges\nconst validateDateRange = (startDate, endDate)=>{\n    const start = new Date(startDate);\n    const end = new Date(endDate);\n    const now = new Date();\n    if (isNaN(start.getTime()) || isNaN(end.getTime())) {\n        return {\n            isValid: false,\n            error: \"تاريخ غير صحيح\"\n        };\n    }\n    if (start >= end) {\n        return {\n            isValid: false,\n            error: \"تاريخ البداية يجب أن يكون قبل تاريخ النهاية\"\n        };\n    }\n    if (end <= now) {\n        return {\n            isValid: false,\n            error: \"تاريخ النهاية يجب أن يكون في المستقبل\"\n        };\n    }\n    return {\n        isValid: true\n    };\n};\n// Validate file uploads\nconst validateFile = (file, allowedTypes, maxSize)=>{\n    if (!file) {\n        return {\n            isValid: false,\n            error: \"لم يتم اختيار ملف\"\n        };\n    }\n    if (!allowedTypes.includes(file.type)) {\n        return {\n            isValid: false,\n            error: \"نوع الملف غير مدعوم\"\n        };\n    }\n    if (file.size > maxSize) {\n        return {\n            isValid: false,\n            error: \"حجم الملف يجب أن يكون أقل من \".concat(maxSize / 1024 / 1024, \"MB\")\n        };\n    }\n    return {\n        isValid: true\n    };\n};\n// Validate user permissions\nconst validateUserPermissions = (userRole, requiredRoles)=>{\n    if (!userRole || !requiredRoles.length) return false;\n    return requiredRoles.includes(userRole);\n};\n// Validate form data comprehensively\nconst validateFormData = (data, rules)=>{\n    const errors = {};\n    for (const [field, rule] of Object.entries(rules)){\n        const value = data[field];\n        // Required field check\n        if (rule.required && (!value || typeof value === \"string\" && !value.trim())) {\n            errors[field] = rule.requiredMessage || \"\".concat(field, \" مطلوب\");\n            continue;\n        }\n        // Skip validation if field is empty and not required\n        if (!value && !rule.required) continue;\n        // Type validation\n        if (rule.type === \"email\" && !validateEmail(value)) {\n            errors[field] = \"البريد الإلكتروني غير صحيح\";\n        }\n        if (rule.type === \"phone\" && !validatePhone(value)) {\n            errors[field] = \"رقم الهاتف غير صحيح\";\n        }\n        if (rule.type === \"number\" && !validateAmount(value, rule.min, rule.max)) {\n            errors[field] = \"الرقم غير صحيح\";\n        }\n        // Length validation\n        if (rule.minLength && value.length < rule.minLength) {\n            errors[field] = \"يجب أن يكون \".concat(rule.minLength, \" أحرف على الأقل\");\n        }\n        if (rule.maxLength && value.length > rule.maxLength) {\n            errors[field] = \"يجب أن يكون \".concat(rule.maxLength, \" أحرف كحد أقصى\");\n        }\n        // Custom validation\n        if (rule.custom && !rule.custom(value)) {\n            errors[field] = rule.customMessage || \"قيمة غير صحيحة\";\n        }\n    }\n    return {\n        isValid: Object.keys(errors).length === 0,\n        errors\n    };\n};\n// Rate limiting helper\nconst checkRateLimit = function(key) {\n    let maxRequests = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 10, windowMs = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 60000;\n    const now = Date.now();\n    const requests = JSON.parse(localStorage.getItem(\"rate_limit_\".concat(key)) || \"[]\");\n    // Remove old requests outside the window\n    const validRequests = requests.filter((timestamp)=>now - timestamp < windowMs);\n    if (validRequests.length >= maxRequests) {\n        return false;\n    }\n    // Add current request\n    validRequests.push(now);\n    localStorage.setItem(\"rate_limit_\".concat(key), JSON.stringify(validRequests));\n    return true;\n};\n// Prevent rapid form submissions\nconst preventRapidSubmission = function(formId) {\n    let cooldownMs = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 2000;\n    const lastSubmission = localStorage.getItem(\"last_submit_\".concat(formId));\n    const now = Date.now();\n    if (lastSubmission && now - parseInt(lastSubmission) < cooldownMs) {\n        return false;\n    }\n    localStorage.setItem(\"last_submit_\".concat(formId), now.toString());\n    return true;\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/validation.ts\n"));

/***/ })

}]);