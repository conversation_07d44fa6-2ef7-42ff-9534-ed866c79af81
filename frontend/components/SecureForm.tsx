'use client'

import { useState, useEffect } from 'react'
import { useToast } from '@/hooks/use-toast'
import { validateFormData, preventRapidSubmission, checkRateLimit } from '@/lib/validation'

interface SecureFormProps {
  children: React.ReactNode
  onSubmit: (data: Record<string, any>) => Promise<void>
  validationRules: Record<string, any>
  formId: string
  className?: string
  preventRapidSubmit?: boolean
  rateLimitKey?: string
  rateLimitMax?: number
  rateLimitWindow?: number
}

export default function SecureForm({
  children,
  onSubmit,
  validationRules,
  formId,
  className = '',
  preventRapidSubmit = true,
  rateLimitKey,
  rateLimitMax = 10,
  rateLimitWindow = 60000
}: SecureFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [errors, setErrors] = useState<Record<string, string>>({})
  const { toast } = useToast()

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    
    if (isSubmitting) return

    // Prevent rapid submissions
    if (preventRapidSubmit && !preventRapidSubmission(formId, 2000)) {
      toast({
        title: 'انتظر قليلاً',
        description: 'يرجى الانتظار قبل إرسال النموذج مرة أخرى',
        variant: 'destructive'
      })
      return
    }

    // Check rate limiting
    if (rateLimitKey && !checkRateLimit(rateLimitKey, rateLimitMax, rateLimitWindow)) {
      toast({
        title: 'تم تجاوز الحد المسموح',
        description: 'لقد تجاوزت الحد المسموح من المحاولات. يرجى الانتظار',
        variant: 'destructive'
      })
      return
    }

    const formData = new FormData(e.currentTarget)
    const data: Record<string, any> = {}
    
    // Extract form data
    for (const [key, value] of formData.entries()) {
      data[key] = value
    }

    // Validate form data
    const validation = validateFormData(data, validationRules)
    
    if (!validation.isValid) {
      setErrors(validation.errors)
      
      // Show first error in toast
      const firstError = Object.values(validation.errors)[0]
      toast({
        title: 'خطأ في البيانات',
        description: firstError,
        variant: 'destructive'
      })
      return
    }

    setErrors({})
    setIsSubmitting(true)

    try {
      await onSubmit(data)
      
      // Clear form on successful submission
      e.currentTarget.reset()
      
      toast({
        title: 'تم بنجاح',
        description: 'تم إرسال النموذج بنجاح',
        variant: 'default'
      })
    } catch (error: any) {
      console.error('Form submission error:', error)
      
      toast({
        title: 'خطأ في الإرسال',
        description: error.message || 'حدث خطأ أثناء إرسال النموذج',
        variant: 'destructive'
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  // Provide error context to child components
  const formContext = {
    errors,
    isSubmitting,
    setErrors
  }

  return (
    <form 
      onSubmit={handleSubmit} 
      className={className}
      noValidate // Disable browser validation to use our custom validation
    >
      <div data-form-context={JSON.stringify(formContext)}>
        {children}
      </div>
    </form>
  )
}

// Hook to access form context in child components
export const useFormContext = () => {
  const [context, setContext] = useState<{
    errors: Record<string, string>
    isSubmitting: boolean
    setErrors: (errors: Record<string, string>) => void
  }>({
    errors: {},
    isSubmitting: false,
    setErrors: () => {}
  })

  useEffect(() => {
    // Find the nearest form context
    const formElement = document.querySelector('[data-form-context]')
    if (formElement) {
      const contextData = formElement.getAttribute('data-form-context')
      if (contextData) {
        try {
          setContext(JSON.parse(contextData))
        } catch (error) {
          console.error('Error parsing form context:', error)
        }
      }
    }
  }, [])

  return context
}

// Secure input component with validation
interface SecureInputProps {
  name: string
  type?: string
  placeholder?: string
  className?: string
  required?: boolean
  disabled?: boolean
  value?: string
  onChange?: (e: React.ChangeEvent<HTMLInputElement>) => void
}

export const SecureInput = ({
  name,
  type = 'text',
  placeholder,
  className = '',
  required = false,
  disabled = false,
  value,
  onChange
}: SecureInputProps) => {
  const { errors, isSubmitting } = useFormContext()
  const hasError = errors[name]

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    // Basic input sanitization
    let sanitizedValue = e.target.value
    
    // Remove potential XSS attempts
    sanitizedValue = sanitizedValue
      .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
      .replace(/javascript:/gi, '')
      .replace(/on\w+\s*=/gi, '')

    // Update the input value
    e.target.value = sanitizedValue

    if (onChange) {
      onChange(e)
    }
  }

  return (
    <div className="space-y-1">
      <input
        name={name}
        type={type}
        placeholder={placeholder}
        className={`${className} ${hasError ? 'border-red-500 focus:border-red-500' : ''}`}
        required={required}
        disabled={disabled || isSubmitting}
        value={value}
        onChange={handleChange}
        autoComplete="off"
        spellCheck="false"
      />
      {hasError && (
        <p className="text-sm text-red-600 mt-1">{hasError}</p>
      )}
    </div>
  )
}

// Secure textarea component
interface SecureTextareaProps {
  name: string
  placeholder?: string
  className?: string
  required?: boolean
  disabled?: boolean
  value?: string
  onChange?: (e: React.ChangeEvent<HTMLTextAreaElement>) => void
  rows?: number
}

export const SecureTextarea = ({
  name,
  placeholder,
  className = '',
  required = false,
  disabled = false,
  value,
  onChange,
  rows = 3
}: SecureTextareaProps) => {
  const { errors, isSubmitting } = useFormContext()
  const hasError = errors[name]

  const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    // Basic input sanitization
    let sanitizedValue = e.target.value
    
    // Remove potential XSS attempts
    sanitizedValue = sanitizedValue
      .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
      .replace(/javascript:/gi, '')
      .replace(/on\w+\s*=/gi, '')

    // Update the textarea value
    e.target.value = sanitizedValue

    if (onChange) {
      onChange(e)
    }
  }

  return (
    <div className="space-y-1">
      <textarea
        name={name}
        placeholder={placeholder}
        className={`${className} ${hasError ? 'border-red-500 focus:border-red-500' : ''}`}
        required={required}
        disabled={disabled || isSubmitting}
        value={value}
        onChange={handleChange}
        rows={rows}
        autoComplete="off"
        spellCheck="false"
      />
      {hasError && (
        <p className="text-sm text-red-600 mt-1">{hasError}</p>
      )}
    </div>
  )
}
