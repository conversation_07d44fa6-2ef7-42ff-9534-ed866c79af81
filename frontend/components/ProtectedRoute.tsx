'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { useToast } from '@/hooks/use-toast'

interface ProtectedRouteProps {
  children: React.ReactNode
  allowedRoles?: string[]
  requireApproved?: boolean
  requireAuth?: boolean
}

export default function ProtectedRoute({ 
  children, 
  allowedRoles = [], 
  requireApproved = true,
  requireAuth = true 
}: ProtectedRouteProps) {
  const [isAuthorized, setIsAuthorized] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const router = useRouter()
  const { toast } = useToast()

  useEffect(() => {
    const checkAuth = async () => {
      try {
        // Check if authentication is required
        if (!requireAuth) {
          setIsAuthorized(true)
          setIsLoading(false)
          return
        }

        const token = localStorage.getItem('token')
        const userData = localStorage.getItem('user')

        // Check if user is authenticated
        if (!token || !userData) {
          toast({
            title: 'غير مصرح',
            description: 'يجب تسجيل الدخول للوصول لهذه الصفحة',
            variant: 'destructive'
          })
          router.push('/auth/login')
          return
        }

        let user
        try {
          user = JSON.parse(userData)
        } catch (error) {
          console.error('Error parsing user data:', error)
          localStorage.removeItem('token')
          localStorage.removeItem('user')
          router.push('/auth/login')
          return
        }

        // Validate token format (basic check)
        if (!token.includes('.') || token.split('.').length !== 3) {
          toast({
            title: 'جلسة غير صالحة',
            description: 'يرجى تسجيل الدخول مرة أخرى',
            variant: 'destructive'
          })
          localStorage.removeItem('token')
          localStorage.removeItem('user')
          router.push('/auth/login')
          return
        }

        // Check if user has required role
        if (allowedRoles.length > 0 && !allowedRoles.includes(user.role)) {
          toast({
            title: 'وصول غير مسموح',
            description: 'ليس لديك صلاحية للوصول لهذه الصفحة',
            variant: 'destructive'
          })
          
          // Redirect to appropriate dashboard
          switch (user.role) {
            case 'admin':
            case 'super_admin':
              router.push('/admin/dashboard')
              break
            case 'company':
              router.push('/company/dashboard')
              break
            case 'individual':
              router.push('/user/dashboard')
              break
            case 'government':
              router.push('/government/dashboard')
              break
            default:
              router.push('/auth/login')
          }
          return
        }

        // Check if account approval is required
        if (requireApproved && user.role !== 'admin' && user.role !== 'super_admin') {
          if (user.status !== 'approved') {
            toast({
              title: 'حساب غير مفعل',
              description: 'يجب تفعيل حسابك للوصول لهذه الصفحة',
              variant: 'destructive'
            })
            router.push('/account-status')
            return
          }
        }

        // Check for suspicious activity (multiple rapid requests)
        const lastActivity = localStorage.getItem('lastActivity')
        const now = Date.now()
        if (lastActivity && (now - parseInt(lastActivity)) < 100) {
          console.warn('Suspicious rapid requests detected')
          // Could implement rate limiting here
        }
        localStorage.setItem('lastActivity', now.toString())

        setIsAuthorized(true)
      } catch (error) {
        console.error('Auth check error:', error)
        toast({
          title: 'خطأ في التحقق',
          description: 'حدث خطأ في التحقق من الصلاحيات',
          variant: 'destructive'
        })
        router.push('/auth/login')
      } finally {
        setIsLoading(false)
      }
    }

    checkAuth()
  }, [allowedRoles, requireApproved, requireAuth, router, toast])

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">جاري التحقق من الصلاحيات...</p>
        </div>
      </div>
    )
  }

  if (!isAuthorized) {
    return null
  }

  return <>{children}</>
}
