'use client'

import React from 'react'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Label } from '@/components/ui/label'
import { useCurrency } from '@/contexts/CurrencyContext'
import { useToast } from '@/hooks/use-toast'

interface CurrencySelectorProps {
  showLabel?: boolean
  className?: string
}

const currencies = [
  { code: 'SAR', symbol: 'ر.س', name: 'SAR' },
  { code: 'EUR', symbol: '€', name: 'EUR' },
  { code: 'GBP', symbol: '£', name: 'GBP' },
  { code: 'AED', symbol: 'د.إ', name: 'AED' },
  { code: 'KWD', symbol: 'د.ك', name: 'K<PERSON>' },
  { code: 'QAR', symbol: 'ر.ق', name: 'QAR' },
  { code: 'BHD', symbol: 'د.ب', name: 'BHD' },
  { code: 'OMR', symbol: 'ر.ع', name: '<PERSON><PERSON>' },
  { code: 'J<PERSON>', symbol: 'د.أ', name: 'J<PERSON>' }
]

// All currencies including USD for reference
const allCurrencies = [
  { code: 'USD', symbol: '$', name: 'USD' },
  ...currencies
]

export function CurrencySelector({ showLabel = true, className = '' }: CurrencySelectorProps) {
  const { userCurrency, setUserCurrency, isLoading } = useCurrency()
  const { toast } = useToast()

  const handleCurrencyChange = async (newCurrency: string) => {
    try {
      await setUserCurrency(newCurrency)

      const selectedCurrency = allCurrencies.find(c => c.code === newCurrency)
      toast({
        title: '💱 تم تغيير العملة',
        description: `تم التحويل إلى ${selectedCurrency?.symbol} (+ USD كعملة مرجعية)`,
        duration: 3000
      })
    } catch (error) {
      console.error('Failed to change currency:', error)
      toast({
        title: '❌ خطأ في تغيير العملة',
        description: 'حدث خطأ أثناء تغيير العملة',
        variant: 'destructive'
      })
    }
  }

  if (isLoading) {
    return (
      <div className={`flex items-center gap-2 ${className}`}>
        {showLabel && <Label>العملة:</Label>}
        <div className="w-32 h-10 bg-gray-200 animate-pulse rounded"></div>
      </div>
    )
  }

  return (
    <div className={`flex items-center gap-2 ${className}`}>
      {showLabel && <Label htmlFor="currency-selector">العملة:</Label>}
      <Select
        value={userCurrency}
        onValueChange={handleCurrencyChange}
        disabled={isLoading}
      >
        <SelectTrigger id="currency-selector" className="w-32">
          <SelectValue />
        </SelectTrigger>
        <SelectContent>
          {currencies.map((currency) => (
            <SelectItem key={currency.code} value={currency.code}>
              {currency.symbol} {currency.name}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  )
}
