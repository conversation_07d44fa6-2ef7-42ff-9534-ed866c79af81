'use client'

import React, { useState, useEffect } from 'react'
import { useCurrency } from '@/contexts/CurrencyContext'

interface CurrencyDisplayProps {
  amount: number
  fromCurrency: string
  className?: string
  showOriginal?: boolean
  showDualCurrency?: boolean
}

export function CurrencyDisplay({
  amount,
  fromCurrency,
  className = '',
  showOriginal = false,
  showDualCurrency = true
}: CurrencyDisplayProps) {
  const { formatAmountWithConversion, userCurrency } = useCurrency()
  const [displayText, setDisplayText] = useState('')
  const [usdDisplayText, setUsdDisplayText] = useState('')
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    const convertAndFormat = async () => {
      try {
        setIsLoading(true)

        // Convert to user's selected currency
        const formattedAmount = await formatAmountWithConversion(amount, fromCurrency, userCurrency)
        setDisplayText(formattedAmount)

        // Convert to USD if user currency is not USD and showDualCurrency is true
        if (showDualCurrency && userCurrency !== 'USD') {
          const usdFormattedAmount = await formatAmountWithConversion(amount, fromCurrency, 'USD')
          setUsdDisplayText(usdFormattedAmount)
        } else {
          setUsdDisplayText('')
        }
      } catch (error) {
        console.error('Currency conversion failed:', error)
        // Fallback to original amount with target currency symbol
        setDisplayText(`${amount.toLocaleString()} ${userCurrency}`)
        setUsdDisplayText('')
      } finally {
        setIsLoading(false)
      }
    }

    convertAndFormat()
  }, [amount, fromCurrency, userCurrency, formatAmountWithConversion, showDualCurrency])

  if (isLoading) {
    return (
      <div className={`animate-pulse ${className}`}>
        <div className="h-4 bg-gray-200 rounded w-20"></div>
      </div>
    )
  }

  return (
    <div className={className}>
      <div className="font-semibold">
        {displayText}
      </div>
      {showDualCurrency && userCurrency !== 'USD' && usdDisplayText && (
        <div className="text-sm text-gray-500 mt-1">
          {usdDisplayText}
        </div>
      )}
      {showOriginal && fromCurrency !== userCurrency && (
        <div className="text-xs text-gray-400 mt-1">
          (من {amount.toLocaleString()} {fromCurrency})
        </div>
      )}
    </div>
  )
}
