'use client'

import React, { useState, useEffect } from 'react'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { useCurrency } from '@/contexts/CurrencyContext'

interface CurrencyInputProps {
  value: number
  onChange: (value: number) => void
  label?: string
  placeholder?: string
  disabled?: boolean
  className?: string
  error?: string
  min?: number
  max?: number
  step?: number
}

export function CurrencyInput({
  value,
  onChange,
  label,
  placeholder,
  disabled = false,
  className = '',
  error,
  min = 0,
  max,
  step = 1
}: CurrencyInputProps) {
  const { userCurrency, getCurrencySymbol } = useCurrency()
  const [displayValue, setDisplayValue] = useState('')
  const [isFocused, setIsFocused] = useState(false)

  // Update display value when value prop changes
  useEffect(() => {
    if (!isFocused) {
      setDisplayValue(value > 0 ? value.toString() : '')
    }
  }, [value, isFocused])

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const inputValue = e.target.value
    setDisplayValue(inputValue)

    // Remove any non-numeric characters except decimal point
    const numericValue = inputValue.replace(/[^0-9.]/g, '')
    
    // Ensure only one decimal point
    const parts = numericValue.split('.')
    const cleanValue = parts.length > 2 
      ? parts[0] + '.' + parts.slice(1).join('')
      : numericValue

    // Convert to number
    const numberValue = parseFloat(cleanValue) || 0

    // Apply min/max constraints
    let finalValue = numberValue
    if (min !== undefined && finalValue < min) {
      finalValue = min
    }
    if (max !== undefined && finalValue > max) {
      finalValue = max
    }

    onChange(finalValue)
  }

  const handleFocus = () => {
    setIsFocused(true)
    // Show raw number when focused
    setDisplayValue(value > 0 ? value.toString() : '')
  }

  const handleBlur = () => {
    setIsFocused(false)
    // Format display value when not focused
    if (value > 0) {
      setDisplayValue(value.toLocaleString())
    }
  }

  const currencySymbol = getCurrencySymbol(userCurrency)

  return (
    <div className={`space-y-2 ${className}`}>
      {label && (
        <Label htmlFor="currency-input" className="text-sm font-medium">
          {label}
        </Label>
      )}
      <div className="relative">
        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
          <span className="text-gray-500 text-sm font-medium">
            {currencySymbol}
          </span>
        </div>
        <Input
          id="currency-input"
          type="text"
          value={displayValue}
          onChange={handleInputChange}
          onFocus={handleFocus}
          onBlur={handleBlur}
          placeholder={placeholder || `0`}
          disabled={disabled}
          className={`pl-12 ${error ? 'border-red-500' : ''}`}
          min={min}
          max={max}
          step={step}
        />
        <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
          <span className="text-gray-400 text-xs">
            {userCurrency}
          </span>
        </div>
      </div>
      {error && (
        <p className="text-sm text-red-600">{error}</p>
      )}
      <p className="text-xs text-gray-500">
        أدخل المبلغ بعملة {getCurrencySymbol(userCurrency)} ({userCurrency})
      </p>
    </div>
  )
}
