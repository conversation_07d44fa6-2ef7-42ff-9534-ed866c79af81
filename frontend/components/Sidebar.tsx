'use client'

import React, { useState, useEffect } from 'react'
import Link from 'next/link'
import { useRouter, usePathname } from 'next/navigation'
import { Button } from '@/components/ui/button'
import {
  LayoutDashboard,
  Users,
  Gavel,
  FileText,
  Building,
  User,
  Shield,
  Crown,
  Settings,
  LogOut,
  ChevronLeft,
  ChevronRight,
  Home,
  PlusCircle,
  Bell,
  TrendingUp,
  Activity,
  Trophy,
  Heart,
  Search
} from 'lucide-react'

interface SidebarProps {
  userRole?: string
}

interface NavItem {
  href: string
  label: string
  icon: React.ReactNode
  roles: string[]
}

const navItems: NavItem[] = [
  // Admin and Super Admin navigation
  {
    href: '/admin/dashboard',
    label: 'لوحة التحكم',
    icon: <LayoutDashboard className="h-5 w-5" />,
    roles: ['admin', 'super_admin']
  },
  {
    href: '/search',
    label: 'البحث والاستكشاف',
    icon: <Search className="h-5 w-5" />,
    roles: ['admin', 'super_admin']
  },
  {
    href: '/admin/pending-accounts',
    label: 'الحسابات المعلقة',
    icon: <Users className="h-5 w-5" />,
    roles: ['admin', 'super_admin']
  },
  {
    href: '/admin/users',
    label: 'إدارة المستخدمين',
    icon: <Shield className="h-5 w-5" />,
    roles: ['admin', 'super_admin']
  },
  {
    href: '/admin/auctions',
    label: 'إدارة المزادات',
    icon: <Gavel className="h-5 w-5" />,
    roles: ['admin', 'super_admin']
  },
  {
    href: '/admin/tenders',
    label: 'إدارة المناقصات',
    icon: <FileText className="h-5 w-5" />,
    roles: ['admin', 'super_admin']
  },
  {
    href: '/admin/create-tender',
    label: 'إنشاء مناقصة',
    icon: <PlusCircle className="h-5 w-5" />,
    roles: ['admin', 'super_admin']
  },
  {
    href: '/favorites',
    label: 'المفضلة',
    icon: <Heart className="h-5 w-5" />,
    roles: ['admin', 'super_admin']
  },
  {
    href: '/admin/settings',
    label: 'الإعدادات',
    icon: <Settings className="h-5 w-5" />,
    roles: ['super_admin']
  },

  // Company navigation
  {
    href: '/company/dashboard',
    label: 'لوحة التحكم',
    icon: <LayoutDashboard className="h-5 w-5" />,
    roles: ['company']
  },
  {
    href: '/search',
    label: 'البحث والاستكشاف',
    icon: <Search className="h-5 w-5" />,
    roles: ['company']
  },
  {
    href: '/company/auctions',
    label: 'مزاداتي',
    icon: <Gavel className="h-5 w-5" />,
    roles: ['company']
  },
  {
    href: '/company/tenders',
    label: 'مناقصاتي',
    icon: <FileText className="h-5 w-5" />,
    roles: ['company']
  },
  {
    href: '/company/create-auction',
    label: 'إنشاء مزاد',
    icon: <PlusCircle className="h-5 w-5" />,
    roles: ['company']
  },
  {
    href: '/company/bids',
    label: 'عطاءاتي',
    icon: <TrendingUp className="h-5 w-5" />,
    roles: ['company']
  },
  {
    href: '/company/notifications',
    label: 'الإشعارات',
    icon: <Bell className="h-5 w-5" />,
    roles: ['company']
  },
  {
    href: '/favorites',
    label: 'المفضلة',
    icon: <Heart className="h-5 w-5" />,
    roles: ['company']
  },
  {
    href: '/company/profile',
    label: 'الملف الشخصي',
    icon: <Building className="h-5 w-5" />,
    roles: ['company']
  },

  // Individual user navigation
  {
    href: '/user/dashboard',
    label: 'لوحة التحكم',
    icon: <LayoutDashboard className="h-5 w-5" />,
    roles: ['individual']
  },
  {
    href: '/search',
    label: 'البحث والاستكشاف',
    icon: <Search className="h-5 w-5" />,
    roles: ['individual']
  },
  {
    href: '/user/leaderboard',
    label: 'لوحة الصدارة',
    icon: <Trophy className="h-5 w-5" />,
    roles: ['individual']
  },
  {
    href: '/user/auctions',
    label: 'المزادات المتاحة',
    icon: <Gavel className="h-5 w-5" />,
    roles: ['individual']
  },
  {
    href: '/user/tenders',
    label: 'المناقصات المتاحة',
    icon: <FileText className="h-5 w-5" />,
    roles: ['individual']
  },
  {
    href: '/user/my-bids',
    label: 'مزايداتي',
    icon: <Activity className="h-5 w-5" />,
    roles: ['individual']
  },
  {
    href: '/user/bids',
    label: 'إدارة المزايدات',
    icon: <Gavel className="h-5 w-5" />,
    roles: ['individual']
  },
  {
    href: '/user/applications',
    label: 'طلبات المناقصات',
    icon: <FileText className="h-5 w-5" />,
    roles: ['individual']
  },
  {
    href: '/user/analytics',
    label: 'التحليلات',
    icon: <TrendingUp className="h-5 w-5" />,
    roles: ['individual']
  },
  {
    href: '/user/notifications',
    label: 'الإشعارات',
    icon: <Bell className="h-5 w-5" />,
    roles: ['individual']
  },
  {
    href: '/favorites',
    label: 'المفضلة',
    icon: <Heart className="h-5 w-5" />,
    roles: ['individual']
  },
  {
    href: '/user/profile',
    label: 'الملف الشخصي',
    icon: <User className="h-5 w-5" />,
    roles: ['individual']
  },

  // Government navigation
  {
    href: '/government/dashboard',
    label: 'لوحة التحكم',
    icon: <LayoutDashboard className="h-5 w-5" />,
    roles: ['government']
  },
  {
    href: '/search',
    label: 'البحث والاستكشاف',
    icon: <Search className="h-5 w-5" />,
    roles: ['government']
  },
  {
    href: '/government/tenders',
    label: 'مناقصاتي',
    icon: <FileText className="h-5 w-5" />,
    roles: ['government']
  },
  {
    href: '/government/applications',
    label: 'طلبات المشاركة',
    icon: <Users className="h-5 w-5" />,
    roles: ['government']
  },
  {
    href: '/government/notifications',
    label: 'الإشعارات',
    icon: <Bell className="h-5 w-5" />,
    roles: ['government']
  },
  {
    href: '/favorites',
    label: 'المفضلة',
    icon: <Heart className="h-5 w-5" />,
    roles: ['government']
  },
  {
    href: '/government/profile',
    label: 'الملف الشخصي',
    icon: <Shield className="h-5 w-5" />,
    roles: ['government']
  }
]

export default function Sidebar({ userRole }: SidebarProps) {
  const [isCollapsed, setIsCollapsed] = useState(false)
  const [user, setUser] = useState<any>(null)
  const router = useRouter()
  const pathname = usePathname()

  useEffect(() => {
    const userData = localStorage.getItem('user')
    if (userData) {
      setUser(JSON.parse(userData))
    }
  }, [])

  const handleLogout = () => {
    localStorage.removeItem('token')
    localStorage.removeItem('user')
    router.push('/auth/login')
  }

  const roleIcon = () => {
    switch (user?.role) {
      case 'super_admin':
        return <Crown className="h-6 w-6 text-yellow-500" />
      case 'admin':
        return <Shield className="h-6 w-6 text-blue-500" />
      case 'company':
        return <Building className="h-6 w-6 text-green-500" />
      case 'government':
        return <Shield className="h-6 w-6 text-red-500" />
      case 'individual':
        return <User className="h-6 w-6 text-purple-500" />
      default:
        return <User className="h-6 w-6" />
    }
  }

  const getRoleLabel = (role: string) => {
    switch (role) {
      case 'super_admin':
        return 'مدير عام'
      case 'admin':
        return 'مدير'
      case 'company':
        return 'شركة'
      case 'government':
        return 'جهة حكومية'
      case 'individual':
        return 'فرد'
      default:
        return role
    }
  }

  const filteredNavItems = navItems.filter(item => 
    item.roles.includes(user?.role || '')
  )

  return (
    <div className={`${isCollapsed ? 'w-16' : 'w-64'} transition-all duration-300 backdrop-blur-xl bg-gradient-to-b from-white/95 via-white/90 to-white/85 border-r border-white/30 shadow-2xl flex flex-col h-screen relative z-10 overflow-hidden`}>
      {/* Animated Background */}
      <div className="absolute inset-0 bg-gradient-to-br from-blue-50/30 via-purple-50/20 to-indigo-50/30 opacity-60"></div>
      <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-blue-400/10 to-purple-400/10 rounded-full blur-3xl animate-pulse"></div>
      <div className="absolute bottom-0 left-0 w-24 h-24 bg-gradient-to-tr from-indigo-400/10 to-pink-400/10 rounded-full blur-2xl animate-pulse animation-delay-2000"></div>

      {/* Enhanced Header */}
      <div className="relative z-10 p-4 border-b border-white/30 backdrop-blur-sm">
        <div className="flex items-center justify-between">
          {!isCollapsed && (
            <Link href="/" className="flex items-center gap-3 group">
              <div className="w-10 h-10 bg-gradient-to-br from-blue-600 via-purple-600 to-indigo-700 rounded-2xl flex items-center justify-center group-hover:scale-110 group-hover:rotate-6 transition-all duration-500 shadow-xl group-hover:shadow-2xl">
                <Crown className="w-5 h-5 text-white drop-shadow-sm" />
              </div>
              <div className="text-right">
                <h1 className="text-lg font-bold bg-gradient-to-r from-gray-900 via-blue-800 to-purple-800 bg-clip-text text-transparent">المنصة</h1>
                <p className="text-xs text-gray-600 font-medium">المزادات والمناقصات</p>
              </div>
            </Link>
          )}
          {isCollapsed && (
            <div className="w-10 h-10 bg-gradient-to-br from-blue-600 via-purple-600 to-indigo-700 rounded-2xl flex items-center justify-center shadow-xl mx-auto">
              <Crown className="w-5 h-5 text-white drop-shadow-sm" />
            </div>
          )}
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsCollapsed(!isCollapsed)}
            className="p-2 rounded-xl bg-white/60 hover:bg-white/80 transition-all duration-300 shadow-md hover:shadow-lg backdrop-blur-sm border border-white/40"
          >
            {isCollapsed ? <ChevronLeft className="h-4 w-4 text-gray-700" /> : <ChevronRight className="h-4 w-4 text-gray-700" />}
          </Button>
        </div>
      </div>

      {/* Enhanced User Info */}
      {user && (
        <div className="relative z-10 px-4 py-4 border-b border-white/30 backdrop-blur-sm">
          <div className={`flex items-center ${isCollapsed ? 'justify-center' : 'gap-4'} p-3 rounded-2xl bg-white/40 backdrop-blur-md border border-white/50 shadow-lg hover:shadow-xl transition-all duration-300`}>
            <div className="relative">
              <div className="w-10 h-10 bg-gradient-to-br from-blue-600 via-purple-600 to-indigo-700 rounded-2xl flex items-center justify-center shadow-xl">
                {roleIcon()}
              </div>
              <div className="absolute -bottom-1 -right-1 w-4 h-4 bg-green-500 rounded-full border-2 border-white shadow-md">
                <div className="w-full h-full bg-green-400 rounded-full animate-ping"></div>
              </div>
            </div>
            {!isCollapsed && (
              <div className="flex-1 min-w-0">
                <p className="text-sm font-bold text-gray-900 truncate">
                  {user.profile?.fullName || user.profile?.companyName || user.profile?.governmentEntity || 'المدير'}
                </p>
                <p className="text-xs text-gray-600 font-medium truncate">
                  {getRoleLabel(user.role)}
                </p>
                <div className="flex items-center gap-2 mt-1">
                  <div className="flex items-center gap-1">
                    <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                    <span className="text-xs text-green-600 font-medium">متصل</span>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Enhanced Scrollable Navigation */}
      <nav className="flex-1 relative z-10 overflow-hidden">
        {/* Gradient fade at top */}
        <div className="absolute top-0 left-0 right-0 h-4 bg-gradient-to-b from-white/80 to-transparent z-20 pointer-events-none"></div>

        {/* Scrollable content */}
        <div className="h-full overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300/50 scrollbar-track-transparent hover:scrollbar-thumb-gray-400/70 px-4 py-4">
          <div className="space-y-1.5">
            {filteredNavItems.map((item, index) => {
              const isActive = pathname === item.href
              return (
                <Link
                  key={item.href}
                  href={item.href}
                  className={`group flex items-center ${isCollapsed ? 'justify-center px-2 py-3' : 'gap-4 px-4 py-3.5'} rounded-2xl transition-all duration-300 relative overflow-hidden ${
                    isActive
                      ? 'bg-gradient-to-r from-blue-500/15 via-purple-500/10 to-indigo-500/15 border border-blue-300/40 shadow-lg backdrop-blur-md scale-[1.02]'
                      : 'hover:bg-white/70 hover:shadow-lg hover:scale-[1.02] border border-transparent hover:border-white/50 backdrop-blur-sm'
                  }`}
                  style={{
                    animationDelay: `${index * 50}ms`
                  }}
                >
                  {/* Active state background effect */}
                  {isActive && (
                    <>
                      <div className="absolute inset-0 bg-gradient-to-r from-blue-500/8 via-purple-500/5 to-indigo-500/8"></div>
                      <div className="absolute right-0 top-1/2 -translate-y-1/2 w-1 h-8 bg-gradient-to-b from-blue-500 to-purple-600 rounded-l-full"></div>
                    </>
                  )}

                  <div className={`${isCollapsed ? 'w-7 h-7' : 'w-10 h-10'} rounded-xl flex items-center justify-center transition-all duration-300 relative z-10 ${
                    isActive
                      ? 'bg-gradient-to-br from-blue-500 via-purple-600 to-indigo-600 shadow-xl scale-110 rotate-3'
                      : 'bg-white/60 group-hover:bg-gradient-to-br group-hover:from-blue-100 group-hover:to-purple-100 group-hover:scale-105 shadow-md'
                  }`}>
                    <div className={`transition-all duration-300 ${
                      isActive ? 'text-white scale-110 drop-shadow-sm' : 'text-gray-600 group-hover:text-blue-600'
                    }`}>
                      {item.icon}
                    </div>
                  </div>

                  {!isCollapsed && (
                    <div className="relative z-10 flex-1">
                      <span className={`text-sm font-semibold transition-all duration-300 ${
                        isActive
                          ? 'text-gray-900'
                          : 'text-gray-700 group-hover:text-gray-900'
                      }`}>
                        {item.label}
                      </span>
                      {isActive && (
                        <div className="flex items-center gap-1 mt-1">
                          <div className="w-2 h-2 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full animate-pulse"></div>
                          <div className="w-1 h-1 bg-gradient-to-r from-blue-400 to-purple-500 rounded-full animate-pulse animation-delay-200"></div>
                        </div>
                      )}
                    </div>
                  )}

                  {isActive && !isCollapsed && (
                    <ChevronRight className="w-5 h-5 text-blue-600 relative z-10 animate-pulse" />
                  )}
                </Link>
              )
            })}
          </div>
        </div>

        {/* Gradient fade at bottom */}
        <div className="absolute bottom-0 left-0 right-0 h-4 bg-gradient-to-t from-white/80 to-transparent z-20 pointer-events-none"></div>
      </nav>

      {/* Enhanced Footer */}
      <div className="relative z-10 px-4 pb-4">
        <div className="border-t border-white/30 pt-4 backdrop-blur-sm">
          <Button
            onClick={handleLogout}
            variant="ghost"
            className={`w-full group flex items-center ${isCollapsed ? 'justify-center px-3 py-3' : 'gap-4 px-4 py-3.5'} rounded-2xl hover:bg-red-50/80 hover:shadow-lg transition-all duration-300 border border-transparent hover:border-red-200/60 backdrop-blur-md hover:scale-[1.02]`}
          >
            <div className={`${isCollapsed ? 'w-7 h-7' : 'w-10 h-10'} rounded-xl bg-red-100/80 group-hover:bg-gradient-to-br group-hover:from-red-500 group-hover:to-pink-500 flex items-center justify-center transition-all duration-300 shadow-md group-hover:shadow-xl group-hover:scale-105`}>
              <LogOut className={`${isCollapsed ? 'w-4 h-4' : 'w-5 h-5'} text-red-600 group-hover:text-white transition-all duration-300 drop-shadow-sm`} />
            </div>
            {!isCollapsed && (
              <span className="text-sm font-semibold text-red-700 group-hover:text-red-800 transition-all duration-300">
                تسجيل الخروج
              </span>
            )}
          </Button>
        </div>
      </div>
    </div>
  )
}
