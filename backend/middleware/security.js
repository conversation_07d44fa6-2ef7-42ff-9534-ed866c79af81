const rateLimit = require('express-rate-limit');
const helmet = require('helmet');
const validator = require('validator');

// Rate limiting configurations
const createRateLimit = (windowMs, max, message) => {
  return rateLimit({
    windowMs,
    max,
    message: {
      success: false,
      message: message || 'Too many requests, please try again later.'
    },
    standardHeaders: true,
    legacyHeaders: false,
  });
};

// General API rate limiting
const generalLimiter = createRateLimit(
  15 * 60 * 1000, // 15 minutes
  100, // limit each IP to 100 requests per windowMs
  'Too many requests from this IP, please try again after 15 minutes.'
);

// Strict rate limiting for sensitive operations
const strictLimiter = createRateLimit(
  15 * 60 * 1000, // 15 minutes
  10, // limit each IP to 10 requests per windowMs
  'Too many sensitive operations from this IP, please try again after 15 minutes.'
);

// Authentication rate limiting
const authLimiter = createRateLimit(
  15 * 60 * 1000, // 15 minutes
  5, // limit each IP to 5 login attempts per windowMs
  'Too many login attempts from this IP, please try again after 15 minutes.'
);

// Bidding rate limiting
const biddingLimiter = createRateLimit(
  1 * 60 * 1000, // 1 minute
  5, // limit each IP to 5 bids per minute
  'Too many bidding attempts, please wait before placing another bid.'
);

// Payment rate limiting
const paymentLimiter = createRateLimit(
  5 * 60 * 1000, // 5 minutes
  3, // limit each IP to 3 payment attempts per 5 minutes
  'Too many payment attempts, please wait before trying again.'
);

// Input validation middleware
const validateInput = (req, res, next) => {
  try {
    // Sanitize all string inputs
    const sanitizeObject = (obj) => {
      for (const key in obj) {
        if (typeof obj[key] === 'string') {
          // Remove potential XSS and injection attempts
          obj[key] = validator.escape(obj[key].trim());
          
          // Additional sanitization for specific patterns
          obj[key] = obj[key]
            .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
            .replace(/javascript:/gi, '')
            .replace(/on\w+\s*=/gi, '');
        } else if (typeof obj[key] === 'object' && obj[key] !== null) {
          sanitizeObject(obj[key]);
        }
      }
    };

    if (req.body) {
      sanitizeObject(req.body);
    }
    
    if (req.query) {
      sanitizeObject(req.query);
    }

    next();
  } catch (error) {
    console.error('Input validation error:', error);
    res.status(400).json({
      success: false,
      message: 'Invalid input data'
    });
  }
};

// Amount validation middleware
const validateAmount = (req, res, next) => {
  const { amount, bidAmount } = req.body;
  const amountToValidate = amount || bidAmount;

  if (amountToValidate !== undefined) {
    const numAmount = parseFloat(amountToValidate);
    
    // Check if amount is a valid number
    if (isNaN(numAmount)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid amount format'
      });
    }

    // Check for reasonable limits
    if (numAmount <= 0) {
      return res.status(400).json({
        success: false,
        message: 'Amount must be greater than zero'
      });
    }

    if (numAmount > 10000000) { // 10 million limit
      return res.status(400).json({
        success: false,
        message: 'Amount exceeds maximum limit'
      });
    }

    // Check for suspicious decimal places (more than 2)
    if (amountToValidate.toString().includes('.')) {
      const decimalPlaces = amountToValidate.toString().split('.')[1].length;
      if (decimalPlaces > 2) {
        return res.status(400).json({
          success: false,
          message: 'Amount cannot have more than 2 decimal places'
        });
      }
    }

    // Store validated amount
    req.validatedAmount = numAmount;
  }

  next();
};

// User permission validation
const validateUserPermissions = (requiredRoles = []) => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required'
      });
    }

    if (requiredRoles.length > 0 && !requiredRoles.includes(req.user.role)) {
      return res.status(403).json({
        success: false,
        message: 'Insufficient permissions'
      });
    }

    // Additional checks for account status
    if (req.user.status === 'blocked' || req.user.status === 'suspended') {
      return res.status(403).json({
        success: false,
        message: 'Account is suspended or blocked'
      });
    }

    next();
  };
};

// Prevent rapid successive requests from same user
const preventRapidRequests = (cooldownMs = 2000) => {
  const userLastRequest = new Map();

  return (req, res, next) => {
    if (!req.user) return next();

    const userId = req.user._id.toString();
    const now = Date.now();
    const lastRequest = userLastRequest.get(userId);

    if (lastRequest && (now - lastRequest) < cooldownMs) {
      return res.status(429).json({
        success: false,
        message: 'Please wait before making another request'
      });
    }

    userLastRequest.set(userId, now);
    
    // Clean up old entries periodically
    if (userLastRequest.size > 1000) {
      const cutoff = now - cooldownMs * 2;
      for (const [id, timestamp] of userLastRequest.entries()) {
        if (timestamp < cutoff) {
          userLastRequest.delete(id);
        }
      }
    }

    next();
  };
};

// Security headers middleware
const securityHeaders = helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
      connectSrc: ["'self'"],
      fontSrc: ["'self'"],
      objectSrc: ["'none'"],
      mediaSrc: ["'self'"],
      frameSrc: ["'none'"],
    },
  },
  crossOriginEmbedderPolicy: false
});

// Audit logging middleware
const auditLog = (action) => {
  return (req, res, next) => {
    const originalSend = res.send;
    
    res.send = function(data) {
      // Log the action
      console.log(`[AUDIT] ${action}:`, {
        userId: req.user?._id,
        userRole: req.user?.role,
        ip: req.ip,
        userAgent: req.get('User-Agent'),
        timestamp: new Date().toISOString(),
        success: res.statusCode < 400,
        statusCode: res.statusCode
      });
      
      originalSend.call(this, data);
    };
    
    next();
  };
};

module.exports = {
  generalLimiter,
  strictLimiter,
  authLimiter,
  biddingLimiter,
  paymentLimiter,
  validateInput,
  validateAmount,
  validateUserPermissions,
  preventRapidRequests,
  securityHeaders,
  auditLog
};
