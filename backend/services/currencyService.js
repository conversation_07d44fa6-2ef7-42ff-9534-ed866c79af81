const axios = require('axios');

// Currency conversion rates cache
let exchangeRates = {
  USD: 1.0,
  SAR: 3.75,
  EUR: 0.85,
  GBP: 0.73,
  AED: 3.67,
  KWD: 0.30,
  QAR: 3.64,
  BHD: 0.38,
  OMR: 0.38
};

let lastUpdated = null;
const CACHE_DURATION = 30 * 60 * 1000; // 30 minutes (more frequent updates)

class CurrencyService {
  
  // Get current exchange rates
  async getExchangeRates() {
    try {
      // Check if cache is still valid
      if (lastUpdated && (Date.now() - lastUpdated) < CACHE_DURATION) {
        return exchangeRates;
      }

      // Try to fetch fresh rates from free ExchangeRate-API (no API key required)
      try {
        const response = await axios.get('https://open.er-api.com/v6/latest/USD', {
          timeout: 10000,
          headers: {
            'User-Agent': 'Auction-Platform/1.0'
          }
        });

        if (response.data && response.data.result === 'success' && response.data.rates) {
          exchangeRates = {
            USD: 1.0,
            SAR: response.data.rates.SAR || 3.75,
            EUR: response.data.rates.EUR || 0.85,
            GBP: response.data.rates.GBP || 0.73,
            AED: response.data.rates.AED || 3.67,
            KWD: response.data.rates.KWD || 0.30,
            QAR: response.data.rates.QAR || 3.64,
            BHD: response.data.rates.BHD || 0.38,
            OMR: response.data.rates.OMR || 0.38
          };
          lastUpdated = Date.now();
          console.log('✅ Currency rates updated from ExchangeRate-API (open source)');
          console.log('📊 Current rates:', exchangeRates);
        }
      } catch (apiError) {
        console.log('⚠️ Failed to fetch from ExchangeRate-API, trying alternative sources:', apiError.message);

        // Try alternative free APIs as fallback
        try {
          // Try exchangerate.host (completely free, no API key)
          const fallbackResponse = await axios.get('https://api.exchangerate.host/latest?base=USD', {
            timeout: 10000,
            headers: {
              'User-Agent': 'Auction-Platform/1.0'
            }
          });

          if (fallbackResponse.data && fallbackResponse.data.success && fallbackResponse.data.rates) {
            const rates = fallbackResponse.data.rates;
            exchangeRates = {
              USD: 1.0,
              SAR: rates.SAR || 3.75,
              EUR: rates.EUR || 0.85,
              GBP: rates.GBP || 0.73,
              AED: rates.AED || 3.67,
              KWD: rates.KWD || 0.30,
              QAR: rates.QAR || 3.64,
              BHD: rates.BHD || 0.38,
              OMR: rates.OMR || 0.38
            };
            lastUpdated = Date.now();
            console.log('✅ Currency rates updated from ExchangeRate.host (fallback)');
            console.log('📊 Current rates:', exchangeRates);
          }
        } catch (fallbackError) {
          console.warn('⚠️ All APIs failed, using cached rates:', fallbackError.message);
        }
      }

      return exchangeRates;
    } catch (error) {
      console.error('❌ Currency service error:', error);
      return exchangeRates; // Return cached rates as fallback
    }
  }

  // Convert amount from one currency to another
  async convertCurrency(amount, fromCurrency, toCurrency) {
    try {
      if (fromCurrency === toCurrency) {
        return amount;
      }

      const rates = await this.getExchangeRates();
      
      // Convert to USD first, then to target currency
      const usdAmount = amount / rates[fromCurrency];
      const convertedAmount = usdAmount * rates[toCurrency];
      
      return Math.round(convertedAmount * 100) / 100; // Round to 2 decimal places
    } catch (error) {
      console.error('❌ Currency conversion error:', error);
      throw new Error('Currency conversion failed');
    }
  }

  // Convert amount to USD (base currency for comparisons)
  async convertToUSD(amount, fromCurrency) {
    return this.convertCurrency(amount, fromCurrency, 'USD');
  }

  // Force refresh exchange rates (bypass cache)
  async forceRefreshRates() {
    try {
      console.log('🔄 Force refreshing currency rates...');
      lastUpdated = null; // Clear cache
      const rates = await this.getExchangeRates();
      console.log('✅ Currency rates force refreshed successfully');
      return rates;
    } catch (error) {
      console.error('❌ Force refresh failed:', error);
      throw error;
    }
  }

  // Get cache status
  getCacheStatus() {
    return {
      lastUpdated: lastUpdated ? new Date(lastUpdated) : null,
      cacheAge: lastUpdated ? Date.now() - lastUpdated : null,
      cacheValid: lastUpdated && (Date.now() - lastUpdated) < CACHE_DURATION,
      nextUpdate: lastUpdated ? new Date(lastUpdated + CACHE_DURATION) : null,
      rates: exchangeRates
    };
  }

  // Convert amount from USD to target currency
  async convertFromUSD(amount, toCurrency) {
    return this.convertCurrency(amount, 'USD', toCurrency);
  }

  // Get currency symbol
  getCurrencySymbol(currency) {
    const symbols = {
      USD: '$',
      SAR: 'ر.س',
      EUR: '€',
      GBP: '£',
      AED: 'د.إ',
      KWD: 'د.ك',
      QAR: 'ر.ق',
      BHD: 'د.ب',
      OMR: 'ر.ع'
    };
    return symbols[currency] || currency;
  }

  // Format amount with currency
  formatAmount(amount, currency) {
    const symbol = this.getCurrencySymbol(currency);
    const formattedAmount = amount.toLocaleString();
    
    // For Arabic currencies, put symbol after number
    if (['SAR', 'AED', 'KWD', 'QAR', 'BHD', 'OMR'].includes(currency)) {
      return `${formattedAmount} ${symbol}`;
    }
    
    // For Western currencies, put symbol before number
    return `${symbol}${formattedAmount}`;
  }

  // Get supported currencies
  getSupportedCurrencies() {
    return [
      { code: 'USD', name: 'US Dollar', symbol: '$' },
      { code: 'SAR', name: 'Saudi Riyal', symbol: 'ر.س' },
      { code: 'EUR', name: 'Euro', symbol: '€' },
      { code: 'GBP', name: 'British Pound', symbol: '£' },
      { code: 'AED', name: 'UAE Dirham', symbol: 'د.إ' },
      { code: 'KWD', name: 'Kuwaiti Dinar', symbol: 'د.ك' },
      { code: 'QAR', name: 'Qatari Riyal', symbol: 'ر.ق' },
      { code: 'BHD', name: 'Bahraini Dinar', symbol: 'د.ب' },
      { code: 'OMR', name: 'Omani Rial', symbol: 'ر.ع' }
    ];
  }

  // Validate currency code
  isValidCurrency(currency) {
    return Object.keys(exchangeRates).includes(currency);
  }
}

module.exports = new CurrencyService();
