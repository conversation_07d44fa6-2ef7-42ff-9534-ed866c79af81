const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');

const userSchema = new mongoose.Schema({
  email: {
    type: String,
    required: true,
    unique: true,
    lowercase: true,
    trim: true
  },
  password: {
    type: String,
    required: true,
    minlength: 6
  },
  role: {
    type: String,
    enum: ['super_admin', 'admin', 'company', 'individual', 'government'],
    required: true
  },
  status: {
    type: String,
    enum: [
      'pending_email_verification',
      'pending_documents',
      'documents_submitted',
      'under_review',
      'approved',
      'rejected',
      'suspended',
      'blocked'
    ],
    default: 'pending_email_verification'
  },
  profile: {
    // Common fields
    fullName: String,
    phone: String,
    
    // Individual specific
    nationalId: String,
    residenceId: String,
    address: String,
    profileImage: String,
    
    // Company specific
    companyName: String,
    commercialRegister: String,
    professionalLicense: String,
    taxCertificate: String,
    foundationContract: String,
    authorizedSignatory: String,
    companyAddress: String,
    
    // Government specific
    governmentEntity: String,
    employeeId: String,
    officialAuthorization: String,
    officialLetter: String,
    entityNumber: String,
    officialEmail: String,

    // Currency and localization preferences
    preferredCurrency: {
      type: String,
      enum: ['USD', 'SAR', 'EUR', 'GBP', 'AED', 'KWD', 'QAR', 'BHD', 'OMR'],
      default: 'SAR'
    },
    country: {
      type: String,
      default: 'SA'
    },
    timezone: {
      type: String,
      default: 'Asia/Riyadh'
    }
  },
  documents: [{
    name: String,
    type: String,
    url: String,
    uploadedAt: {
      type: Date,
      default: Date.now
    },
    verified: {
      type: Boolean,
      default: false
    }
  }],
  emailVerified: {
    type: Boolean,
    default: false
  },
  emailVerificationToken: String,
  emailVerificationExpires: Date,
  passwordResetToken: String,
  passwordResetExpires: Date,
  refreshToken: String,
  refreshTokenExpiry: Date,
  lastLogin: Date,
  loginAttempts: {
    type: Number,
    default: 0
  },
  lockUntil: Date,
  rejectionReason: String,
  submittedAt: Date,
  reviewedAt: Date,
  reviewedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }
}, {
  timestamps: true
});

// Indexes
// Note: email index is automatically created by unique: true in schema
userSchema.index({ role: 1 });
userSchema.index({ status: 1 });
userSchema.index({ 'profile.commercialRegister': 1 });
userSchema.index({ 'profile.nationalId': 1 });

// Virtual for account lock status
userSchema.virtual('isLocked').get(function() {
  return !!(this.lockUntil && this.lockUntil > Date.now());
});

// Pre-save middleware to hash password
userSchema.pre('save', async function(next) {
  if (!this.isModified('password')) return next();
  
  try {
    const salt = await bcrypt.genSalt(12);
    this.password = await bcrypt.hash(this.password, salt);
    next();
  } catch (error) {
    next(error);
  }
});

// Method to compare password
userSchema.methods.comparePassword = async function(candidatePassword) {
  if (!this.password) return false;
  return await bcrypt.compare(candidatePassword, this.password);
};

// Method to increment login attempts
userSchema.methods.incLoginAttempts = function() {
  // If we have a previous lock that has expired, restart at 1
  if (this.lockUntil && this.lockUntil < Date.now()) {
    return this.updateOne({
      $unset: {
        lockUntil: 1
      },
      $set: {
        loginAttempts: 1
      }
    });
  }
  
  const updates = { $inc: { loginAttempts: 1 } };
  
  // If we already have max attempts and it's not locked, lock the account
  if (this.loginAttempts + 1 >= 5 && !this.isLocked) {
    updates.$set = {
      lockUntil: Date.now() + 2 * 60 * 60 * 1000 // 2 hours
    };
  }
  
  return this.updateOne(updates);
};

// Method to reset login attempts
userSchema.methods.resetLoginAttempts = function() {
  return this.updateOne({
    $unset: {
      loginAttempts: 1,
      lockUntil: 1
    }
  });
};

// Method to check if user can perform action based on role
userSchema.methods.hasPermission = function(action) {
  const permissions = {
    super_admin: [
      'manage_admins',
      'manage_users',
      'manage_auctions',
      'manage_tenders',
      'view_all_reports',
      'system_settings'
    ],
    admin: [
      'activate_accounts',
      'review_documents',
      'manage_auctions',
      'manage_tenders',
      'resolve_disputes',
      'view_reports'
    ],
    company: [
      'create_auctions',
      'submit_tenders',
      'manage_profile',
      'view_own_data'
    ],
    individual: [
      'participate_auctions',
      'manage_profile',
      'view_own_data'
    ],
    government: [
      'create_tenders',
      'evaluate_bids',
      'manage_contracts',
      'manage_profile',
      'view_own_data'
    ]
  };
  
  return permissions[this.role]?.includes(action) || false;
};

// Static method to find by email
userSchema.statics.findByEmail = function(email) {
  return this.findOne({ email: email.toLowerCase() });
};

// Static method to get users pending review
userSchema.statics.getPendingReviews = function() {
  return this.find({
    status: { $in: ['documents_submitted', 'under_review'] }
  }).sort({ submittedAt: 1 });
};

module.exports = mongoose.model('User', userSchema);
