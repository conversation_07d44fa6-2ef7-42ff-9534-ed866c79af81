{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 14:08:31:831"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 14:08:31:831"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 14:09:00:90"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 14:10:00:100"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 14:10:34:1034"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 14:10:34:1034"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 14:10:43:1043"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 14:10:43:1043"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 14:10:55:1055"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 14:10:55:1055"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 14:11:00:110"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 14:12:00:120"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 14:12:59:1259"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 14:12:59:1259"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 14:13:00:130"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 14:13:17:1317"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 14:13:17:1317"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 14:14:00:140"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 14:14:06:146"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 14:14:06:146"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 14:14:36:1436"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 14:14:36:1436"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 14:15:00:150"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 14:16:00:160"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-11 14:16:35:1635","url":"/api/user/dashboard/stats","userAgent":"axios/1.10.0"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/profile\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:85:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"PUT","timestamp":"2025-07-11 14:16:36:1636","url":"/api/user/profile","userAgent":"axios/1.10.0"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/company/applications\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at authenticate (/Users/<USER>/Desktop/brid1/backend/middleware/auth.js:35:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","method":"GET","timestamp":"2025-07-11 14:16:36:1636","url":"/api/company/applications","userAgent":"axios/1.10.0"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/government/dashboard\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at authenticate (/Users/<USER>/Desktop/brid1/backend/middleware/auth.js:35:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","method":"GET","timestamp":"2025-07-11 14:16:37:1637","url":"/api/government/dashboard","userAgent":"axios/1.10.0"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 14:17:00:170"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 14:18:00:180"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 14:18:09:189"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 14:18:09:189"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 14:18:43:1843"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 14:18:43:1843"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 14:19:00:190"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 14:19:18:1918"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 14:19:18:1918"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 14:20:00:200"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-11 14:20:14:2014","url":"/api/user/dashboard/stats","userAgent":"axios/1.10.0"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/profile\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:85:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"PUT","timestamp":"2025-07-11 14:20:14:2014","url":"/api/user/profile","userAgent":"axios/1.10.0"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/company/applications\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at authenticate (/Users/<USER>/Desktop/brid1/backend/middleware/auth.js:35:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","method":"GET","timestamp":"2025-07-11 14:20:15:2015","url":"/api/company/applications","userAgent":"axios/1.10.0"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/government/dashboard\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at authenticate (/Users/<USER>/Desktop/brid1/backend/middleware/auth.js:35:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","method":"GET","timestamp":"2025-07-11 14:20:15:2015","url":"/api/government/dashboard","userAgent":"axios/1.10.0"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/admin/users/6870f3660040fd0e29176f8c/approve\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at authenticate (/Users/<USER>/Desktop/brid1/backend/middleware/auth.js:35:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","method":"PUT","timestamp":"2025-07-11 14:20:16:2016","url":"/api/admin/users/6870f3660040fd0e29176f8c/approve","userAgent":"axios/1.10.0"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 14:21:00:210"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 14:22:00:220"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 14:23:00:230"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 14:24:00:240"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 14:25:00:250"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 14:26:00:260"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 14:27:00:270"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 14:28:00:280"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/company/applications\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at authenticate (/Users/<USER>/Desktop/brid1/backend/middleware/auth.js:35:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","method":"GET","timestamp":"2025-07-11 14:28:33:2833","url":"/api/company/applications","userAgent":"axios/1.10.0"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/government/dashboard\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at authenticate (/Users/<USER>/Desktop/brid1/backend/middleware/auth.js:35:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","method":"GET","timestamp":"2025-07-11 14:28:33:2833","url":"/api/government/dashboard","userAgent":"axios/1.10.0"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 14:29:00:290"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 14:30:00:300"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 14:31:00:310"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 14:31:47:3147"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 14:31:47:3147"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 14:32:00:320"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 14:32:09:329"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 14:32:09:329"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 14:32:49:3249"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 14:32:49:3249"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 14:33:00:330"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 14:33:04:334"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 14:33:04:334"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 14:33:20:3320"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 14:33:20:3320"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 14:33:31:3331"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 14:33:31:3331"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 14:33:41:3341"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 14:33:41:3341"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 14:34:00:340"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 14:35:00:350"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 14:36:00:360"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 14:37:00:370"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 14:38:00:380"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 14:39:00:390"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 14:40:00:400"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 14:40:41:4041"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 14:40:41:4041"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 14:41:00:410"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 14:42:00:420"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 14:43:00:430"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 14:44:00:440"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 14:45:00:450"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 14:46:00:460"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 14:48:02:482"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 14:48:02:482"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 14:49:00:490"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 14:50:00:500"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 14:51:00:510"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 14:52:00:520"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 14:52:00:520"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 14:52:00:520"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 14:53:00:530"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 14:53:39:5339"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 14:53:39:5339"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 14:54:00:540"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 14:54:12:5412"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 14:54:12:5412"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 14:54:29:5429"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 14:54:29:5429"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 14:55:00:550"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 14:56:00:560"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 14:57:00:570"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 14:58:00:580"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 14:59:00:590"}
{"level":"info","message":"Running account activation timeout check...","timestamp":"2025-07-11 15:00:00:00"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:00:00:00"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:01:00:10"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:02:00:20"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:03:00:30"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:04:00:40"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:05:00:50"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:06:00:60"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:07:00:70"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:08:00:80"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:09:00:90"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:10:00:100"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:11:00:110"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:12:00:120"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:13:00:130"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:14:00:140"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:15:00:150"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:16:00:160"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:17:00:170"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:18:00:180"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:19:00:190"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:20:00:200"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:21:00:210"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:22:00:220"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:23:00:230"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:24:00:240"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:25:00:250"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:26:00:260"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:27:00:270"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:28:00:280"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:29:00:290"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:30:00:300"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:31:00:310"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:32:00:320"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:33:00:330"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:34:00:340"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:35:00:350"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:36:00:360"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:37:00:370"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:38:00:380"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:39:01:391"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:40:00:400"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:41:00:410"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:42:00:420"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:43:00:430"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 15:43:48:4348"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 15:43:48:4348"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:44:00:440"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:45:00:450"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 15:45:14:4514"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 15:45:14:4514"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:46:00:460"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 15:46:19:4619"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 15:46:19:4619"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:47:00:470"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 15:47:16:4716"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 15:47:16:4716"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/tenders/687107e023168d085301922c/apply\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at Function.handle (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:175:3)\n    at router (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:47:12)","method":"POST","timestamp":"2025-07-11 15:47:28:4728","url":"/api/tenders/687107e023168d085301922c/apply","userAgent":"axios/1.10.0"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:48:00:480"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:49:00:490"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:50:00:500"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:51:00:510"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:52:00:520"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:53:00:530"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/government/tenders/6871093b23168d085301924c/applications\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at authenticate (/Users/<USER>/Desktop/brid1/backend/middleware/auth.js:35:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","method":"GET","timestamp":"2025-07-11 15:53:15:5315","url":"/api/government/tenders/6871093b23168d085301924c/applications","userAgent":"axios/1.10.0"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:54:00:540"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:55:00:550"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:56:00:560"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:57:00:570"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 15:57:24:5724"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 15:57:24:5724"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 15:57:40:5740"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 15:57:40:5740"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:58:00:580"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 15:58:02:582"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 15:58:02:582"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 15:59:00:590"}
{"level":"info","message":"Running account activation timeout check...","timestamp":"2025-07-11 16:00:00:00"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 16:00:00:00"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 16:00:22:022"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 16:00:22:022"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 16:01:00:10"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 16:02:00:20"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/government/tenders/68710b4f07a04ce831926d6d/applications\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at authenticate (/Users/<USER>/Desktop/brid1/backend/middleware/auth.js:35:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","method":"GET","timestamp":"2025-07-11 16:02:07:27","url":"/api/government/tenders/68710b4f07a04ce831926d6d/applications","userAgent":"axios/1.10.0"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 16:03:00:30"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 16:04:00:40"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 16:05:00:50"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/government/tenders/68710c1e07a04ce831926ea9/applications\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at authenticate (/Users/<USER>/Desktop/brid1/backend/middleware/auth.js:35:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","method":"GET","timestamp":"2025-07-11 16:05:34:534","url":"/api/government/tenders/68710c1e07a04ce831926ea9/applications","userAgent":"axios/1.10.0"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 16:06:00:60"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 16:07:24:724"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 16:07:24:724"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 16:08:00:80"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 16:09:00:90"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 16:10:00:100"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 16:11:00:110"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 16:12:00:120"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 16:13:00:130"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 16:14:00:140"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 16:15:00:150"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 16:16:00:160"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 16:17:00:170"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 16:18:00:180"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 16:19:00:190"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 16:20:00:200"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 16:20:57:2057"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 16:20:57:2057"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 16:21:00:210"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 16:22:00:220"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 16:23:00:230"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/government/applications?limit=100\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at authenticate (/Users/<USER>/Desktop/brid1/backend/middleware/auth.js:35:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","method":"GET","timestamp":"2025-07-11 16:23:26:2326","url":"/api/government/applications?limit=100","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/government/applications?limit=100\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at authenticate (/Users/<USER>/Desktop/brid1/backend/middleware/auth.js:35:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","method":"GET","timestamp":"2025-07-11 16:23:26:2326","url":"/api/government/applications?limit=100","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 16:24:00:240"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/notifications?limit=5\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-11 16:24:33:2433","url":"/api/notifications?limit=5","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/notifications?limit=5\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-11 16:24:33:2433","url":"/api/notifications?limit=5","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 16:25:00:250"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/notifications?limit=5\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-11 16:25:01:251","url":"/api/notifications?limit=5","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/notifications?limit=5\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-11 16:25:01:251","url":"/api/notifications?limit=5","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 16:26:00:260"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 16:27:00:270"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 16:28:00:280"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 16:29:00:290"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 16:30:00:300"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 16:31:00:310"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 16:32:00:320"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 16:33:00:330"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 16:34:00:340"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 16:34:29:3429"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 16:34:29:3429"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 16:35:00:350"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 16:35:28:3528"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 16:35:28:3528"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 16:36:00:360"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 16:36:09:369"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 16:36:09:369"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 16:36:42:3642"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 16:36:42:3642"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 16:36:55:3655"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 16:36:55:3655"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 16:37:00:370"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 16:37:09:379"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 16:37:09:379"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 16:37:43:3743"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 16:37:43:3743"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 16:38:00:380"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 16:39:00:390"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 16:40:00:400"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 16:41:00:410"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 16:42:00:420"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 16:42:10:4210"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 16:42:10:4210"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 16:43:00:430"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 16:44:00:440"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 16:45:00:450"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 16:46:00:460"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 16:47:00:470"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 16:48:00:480"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 16:49:00:490"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 16:50:00:500"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 16:51:00:510"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 16:52:00:520"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 16:53:00:530"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 16:54:00:540"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 16:55:00:550"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 16:56:00:560"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 16:57:00:570"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 16:58:00:580"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 16:59:00:590"}
{"level":"info","message":"Running account activation timeout check...","timestamp":"2025-07-11 17:00:00:00"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 17:00:00:00"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 17:01:00:10"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 17:02:00:20"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 17:03:00:30"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 17:04:00:40"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 17:05:00:50"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 17:06:00:60"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 17:07:00:70"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 17:08:00:80"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 17:09:00:90"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 17:10:00:100"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 17:11:00:110"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 17:12:00:120"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 17:13:00:130"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 17:14:00:140"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 17:15:00:150"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 17:16:00:160"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 17:17:00:170"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 17:18:00:180"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 17:19:00:190"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 17:20:00:200"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 17:21:00:210"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 17:22:00:220"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 17:23:00:230"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 17:24:00:240"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 17:25:00:250"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 17:26:00:260"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 17:27:00:270"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 17:28:00:280"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 17:29:00:290"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 17:30:00:300"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 17:31:00:310"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 17:32:00:320"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 17:33:00:330"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 17:34:00:340"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 17:35:00:350"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 17:36:00:360"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 17:37:00:370"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 17:38:00:380"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 17:39:00:390"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 17:40:00:400"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 17:41:00:410"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 17:42:00:420"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 17:43:00:430"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 17:44:00:440"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 17:45:00:450"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 17:46:00:460"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 17:47:00:470"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 17:48:00:480"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 17:49:00:490"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 17:50:00:500"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 17:51:00:510"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 17:52:00:520"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 17:53:00:530"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 17:54:00:540"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 17:55:00:550"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 17:56:00:560"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 17:57:00:570"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 17:58:00:580"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 17:59:00:590"}
{"level":"info","message":"Running account activation timeout check...","timestamp":"2025-07-11 18:00:00:00"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 18:00:00:00"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 18:01:00:10"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 18:02:00:20"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 18:03:00:30"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 18:04:00:40"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 18:05:00:50"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 18:06:00:60"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-11 18:06:57:657","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-11 18:06:57:657","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 18:07:00:70"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 18:08:00:80"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 18:09:00:90"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 18:10:00:100"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 18:11:00:110"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 18:12:00:120"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 18:13:00:130"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 18:14:00:140"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 18:15:00:150"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 18:16:00:160"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 18:17:00:170"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 18:18:00:180"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 18:19:00:190"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 18:20:00:200"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 18:21:00:210"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 18:22:00:220"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 18:23:00:230"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 18:24:00:240"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 18:25:00:250"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 18:26:46:2646"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 18:26:46:2646"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 18:27:00:270"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/admin/users/68712d53fd8732605b267a80\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at authenticate (/Users/<USER>/Desktop/brid1/backend/middleware/auth.js:35:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","method":"GET","timestamp":"2025-07-11 18:27:36:2736","url":"/api/admin/users/68712d53fd8732605b267a80","userAgent":"axios/1.10.0"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 18:28:00:280"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 18:29:00:290"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 18:30:00:300"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 18:31:00:310"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 18:32:00:320"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/admin/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at authenticate (/Users/<USER>/Desktop/brid1/backend/middleware/auth.js:35:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","method":"GET","timestamp":"2025-07-11 18:32:19:3219","url":"/api/admin/stats","userAgent":"axios/1.10.0"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 18:33:00:330"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 18:34:00:340"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 18:35:00:350"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/admin/reports/user-activity\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at authenticate (/Users/<USER>/Desktop/brid1/backend/middleware/auth.js:35:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","method":"GET","timestamp":"2025-07-11 18:35:20:3520","url":"/api/admin/reports/user-activity","userAgent":"axios/1.10.0"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 18:36:00:360"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/admin/performance\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at authenticate (/Users/<USER>/Desktop/brid1/backend/middleware/auth.js:35:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","method":"GET","timestamp":"2025-07-11 18:36:02:362","url":"/api/admin/performance","userAgent":"axios/1.10.0"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 18:37:00:370"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/admin/moderation/flagged\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at authenticate (/Users/<USER>/Desktop/brid1/backend/middleware/auth.js:35:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","method":"GET","timestamp":"2025-07-11 18:37:43:3743","url":"/api/admin/moderation/flagged","userAgent":"axios/1.10.0"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 18:38:00:380"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 18:39:00:390"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 18:40:00:400"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 18:40:57:4057"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 18:40:57:4057"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 18:41:00:410"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 18:42:00:420"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 18:43:00:430"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 18:44:00:440"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 18:45:00:450"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 18:46:00:460"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 18:47:00:470"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 18:48:00:480"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 18:49:00:490"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 18:50:00:500"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 18:51:00:510"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 18:52:00:520"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 18:53:00:530"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 18:54:00:540"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 18:55:00:550"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 18:56:00:560"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 18:57:00:570"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 18:58:00:580"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 18:59:00:590"}
{"level":"info","message":"Running account activation timeout check...","timestamp":"2025-07-11 19:00:00:00"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 19:00:00:00"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 19:01:00:10"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/payment/methods\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-11 19:01:54:154","url":"/api/payment/methods","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/payment/methods\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-11 19:01:54:154","url":"/api/payment/methods","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 19:02:00:20"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/payment/methods\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-11 19:02:03:23","url":"/api/payment/methods","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/payment/methods\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-11 19:02:03:23","url":"/api/payment/methods","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/wallet/balance\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-11 19:02:13:213","url":"/api/wallet/balance","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/wallet/transactions\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-11 19:02:13:213","url":"/api/wallet/transactions","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/wallet/balance\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-11 19:02:13:213","url":"/api/wallet/balance","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/wallet/transactions\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-11 19:02:13:213","url":"/api/wallet/transactions","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/system/status\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-11 19:02:44:244","url":"/api/system/status","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/system/status\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-11 19:02:44:244","url":"/api/system/status","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 19:03:00:30"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/billing/invoices\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-11 19:03:11:311","url":"/api/billing/invoices","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/billing/invoices\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-11 19:03:11:311","url":"/api/billing/invoices","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 19:04:00:40"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 19:05:00:50"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/billing/invoices/2/pay\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:103:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"POST","timestamp":"2025-07-11 19:05:21:521","url":"/api/billing/invoices/2/pay","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/system/status\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-11 19:05:25:525","url":"/api/system/status","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/system/status\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-11 19:05:25:525","url":"/api/system/status","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/wallet/balance\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-11 19:05:28:528","url":"/api/wallet/balance","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/wallet/transactions\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-11 19:05:28:528","url":"/api/wallet/transactions","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/wallet/transactions\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-11 19:05:28:528","url":"/api/wallet/transactions","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/wallet/balance\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-11 19:05:28:528","url":"/api/wallet/balance","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/payment/methods\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-11 19:05:29:529","url":"/api/payment/methods","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/payment/methods\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-11 19:05:29:529","url":"/api/payment/methods","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 19:06:00:60"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-11 19:06:10:610","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-11 19:06:10:610","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 19:07:01:71"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 19:08:00:80"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 19:09:00:90"}
{"ip":"::1","level":"error","message":"Error: Not found - /favicon.ico\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-11 19:09:41:941","url":"/favicon.ico","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 19:10:00:100"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 19:10:42:1042"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 19:10:42:1042"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 19:11:00:110"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 19:11:27:1127"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 19:11:27:1127"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 19:12:00:120"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 19:12:04:124"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 19:12:04:124"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 19:13:00:130"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 19:14:00:140"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 19:15:00:150"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 19:16:00:160"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 19:17:00:170"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 19:18:00:180"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 19:18:18:1818"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 19:18:18:1818"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 19:18:33:1833"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 19:18:33:1833"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 19:18:47:1847"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 19:18:47:1847"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 19:19:00:190"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 19:19:59:1959"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 19:19:59:1959"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 19:20:00:200"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 19:20:13:2013"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 19:20:13:2013"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 19:20:26:2026"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 19:20:26:2026"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 19:20:40:2040"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 19:20:40:2040"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 19:21:00:210"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 19:22:00:220"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 19:22:03:223"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 19:22:03:223"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 19:22:27:2227"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 19:22:27:2227"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 19:22:49:2249"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 19:22:49:2249"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 19:23:00:230"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 19:23:48:2348"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 19:23:48:2348"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 19:24:00:240"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 19:24:11:2411"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 19:24:11:2411"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 19:24:28:2428"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 19:24:28:2428"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 19:25:00:250"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 19:26:00:260"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 19:27:00:270"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 19:28:00:280"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 19:29:00:290"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 19:30:00:300"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 19:31:00:310"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 19:32:00:320"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 19:33:00:330"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 19:33:19:3319"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 19:33:19:3319"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 19:34:00:340"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 19:35:00:350"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-11 19:35:25:3525","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-11 19:35:25:3525","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 19:36:00:360"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 19:37:00:370"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/wallet/balance\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-11 19:37:47:3747","url":"/api/wallet/balance","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/wallet/transactions\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-11 19:37:47:3747","url":"/api/wallet/transactions","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/wallet/balance\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-11 19:37:47:3747","url":"/api/wallet/balance","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/wallet/transactions\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-11 19:37:47:3747","url":"/api/wallet/transactions","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 19:38:00:380"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 19:39:00:390"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 19:40:00:400"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 19:41:00:410"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 19:42:00:420"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 19:43:00:430"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 19:44:00:440"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 19:45:00:450"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 19:46:00:460"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 19:47:00:470"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 19:48:00:480"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 19:49:00:490"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 19:50:00:500"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 19:51:00:510"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 19:52:00:520"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 19:53:00:530"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 19:54:00:540"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 19:55:00:550"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 19:56:00:560"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 19:57:00:570"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 19:58:00:580"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 19:59:00:590"}
{"level":"info","message":"Running account activation timeout check...","timestamp":"2025-07-11 20:00:00:00"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 20:00:00:00"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 20:01:00:10"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 20:02:00:20"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 20:03:00:30"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 20:04:00:40"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 20:05:00:50"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 20:06:00:60"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 20:07:00:70"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 20:08:00:80"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 20:09:00:90"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 20:10:00:100"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 20:11:00:110"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 20:12:00:120"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 20:13:00:130"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 20:14:00:140"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 20:15:00:150"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 20:16:00:160"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 20:17:00:170"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 20:18:00:180"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 20:19:00:190"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 20:20:00:200"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 20:21:00:210"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 20:22:00:220"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 20:23:00:230"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 20:24:00:240"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 20:25:00:250"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 20:26:00:260"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 20:27:00:270"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 20:28:00:280"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 20:29:00:290"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 20:30:00:300"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 20:31:00:310"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 20:32:00:320"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 20:33:00:330"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 20:34:00:340"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 20:35:00:350"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 20:36:00:360"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 20:37:00:370"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 20:38:00:380"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 20:39:00:390"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 20:40:00:400"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 20:41:00:410"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 20:42:00:420"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 20:43:00:430"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 20:44:00:440"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 20:45:00:450"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 20:46:00:460"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 20:47:00:470"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 20:48:00:480"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 20:49:00:490"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 20:50:00:500"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 20:51:00:510"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 20:52:00:520"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 20:53:00:530"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 20:54:00:540"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 20:55:00:550"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 20:56:00:560"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 20:57:00:570"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 20:58:00:580"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 20:59:00:590"}
{"level":"info","message":"Running account activation timeout check...","timestamp":"2025-07-11 21:00:00:00"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 21:00:00:00"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/government/tenders/6871501b1be4922db2f3d424\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at authenticate (/Users/<USER>/Desktop/brid1/backend/middleware/auth.js:35:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","method":"GET","timestamp":"2025-07-11 21:00:35:035","url":"/api/government/tenders/6871501b1be4922db2f3d424","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/government/tenders/6871501b1be4922db2f3d424\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at authenticate (/Users/<USER>/Desktop/brid1/backend/middleware/auth.js:35:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","method":"GET","timestamp":"2025-07-11 21:00:35:035","url":"/api/government/tenders/6871501b1be4922db2f3d424","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/government/tenders/687150821be4922db2f3d42b\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at authenticate (/Users/<USER>/Desktop/brid1/backend/middleware/auth.js:35:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","method":"GET","timestamp":"2025-07-11 21:00:50:050","url":"/api/government/tenders/687150821be4922db2f3d42b","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/government/tenders/687150821be4922db2f3d42b\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at authenticate (/Users/<USER>/Desktop/brid1/backend/middleware/auth.js:35:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","method":"GET","timestamp":"2025-07-11 21:00:51:051","url":"/api/government/tenders/687150821be4922db2f3d42b","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 21:01:00:10"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/government/tenders/687150821be4922db2f3d42b\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at authenticate (/Users/<USER>/Desktop/brid1/backend/middleware/auth.js:35:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","method":"GET","timestamp":"2025-07-11 21:01:32:132","url":"/api/government/tenders/687150821be4922db2f3d42b","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/government/tenders/687150821be4922db2f3d42b\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at authenticate (/Users/<USER>/Desktop/brid1/backend/middleware/auth.js:35:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","method":"GET","timestamp":"2025-07-11 21:01:32:132","url":"/api/government/tenders/687150821be4922db2f3d42b","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 21:02:00:20"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/government/tenders/687150821be4922db2f3d42b\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at authenticate (/Users/<USER>/Desktop/brid1/backend/middleware/auth.js:35:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","method":"GET","timestamp":"2025-07-11 21:02:02:22","url":"/api/government/tenders/687150821be4922db2f3d42b","userAgent":"curl/8.7.1"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 21:03:00:30"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 21:04:00:40"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/government/tenders/687150821be4922db2f3d42b\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at authenticate (/Users/<USER>/Desktop/brid1/backend/middleware/auth.js:35:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","method":"GET","timestamp":"2025-07-11 21:04:02:42","url":"/api/government/tenders/687150821be4922db2f3d42b","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/government/tenders/687150821be4922db2f3d42b\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at authenticate (/Users/<USER>/Desktop/brid1/backend/middleware/auth.js:35:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","method":"GET","timestamp":"2025-07-11 21:04:02:42","url":"/api/government/tenders/687150821be4922db2f3d42b","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 21:05:00:50"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 21:06:00:60"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 21:07:00:70"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 21:08:00:80"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 21:09:00:90"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 21:10:00:100"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 21:11:00:110"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 21:12:00:120"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/government/tenders/687150821be4922db2f3d42b\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at authenticate (/Users/<USER>/Desktop/brid1/backend/middleware/auth.js:35:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","method":"GET","timestamp":"2025-07-11 21:12:10:1210","url":"/api/government/tenders/687150821be4922db2f3d42b","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/government/tenders/687150821be4922db2f3d42b\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at authenticate (/Users/<USER>/Desktop/brid1/backend/middleware/auth.js:35:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","method":"GET","timestamp":"2025-07-11 21:12:10:1210","url":"/api/government/tenders/687150821be4922db2f3d42b","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/government/applications?limit=100\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at authenticate (/Users/<USER>/Desktop/brid1/backend/middleware/auth.js:35:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","method":"GET","timestamp":"2025-07-11 21:12:18:1218","url":"/api/government/applications?limit=100","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/government/applications?limit=100\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at authenticate (/Users/<USER>/Desktop/brid1/backend/middleware/auth.js:35:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","method":"GET","timestamp":"2025-07-11 21:12:18:1218","url":"/api/government/applications?limit=100","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 21:13:00:130"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 21:14:00:140"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 21:15:00:150"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/government/tenders/687150821be4922db2f3d42b\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at authenticate (/Users/<USER>/Desktop/brid1/backend/middleware/auth.js:35:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","method":"GET","timestamp":"2025-07-11 21:15:53:1553","url":"/api/government/tenders/687150821be4922db2f3d42b","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/government/tenders/687150821be4922db2f3d42b\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at authenticate (/Users/<USER>/Desktop/brid1/backend/middleware/auth.js:35:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","method":"GET","timestamp":"2025-07-11 21:15:53:1553","url":"/api/government/tenders/687150821be4922db2f3d42b","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 21:16:00:160"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/government/tenders/687150821be4922db2f3d42b\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at authenticate (/Users/<USER>/Desktop/brid1/backend/middleware/auth.js:35:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","method":"GET","timestamp":"2025-07-11 21:16:53:1653","url":"/api/government/tenders/687150821be4922db2f3d42b","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/government/tenders/687150821be4922db2f3d42b\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at authenticate (/Users/<USER>/Desktop/brid1/backend/middleware/auth.js:35:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","method":"GET","timestamp":"2025-07-11 21:16:54:1654","url":"/api/government/tenders/687150821be4922db2f3d42b","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 21:17:00:170"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 21:18:00:180"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 21:19:00:190"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 21:20:00:200"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/government/tenders/6871501b1be4922db2f3d424\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at authenticate (/Users/<USER>/Desktop/brid1/backend/middleware/auth.js:35:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","method":"DELETE","timestamp":"2025-07-11 21:20:16:2016","url":"/api/government/tenders/6871501b1be4922db2f3d424","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 21:21:00:210"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 21:22:00:220"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/government/tenders/6871501b1be4922db2f3d424\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at authenticate (/Users/<USER>/Desktop/brid1/backend/middleware/auth.js:35:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","method":"DELETE","timestamp":"2025-07-11 21:22:26:2226","url":"/api/government/tenders/6871501b1be4922db2f3d424","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 21:23:00:230"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 21:24:00:240"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 21:25:00:250"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 21:26:00:260"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 21:27:00:270"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 21:28:00:280"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 21:29:00:290"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 21:30:00:300"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 21:31:00:310"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 21:32:00:320"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 21:33:00:330"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 21:34:00:340"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 21:35:00:350"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 21:36:00:360"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 21:37:00:370"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 21:38:00:380"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 21:39:00:390"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 21:40:00:400"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 21:41:00:410"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 21:42:00:420"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 21:43:00:430"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 21:44:00:440"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 21:45:00:450"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 21:46:00:460"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 21:47:00:470"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 21:48:00:480"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 21:49:00:490"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 21:50:00:500"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 21:51:00:510"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 21:52:00:520"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 21:53:00:530"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 21:54:00:540"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 21:55:00:550"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 21:56:00:560"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 21:57:00:570"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 21:58:00:580"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 21:59:00:590"}
{"level":"info","message":"Running account activation timeout check...","timestamp":"2025-07-11 22:00:00:00"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 22:00:00:00"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 22:01:00:10"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 22:02:00:20"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 22:03:00:30"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 22:04:00:40"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 22:05:00:50"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 22:06:00:60"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 22:07:00:70"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 22:08:00:80"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 22:09:00:90"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 22:10:00:100"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 22:11:00:110"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 22:12:01:121"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 22:13:00:130"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 22:14:00:140"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 22:15:00:150"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 22:16:00:160"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 22:17:00:170"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 22:18:00:180"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 22:19:00:190"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 22:20:00:200"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 22:21:00:210"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 22:22:00:220"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 22:23:00:230"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 22:24:00:240"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 22:25:00:250"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 22:26:00:260"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 22:27:00:270"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 22:28:00:280"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 22:29:00:290"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 22:30:00:300"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 22:31:00:310"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 22:31:35:3135"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 22:31:35:3135"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 22:32:00:320"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 22:33:00:330"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 22:34:00:340"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 22:35:00:350"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 22:36:00:360"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 22:37:00:370"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 22:38:00:380"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 22:39:00:390"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 22:40:00:400"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 22:41:00:410"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 22:42:00:420"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 22:43:00:430"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 22:44:00:440"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 22:45:00:450"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 22:46:00:460"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 22:47:00:470"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 22:48:00:480"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 22:49:00:490"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 22:50:00:500"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/government/applications/68716592746ef9155eefe3a9\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at authenticate (/Users/<USER>/Desktop/brid1/backend/middleware/auth.js:35:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","method":"GET","timestamp":"2025-07-11 22:50:53:5053","url":"/api/government/applications/68716592746ef9155eefe3a9","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/government/applications/68716592746ef9155eefe3a9\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at authenticate (/Users/<USER>/Desktop/brid1/backend/middleware/auth.js:35:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","method":"GET","timestamp":"2025-07-11 22:50:53:5053","url":"/api/government/applications/68716592746ef9155eefe3a9","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 22:51:00:510"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/government/applications/68716592746ef9155eefe3a9\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at authenticate (/Users/<USER>/Desktop/brid1/backend/middleware/auth.js:35:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","method":"GET","timestamp":"2025-07-11 22:51:17:5117","url":"/api/government/applications/68716592746ef9155eefe3a9","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/government/applications/68716592746ef9155eefe3a9\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at authenticate (/Users/<USER>/Desktop/brid1/backend/middleware/auth.js:35:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","method":"GET","timestamp":"2025-07-11 22:51:17:5117","url":"/api/government/applications/68716592746ef9155eefe3a9","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 22:52:00:520"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 22:52:08:528"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 22:52:08:528"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 22:53:00:530"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 22:53:16:5316"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 22:53:16:5316"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 22:54:00:540"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 22:55:00:550"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 22:56:00:560"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/government/applications/68716592746ef9155eefe3a9/status\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at authenticate (/Users/<USER>/Desktop/brid1/backend/middleware/auth.js:35:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","method":"PATCH","timestamp":"2025-07-11 22:56:37:5637","url":"/api/government/applications/68716592746ef9155eefe3a9/status","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 22:57:00:570"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 22:57:03:573"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 22:57:03:573"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 22:57:54:5754"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 22:57:54:5754"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 22:58:00:580"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 22:58:04:584"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 22:58:04:584"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 22:59:00:590"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 22:59:16:5916"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 22:59:16:5916"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 22:59:29:5929"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 22:59:29:5929"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 23:01:00:10"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 23:02:00:20"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 23:03:00:30"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 23:04:00:40"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 23:05:00:50"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 23:06:00:60"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 23:07:00:70"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 23:08:00:80"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 23:09:00:90"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 23:10:00:100"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 23:11:00:110"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 23:12:00:120"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 23:13:00:130"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 23:14:00:140"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 23:15:00:150"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 23:16:00:160"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 23:17:00:170"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 23:18:00:180"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 23:19:00:190"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 23:20:00:200"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 23:21:00:210"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 23:22:00:220"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 23:22:30:2230"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 23:22:30:2230"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 23:23:00:230"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 23:23:47:2347"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 23:23:47:2347"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 23:24:00:240"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 23:25:00:250"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 23:26:00:260"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 23:27:00:270"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 23:27:35:2735"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 23:27:35:2735"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 23:27:52:2752"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 23:27:52:2752"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 23:28:00:280"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-11 23:28:05:285"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-11 23:28:05:285"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 23:29:00:290"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 23:30:00:300"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 23:31:00:310"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 23:32:00:320"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 23:33:00:330"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 23:34:00:340"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 23:35:00:350"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 23:36:00:360"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 23:37:00:370"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 23:38:00:380"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 23:39:00:390"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 23:40:00:400"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 23:41:00:410"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 23:42:00:420"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 23:43:00:430"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 23:44:00:440"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 23:45:00:450"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 23:46:00:460"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 23:47:00:470"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 23:48:00:480"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 23:49:00:490"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 23:50:00:500"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 23:51:00:510"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 23:52:00:520"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 23:53:00:530"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 23:54:00:540"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 23:55:00:550"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 23:56:00:560"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 23:57:00:570"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 23:58:00:580"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-11 23:59:00:590"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 00:00:00:00"}
{"level":"info","message":"Running account activation timeout check...","timestamp":"2025-07-12 00:00:00:00"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 00:01:00:10"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 00:02:00:20"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/government/notifications/1/read\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at authenticate (/Users/<USER>/Desktop/brid1/backend/middleware/auth.js:35:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","method":"PATCH","timestamp":"2025-07-12 00:02:33:233","url":"/api/government/notifications/1/read","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 00:03:00:30"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 00:04:00:40"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 00:05:00:50"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 00:06:00:60"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 00:07:00:70"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 00:08:00:80"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-12 00:55:55:5555"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-12 00:55:55:5555"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 00:56:00:560"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 00:57:00:570"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 00:58:00:580"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 00:59:00:590"}
{"level":"info","message":"Running account activation timeout check...","timestamp":"2025-07-12 01:00:00:00"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 01:00:00:00"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 01:01:00:10"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 01:02:00:20"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 01:03:00:30"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 01:04:00:40"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 01:05:00:50"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 01:06:00:60"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 01:07:00:70"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 01:08:00:80"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 01:09:00:90"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 01:10:00:100"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 01:11:00:110"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 01:12:00:120"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 01:13:00:130"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 01:14:00:140"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 01:15:00:150"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 01:16:00:160"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/government/notifications/mark-all-read\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at authenticate (/Users/<USER>/Desktop/brid1/backend/middleware/auth.js:35:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","method":"PATCH","timestamp":"2025-07-12 01:16:26:1626","url":"/api/government/notifications/mark-all-read","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 01:17:00:170"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-12 01:17:48:1748"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-12 01:17:48:1748"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 01:18:00:180"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 01:19:00:190"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/government/notifications/1\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at authenticate (/Users/<USER>/Desktop/brid1/backend/middleware/auth.js:35:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","method":"DELETE","timestamp":"2025-07-12 01:19:36:1936","url":"/api/government/notifications/1","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 01:20:00:200"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-12 01:20:18:2018"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-12 01:20:18:2018"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 01:21:00:210"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 01:22:00:220"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 01:23:00:230"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 01:24:00:240"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 01:25:00:250"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 01:26:00:260"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 01:27:00:270"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 01:28:00:280"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 01:29:00:290"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 01:30:00:300"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 01:31:00:310"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 01:32:00:320"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 01:33:00:330"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 01:34:00:340"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 01:35:00:350"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 01:36:00:360"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 01:37:00:370"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 01:38:00:380"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 01:39:00:390"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 01:40:00:400"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 01:41:00:410"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 01:42:00:420"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 01:43:00:430"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 01:44:00:440"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 01:45:00:450"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 01:46:00:460"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-12 01:46:09:469"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-12 01:46:09:469"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-12 01:46:30:4630"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-12 01:46:30:4630"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 01:47:00:470"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 01:48:00:480"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 01:49:00:490"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 01:50:00:500"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-12 01:50:57:5057"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-12 01:50:57:5057"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 01:51:00:510"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-12 01:51:19:5119"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-12 01:51:19:5119"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-12 01:51:46:5146"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-12 01:51:46:5146"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 01:52:00:520"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-12 01:52:04:524"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-12 01:52:04:524"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-12 01:52:18:5218"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-12 01:52:18:5218"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-12 01:52:32:5232"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-12 01:52:32:5232"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-12 01:52:43:5243"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-12 01:52:43:5243"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 01:53:00:530"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-12 01:53:31:5331"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-12 01:53:31:5331"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-12 01:53:45:5345"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-12 01:53:45:5345"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 01:54:00:540"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-12 01:54:03:543"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-12 01:54:03:543"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-12 01:54:47:5447"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-12 01:54:47:5447"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 01:55:00:550"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 01:56:00:560"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 01:57:00:570"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 01:58:00:580"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 01:59:00:590"}
{"level":"info","message":"Running account activation timeout check...","timestamp":"2025-07-12 02:00:00:00"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 02:00:00:00"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 02:01:00:10"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 02:02:00:20"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 02:03:00:30"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 02:04:00:40"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 02:05:00:50"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 02:06:00:60"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 02:07:00:70"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 02:08:00:80"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 02:09:00:90"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 02:10:00:100"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 02:11:00:110"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 02:12:00:120"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-12 02:12:09:129"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-12 02:12:09:129"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-12 02:12:47:1247"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-12 02:12:47:1247"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 02:13:00:130"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-12 02:13:22:1322"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-12 02:13:22:1322"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-12 02:13:54:1354"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-12 02:13:54:1354"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 02:14:00:140"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-12 02:14:04:144"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-12 02:14:04:144"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-12 02:14:48:1448"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-12 02:14:48:1448"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 02:15:00:150"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-12 02:15:05:155"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-12 02:15:05:155"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-12 02:15:21:1521"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-12 02:15:21:1521"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-12 02:15:37:1537"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-12 02:15:37:1537"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-12 02:15:55:1555"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-12 02:15:55:1555"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 02:16:00:160"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 02:17:00:170"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 02:18:00:180"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-12 08:34:31:3431"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-12 08:34:31:3431"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 08:35:00:350"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 08:35:47:3547","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 08:35:47:3547","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 08:36:00:360"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 08:36:08:368","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 08:36:08:368","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 08:36:10:3610","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 08:36:10:3610","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 08:36:21:3621","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 08:36:21:3621","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 08:36:49:3649","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 08:36:49:3649","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 08:37:00:370"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 08:37:09:379","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 08:37:09:379","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 08:37:20:3720","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 08:37:20:3720","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 08:37:41:3741","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 08:37:41:3741","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 08:37:52:3752","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 08:37:52:3752","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 08:38:00:380"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/favorites\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at Function.handle (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:175:3)\n    at router (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:47:12)","method":"POST","timestamp":"2025-07-12 08:38:37:3837","url":"/api/favorites","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 08:38:46:3846","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 08:38:46:3846","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 08:39:00:390"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 08:40:00:400"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 08:41:00:410"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 08:42:00:420"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 08:42:47:4247","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 08:42:47:4247","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 08:42:51:4251","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 08:42:51:4251","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 08:43:00:430","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 08:43:00:430","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 08:43:00:430"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 08:43:12:4312","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 08:43:12:4312","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 08:43:20:4320","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 08:43:20:4320","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 08:44:00:440"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 08:44:53:4453","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 08:44:53:4453","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 08:44:54:4454","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 08:44:54:4454","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 08:45:00:450"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/favorites\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at Function.handle (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:175:3)\n    at router (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:47:12)","method":"POST","timestamp":"2025-07-12 08:45:52:4552","url":"/api/favorites","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 08:46:00:460"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 08:47:00:470"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 08:47:46:4746","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 08:47:46:4746","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 08:48:00:480"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 08:49:00:490"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 08:50:00:500"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 08:50:06:506","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 08:50:06:506","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 08:50:07:507","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 08:50:07:507","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 08:51:00:510"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 08:52:00:520"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 08:52:07:527","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 08:52:07:527","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 08:53:00:530"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 08:54:00:540"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 08:55:00:550"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 08:56:00:560"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 08:56:55:5655","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 08:56:55:5655","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 08:57:00:570"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 08:58:00:580"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 08:59:00:590"}
{"level":"info","message":"Running account activation timeout check...","timestamp":"2025-07-12 09:00:00:00"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 09:00:00:00"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 09:01:00:10"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 09:02:00:20"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 09:03:00:30"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 09:04:00:40"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/analytics?range=30d\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 09:04:46:446","url":"/api/user/analytics?range=30d","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/analytics?range=30d\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 09:04:46:446","url":"/api/user/analytics?range=30d","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 09:04:51:451","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 09:04:51:451","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 09:04:55:455","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 09:04:55:455","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 09:05:00:50"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 09:06:00:60"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 09:07:00:70"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 09:07:24:724","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 09:07:24:724","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 09:08:00:80"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 09:09:00:90"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 09:10:00:100"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 09:10:46:1046","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 09:10:46:1046","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 09:10:50:1050","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 09:10:50:1050","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 09:11:00:110"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 09:11:20:1120","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 09:11:20:1120","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 09:12:00:120"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 09:13:00:130"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/analytics?range=30d\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 09:13:07:137","url":"/api/user/analytics?range=30d","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/analytics?range=30d\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 09:13:07:137","url":"/api/user/analytics?range=30d","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 09:14:00:140"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/analytics?range=30d\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 09:14:41:1441","url":"/api/user/analytics?range=30d","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/analytics?range=30d\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 09:14:41:1441","url":"/api/user/analytics?range=30d","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 09:15:00:150"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 09:16:00:160"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 09:17:00:170"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 09:17:59:1759","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 09:17:59:1759","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 09:18:00:180"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 09:19:00:190"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/analytics?range=30d\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 09:19:49:1949","url":"/api/user/analytics?range=30d","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/analytics?range=30d\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 09:19:49:1949","url":"/api/user/analytics?range=30d","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 09:20:00:200"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 09:21:00:210"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 09:22:00:220"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 09:22:26:2226","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 09:22:26:2226","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/analytics?range=30d\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 09:22:36:2236","url":"/api/user/analytics?range=30d","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/analytics?range=30d\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 09:22:36:2236","url":"/api/user/analytics?range=30d","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 09:22:44:2244","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 09:22:44:2244","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 09:22:54:2254","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 09:22:54:2254","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 09:23:00:230"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-12 09:26:20:2620"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-12 09:26:20:2620"}
{"ip":"::1","level":"error","message":"Error: Not found - /favicon.ico\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 09:26:51:2651","url":"/favicon.ico","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 09:27:00:270"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 09:28:00:280"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 09:29:00:290"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 09:30:00:300"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 09:31:00:310"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 09:31:38:3138","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 09:31:38:3138","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 09:32:00:320"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 09:33:00:330"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 09:34:00:340"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 09:35:00:350"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 09:36:00:360"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 09:37:00:370"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 09:38:00:380"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 09:39:00:390"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 09:40:00:400"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 09:41:00:410"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 09:42:00:420"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 09:43:00:430"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 09:44:00:440"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 09:45:00:450"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 09:46:00:460"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 09:47:00:470"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 09:48:00:480"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 09:49:00:490"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-12 09:49:33:4933"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-12 09:49:33:4933"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 09:50:00:500"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 09:51:00:510"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 09:52:00:520"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 09:53:00:530"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-12 09:53:37:5337"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-12 09:53:37:5337"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-12 09:53:55:5355"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-12 09:53:55:5355"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 09:54:00:540"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-12 10:01:30:130"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-12 10:01:30:130"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 10:02:00:20"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 10:03:00:30"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-12 10:03:35:335"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-12 10:03:35:335"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-12 10:03:45:345"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-12 10:03:45:345"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 10:04:00:40"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-12 10:04:08:48"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-12 10:04:08:48"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 10:05:00:50"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 10:06:00:60"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 10:07:00:70"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 10:08:00:80"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 10:09:00:90"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-12 10:09:15:915"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-12 10:09:15:915"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-12 10:09:27:927"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-12 10:09:27:927"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 10:10:00:100"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 10:11:00:110"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 10:12:00:120"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 10:13:00:130"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 10:14:00:140"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/favorites\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:265:14)\n    at Function.handle (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:175:3)\n    at router (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:47:12)","method":"POST","timestamp":"2025-07-12 10:14:17:1417","url":"/api/favorites","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 10:15:00:150"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 10:16:00:160"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 10:17:00:170"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 10:18:00:180"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 10:19:00:190"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 10:20:00:200"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 10:21:00:210"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 10:22:00:220"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 10:23:00:230"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 10:24:00:240"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 10:25:00:250"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 10:26:00:260"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 10:27:00:270"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 10:28:00:280"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 10:28:38:2838","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 10:28:38:2838","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 10:29:00:290"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 10:30:00:300"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 10:31:00:310"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 10:32:00:320"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 10:33:00:330"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 10:33:13:3313","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 10:33:13:3313","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 10:34:00:340"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 10:35:00:350"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 10:36:00:360"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 10:37:00:370"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 10:37:51:3751","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 10:37:51:3751","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 10:38:00:380"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 10:39:00:390"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 10:40:00:400"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 10:41:00:410"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 10:42:00:420"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 10:43:00:430"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/analytics?range=30d\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 10:43:19:4319","url":"/api/user/analytics?range=30d","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/analytics?range=30d\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 10:43:19:4319","url":"/api/user/analytics?range=30d","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 10:44:00:440"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/analytics?range=30d\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 10:44:19:4419","url":"/api/user/analytics?range=30d","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/analytics?range=30d\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 10:44:19:4419","url":"/api/user/analytics?range=30d","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 10:45:00:450"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/analytics?range=30d\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 10:45:15:4515","url":"/api/user/analytics?range=30d","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/analytics?range=30d\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 10:45:15:4515","url":"/api/user/analytics?range=30d","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 10:46:00:460"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 10:47:00:470"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 10:48:00:480"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 10:49:00:490"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 10:50:00:500"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 10:51:00:510"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 10:52:00:520"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 10:53:00:530"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 10:54:00:540"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 10:55:00:550"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 10:56:00:560"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 10:57:00:570"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 10:58:00:580"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 10:59:00:590"}
{"level":"info","message":"Running account activation timeout check...","timestamp":"2025-07-12 11:00:00:00"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 11:00:00:00"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 11:01:00:10"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 11:01:11:111","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 11:01:11:111","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 11:02:00:20"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 11:03:00:30"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 11:04:00:40"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 11:05:00:50"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 11:05:18:518","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 11:05:18:518","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/analytics?range=30d\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 11:05:53:553","url":"/api/user/analytics?range=30d","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/analytics?range=30d\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 11:05:53:553","url":"/api/user/analytics?range=30d","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 11:06:00:60"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 11:07:00:70"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 11:08:00:80"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/analytics?range=30d\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 11:08:44:844","url":"/api/user/analytics?range=30d","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/analytics?range=30d\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 11:08:44:844","url":"/api/user/analytics?range=30d","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 11:09:00:90"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/analytics?range=30d\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 11:09:05:95","url":"/api/user/analytics?range=30d","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/analytics?range=30d\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 11:09:05:95","url":"/api/user/analytics?range=30d","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/analytics?range=30d\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 11:09:22:922","url":"/api/user/analytics?range=30d","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/analytics?range=30d\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 11:09:22:922","url":"/api/user/analytics?range=30d","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/analytics?range=30d\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 11:09:39:939","url":"/api/user/analytics?range=30d","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/analytics?range=30d\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 11:09:39:939","url":"/api/user/analytics?range=30d","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 11:09:52:952","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/dashboard/stats\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 11:09:52:952","url":"/api/user/dashboard/stats","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 11:10:00:100"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 11:11:00:110"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 11:12:00:120"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 11:13:00:130"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 11:14:00:140"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 11:15:00:150"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 11:16:00:160"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 11:17:00:170"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 11:18:00:180"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 11:19:00:190"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 11:20:00:200"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 11:21:00:210"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 11:22:00:220"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 11:23:00:230"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 11:24:00:240"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 11:25:00:250"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 11:26:00:260"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 11:27:00:270"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 11:28:00:280"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 11:29:00:290"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-12 11:29:47:2947"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-12 11:29:47:2947"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 11:30:00:300"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/bids?limit=5\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 11:30:05:305","url":"/api/user/bids?limit=5","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/user/bids?limit=5\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 11:30:05:305","url":"/api/user/bids?limit=5","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 11:31:00:310"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 11:32:00:320"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 11:33:00:330"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 11:34:00:340"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 11:35:00:350"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 11:36:00:360"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 11:37:00:370"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 11:38:00:380"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 11:39:00:390"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 11:40:00:400"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 11:41:00:410"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 11:42:00:420"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 11:43:00:430"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 11:44:00:440"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 11:45:00:450"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 11:46:00:460"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 11:47:00:470"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 11:48:00:480"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 11:49:00:490"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 11:50:00:500"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 11:51:00:510"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 11:52:00:520"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 11:53:00:530"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 11:54:00:540"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-12 11:54:47:5447"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-12 11:54:47:5447"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 11:55:00:550"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-12 11:55:03:553"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-12 11:55:03:553"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 11:56:00:560"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 11:57:00:570"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 11:58:00:580"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 11:59:00:590"}
{"level":"info","message":"Running account activation timeout check...","timestamp":"2025-07-12 12:00:00:00"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 12:00:00:00"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-12 12:00:14:014"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-12 12:00:14:014"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-12 12:00:46:046"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-12 12:00:46:046"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 12:01:00:10"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-12 12:01:01:11"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-12 12:01:01:11"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-12 12:01:26:126"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-12 12:01:26:126"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 12:02:00:20"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-12 12:02:02:22"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-12 12:02:02:22"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 12:03:00:30"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 12:04:00:40"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 12:05:00:50"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 12:06:00:60"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 12:07:00:70"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 12:08:00:80"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 12:09:00:90"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 12:10:00:100"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 12:11:00:110"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 12:12:00:120"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 12:13:00:130"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-12 12:13:14:1314"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-12 12:13:14:1314"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-12 12:13:34:1334"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-12 12:13:34:1334"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-12 12:13:51:1351"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-12 12:13:51:1351"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 12:14:00:140"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-12 12:14:08:148"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-12 12:14:08:148"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 12:15:00:150"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-12 12:15:01:151"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-12 12:15:01:151"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-12 12:15:32:1532"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-12 12:15:32:1532"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 12:16:00:160"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 12:17:00:170"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 12:18:00:180"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 12:19:00:190"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 12:20:00:200"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 12:21:00:210"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 12:22:00:220"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 12:23:00:230"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 12:24:00:240"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 12:25:00:250"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 12:26:00:260"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 12:27:00:270"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 12:28:00:280"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 12:29:00:290"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 12:30:00:300"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 12:31:00:310"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 12:32:00:320"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 12:33:00:330"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 12:34:00:340"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 12:35:00:350"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 12:36:00:360"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 12:37:00:370"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 12:38:00:380"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 12:39:00:390"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 12:40:00:400"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/payment/methods\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 12:40:04:404","url":"/api/payment/methods","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/payment/methods\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 12:40:04:404","url":"/api/payment/methods","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 12:41:00:410"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/payment/methods\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:85:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"POST","timestamp":"2025-07-12 12:41:17:4117","url":"/api/payment/methods","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/billing/invoices\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 12:41:38:4138","url":"/api/billing/invoices","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/billing/invoices\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 12:41:38:4138","url":"/api/billing/invoices","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/billing/invoices/2/pay\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:103:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"POST","timestamp":"2025-07-12 12:41:48:4148","url":"/api/billing/invoices/2/pay","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/wallet/balance\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 12:41:58:4158","url":"/api/wallet/balance","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/wallet/transactions\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 12:41:58:4158","url":"/api/wallet/transactions","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/wallet/balance\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 12:41:58:4158","url":"/api/wallet/balance","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/wallet/transactions\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 12:41:58:4158","url":"/api/wallet/transactions","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 12:42:00:420"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/wallet/deposit\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:85:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"POST","timestamp":"2025-07-12 12:42:40:4240","url":"/api/wallet/deposit","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 12:43:00:430"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 12:44:00:440"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 12:45:00:450"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 12:46:00:460"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 12:47:00:470"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/wallet/balance\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 12:47:57:4757","url":"/api/wallet/balance","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/wallet/transactions\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 12:47:57:4757","url":"/api/wallet/transactions","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/wallet/balance\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 12:47:57:4757","url":"/api/wallet/balance","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/wallet/transactions\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 12:47:57:4757","url":"/api/wallet/transactions","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 12:48:00:480"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 12:49:00:490"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/wallet/balance\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 12:49:37:4937","url":"/api/wallet/balance","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/wallet/transactions\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 12:49:37:4937","url":"/api/wallet/transactions","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/wallet/balance\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 12:49:37:4937","url":"/api/wallet/balance","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/wallet/transactions\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 12:49:37:4937","url":"/api/wallet/transactions","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 12:50:00:500"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/wallet/balance\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 12:50:15:5015","url":"/api/wallet/balance","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/wallet/transactions\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 12:50:15:5015","url":"/api/wallet/transactions","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/wallet/balance\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 12:50:15:5015","url":"/api/wallet/balance","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error: Not found - /api/wallet/transactions\n    at notFound (/Users/<USER>/Desktop/brid1/backend/middleware/notFound.js:3:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/brid1/backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/brid1/backend/node_modules/express/lib/router/index.js:286:9","method":"GET","timestamp":"2025-07-12 12:50:15:5015","url":"/api/wallet/transactions","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 12:51:00:510"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 12:52:00:520"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 12:53:00:530"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 12:54:00:540"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 12:55:00:550"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 12:56:00:560"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 12:57:00:570"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 12:58:00:580"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 12:59:00:590"}
{"level":"info","message":"Running account activation timeout check...","timestamp":"2025-07-12 13:00:00:00"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 13:00:00:00"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 13:01:00:10"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 13:02:00:20"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 13:03:00:30"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 13:04:00:40"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 13:05:00:50"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-12 13:05:36:536"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-12 13:05:36:536"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-12 13:05:47:547"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-12 13:05:47:547"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 13:06:00:60"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-12 13:06:20:620"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-12 13:06:20:620"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-12 13:06:34:634"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-12 13:06:34:634"}
{"level":"info","message":"Checking for auction endings...","timestamp":"2025-07-12 13:07:00:70"}
{"level":"info","message":"Server running on port 5000","timestamp":"2025-07-12 13:07:10:710"}
{"level":"info","message":"Environment: development","timestamp":"2025-07-12 13:07:10:710"}
