const express = require('express');
const { body, validationResult } = require('express-validator');
const { authenticate } = require('../middleware/auth');
const {
  paymentLimiter,
  validateInput,
  validateAmount,
  validateUserPermissions,
  preventRapidRequests,
  auditLog
} = require('../middleware/security');
const PaymentService = require('../services/paymentService');
const Auction = require('../models/Auction');
const Tender = require('../models/Tender');
const User = require('../models/User');

const router = express.Router();
const paymentService = new PaymentService();

// Validation middleware
const validatePayment = [
  body('amount').isFloat({ min: 1 }).withMessage('Amount must be greater than 0'),
  body('paymentMethod').isIn(['mada', 'visa', 'mastercard', 'apple_pay', 'stc_pay', 'bank_transfer']).withMessage('Invalid payment method'),
  body('gatewayProvider').optional().isIn(['mada', 'moyasar', 'hyperpay', 'tap_payments']).withMessage('Invalid gateway provider'),
  body('type').isIn(['auction_payment', 'tender_deposit', 'tender_payment']).withMessage('Invalid payment type'),
];

// @route   POST /api/payments/create
// @desc    Create a new payment
// @access  Private
router.post('/create',
  paymentLimiter,
  validateInput,
  authenticate,
  validateUserPermissions(['individual', 'company', 'government']),
  preventRapidRequests(5000),
  validateAmount,
  auditLog('CREATE_PAYMENT'),
  ...validatePayment,
  async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const {
      amount,
      paymentMethod,
      gatewayProvider = 'mada',
      auction,
      tender,
      type,
      description
    } = req.body;

    // Validate auction or tender exists
    let auctionData = null;
    let tenderData = null;
    let payee = null;

    if (auction) {
      auctionData = await Auction.findById(auction).populate('auctioneer');
      if (!auctionData) {
        return res.status(404).json({
          success: false,
          message: 'Auction not found'
        });
      }
      payee = auctionData.auctioneer._id;

      // Validate user can pay for this auction
      if (type === 'auction_payment') {
        const winningBid = auctionData.bids
          .sort((a, b) => b.amount - a.amount)[0];
        
        if (!winningBid || winningBid.bidder.toString() !== req.user._id.toString()) {
          return res.status(403).json({
            success: false,
            message: 'You are not the winning bidder for this auction'
          });
        }

        if (amount !== winningBid.amount) {
          return res.status(400).json({
            success: false,
            message: 'Payment amount must match winning bid amount'
          });
        }
      }
    }

    if (tender) {
      tenderData = await Tender.findById(tender).populate('organization');
      if (!tenderData) {
        return res.status(404).json({
          success: false,
          message: 'Tender not found'
        });
      }
      payee = tenderData.organization._id;

      // Validate tender payment
      if (type === 'tender_deposit') {
        if (amount < tenderData.requiredDeposit) {
          return res.status(400).json({
            success: false,
            message: `Deposit amount must be at least ${tenderData.requiredDeposit} SAR`
          });
        }
      }
    }

    // Get client IP and user agent for fraud prevention
    const clientIP = req.ip || req.connection.remoteAddress;
    const userAgent = req.headers['user-agent'];

    const paymentData = {
      amount,
      paymentMethod,
      gatewayProvider,
      payer: req.user._id,
      payee,
      auction,
      tender,
      type,
      description: description || `Payment for ${type}`,
      metadata: {
        ipAddress: clientIP,
        userAgent,
        customerEmail: req.user.email,
        customerPhone: req.user.profile?.phone,
        customerName: req.user.profile?.fullName,
        successUrl: `${process.env.FRONTEND_URL}/payment/success`,
        cancelUrl: `${process.env.FRONTEND_URL}/payment/cancel`,
        deviceFingerprint: req.headers['x-device-fingerprint']
      }
    };

    const payment = await paymentService.createPayment(paymentData);

    res.status(201).json({
      success: true,
      message: 'Payment created successfully',
      data: payment
    });

  } catch (error) {
    console.error('Payment creation error:', error);
    res.status(500).json({
      success: false,
      message: error.message || 'Failed to create payment'
    });
  }
});

// @route   GET /api/payments/my-payments
// @desc    Get user's payment history
// @access  Private
router.get('/my-payments', authenticate, async (req, res) => {
  try {
    const { status, type, page = 1, limit = 10 } = req.query;
    
    const options = {};
    if (status) options.status = status;
    if (type) options.type = type;

    const payments = await paymentService.getUserPayments(req.user._id, options);
    
    // Ensure payments is an array
    const paymentsArray = Array.isArray(payments) ? payments : [];
    
    // Pagination
    const startIndex = (page - 1) * parseInt(limit);
    const paginatedPayments = paymentsArray.slice(startIndex, startIndex + parseInt(limit));

    res.json({
      success: true,
      data: {
        payments: paginatedPayments,
        total: paymentsArray.length,
        page: parseInt(page),
        totalPages: Math.ceil(paymentsArray.length / parseInt(limit))
      }
    });

  } catch (error) {
    console.error('Get payments error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve payments'
    });
  }
});

// @route   GET /api/payments/admin/stats
// @desc    Get payment statistics (Admin only)
// @access  Private (Admin)
router.get('/admin/stats', authenticate, async (req, res) => {
  try {
    if (req.user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        message: 'Access denied'
      });
    }

    const { startDate, endDate } = req.query;
    
    // Build aggregation pipeline
    const pipeline = [
      {
        $match: {
          createdAt: {
            $gte: startDate ? new Date(startDate) : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
            $lte: endDate ? new Date(endDate) : new Date()
          }
        }
      },
      {
        $group: {
          _id: null,
          totalAmount: { $sum: '$amount' },
          totalPlatformFees: { $sum: '$platformFee.amount' },
          totalPayments: { $sum: 1 },
          completedPayments: {
            $sum: { $cond: [{ $eq: ['$status', 'completed'] }, 1, 0] }
          },
          pendingPayments: {
            $sum: { $cond: [{ $eq: ['$status', 'pending'] }, 1, 0] }
          },
          failedPayments: {
            $sum: { $cond: [{ $eq: ['$status', 'failed'] }, 1, 0] }
          },
          escrowHeld: {
            $sum: { $cond: [{ $eq: ['$escrow.status', 'held'] }, '$amount', 0] }
          }
        }
      }
    ];

    const Payment = require('../models/Payment');
    const stats = await Payment.aggregate(pipeline);

    res.json({
      success: true,
      data: stats[0] || {
        totalAmount: 0,
        totalPlatformFees: 0,
        totalPayments: 0,
        completedPayments: 0,
        pendingPayments: 0,
        failedPayments: 0,
        escrowHeld: 0
      }
    });

  } catch (error) {
    console.error('Payment stats error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve payment statistics'
    });
  }
});

// @route   GET /api/payments/:id
// @desc    Get payment details
// @access  Private
router.get('/:id', authenticate, async (req, res) => {
  try {
    const payment = await paymentService.getPayment(req.params.id);
    
    if (!payment) {
      return res.status(404).json({
        success: false,
        message: 'Payment not found'
      });
    }

    // Check if user is authorized to view this payment
    // Handle cases where payer/payee might be undefined
    const payerId = payment.payer?._id?.toString() || payment.payer?.toString();
    const payeeId = payment.payee?._id?.toString() || payment.payee?.toString();
    const userId = req.user._id.toString();
    
    if (payerId !== userId && payeeId !== userId && req.user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        message: 'Access denied'
      });
    }

    res.json({
      success: true,
      data: { payment }
    });

  } catch (error) {
    console.error('Get payment error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve payment'
    });
  }
});

// @route   POST /api/payments/:id/refund
// @desc    Refund a payment
// @access  Private (Admin or payee only)
router.post('/:id/refund', authenticate, [
  body('reason').notEmpty().withMessage('Refund reason is required'),
  body('amount').optional().isFloat({ min: 0 }).withMessage('Refund amount must be positive')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const payment = await paymentService.getPayment(req.params.id);
    
    if (!payment) {
      return res.status(404).json({
        success: false,
        message: 'Payment not found'
      });
    }

    // Check authorization
    const payeeId = payment.payee?._id?.toString() || payment.payee?.toString();
    const userId = req.user._id.toString();
    
    if (req.user.role !== 'admin' && payeeId !== userId) {
      return res.status(403).json({
        success: false,
        message: 'Access denied'
      });
    }

    // Check if payment can be refunded
    if (!['completed', 'processing'].includes(payment.status)) {
      return res.status(400).json({
        success: false,
        message: 'Payment cannot be refunded'
      });
    }

    const { reason, amount } = req.body;
    const result = await paymentService.refundPayment(
      req.params.id, 
      req.user._id, 
      reason, 
      amount
    );

    res.json({
      success: true,
      message: 'Refund processed successfully',
      data: result
    });

  } catch (error) {
    console.error('Refund error:', error);
    res.status(500).json({
      success: false,
      message: error.message || 'Failed to process refund'
    });
  }
});

// @route   POST /api/payments/:id/release-escrow
// @desc    Release payment from escrow
// @access  Private (Admin or authorized user)
router.post('/:id/release-escrow', authenticate, [
  body('reason').notEmpty().withMessage('Release reason is required')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const payment = await paymentService.getPayment(req.params.id);
    
    if (!payment) {
      return res.status(404).json({
        success: false,
        message: 'Payment not found'
      });
    }

    // Check authorization - only payer, admin, or system can release escrow
    const payerId = payment.payer?._id?.toString() || payment.payer?.toString();
    const userId = req.user._id.toString();
    
    if (req.user.role !== 'admin' && payerId !== userId) {
      return res.status(403).json({
        success: false,
        message: 'Access denied'
      });
    }

    // Check if escrow can be released
    if (!payment.escrow || payment.escrow.status !== 'held') {
      return res.status(400).json({
        success: false,
        message: 'Escrow is not currently held'
      });
    }

    const { reason } = req.body;
    const updatedPayment = await paymentService.releaseEscrow(
      req.params.id, 
      req.user._id, 
      reason
    );

    res.json({
      success: true,
      message: 'Escrow released successfully',
      data: { payment: updatedPayment }
    });

  } catch (error) {
    console.error('Escrow release error:', error);
    res.status(500).json({
      success: false,
      message: error.message || 'Failed to release escrow'
    });
  }
});

// @route   POST /api/payments/webhook/:gateway
// @desc    Handle payment gateway webhooks
// @access  Public (but verified by signature)
router.post('/webhook/:gateway', async (req, res) => {
  try {
    const { gateway } = req.params;
    const signature = req.headers['x-signature'] || req.headers['signature'];
    
    if (!signature) {
      return res.status(400).json({
        success: false,
        message: 'Missing signature'
      });
    }

    const payment = await paymentService.processWebhook(gateway, req.body, signature);
    
    // Log webhook for debugging - check if payment exists
    if (payment) {
      console.log(`Webhook processed for gateway ${gateway}:`, {
        paymentId: payment._id,
        status: payment.status,
        transactionId: payment.gatewayTransactionId
      });
    } else {
      console.log(`Webhook processed for gateway ${gateway}: No payment returned`);
    }

    res.json({
      success: true,
      message: 'Webhook processed successfully'
    });

  } catch (error) {
    console.error(`Webhook error for ${req.params.gateway}:`, error);
    
    // Return 200 to prevent gateway retries for invalid webhooks
    res.status(200).json({
      success: false,
      message: error.message
    });
  }
});


module.exports = router;
