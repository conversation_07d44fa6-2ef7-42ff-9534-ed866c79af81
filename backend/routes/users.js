const express = require('express');
const { body, validationResult } = require('express-validator');
const User = require('../models/User');
const { authenticate, requireApproved } = require('../middleware/auth');
const { uploadToCloudinary } = require('../utils/cloudinary');
const upload = require('../middleware/upload');

const router = express.Router();

// Apply authentication to all user routes
router.use(authenticate);

// @route   GET /api/users/profile
// @desc    Get current user profile
// @access  Private
router.get('/profile', async (req, res) => {
  try {
    const user = await User.findById(req.user._id).select('-password');

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    res.json({
      success: true,
      data: { user }
    });
  } catch (error) {
    console.error('Get profile error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching profile'
    });
  }
});

// @route   PUT /api/users/profile
// @desc    Update user profile
// @access  Private
router.put('/profile',
  [
    body('profile.phone').optional().isMobilePhone(),
    body('profile.fullName').optional().trim().isLength({ min: 2 }),
    body('profile.companyName').optional().trim().isLength({ min: 2 }),
    body('profile.governmentEntity').optional().trim().isLength({ min: 2 })
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array()
        });
      }

      const { profile } = req.body;
      const user = await User.findById(req.user._id);

      if (!user) {
        return res.status(404).json({
          success: false,
          message: 'User not found'
        });
      }

      // Update profile fields
      if (profile) {
        Object.keys(profile).forEach(key => {
          if (profile[key] !== undefined) {
            user.profile[key] = profile[key];
          }
        });
      }

      await user.save();

      const updatedUser = await User.findById(req.user._id).select('-password');

      res.json({
        success: true,
        message: 'Profile updated successfully',
        data: { user: updatedUser }
      });
    } catch (error) {
      console.error('Update profile error:', error);
      res.status(500).json({
        success: false,
        message: 'Server error while updating profile'
      });
    }
  }
);

// @route   POST /api/users/upload-documents
// @desc    Upload additional documents
// @access  Private
router.post('/upload-documents',
  upload.array('documents', 10),
  async (req, res) => {
    try {
      if (!req.files || req.files.length === 0) {
        return res.status(400).json({
          success: false,
          message: 'No files uploaded'
        });
      }

      const user = await User.findById(req.user._id);

      if (!user) {
        return res.status(404).json({
          success: false,
          message: 'User not found'
        });
      }

      // Upload documents to cloudinary
      const uploadedDocs = [];
      for (const file of req.files) {
        const uploadResult = await uploadToCloudinary(file.buffer, {
          folder: `user-documents/${user.email}`,
          resource_type: 'auto'
        });
        
        uploadedDocs.push({
          name: file.originalname,
          type: file.mimetype,
          url: uploadResult.secure_url
        });
      }

      // Add new documents to user
      user.documents.push(...uploadedDocs);

      // If user is in rejected status and uploading new docs, change to documents_submitted
      if (user.status === 'rejected') {
        user.status = 'documents_submitted';
        user.submittedAt = new Date();
        user.rejectionReason = undefined;
      }

      await user.save();

      res.json({
        success: true,
        message: 'Documents uploaded successfully',
        data: {
          documents: uploadedDocs,
          user: {
            id: user._id,
            status: user.status,
            documentsCount: user.documents.length
          }
        }
      });
    } catch (error) {
      console.error('Upload documents error:', error);
      res.status(500).json({
        success: false,
        message: 'Server error while uploading documents'
      });
    }
  }
);

// @route   DELETE /api/users/document/:documentId
// @desc    Delete a document
// @access  Private
router.delete('/document/:documentId', async (req, res) => {
  try {
    const user = await User.findById(req.user._id);

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    const documentIndex = user.documents.findIndex(
      doc => doc._id.toString() === req.params.documentId
    );

    if (documentIndex === -1) {
      return res.status(404).json({
        success: false,
        message: 'Document not found'
      });
    }

    // Remove document from array
    user.documents.splice(documentIndex, 1);
    await user.save();

    res.json({
      success: true,
      message: 'Document deleted successfully'
    });
  } catch (error) {
    console.error('Delete document error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while deleting document'
    });
  }
});

// @route   POST /api/users/submit-for-review
// @desc    Submit account for admin review
// @access  Private
router.post('/submit-for-review', async (req, res) => {
  try {
    const user = await User.findById(req.user._id);

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    if (user.status !== 'pending_documents' && user.status !== 'rejected') {
      return res.status(400).json({
        success: false,
        message: 'Account cannot be submitted for review at this time'
      });
    }

    if (user.documents.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Please upload required documents before submitting for review'
      });
    }

    user.status = 'documents_submitted';
    user.submittedAt = new Date();
    user.rejectionReason = undefined;

    await user.save();

    res.json({
      success: true,
      message: 'Account submitted for review successfully',
      data: {
        user: {
          id: user._id,
          status: user.status,
          submittedAt: user.submittedAt
        }
      }
    });
  } catch (error) {
    console.error('Submit for review error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while submitting for review'
    });
  }
});

// @route   GET /api/users/account-status
// @desc    Get account status and requirements
// @access  Private
router.get('/account-status', async (req, res) => {
  try {
    const user = await User.findById(req.user._id).select('-password');

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Define required documents based on role
    const getRequiredDocuments = (role) => {
      switch (role) {
        case 'individual':
          return [
            'الهوية الوطنية أو الإقامة',
            'صورة شخصية',
            'إثبات العنوان'
          ];
        case 'company':
          return [
            'السجل التجاري',
            'رخصة المهنة',
            'شهادة الزكاة والضريبة',
            'عقد التأسيس',
            'صورة المفوض بالتوقيع'
          ];
        case 'government':
          return [
            'التفويض الرسمي من الجهة',
            'هوية الموظف المخول',
            'خطاب رسمي من الجهة',
            'معرف الجهة الحكومية'
          ];
        default:
          return [];
      }
    };

    const requiredDocuments = getRequiredDocuments(user.role);
    const uploadedCount = user.documents.length;
    const isComplete = uploadedCount >= requiredDocuments.length;

    res.json({
      success: true,
      data: {
        user: {
          id: user._id,
          email: user.email,
          role: user.role,
          status: user.status,
          emailVerified: user.emailVerified,
          profile: user.profile,
          rejectionReason: user.rejectionReason,
          submittedAt: user.submittedAt,
          reviewedAt: user.reviewedAt
        },
        requirements: {
          requiredDocuments,
          uploadedDocuments: user.documents,
          uploadedCount,
          isComplete,
          canSubmit: isComplete && (user.status === 'pending_documents' || user.status === 'rejected')
        }
      }
    });
  } catch (error) {
    console.error('Account status error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching account status'
    });
  }
});

// @route   POST /api/users/change-password
// @desc    Change user password
// @access  Private
router.post('/change-password',
  [
    body('currentPassword').notEmpty().withMessage('Current password is required'),
    body('newPassword').isLength({ min: 6 }).withMessage('New password must be at least 6 characters')
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array()
        });
      }

      const { currentPassword, newPassword } = req.body;
      const user = await User.findById(req.user._id);

      if (!user) {
        return res.status(404).json({
          success: false,
          message: 'User not found'
        });
      }

      // Check current password
      const isCurrentPasswordValid = await user.comparePassword(currentPassword);
      if (!isCurrentPasswordValid) {
        return res.status(400).json({
          success: false,
          message: 'Current password is incorrect'
        });
      }

      // Update password
      user.password = newPassword;
      await user.save();

      res.json({
        success: true,
        message: 'Password changed successfully'
      });
    } catch (error) {
      console.error('Change password error:', error);
      res.status(500).json({
        success: false,
        message: 'Server error while changing password'
      });
    }
  }
);

// @route   DELETE /api/users/account
// @desc    Delete user account (soft delete)
// @access  Private
router.delete('/account', async (req, res) => {
  try {
    const user = await User.findById(req.user._id);

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Soft delete by changing status
    user.status = 'blocked';
    user.rejectionReason = 'Account deleted by user request';
    await user.save();

    res.json({
      success: true,
      message: 'Account deleted successfully'
    });
  } catch (error) {
    console.error('Delete account error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while deleting account'
    });
  }
});

// @route   GET /api/users/dashboard/stats
// @desc    Get user dashboard statistics
// @access  Private (Individual users)
router.get('/dashboard/stats', requireApproved, async (req, res) => {
  try {
    const userId = req.user._id;

    // Import models here to avoid circular dependencies
    const Auction = require('../models/Auction');

    // Get auctions where user has placed bids
    const auctionsWithUserBids = await Auction.find({
      'bids.bidder': userId
    }).select('title status winner bids currentBid finalPrice');

    // Calculate stats
    let activeBids = 0;
    let wonAuctions = 0;
    let totalSpent = 0;

    auctionsWithUserBids.forEach(auction => {
      const userBidsInAuction = auction.bids.filter(bid =>
        bid.bidder.toString() === userId.toString()
      );

      if (userBidsInAuction.length > 0) {
        if (auction.status === 'active') {
          activeBids++;
        }

        if (auction.status === 'completed' && auction.winner?.toString() === userId.toString()) {
          wonAuctions++;
          totalSpent += auction.finalPrice || auction.currentBid;
        }
      }
    });

    // Get saved auctions count (favorites)
    const savedAuctions = req.user.favorites ? req.user.favorites.length : 0;

    const totalBids = auctionsWithUserBids.reduce((total, auction) => {
      return total + auction.bids.filter(bid => bid.bidder.toString() === userId.toString()).length;
    }, 0);

    const successRate = totalBids > 0 ? Math.round((wonAuctions / auctionsWithUserBids.length) * 100) : 0;

    res.json({
      success: true,
      data: {
        activeBids,
        wonAuctions,
        totalSpent,
        savedAuctions,
        totalBids,
        successRate
      }
    });
  } catch (error) {
    console.error('Dashboard stats error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching dashboard stats'
    });
  }
});



// @route   GET /api/users/notifications
// @desc    Get user notifications
// @access  Private
router.get('/notifications', requireApproved, async (req, res) => {
  try {
    const userId = req.user._id;
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    // For now, return sample notifications
    // In a real app, you'd have a Notification model
    const sampleNotifications = [
      {
        _id: '1',
        type: 'auction_won',
        title: 'مبروك! فزت بالمزاد',
        message: 'لقد فزت بمزاد جهاز كمبيوتر محمول Dell',
        createdAt: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
        read: false
      },
      {
        _id: '2',
        type: 'auction_ending',
        title: 'تذكير: المزاد ينتهي قريباً',
        message: 'مزاد السيارة BMW ينتهي خلال ساعة واحدة',
        createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
        read: false
      },
      {
        _id: '3',
        type: 'new_auction',
        title: 'مزاد جديد قد يهمك',
        message: 'تم إضافة مزاد جديد في فئة الإلكترونيات',
        createdAt: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(),
        read: true
      },
      {
        _id: '4',
        type: 'bid_outbid',
        title: 'تم تجاوز مزايدتك',
        message: 'تم تجاوز مزايدتك في مزاد الأثاث المكتبي',
        createdAt: new Date(Date.now() - 12 * 60 * 60 * 1000).toISOString(),
        read: true
      }
    ];

    const notifications = sampleNotifications.slice(skip, skip + limit);
    const total = sampleNotifications.length;

    res.json({
      success: true,
      data: {
        notifications,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      }
    });
  } catch (error) {
    console.error('User notifications error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching notifications'
    });
  }
});

// @route   GET /api/users/analytics/charts
// @desc    Get user analytics data for charts
// @access  Private (Individual users)
router.get('/analytics/charts', requireApproved, async (req, res) => {
  try {
    const userId = req.user._id;
    const Auction = require('../models/Auction');

    // Get current date and 6 months ago
    const now = new Date();
    const sixMonthsAgo = new Date();
    sixMonthsAgo.setMonth(now.getMonth() - 6);

    // Get auctions where user has placed bids in the last 6 months
    const auctionsWithUserBids = await Auction.find({
      'bids.bidder': userId,
      createdAt: { $gte: sixMonthsAgo }
    }).select('title category status winner bids currentBid finalPrice createdAt endTime');

    // Generate bidding activity data (monthly)
    const biddingActivity = [];
    const monthNames = ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو', 'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'];

    for (let i = 5; i >= 0; i--) {
      const monthDate = new Date();
      monthDate.setMonth(now.getMonth() - i);
      const monthStart = new Date(monthDate.getFullYear(), monthDate.getMonth(), 1);
      const monthEnd = new Date(monthDate.getFullYear(), monthDate.getMonth() + 1, 0);

      const monthAuctions = auctionsWithUserBids.filter(auction => {
        const auctionDate = new Date(auction.createdAt);
        return auctionDate >= monthStart && auctionDate <= monthEnd;
      });

      const bids = monthAuctions.reduce((total, auction) => {
        return total + auction.bids.filter(bid => bid.bidder.toString() === userId.toString()).length;
      }, 0);

      const wins = monthAuctions.filter(auction =>
        auction.status === 'completed' && auction.winner?.toString() === userId.toString()
      ).length;

      biddingActivity.push({
        month: monthNames[monthDate.getMonth()],
        bids,
        wins
      });
    }

    // Generate category spending data
    const categorySpending = {};
    const categoryColors = ['#3B82F6', '#8B5CF6', '#10B981', '#F59E0B', '#EF4444', '#06B6D4'];

    auctionsWithUserBids.forEach(auction => {
      if (auction.status === 'completed' && auction.winner?.toString() === userId.toString()) {
        const amount = auction.finalPrice || auction.currentBid;
        const category = auction.category || 'أخرى';

        if (!categorySpending[category]) {
          categorySpending[category] = 0;
        }
        categorySpending[category] += amount;
      }
    });

    const categorySpendingArray = Object.entries(categorySpending).map(([name, value], index) => ({
      name: getCategoryNameInArabic(name),
      value,
      color: categoryColors[index % categoryColors.length]
    }));

    // Generate monthly performance data
    const monthlyPerformance = [];
    for (let i = 5; i >= 0; i--) {
      const monthDate = new Date();
      monthDate.setMonth(now.getMonth() - i);
      const monthStart = new Date(monthDate.getFullYear(), monthDate.getMonth(), 1);
      const monthEnd = new Date(monthDate.getFullYear(), monthDate.getMonth() + 1, 0);

      const monthAuctions = auctionsWithUserBids.filter(auction => {
        const auctionDate = new Date(auction.createdAt);
        return auctionDate >= monthStart && auctionDate <= monthEnd;
      });

      const spent = monthAuctions
        .filter(auction => auction.status === 'completed' && auction.winner?.toString() === userId.toString())
        .reduce((total, auction) => total + (auction.finalPrice || auction.currentBid), 0);

      // Calculate "saved" as difference between highest bid and winning bid
      const saved = monthAuctions
        .filter(auction => auction.status === 'completed' && auction.winner?.toString() === userId.toString())
        .reduce((total, auction) => {
          const userBids = auction.bids.filter(bid => bid.bidder.toString() === userId.toString());
          const maxUserBid = Math.max(...userBids.map(bid => bid.amount));
          const finalPrice = auction.finalPrice || auction.currentBid;
          return total + Math.max(0, maxUserBid - finalPrice);
        }, 0);

      monthlyPerformance.push({
        month: monthNames[monthDate.getMonth()],
        spent,
        saved
      });
    }

    // Calculate win/loss ratio
    const totalAuctions = auctionsWithUserBids.length;
    const wonAuctions = auctionsWithUserBids.filter(auction =>
      auction.status === 'completed' && auction.winner?.toString() === userId.toString()
    ).length;

    const winPercentage = totalAuctions > 0 ? Math.round((wonAuctions / totalAuctions) * 100) : 0;
    const lossPercentage = 100 - winPercentage;

    const winLossRatio = [
      { name: 'فوز', value: winPercentage, color: '#10B981' },
      { name: 'خسارة', value: lossPercentage, color: '#EF4444' }
    ];

    res.json({
      success: true,
      data: {
        biddingActivity,
        categorySpending: categorySpendingArray,
        monthlyPerformance,
        winLossRatio
      }
    });
  } catch (error) {
    console.error('User analytics charts error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching analytics data'
    });
  }
});

// Helper function to translate category names to Arabic
function getCategoryNameInArabic(category) {
  const translations = {
    'electronics': 'إلكترونيات',
    'vehicles': 'سيارات',
    'real_estate': 'عقارات',
    'art_collectibles': 'فنون ومقتنيات',
    'machinery': 'معدات',
    'furniture': 'أثاث',
    'jewelry': 'مجوهرات',
    'books_media': 'كتب ووسائط',
    'sports_recreation': 'رياضة وترفيه',
    'fashion_accessories': 'أزياء وإكسسوارات',
    'home_garden': 'منزل وحديقة',
    'business_industrial': 'أعمال وصناعة',
    'other': 'أخرى'
  };
  return translations[category] || category;
}


// @route   GET /api/users/leaderboard
// @desc    Get leaderboard data
// @access  Private
router.get('/leaderboard', requireApproved, async (req, res) => {
  try {
    const period = req.query.period || 'all-time'; // daily, weekly, monthly, all-time
    const limit = parseInt(req.query.limit) || 50;

    const Auction = require('../models/Auction');

    // Calculate date range based on period
    let dateFilter = {};
    const now = new Date();

    switch (period) {
      case 'daily':
        const startOfDay = new Date(now.getFullYear(), now.getMonth(), now.getDate());
        dateFilter = { createdAt: { $gte: startOfDay } };
        break;
      case 'weekly':
        const startOfWeek = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        dateFilter = { createdAt: { $gte: startOfWeek } };
        break;
      case 'monthly':
        const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
        dateFilter = { createdAt: { $gte: startOfMonth } };
        break;
      default:
        // all-time: no date filter
        break;
    }

    // Aggregate user statistics
    const leaderboardData = await Auction.aggregate([
      { $match: dateFilter },
      { $unwind: '$bids' },
      {
        $group: {
          _id: '$bids.bidder',
          totalBids: { $sum: 1 },
          totalAmount: { $sum: '$bids.amount' },
          wonAuctions: {
            $sum: {
              $cond: [
                { $and: [
                  { $eq: ['$status', 'completed'] },
                  { $eq: ['$winner', '$bids.bidder'] }
                ]},
                1,
                0
              ]
            }
          }
        }
      },
      {
        $lookup: {
          from: 'users',
          localField: '_id',
          foreignField: '_id',
          as: 'user'
        }
      },
      { $unwind: '$user' },
      {
        $project: {
          _id: 1,
          user: {
            profile: '$user.profile',
            email: '$user.email'
          },
          totalBids: 1,
          totalAmount: 1,
          wonAuctions: 1,
          points: {
            $add: [
              { $multiply: ['$totalBids', 1] },      // 1 point per bid
              { $multiply: ['$wonAuctions', 10] },   // 10 points per win
              { $divide: ['$totalAmount', 1000] }    // 1 point per 1000 SAR
            ]
          }
        }
      },
      { $sort: { points: -1 } },
      { $limit: limit }
    ]);

    // Add rank and badge to each entry
    let leaderboard = leaderboardData.map((entry, index) => {
      const rank = index + 1;
      let badge = 'Beginner';

      if (entry.points >= 1000) badge = 'Champion';
      else if (entry.points >= 500) badge = 'Master';
      else if (entry.points >= 200) badge = 'Expert';
      else if (entry.points >= 100) badge = 'Pro';
      else if (entry.points >= 50) badge = 'Active';

      return {
        ...entry,
        rank,
        badge,
        points: Math.round(entry.points)
      };
    });

    // If no real data, return mock data for demonstration
    if (leaderboard.length === 0) {
      const User = require('../models/User');
      const mockUsers = await User.find({ role: 'individual', status: 'approved' }).limit(10);

      leaderboard = mockUsers.map((user, index) => {
        const mockStats = {
          totalBids: Math.floor(Math.random() * 50) + 5,
          totalAmount: Math.floor(Math.random() * 50000) + 1000,
          wonAuctions: Math.floor(Math.random() * 10) + 1,
        };

        const points = mockStats.totalBids * 1 + mockStats.wonAuctions * 10 + mockStats.totalAmount / 1000;

        let badge = 'Beginner';
        if (points >= 1000) badge = 'Champion';
        else if (points >= 500) badge = 'Master';
        else if (points >= 200) badge = 'Expert';
        else if (points >= 100) badge = 'Pro';
        else if (points >= 50) badge = 'Active';

        return {
          _id: user._id,
          user: {
            profile: user.profile,
            email: user.email
          },
          totalBids: mockStats.totalBids,
          totalAmount: mockStats.totalAmount,
          wonAuctions: mockStats.wonAuctions,
          points: Math.round(points),
          rank: index + 1,
          badge
        };
      }).sort((a, b) => b.points - a.points).map((entry, index) => ({
        ...entry,
        rank: index + 1
      }));
    }

    res.json({
      success: true,
      data: {
        leaderboard,
        period,
        total: leaderboard.length
      }
    });
  } catch (error) {
    console.error('Leaderboard error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching leaderboard'
    });
  }
});

// @route   GET /api/users/applications
// @desc    Get user's tender applications
// @access  Private (Individual users)
router.get('/applications', requireApproved, async (req, res) => {
  try {
    const userId = req.user._id;
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    const Tender = require('../models/Tender');

    // Get tenders where user has applied
    const tenders = await Tender.find({
      'applications.applicant': userId
    })
    .populate('organizer', 'profile.governmentEntity profile.fullName')
    .sort({ 'applications.submittedAt': -1 });

    // Extract user's applications with tender context
    let allApplications = [];
    tenders.forEach(tender => {
      const userApplications = tender.applications
        .filter(app => app.applicant.toString() === userId.toString())
        .map(app => ({
          _id: app._id,
          tender: {
            _id: tender._id,
            title: tender.title,
            budget: tender.budget,
            deadline: tender.deadline,
            status: tender.status,
            organizer: tender.organizer
          },
          submittedAt: app.submittedAt,
          status: app.status,
          proposedBudget: app.proposedBudget,
          timeline: app.timeline,
          score: app.score,
          reviewNotes: app.reviewNotes
        }));
      allApplications = allApplications.concat(userApplications);
    });

    // Sort by submission date and paginate
    allApplications.sort((a, b) => new Date(b.submittedAt) - new Date(a.submittedAt));
    const paginatedApplications = allApplications.slice(skip, skip + limit);
    const total = allApplications.length;

    res.json({
      success: true,
      data: {
        applications: paginatedApplications,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      }
    });
  } catch (error) {
    console.error('User applications error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching user applications'
    });
  }
});

// @route   GET /api/users/bids
// @desc    Get user's bids
// @access  Private (Individual users)
router.get('/bids', requireApproved, async (req, res) => {
  try {
    const userId = req.user._id;
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    const Auction = require('../models/Auction');

    // Get auctions where user has placed bids
    const auctionsWithUserBids = await Auction.find({
      'bids.bidder': userId
    })
    .populate('seller', 'profile.companyName profile.fullName')
    .sort({ 'bids.timestamp': -1 });

    // Extract user's bids with auction context
    let allBids = [];
    auctionsWithUserBids.forEach(auction => {
      const userBids = auction.bids
        .filter(bid => bid.bidder.toString() === userId.toString())
        .map(bid => ({
          _id: bid._id,
          auction: {
            _id: auction._id,
            title: auction.title,
            status: auction.status,
            endTime: auction.endDate,
            currentBid: auction.currentPrice,
            seller: auction.seller
          },
          amount: bid.amount,
          timestamp: bid.timestamp,
          status: bid.status || (auction.status === 'active' ?
            (bid.amount === auction.currentPrice ? 'winning' : 'outbid') :
            (auction.winner && auction.winner.toString() === userId.toString() ? 'won' : 'lost')),
          isHighest: bid.amount === auction.currentPrice
        }));
      allBids = allBids.concat(userBids);
    });

    // Sort by timestamp and paginate
    allBids.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
    const paginatedBids = allBids.slice(skip, skip + limit);
    const total = allBids.length;

    res.json({
      success: true,
      data: {
        bids: paginatedBids,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      }
    });
  } catch (error) {
    console.error('User bids error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching user bids'
    });
  }
});

module.exports = router;
