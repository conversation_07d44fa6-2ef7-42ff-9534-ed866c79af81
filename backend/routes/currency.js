const express = require('express');
const router = express.Router();
const currencyService = require('../services/currencyService');

// Get current exchange rates
router.get('/rates', async (req, res) => {
  try {
    const rates = await currencyService.getExchangeRates();
    
    res.json({
      success: true,
      data: {
        rates,
        lastUpdated: Date.now(),
        baseCurrency: 'USD'
      }
    });
  } catch (error) {
    console.error('Error fetching currency rates:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch currency rates',
      error: error.message
    });
  }
});

// Convert currency
router.post('/convert', async (req, res) => {
  try {
    const { amount, fromCurrency, toCurrency } = req.body;
    
    if (!amount || !fromCurrency || !toCurrency) {
      return res.status(400).json({
        success: false,
        message: 'Amount, fromCurrency, and toCurrency are required'
      });
    }
    
    if (!currencyService.isValidCurrency(fromCurrency) || !currencyService.isValidCurrency(toCurrency)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid currency code'
      });
    }
    
    const convertedAmount = await currencyService.convertCurrency(amount, fromCurrency, toCurrency);
    
    res.json({
      success: true,
      data: {
        originalAmount: amount,
        fromCurrency,
        toCurrency,
        convertedAmount,
        rate: convertedAmount / amount,
        timestamp: Date.now()
      }
    });
  } catch (error) {
    console.error('Error converting currency:', error);
    res.status(500).json({
      success: false,
      message: 'Currency conversion failed',
      error: error.message
    });
  }
});

// Get supported currencies
router.get('/supported', (req, res) => {
  try {
    const currencies = currencyService.getSupportedCurrencies();
    
    res.json({
      success: true,
      data: {
        currencies,
        count: currencies.length
      }
    });
  } catch (error) {
    console.error('Error fetching supported currencies:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch supported currencies',
      error: error.message
    });
  }
});

module.exports = router;
